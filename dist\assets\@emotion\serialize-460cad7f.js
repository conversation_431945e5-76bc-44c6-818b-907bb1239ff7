import{m as C}from"./cache-9a5b99cd.js";function w(r){for(var e=0,n,t=0,a=r.length;a>=4;++t,a-=4)n=r.charCodeAt(t)&255|(r.charCodeAt(++t)&255)<<8|(r.charCodeAt(++t)&255)<<16|(r.charCodeAt(++t)&255)<<24,n=(n&65535)*1540483477+((n>>>16)*59797<<16),n^=n>>>24,e=(n&65535)*1540483477+((n>>>16)*59797<<16)^(e&65535)*1540483477+((e>>>16)*59797<<16);switch(a){case 3:e^=(r.charCodeAt(t+2)&255)<<16;case 2:e^=(r.charCodeAt(t+1)&255)<<8;case 1:e^=r.charCodeAt(t)&255,e=(e&65535)*1540483477+((e>>>16)*59797<<16)}return e^=e>>>13,e=(e&65535)*1540483477+((e>>>16)*59797<<16),((e^e>>>15)>>>0).toString(36)}var y={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},O=!1,S=/[A-Z]|^ms/g,A=/_EMO_([^_]+?)_([^]*?)_EMO_/g,b=function(e){return e.charCodeAt(1)===45},h=function(e){return e!=null&&typeof e!="boolean"},x=C(function(r){return b(r)?r:r.replace(S,"-$&").toLowerCase()}),p=function(e,n){switch(e){case"animation":case"animationName":if(typeof n=="string")return n.replace(A,function(t,a,o){return l={name:a,styles:o,next:l},a})}return y[e]!==1&&!b(e)&&typeof n=="number"&&n!==0?n+"px":n},g="Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.";function m(r,e,n){if(n==null)return"";var t=n;if(t.__emotion_styles!==void 0)return t;switch(typeof n){case"boolean":return"";case"object":{var a=n;if(a.anim===1)return l={name:a.name,styles:a.styles,next:l},a.name;var o=n;if(o.styles!==void 0){var f=o.next;if(f!==void 0)for(;f!==void 0;)l={name:f.name,styles:f.styles,next:l},f=f.next;var i=o.styles+";";return i}return E(r,e,n)}case"function":{if(r!==void 0){var s=l,c=n(r);return l=s,m(r,e,c)}break}}var u=n;if(e==null)return u;var d=e[u];return d!==void 0?d:u}function E(r,e,n){var t="";if(Array.isArray(n))for(var a=0;a<n.length;a++)t+=m(r,e,n[a])+";";else for(var o in n){var f=n[o];if(typeof f!="object"){var i=f;e!=null&&e[i]!==void 0?t+=o+"{"+e[i]+"}":h(i)&&(t+=x(o)+":"+p(o,i)+";")}else{if(o==="NO_COMPONENT_SELECTOR"&&O)throw new Error(g);if(Array.isArray(f)&&typeof f[0]=="string"&&(e==null||e[f[0]]===void 0))for(var s=0;s<f.length;s++)h(f[s])&&(t+=x(o)+":"+p(o,f[s])+";");else{var c=m(r,e,f);switch(o){case"animation":case"animationName":{t+=x(o)+":"+c+";";break}default:t+=o+"{"+c+"}"}}}}return t}var v=/label:\s*([^\s;{]+)\s*(;|$)/g,l;function _(r,e,n){if(r.length===1&&typeof r[0]=="object"&&r[0]!==null&&r[0].styles!==void 0)return r[0];var t=!0,a="";l=void 0;var o=r[0];if(o==null||o.raw===void 0)t=!1,a+=m(n,e,o);else{var f=o;a+=f[0]}for(var i=1;i<r.length;i++)if(a+=m(n,e,r[i]),t){var s=o;a+=s[i]}v.lastIndex=0;for(var c="",u;(u=v.exec(a))!==null;)c+="-"+u[1];var d=w(a)+c;return{name:d,styles:a,next:l}}export{_ as s};
