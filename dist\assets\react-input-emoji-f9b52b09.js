import{r as k,b as L,d as oi}from"./vendor-851db8c1.js";var Ze={};/**
 * @license React
 * react-dom-server-legacy.browser.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Eo=k;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ne=Object.prototype.hasOwnProperty,ui=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,nr={},rr={};function jo(e){return ne.call(rr,e)?!0:ne.call(nr,e)?!1:ui.test(e)?rr[e]=!0:(nr[e]=!0,!1)}function Q(e,t,n,r,u,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=u,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var K={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){K[e]=new Q(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];K[t]=new Q(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){K[e]=new Q(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){K[e]=new Q(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){K[e]=new Q(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){K[e]=new Q(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){K[e]=new Q(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){K[e]=new Q(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){K[e]=new Q(e,5,!1,e.toLowerCase(),null,!1,!1)});var zn=/[\-:]([a-z])/g;function In(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(zn,In);K[t]=new Q(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(zn,In);K[t]=new Q(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(zn,In);K[t]=new Q(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){K[e]=new Q(e,1,!1,e.toLowerCase(),null,!1,!1)});K.xlinkHref=new Q("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){K[e]=new Q(e,1,!1,e.toLowerCase(),null,!0,!0)});var gt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ii=["Webkit","ms","Moz","O"];Object.keys(gt).forEach(function(e){ii.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),gt[t]=gt[e]})});var ai=/["'&<>]/;function Y(e){if(typeof e=="boolean"||typeof e=="number")return""+e;e=""+e;var t=ai.exec(e);if(t){var n="",r,u=0;for(r=t.index;r<e.length;r++){switch(e.charCodeAt(r)){case 34:t="&quot;";break;case 38:t="&amp;";break;case 39:t="&#x27;";break;case 60:t="&lt;";break;case 62:t="&gt;";break;default:continue}u!==r&&(n+=e.substring(u,r)),u=r+1,n+=t}e=u!==r?n+e.substring(u,r):n}return e}var di=/([A-Z])/g,si=/^ms-/,vn=Array.isArray;function _e(e,t){return{insertionMode:e,selectedValue:t}}function li(e,t,n){switch(t){case"select":return _e(1,n.value!=null?n.value:n.defaultValue);case"svg":return _e(2,null);case"math":return _e(3,null);case"foreignObject":return _e(1,null);case"table":return _e(4,null);case"thead":case"tbody":case"tfoot":return _e(5,null);case"colgroup":return _e(7,null);case"tr":return _e(6,null)}return 4<=e.insertionMode||e.insertionMode===0?_e(1,null):e}var or=new Map;function Ro(e,t,n){if(typeof n!="object")throw Error(T(62));t=!0;for(var r in n)if(ne.call(n,r)){var u=n[r];if(u!=null&&typeof u!="boolean"&&u!==""){if(r.indexOf("--")===0){var o=Y(r);u=Y((""+u).trim())}else{o=r;var i=or.get(o);i!==void 0||(i=Y(o.replace(di,"-$1").toLowerCase().replace(si,"-ms-")),or.set(o,i)),o=i,u=typeof u=="number"?u===0||ne.call(gt,r)?""+u:u+"px":Y((""+u).trim())}t?(t=!1,e.push(' style="',o,":",u)):e.push(";",o,":",u)}}t||e.push('"')}function oe(e,t,n,r){switch(n){case"style":Ro(e,t,r);return;case"defaultValue":case"defaultChecked":case"innerHTML":case"suppressContentEditableWarning":case"suppressHydrationWarning":return}if(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N"){if(t=K.hasOwnProperty(n)?K[n]:null,t!==null){switch(typeof r){case"function":case"symbol":return;case"boolean":if(!t.acceptsBooleans)return}switch(n=t.attributeName,t.type){case 3:r&&e.push(" ",n,'=""');break;case 4:r===!0?e.push(" ",n,'=""'):r!==!1&&e.push(" ",n,'="',Y(r),'"');break;case 5:isNaN(r)||e.push(" ",n,'="',Y(r),'"');break;case 6:!isNaN(r)&&1<=r&&e.push(" ",n,'="',Y(r),'"');break;default:t.sanitizeURL&&(r=""+r),e.push(" ",n,'="',Y(r),'"')}}else if(jo(n)){switch(typeof r){case"function":case"symbol":return;case"boolean":if(t=n.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-")return}e.push(" ",n,'="',Y(r),'"')}}}function bt(e,t,n){if(t!=null){if(n!=null)throw Error(T(60));if(typeof t!="object"||!("__html"in t))throw Error(T(61));t=t.__html,t!=null&&e.push(""+t)}}function ci(e){var t="";return Eo.Children.forEach(e,function(n){n!=null&&(t+=n)}),t}function nn(e,t,n,r){e.push(ve(n));var u=n=null,o;for(o in t)if(ne.call(t,o)){var i=t[o];if(i!=null)switch(o){case"children":n=i;break;case"dangerouslySetInnerHTML":u=i;break;default:oe(e,r,o,i)}}return e.push(">"),bt(e,u,n),typeof n=="string"?(e.push(Y(n)),null):n}var fi=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,ur=new Map;function ve(e){var t=ur.get(e);if(t===void 0){if(!fi.test(e))throw Error(T(65,e));t="<"+e,ur.set(e,t)}return t}function pi(e,t,n,r,u){switch(t){case"select":e.push(ve("select"));var o=null,i=null;for(s in n)if(ne.call(n,s)){var a=n[s];if(a!=null)switch(s){case"children":o=a;break;case"dangerouslySetInnerHTML":i=a;break;case"defaultValue":case"value":break;default:oe(e,r,s,a)}}return e.push(">"),bt(e,i,o),o;case"option":i=u.selectedValue,e.push(ve("option"));var d=a=null,l=null,s=null;for(o in n)if(ne.call(n,o)){var c=n[o];if(c!=null)switch(o){case"children":a=c;break;case"selected":l=c;break;case"dangerouslySetInnerHTML":s=c;break;case"value":d=c;default:oe(e,r,o,c)}}if(i!=null)if(n=d!==null?""+d:ci(a),vn(i)){for(r=0;r<i.length;r++)if(""+i[r]===n){e.push(' selected=""');break}}else""+i===n&&e.push(' selected=""');else l&&e.push(' selected=""');return e.push(">"),bt(e,s,a),a;case"textarea":e.push(ve("textarea")),s=i=o=null;for(a in n)if(ne.call(n,a)&&(d=n[a],d!=null))switch(a){case"children":s=d;break;case"value":o=d;break;case"defaultValue":i=d;break;case"dangerouslySetInnerHTML":throw Error(T(91));default:oe(e,r,a,d)}if(o===null&&i!==null&&(o=i),e.push(">"),s!=null){if(o!=null)throw Error(T(92));if(vn(s)&&1<s.length)throw Error(T(93));o=""+s}return typeof o=="string"&&o[0]===`
`&&e.push(`
`),o!==null&&e.push(Y(""+o)),null;case"input":e.push(ve("input")),d=s=a=o=null;for(i in n)if(ne.call(n,i)&&(l=n[i],l!=null))switch(i){case"children":case"dangerouslySetInnerHTML":throw Error(T(399,"input"));case"defaultChecked":d=l;break;case"defaultValue":a=l;break;case"checked":s=l;break;case"value":o=l;break;default:oe(e,r,i,l)}return s!==null?oe(e,r,"checked",s):d!==null&&oe(e,r,"checked",d),o!==null?oe(e,r,"value",o):a!==null&&oe(e,r,"value",a),e.push("/>"),null;case"menuitem":e.push(ve("menuitem"));for(var f in n)if(ne.call(n,f)&&(o=n[f],o!=null))switch(f){case"children":case"dangerouslySetInnerHTML":throw Error(T(400));default:oe(e,r,f,o)}return e.push(">"),null;case"title":e.push(ve("title")),o=null;for(c in n)if(ne.call(n,c)&&(i=n[c],i!=null))switch(c){case"children":o=i;break;case"dangerouslySetInnerHTML":throw Error(T(434));default:oe(e,r,c,i)}return e.push(">"),o;case"listing":case"pre":e.push(ve(t)),i=o=null;for(d in n)if(ne.call(n,d)&&(a=n[d],a!=null))switch(d){case"children":o=a;break;case"dangerouslySetInnerHTML":i=a;break;default:oe(e,r,d,a)}if(e.push(">"),i!=null){if(o!=null)throw Error(T(60));if(typeof i!="object"||!("__html"in i))throw Error(T(61));n=i.__html,n!=null&&(typeof n=="string"&&0<n.length&&n[0]===`
`?e.push(`
`,n):e.push(""+n))}return typeof o=="string"&&o[0]===`
`&&e.push(`
`),o;case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":e.push(ve(t));for(var m in n)if(ne.call(n,m)&&(o=n[m],o!=null))switch(m){case"children":case"dangerouslySetInnerHTML":throw Error(T(399,t));default:oe(e,r,m,o)}return e.push("/>"),null;case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return nn(e,n,t,r);case"html":return u.insertionMode===0&&e.push("<!DOCTYPE html>"),nn(e,n,t,r);default:if(t.indexOf("-")===-1&&typeof n.is!="string")return nn(e,n,t,r);e.push(ve(t)),i=o=null;for(l in n)if(ne.call(n,l)&&(a=n[l],a!=null))switch(l){case"children":o=a;break;case"dangerouslySetInnerHTML":i=a;break;case"style":Ro(e,r,a);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":break;default:jo(l)&&typeof a!="function"&&typeof a!="symbol"&&e.push(" ",l,'="',Y(a),'"')}return e.push(">"),bt(e,i,o),o}}function ir(e,t,n){if(e.push('<!--$?--><template id="'),n===null)throw Error(T(395));return e.push(n),e.push('"></template>')}function hi(e,t,n,r){switch(n.insertionMode){case 0:case 1:return e.push('<div hidden id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 2:return e.push('<svg aria-hidden="true" style="display:none" id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 3:return e.push('<math aria-hidden="true" style="display:none" id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 4:return e.push('<table hidden id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 5:return e.push('<table hidden><tbody id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 6:return e.push('<table hidden><tr id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');case 7:return e.push('<table hidden><colgroup id="'),e.push(t.segmentPrefix),t=r.toString(16),e.push(t),e.push('">');default:throw Error(T(397))}}function mi(e,t){switch(t.insertionMode){case 0:case 1:return e.push("</div>");case 2:return e.push("</svg>");case 3:return e.push("</math>");case 4:return e.push("</table>");case 5:return e.push("</tbody></table>");case 6:return e.push("</tr></table>");case 7:return e.push("</colgroup></table>");default:throw Error(T(397))}}var vi=/[<\u2028\u2029]/g;function rn(e){return JSON.stringify(e).replace(vi,function(t){switch(t){case"<":return"\\u003c";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React")}})}function gi(e,t){return t=t===void 0?"":t,{bootstrapChunks:[],startInlineScript:"<script>",placeholderPrefix:t+"P:",segmentPrefix:t+"S:",boundaryPrefix:t+"B:",idPrefix:t,nextSuspenseID:0,sentCompleteSegmentFunction:!1,sentCompleteBoundaryFunction:!1,sentClientRenderFunction:!1,generateStaticMarkup:e}}function ar(e,t,n,r){return n.generateStaticMarkup?(e.push(Y(t)),!1):(t===""?e=r:(r&&e.push("<!-- -->"),e.push(Y(t)),e=!0),e)}var rt=Object.assign,bi=Symbol.for("react.element"),To=Symbol.for("react.portal"),Mo=Symbol.for("react.fragment"),Po=Symbol.for("react.strict_mode"),Lo=Symbol.for("react.profiler"),Fo=Symbol.for("react.provider"),zo=Symbol.for("react.context"),Io=Symbol.for("react.forward_ref"),Ao=Symbol.for("react.suspense"),Do=Symbol.for("react.suspense_list"),Bo=Symbol.for("react.memo"),An=Symbol.for("react.lazy"),yi=Symbol.for("react.scope"),wi=Symbol.for("react.debug_trace_mode"),_i=Symbol.for("react.legacy_hidden"),ki=Symbol.for("react.default_value"),dr=Symbol.iterator;function gn(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Mo:return"Fragment";case To:return"Portal";case Lo:return"Profiler";case Po:return"StrictMode";case Ao:return"Suspense";case Do:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case zo:return(e.displayName||"Context")+".Consumer";case Fo:return(e._context.displayName||"Context")+".Provider";case Io:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Bo:return t=e.displayName||null,t!==null?t:gn(e.type)||"Memo";case An:t=e._payload,e=e._init;try{return gn(e(t))}catch{}}return null}var Oo={};function sr(e,t){if(e=e.contextTypes,!e)return Oo;var n={},r;for(r in e)n[r]=t[r];return n}var He=null;function Gt(e,t){if(e!==t){e.context._currentValue2=e.parentValue,e=e.parent;var n=t.parent;if(e===null){if(n!==null)throw Error(T(401))}else{if(n===null)throw Error(T(401));Gt(e,n)}t.context._currentValue2=t.value}}function Ho(e){e.context._currentValue2=e.parentValue,e=e.parent,e!==null&&Ho(e)}function No(e){var t=e.parent;t!==null&&No(t),e.context._currentValue2=e.value}function Uo(e,t){if(e.context._currentValue2=e.parentValue,e=e.parent,e===null)throw Error(T(402));e.depth===t.depth?Gt(e,t):Uo(e,t)}function Vo(e,t){var n=t.parent;if(n===null)throw Error(T(402));e.depth===n.depth?Gt(e,n):Vo(e,n),t.context._currentValue2=t.value}function Tt(e){var t=He;t!==e&&(t===null?No(e):e===null?Ho(t):t.depth===e.depth?Gt(t,e):t.depth>e.depth?Uo(t,e):Vo(t,e),He=e)}var lr={isMounted:function(){return!1},enqueueSetState:function(e,t){e=e._reactInternals,e.queue!==null&&e.queue.push(t)},enqueueReplaceState:function(e,t){e=e._reactInternals,e.replace=!0,e.queue=[t]},enqueueForceUpdate:function(){}};function cr(e,t,n,r){var u=e.state!==void 0?e.state:null;e.updater=lr,e.props=n,e.state=u;var o={queue:[],replace:!1};e._reactInternals=o;var i=t.contextType;if(e.context=typeof i=="object"&&i!==null?i._currentValue2:r,i=t.getDerivedStateFromProps,typeof i=="function"&&(i=i(n,u),u=i==null?u:rt({},u,i),e.state=u),typeof t.getDerivedStateFromProps!="function"&&typeof e.getSnapshotBeforeUpdate!="function"&&(typeof e.UNSAFE_componentWillMount=="function"||typeof e.componentWillMount=="function"))if(t=e.state,typeof e.componentWillMount=="function"&&e.componentWillMount(),typeof e.UNSAFE_componentWillMount=="function"&&e.UNSAFE_componentWillMount(),t!==e.state&&lr.enqueueReplaceState(e,e.state,null),o.queue!==null&&0<o.queue.length)if(t=o.queue,i=o.replace,o.queue=null,o.replace=!1,i&&t.length===1)e.state=t[0];else{for(o=i?t[0]:e.state,u=!0,i=i?1:0;i<t.length;i++){var a=t[i];a=typeof a=="function"?a.call(e,o,n,r):a,a!=null&&(u?(u=!1,o=rt({},o,a)):rt(o,a))}e.state=o}else o.queue=null}var xi={id:1,overflow:""};function bn(e,t,n){var r=e.id;e=e.overflow;var u=32-yt(r)-1;r&=~(1<<u),n+=1;var o=32-yt(t)+u;if(30<o){var i=u-u%5;return o=(r&(1<<i)-1).toString(32),r>>=i,u-=i,{id:1<<32-yt(t)+u|n<<u|r,overflow:o+e}}return{id:1<<o|n<<u|r,overflow:e}}var yt=Math.clz32?Math.clz32:$i,Si=Math.log,Ci=Math.LN2;function $i(e){return e>>>=0,e===0?32:31-(Si(e)/Ci|0)|0}function Ei(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ji=typeof Object.is=="function"?Object.is:Ei,Ce=null,Dn=null,wt=null,D=null,Qe=!1,Mt=!1,it=0,Me=null,Kt=0;function De(){if(Ce===null)throw Error(T(321));return Ce}function fr(){if(0<Kt)throw Error(T(312));return{memoizedState:null,queue:null,next:null}}function Bn(){return D===null?wt===null?(Qe=!1,wt=D=fr()):(Qe=!0,D=wt):D.next===null?(Qe=!1,D=D.next=fr()):(Qe=!0,D=D.next),D}function On(){Dn=Ce=null,Mt=!1,wt=null,Kt=0,D=Me=null}function Wo(e,t){return typeof t=="function"?t(e):t}function pr(e,t,n){if(Ce=De(),D=Bn(),Qe){var r=D.queue;if(t=r.dispatch,Me!==null&&(n=Me.get(r),n!==void 0)){Me.delete(r),r=D.memoizedState;do r=e(r,n.action),n=n.next;while(n!==null);return D.memoizedState=r,[r,t]}return[D.memoizedState,t]}return e=e===Wo?typeof t=="function"?t():t:n!==void 0?n(t):t,D.memoizedState=e,e=D.queue={last:null,dispatch:null},e=e.dispatch=Ri.bind(null,Ce,e),[D.memoizedState,e]}function hr(e,t){if(Ce=De(),D=Bn(),t=t===void 0?null:t,D!==null){var n=D.memoizedState;if(n!==null&&t!==null){var r=n[1];e:if(r===null)r=!1;else{for(var u=0;u<r.length&&u<t.length;u++)if(!ji(t[u],r[u])){r=!1;break e}r=!0}if(r)return n[0]}}return e=e(),D.memoizedState=[e,t],e}function Ri(e,t,n){if(25<=Kt)throw Error(T(301));if(e===Ce)if(Mt=!0,e={action:n,next:null},Me===null&&(Me=new Map),n=Me.get(t),n===void 0)Me.set(t,e);else{for(t=n;t.next!==null;)t=t.next;t.next=e}}function Ti(){throw Error(T(394))}function ct(){}var mr={readContext:function(e){return e._currentValue2},useContext:function(e){return De(),e._currentValue2},useMemo:hr,useReducer:pr,useRef:function(e){Ce=De(),D=Bn();var t=D.memoizedState;return t===null?(e={current:e},D.memoizedState=e):t},useState:function(e){return pr(Wo,e)},useInsertionEffect:ct,useLayoutEffect:function(){},useCallback:function(e,t){return hr(function(){return e},t)},useImperativeHandle:ct,useEffect:ct,useDebugValue:ct,useDeferredValue:function(e){return De(),e},useTransition:function(){return De(),[!1,Ti]},useId:function(){var e=Dn.treeContext,t=e.overflow;e=e.id,e=(e&~(1<<32-yt(e)-1)).toString(32)+t;var n=_t;if(n===null)throw Error(T(404));return t=it++,e=":"+n.idPrefix+"R"+e,0<t&&(e+="H"+t.toString(32)),e+":"},useMutableSource:function(e,t){return De(),t(e._source)},useSyncExternalStore:function(e,t,n){if(n===void 0)throw Error(T(407));return n()}},_t=null,on=Eo.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher;function Mi(e){return console.error(e),null}function et(){}function Pi(e,t,n,r,u,o,i,a,d){var l=[],s=new Set;return t={destination:null,responseState:t,progressiveChunkSize:r===void 0?12800:r,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:s,pingedTasks:l,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],onError:u===void 0?Mi:u,onAllReady:o===void 0?et:o,onShellReady:i===void 0?et:i,onShellError:a===void 0?et:a,onFatalError:d===void 0?et:d},n=Pt(t,0,null,n,!1,!1),n.parentFlushed=!0,e=Hn(t,e,null,n,s,Oo,null,xi),l.push(e),t}function Hn(e,t,n,r,u,o,i,a){e.allPendingTasks++,n===null?e.pendingRootTasks++:n.pendingTasks++;var d={node:t,ping:function(){var l=e.pingedTasks;l.push(d),l.length===1&&Ko(e)},blockedBoundary:n,blockedSegment:r,abortSet:u,legacyContext:o,context:i,treeContext:a};return u.add(d),d}function Pt(e,t,n,r,u,o){return{status:0,id:-1,index:t,parentFlushed:!1,chunks:[],children:[],formatContext:r,boundary:n,lastPushedText:u,textEmbedded:o}}function at(e,t){if(e=e.onError(t),e!=null&&typeof e!="string")throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof e+'" instead');return e}function Lt(e,t){var n=e.onShellError;n(t),n=e.onFatalError,n(t),e.destination!==null?(e.status=2,e.destination.destroy(t)):(e.status=1,e.fatalError=t)}function vr(e,t,n,r,u){for(Ce={},Dn=t,it=0,e=n(r,u);Mt;)Mt=!1,it=0,Kt+=1,D=null,e=n(r,u);return On(),e}function gr(e,t,n,r){var u=n.render(),o=r.childContextTypes;if(o!=null){var i=t.legacyContext;if(typeof n.getChildContext!="function")r=i;else{n=n.getChildContext();for(var a in n)if(!(a in o))throw Error(T(108,gn(r)||"Unknown",a));r=rt({},i,n)}t.legacyContext=r,ie(e,t,u),t.legacyContext=i}else ie(e,t,u)}function br(e,t){if(e&&e.defaultProps){t=rt({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function yn(e,t,n,r,u){if(typeof n=="function")if(n.prototype&&n.prototype.isReactComponent){u=sr(n,t.legacyContext);var o=n.contextType;o=new n(r,typeof o=="object"&&o!==null?o._currentValue2:u),cr(o,n,r,u),gr(e,t,o,n)}else{o=sr(n,t.legacyContext),u=vr(e,t,n,r,o);var i=it!==0;if(typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0)cr(u,n,r,o),gr(e,t,u,n);else if(i){r=t.treeContext,t.treeContext=bn(r,1,0);try{ie(e,t,u)}finally{t.treeContext=r}}else ie(e,t,u)}else if(typeof n=="string"){switch(u=t.blockedSegment,o=pi(u.chunks,n,r,e.responseState,u.formatContext),u.lastPushedText=!1,i=u.formatContext,u.formatContext=li(i,n,r),wn(e,t,o),u.formatContext=i,n){case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"input":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":break;default:u.chunks.push("</",n,">")}u.lastPushedText=!1}else{switch(n){case _i:case wi:case Po:case Lo:case Mo:ie(e,t,r.children);return;case Do:ie(e,t,r.children);return;case yi:throw Error(T(343));case Ao:e:{n=t.blockedBoundary,u=t.blockedSegment,o=r.fallback,r=r.children,i=new Set;var a={id:null,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,forceClientRender:!1,completedSegments:[],byteSize:0,fallbackAbortableTasks:i,errorDigest:null},d=Pt(e,u.chunks.length,a,u.formatContext,!1,!1);u.children.push(d),u.lastPushedText=!1;var l=Pt(e,0,null,u.formatContext,!1,!1);l.parentFlushed=!0,t.blockedBoundary=a,t.blockedSegment=l;try{if(wn(e,t,r),e.responseState.generateStaticMarkup||l.lastPushedText&&l.textEmbedded&&l.chunks.push("<!-- -->"),l.status=1,Ft(a,l),a.pendingTasks===0)break e}catch(s){l.status=4,a.forceClientRender=!0,a.errorDigest=at(e,s)}finally{t.blockedBoundary=n,t.blockedSegment=u}t=Hn(e,o,n,d,i,t.legacyContext,t.context,t.treeContext),e.pingedTasks.push(t)}return}if(typeof n=="object"&&n!==null)switch(n.$$typeof){case Io:if(r=vr(e,t,n.render,r,u),it!==0){n=t.treeContext,t.treeContext=bn(n,1,0);try{ie(e,t,r)}finally{t.treeContext=n}}else ie(e,t,r);return;case Bo:n=n.type,r=br(n,r),yn(e,t,n,r,u);return;case Fo:if(u=r.children,n=n._context,r=r.value,o=n._currentValue2,n._currentValue2=r,i=He,He=r={parent:i,depth:i===null?0:i.depth+1,context:n,parentValue:o,value:r},t.context=r,ie(e,t,u),e=He,e===null)throw Error(T(403));r=e.parentValue,e.context._currentValue2=r===ki?e.context._defaultValue:r,e=He=e.parent,t.context=e;return;case zo:r=r.children,r=r(n._currentValue2),ie(e,t,r);return;case An:u=n._init,n=u(n._payload),r=br(n,r),yn(e,t,n,r,void 0);return}throw Error(T(130,n==null?n:typeof n,""))}}function ie(e,t,n){if(t.node=n,typeof n=="object"&&n!==null){switch(n.$$typeof){case bi:yn(e,t,n.type,n.props,n.ref);return;case To:throw Error(T(257));case An:var r=n._init;n=r(n._payload),ie(e,t,n);return}if(vn(n)){yr(e,t,n);return}if(n===null||typeof n!="object"?r=null:(r=dr&&n[dr]||n["@@iterator"],r=typeof r=="function"?r:null),r&&(r=r.call(n))){if(n=r.next(),!n.done){var u=[];do u.push(n.value),n=r.next();while(!n.done);yr(e,t,u)}return}throw e=Object.prototype.toString.call(n),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e))}typeof n=="string"?(r=t.blockedSegment,r.lastPushedText=ar(t.blockedSegment.chunks,n,e.responseState,r.lastPushedText)):typeof n=="number"&&(r=t.blockedSegment,r.lastPushedText=ar(t.blockedSegment.chunks,""+n,e.responseState,r.lastPushedText))}function yr(e,t,n){for(var r=n.length,u=0;u<r;u++){var o=t.treeContext;t.treeContext=bn(o,r,u);try{wn(e,t,n[u])}finally{t.treeContext=o}}}function wn(e,t,n){var r=t.blockedSegment.formatContext,u=t.legacyContext,o=t.context;try{return ie(e,t,n)}catch(d){if(On(),typeof d=="object"&&d!==null&&typeof d.then=="function"){n=d;var i=t.blockedSegment,a=Pt(e,i.chunks.length,null,i.formatContext,i.lastPushedText,!0);i.children.push(a),i.lastPushedText=!1,e=Hn(e,t.node,t.blockedBoundary,a,t.abortSet,t.legacyContext,t.context,t.treeContext).ping,n.then(e,e),t.blockedSegment.formatContext=r,t.legacyContext=u,t.context=o,Tt(o)}else throw t.blockedSegment.formatContext=r,t.legacyContext=u,t.context=o,Tt(o),d}}function Li(e){var t=e.blockedBoundary;e=e.blockedSegment,e.status=3,Go(this,t,e)}function qo(e,t,n){var r=e.blockedBoundary;e.blockedSegment.status=3,r===null?(t.allPendingTasks--,t.status!==2&&(t.status=2,t.destination!==null&&t.destination.push(null))):(r.pendingTasks--,r.forceClientRender||(r.forceClientRender=!0,e=n===void 0?Error(T(432)):n,r.errorDigest=t.onError(e),r.parentFlushed&&t.clientRenderedBoundaries.push(r)),r.fallbackAbortableTasks.forEach(function(u){return qo(u,t,n)}),r.fallbackAbortableTasks.clear(),t.allPendingTasks--,t.allPendingTasks===0&&(r=t.onAllReady,r()))}function Ft(e,t){if(t.chunks.length===0&&t.children.length===1&&t.children[0].boundary===null){var n=t.children[0];n.id=t.id,n.parentFlushed=!0,n.status===1&&Ft(e,n)}else e.completedSegments.push(t)}function Go(e,t,n){if(t===null){if(n.parentFlushed){if(e.completedRootSegment!==null)throw Error(T(389));e.completedRootSegment=n}e.pendingRootTasks--,e.pendingRootTasks===0&&(e.onShellError=et,t=e.onShellReady,t())}else t.pendingTasks--,t.forceClientRender||(t.pendingTasks===0?(n.parentFlushed&&n.status===1&&Ft(t,n),t.parentFlushed&&e.completedBoundaries.push(t),t.fallbackAbortableTasks.forEach(Li,e),t.fallbackAbortableTasks.clear()):n.parentFlushed&&n.status===1&&(Ft(t,n),t.completedSegments.length===1&&t.parentFlushed&&e.partialBoundaries.push(t)));e.allPendingTasks--,e.allPendingTasks===0&&(e=e.onAllReady,e())}function Ko(e){if(e.status!==2){var t=He,n=on.current;on.current=mr;var r=_t;_t=e.responseState;try{var u=e.pingedTasks,o;for(o=0;o<u.length;o++){var i=u[o],a=e,d=i.blockedSegment;if(d.status===0){Tt(i.context);try{ie(a,i,i.node),a.responseState.generateStaticMarkup||d.lastPushedText&&d.textEmbedded&&d.chunks.push("<!-- -->"),i.abortSet.delete(i),d.status=1,Go(a,i.blockedBoundary,d)}catch(p){if(On(),typeof p=="object"&&p!==null&&typeof p.then=="function"){var l=i.ping;p.then(l,l)}else{i.abortSet.delete(i),d.status=4;var s=i.blockedBoundary,c=p,f=at(a,c);if(s===null?Lt(a,c):(s.pendingTasks--,s.forceClientRender||(s.forceClientRender=!0,s.errorDigest=f,s.parentFlushed&&a.clientRenderedBoundaries.push(s))),a.allPendingTasks--,a.allPendingTasks===0){var m=a.onAllReady;m()}}}finally{}}}u.splice(0,o),e.destination!==null&&Nn(e,e.destination)}catch(p){at(e,p),Lt(e,p)}finally{_t=r,on.current=n,n===mr&&Tt(t)}}}function ft(e,t,n){switch(n.parentFlushed=!0,n.status){case 0:var r=n.id=e.nextSegmentId++;return n.lastPushedText=!1,n.textEmbedded=!1,e=e.responseState,t.push('<template id="'),t.push(e.placeholderPrefix),e=r.toString(16),t.push(e),t.push('"></template>');case 1:n.status=2;var u=!0;r=n.chunks;var o=0;n=n.children;for(var i=0;i<n.length;i++){for(u=n[i];o<u.index;o++)t.push(r[o]);u=Xt(e,t,u)}for(;o<r.length-1;o++)t.push(r[o]);return o<r.length&&(u=t.push(r[o])),u;default:throw Error(T(390))}}function Xt(e,t,n){var r=n.boundary;if(r===null)return ft(e,t,n);if(r.parentFlushed=!0,r.forceClientRender)return e.responseState.generateStaticMarkup||(r=r.errorDigest,t.push("<!--$!-->"),t.push("<template"),r&&(t.push(' data-dgst="'),r=Y(r),t.push(r),t.push('"')),t.push("></template>")),ft(e,t,n),e=e.responseState.generateStaticMarkup?!0:t.push("<!--/$-->"),e;if(0<r.pendingTasks){r.rootSegmentID=e.nextSegmentId++,0<r.completedSegments.length&&e.partialBoundaries.push(r);var u=e.responseState,o=u.nextSuspenseID++;return u=u.boundaryPrefix+o.toString(16),r=r.id=u,ir(t,e.responseState,r),ft(e,t,n),t.push("<!--/$-->")}if(r.byteSize>e.progressiveChunkSize)return r.rootSegmentID=e.nextSegmentId++,e.completedBoundaries.push(r),ir(t,e.responseState,r.id),ft(e,t,n),t.push("<!--/$-->");if(e.responseState.generateStaticMarkup||t.push("<!--$-->"),n=r.completedSegments,n.length!==1)throw Error(T(391));return Xt(e,t,n[0]),e=e.responseState.generateStaticMarkup?!0:t.push("<!--/$-->"),e}function wr(e,t,n){return hi(t,e.responseState,n.formatContext,n.id),Xt(e,t,n),mi(t,n.formatContext)}function _r(e,t,n){for(var r=n.completedSegments,u=0;u<r.length;u++)Xo(e,t,n,r[u]);if(r.length=0,e=e.responseState,r=n.id,n=n.rootSegmentID,t.push(e.startInlineScript),e.sentCompleteBoundaryFunction?t.push('$RC("'):(e.sentCompleteBoundaryFunction=!0,t.push('function $RC(a,b){a=document.getElementById(a);b=document.getElementById(b);b.parentNode.removeChild(b);if(a){a=a.previousSibling;var f=a.parentNode,c=a.nextSibling,e=0;do{if(c&&8===c.nodeType){var d=c.data;if("/$"===d)if(0===e)break;else e--;else"$"!==d&&"$?"!==d&&"$!"!==d||e++}d=c.nextSibling;f.removeChild(c);c=d}while(c);for(;b.firstChild;)f.insertBefore(b.firstChild,c);a.data="$";a._reactRetry&&a._reactRetry()}};$RC("')),r===null)throw Error(T(395));return n=n.toString(16),t.push(r),t.push('","'),t.push(e.segmentPrefix),t.push(n),t.push('")<\/script>')}function Xo(e,t,n,r){if(r.status===2)return!0;var u=r.id;if(u===-1){if((r.id=n.rootSegmentID)===-1)throw Error(T(392));return wr(e,t,r)}return wr(e,t,r),e=e.responseState,t.push(e.startInlineScript),e.sentCompleteSegmentFunction?t.push('$RS("'):(e.sentCompleteSegmentFunction=!0,t.push('function $RS(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')),t.push(e.segmentPrefix),u=u.toString(16),t.push(u),t.push('","'),t.push(e.placeholderPrefix),t.push(u),t.push('")<\/script>')}function Nn(e,t){try{var n=e.completedRootSegment;if(n!==null&&e.pendingRootTasks===0){Xt(e,t,n),e.completedRootSegment=null;var r=e.responseState.bootstrapChunks;for(n=0;n<r.length-1;n++)t.push(r[n]);n<r.length&&t.push(r[n])}var u=e.clientRenderedBoundaries,o;for(o=0;o<u.length;o++){var i=u[o];r=t;var a=e.responseState,d=i.id,l=i.errorDigest,s=i.errorMessage,c=i.errorComponentStack;if(r.push(a.startInlineScript),a.sentClientRenderFunction?r.push('$RX("'):(a.sentClientRenderFunction=!0,r.push('function $RX(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};$RX("')),d===null)throw Error(T(395));if(r.push(d),r.push('"'),l||s||c){r.push(",");var f=rn(l||"");r.push(f)}if(s||c){r.push(",");var m=rn(s||"");r.push(m)}if(c){r.push(",");var p=rn(c);r.push(p)}if(!r.push(")<\/script>")){e.destination=null,o++,u.splice(0,o);return}}u.splice(0,o);var y=e.completedBoundaries;for(o=0;o<y.length;o++)if(!_r(e,t,y[o])){e.destination=null,o++,y.splice(0,o);return}y.splice(0,o);var g=e.partialBoundaries;for(o=0;o<g.length;o++){var S=g[o];e:{u=e,i=t;var $=S.completedSegments;for(a=0;a<$.length;a++)if(!Xo(u,i,S,$[a])){a++,$.splice(0,a);var R=!1;break e}$.splice(0,a),R=!0}if(!R){e.destination=null,o++,g.splice(0,o);return}}g.splice(0,o);var M=e.completedBoundaries;for(o=0;o<M.length;o++)if(!_r(e,t,M[o])){e.destination=null,o++,M.splice(0,o);return}M.splice(0,o)}finally{e.allPendingTasks===0&&e.pingedTasks.length===0&&e.clientRenderedBoundaries.length===0&&e.completedBoundaries.length===0&&t.push(null)}}function Fi(e,t){try{var n=e.abortableTasks;n.forEach(function(r){return qo(r,e,t)}),n.clear(),e.destination!==null&&Nn(e,e.destination)}catch(r){at(e,r),Lt(e,r)}}function zi(){}function Zo(e,t,n,r){var u=!1,o=null,i="",a={push:function(l){return l!==null&&(i+=l),!0},destroy:function(l){u=!0,o=l}},d=!1;if(e=Pi(e,gi(n,t?t.identifierPrefix:void 0),{insertionMode:1,selectedValue:null},1/0,zi,void 0,function(){d=!0},void 0,void 0),Ko(e),Fi(e,r),e.status===1)e.status=2,a.destroy(e.fatalError);else if(e.status!==2&&e.destination===null){e.destination=a;try{Nn(e,a)}catch(l){at(e,l),Lt(e,l)}}if(u)throw o;if(!d)throw Error(T(426));return i}Ze.renderToNodeStream=function(){throw Error(T(207))};Ze.renderToStaticMarkup=function(e,t){return Zo(e,t,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')};Ze.renderToStaticNodeStream=function(){throw Error(T(208))};Ze.renderToString=function(e,t){return Zo(e,t,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToReadableStream" which supports Suspense on the server')};Ze.version="18.3.1";var Un={};/**
 * @license React
 * react-dom-server.browser.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Jo=k;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ae=null,de=0;function x(e,t){if(t.length!==0)if(512<t.length)0<de&&(e.enqueue(new Uint8Array(ae.buffer,0,de)),ae=new Uint8Array(512),de=0),e.enqueue(t);else{var n=ae.length-de;n<t.length&&(n===0?e.enqueue(ae):(ae.set(t.subarray(0,n),de),e.enqueue(ae),t=t.subarray(n)),ae=new Uint8Array(512),de=0),ae.set(t,de),de+=t.length}}function O(e,t){return x(e,t),!0}function kr(e){ae&&0<de&&(e.enqueue(new Uint8Array(ae.buffer,0,de)),ae=null,de=0)}var Yo=new TextEncoder;function F(e){return Yo.encode(e)}function _(e){return Yo.encode(e)}function Qo(e,t){typeof e.error=="function"?e.error(t):e.close()}var re=Object.prototype.hasOwnProperty,Ii=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,xr={},Sr={};function eu(e){return re.call(Sr,e)?!0:re.call(xr,e)?!1:Ii.test(e)?Sr[e]=!0:(xr[e]=!0,!1)}function ee(e,t,n,r,u,o,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=u,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var X={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){X[e]=new ee(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];X[t]=new ee(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){X[e]=new ee(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){X[e]=new ee(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){X[e]=new ee(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){X[e]=new ee(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){X[e]=new ee(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){X[e]=new ee(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){X[e]=new ee(e,5,!1,e.toLowerCase(),null,!1,!1)});var Vn=/[\-:]([a-z])/g;function Wn(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Vn,Wn);X[t]=new ee(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Vn,Wn);X[t]=new ee(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Vn,Wn);X[t]=new ee(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){X[e]=new ee(e,1,!1,e.toLowerCase(),null,!1,!1)});X.xlinkHref=new ee("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){X[e]=new ee(e,1,!1,e.toLowerCase(),null,!0,!0)});var kt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ai=["Webkit","ms","Moz","O"];Object.keys(kt).forEach(function(e){Ai.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),kt[t]=kt[e]})});var Di=/["'&<>]/;function G(e){if(typeof e=="boolean"||typeof e=="number")return""+e;e=""+e;var t=Di.exec(e);if(t){var n="",r,u=0;for(r=t.index;r<e.length;r++){switch(e.charCodeAt(r)){case 34:t="&quot;";break;case 38:t="&amp;";break;case 39:t="&#x27;";break;case 60:t="&lt;";break;case 62:t="&gt;";break;default:continue}u!==r&&(n+=e.substring(u,r)),u=r+1,n+=t}e=u!==r?n+e.substring(u,r):n}return e}var Bi=/([A-Z])/g,Oi=/^ms-/,_n=Array.isArray,Hi=_("<script>"),Ni=_("<\/script>"),Ui=_('<script src="'),Vi=_('<script type="module" src="'),Cr=_('" async=""><\/script>'),Wi=/(<\/|<)(s)(cript)/gi;function qi(e,t,n,r){return""+t+(n==="s"?"\\u0073":"\\u0053")+r}function Gi(e,t,n,r,u){e=e===void 0?"":e,t=t===void 0?Hi:_('<script nonce="'+G(t)+'">');var o=[];if(n!==void 0&&o.push(t,F((""+n).replace(Wi,qi)),Ni),r!==void 0)for(n=0;n<r.length;n++)o.push(Ui,F(G(r[n])),Cr);if(u!==void 0)for(r=0;r<u.length;r++)o.push(Vi,F(G(u[r])),Cr);return{bootstrapChunks:o,startInlineScript:t,placeholderPrefix:_(e+"P:"),segmentPrefix:_(e+"S:"),boundaryPrefix:e+"B:",idPrefix:e,nextSuspenseID:0,sentCompleteSegmentFunction:!1,sentCompleteBoundaryFunction:!1,sentClientRenderFunction:!1}}function ge(e,t){return{insertionMode:e,selectedValue:t}}function Ki(e){return ge(e==="http://www.w3.org/2000/svg"?2:e==="http://www.w3.org/1998/Math/MathML"?3:0,null)}function Xi(e,t,n){switch(t){case"select":return ge(1,n.value!=null?n.value:n.defaultValue);case"svg":return ge(2,null);case"math":return ge(3,null);case"foreignObject":return ge(1,null);case"table":return ge(4,null);case"thead":case"tbody":case"tfoot":return ge(5,null);case"colgroup":return ge(7,null);case"tr":return ge(6,null)}return 4<=e.insertionMode||e.insertionMode===0?ge(1,null):e}var qn=_("<!-- -->");function $r(e,t,n,r){return t===""?r:(r&&e.push(qn),e.push(F(G(t))),!0)}var Er=new Map,Zi=_(' style="'),jr=_(":"),Ji=_(";");function tu(e,t,n){if(typeof n!="object")throw Error(P(62));t=!0;for(var r in n)if(re.call(n,r)){var u=n[r];if(u!=null&&typeof u!="boolean"&&u!==""){if(r.indexOf("--")===0){var o=F(G(r));u=F(G((""+u).trim()))}else{o=r;var i=Er.get(o);i!==void 0||(i=_(G(o.replace(Bi,"-$1").toLowerCase().replace(Oi,"-ms-"))),Er.set(o,i)),o=i,u=typeof u=="number"?u===0||re.call(kt,r)?F(""+u):F(u+"px"):F(G((""+u).trim()))}t?(t=!1,e.push(Zi,o,jr,u)):e.push(Ji,o,jr,u)}}t||e.push(Be)}var Re=_(" "),qe=_('="'),Be=_('"'),Rr=_('=""');function ue(e,t,n,r){switch(n){case"style":tu(e,t,r);return;case"defaultValue":case"defaultChecked":case"innerHTML":case"suppressContentEditableWarning":case"suppressHydrationWarning":return}if(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N"){if(t=X.hasOwnProperty(n)?X[n]:null,t!==null){switch(typeof r){case"function":case"symbol":return;case"boolean":if(!t.acceptsBooleans)return}switch(n=F(t.attributeName),t.type){case 3:r&&e.push(Re,n,Rr);break;case 4:r===!0?e.push(Re,n,Rr):r!==!1&&e.push(Re,n,qe,F(G(r)),Be);break;case 5:isNaN(r)||e.push(Re,n,qe,F(G(r)),Be);break;case 6:!isNaN(r)&&1<=r&&e.push(Re,n,qe,F(G(r)),Be);break;default:t.sanitizeURL&&(r=""+r),e.push(Re,n,qe,F(G(r)),Be)}}else if(eu(n)){switch(typeof r){case"function":case"symbol":return;case"boolean":if(t=n.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-")return}e.push(Re,F(n),qe,F(G(r)),Be)}}}var Te=_(">"),Tr=_("/>");function xt(e,t,n){if(t!=null){if(n!=null)throw Error(P(60));if(typeof t!="object"||!("__html"in t))throw Error(P(61));t=t.__html,t!=null&&e.push(F(""+t))}}function Yi(e){var t="";return Jo.Children.forEach(e,function(n){n!=null&&(t+=n)}),t}var un=_(' selected=""');function an(e,t,n,r){e.push(be(n));var u=n=null,o;for(o in t)if(re.call(t,o)){var i=t[o];if(i!=null)switch(o){case"children":n=i;break;case"dangerouslySetInnerHTML":u=i;break;default:ue(e,r,o,i)}}return e.push(Te),xt(e,u,n),typeof n=="string"?(e.push(F(G(n))),null):n}var dn=_(`
`),Qi=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,Mr=new Map;function be(e){var t=Mr.get(e);if(t===void 0){if(!Qi.test(e))throw Error(P(65,e));t=_("<"+e),Mr.set(e,t)}return t}var ea=_("<!DOCTYPE html>");function ta(e,t,n,r,u){switch(t){case"select":e.push(be("select"));var o=null,i=null;for(s in n)if(re.call(n,s)){var a=n[s];if(a!=null)switch(s){case"children":o=a;break;case"dangerouslySetInnerHTML":i=a;break;case"defaultValue":case"value":break;default:ue(e,r,s,a)}}return e.push(Te),xt(e,i,o),o;case"option":i=u.selectedValue,e.push(be("option"));var d=a=null,l=null,s=null;for(o in n)if(re.call(n,o)){var c=n[o];if(c!=null)switch(o){case"children":a=c;break;case"selected":l=c;break;case"dangerouslySetInnerHTML":s=c;break;case"value":d=c;default:ue(e,r,o,c)}}if(i!=null)if(n=d!==null?""+d:Yi(a),_n(i)){for(r=0;r<i.length;r++)if(""+i[r]===n){e.push(un);break}}else""+i===n&&e.push(un);else l&&e.push(un);return e.push(Te),xt(e,s,a),a;case"textarea":e.push(be("textarea")),s=i=o=null;for(a in n)if(re.call(n,a)&&(d=n[a],d!=null))switch(a){case"children":s=d;break;case"value":o=d;break;case"defaultValue":i=d;break;case"dangerouslySetInnerHTML":throw Error(P(91));default:ue(e,r,a,d)}if(o===null&&i!==null&&(o=i),e.push(Te),s!=null){if(o!=null)throw Error(P(92));if(_n(s)&&1<s.length)throw Error(P(93));o=""+s}return typeof o=="string"&&o[0]===`
`&&e.push(dn),o!==null&&e.push(F(G(""+o))),null;case"input":e.push(be("input")),d=s=a=o=null;for(i in n)if(re.call(n,i)&&(l=n[i],l!=null))switch(i){case"children":case"dangerouslySetInnerHTML":throw Error(P(399,"input"));case"defaultChecked":d=l;break;case"defaultValue":a=l;break;case"checked":s=l;break;case"value":o=l;break;default:ue(e,r,i,l)}return s!==null?ue(e,r,"checked",s):d!==null&&ue(e,r,"checked",d),o!==null?ue(e,r,"value",o):a!==null&&ue(e,r,"value",a),e.push(Tr),null;case"menuitem":e.push(be("menuitem"));for(var f in n)if(re.call(n,f)&&(o=n[f],o!=null))switch(f){case"children":case"dangerouslySetInnerHTML":throw Error(P(400));default:ue(e,r,f,o)}return e.push(Te),null;case"title":e.push(be("title")),o=null;for(c in n)if(re.call(n,c)&&(i=n[c],i!=null))switch(c){case"children":o=i;break;case"dangerouslySetInnerHTML":throw Error(P(434));default:ue(e,r,c,i)}return e.push(Te),o;case"listing":case"pre":e.push(be(t)),i=o=null;for(d in n)if(re.call(n,d)&&(a=n[d],a!=null))switch(d){case"children":o=a;break;case"dangerouslySetInnerHTML":i=a;break;default:ue(e,r,d,a)}if(e.push(Te),i!=null){if(o!=null)throw Error(P(60));if(typeof i!="object"||!("__html"in i))throw Error(P(61));n=i.__html,n!=null&&(typeof n=="string"&&0<n.length&&n[0]===`
`?e.push(dn,F(n)):e.push(F(""+n)))}return typeof o=="string"&&o[0]===`
`&&e.push(dn),o;case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":e.push(be(t));for(var m in n)if(re.call(n,m)&&(o=n[m],o!=null))switch(m){case"children":case"dangerouslySetInnerHTML":throw Error(P(399,t));default:ue(e,r,m,o)}return e.push(Tr),null;case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return an(e,n,t,r);case"html":return u.insertionMode===0&&e.push(ea),an(e,n,t,r);default:if(t.indexOf("-")===-1&&typeof n.is!="string")return an(e,n,t,r);e.push(be(t)),i=o=null;for(l in n)if(re.call(n,l)&&(a=n[l],a!=null))switch(l){case"children":o=a;break;case"dangerouslySetInnerHTML":i=a;break;case"style":tu(e,r,a);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":break;default:eu(l)&&typeof a!="function"&&typeof a!="symbol"&&e.push(Re,F(l),qe,F(G(a)),Be)}return e.push(Te),xt(e,i,o),o}}var na=_("</"),ra=_(">"),oa=_('<template id="'),ua=_('"></template>'),ia=_("<!--$-->"),aa=_('<!--$?--><template id="'),da=_('"></template>'),sa=_("<!--$!-->"),la=_("<!--/$-->"),ca=_("<template"),fa=_('"'),pa=_(' data-dgst="');_(' data-msg="');_(' data-stck="');var ha=_("></template>");function Pr(e,t,n){if(x(e,aa),n===null)throw Error(P(395));return x(e,n),O(e,da)}var ma=_('<div hidden id="'),va=_('">'),ga=_("</div>"),ba=_('<svg aria-hidden="true" style="display:none" id="'),ya=_('">'),wa=_("</svg>"),_a=_('<math aria-hidden="true" style="display:none" id="'),ka=_('">'),xa=_("</math>"),Sa=_('<table hidden id="'),Ca=_('">'),$a=_("</table>"),Ea=_('<table hidden><tbody id="'),ja=_('">'),Ra=_("</tbody></table>"),Ta=_('<table hidden><tr id="'),Ma=_('">'),Pa=_("</tr></table>"),La=_('<table hidden><colgroup id="'),Fa=_('">'),za=_("</colgroup></table>");function Ia(e,t,n,r){switch(n.insertionMode){case 0:case 1:return x(e,ma),x(e,t.segmentPrefix),x(e,F(r.toString(16))),O(e,va);case 2:return x(e,ba),x(e,t.segmentPrefix),x(e,F(r.toString(16))),O(e,ya);case 3:return x(e,_a),x(e,t.segmentPrefix),x(e,F(r.toString(16))),O(e,ka);case 4:return x(e,Sa),x(e,t.segmentPrefix),x(e,F(r.toString(16))),O(e,Ca);case 5:return x(e,Ea),x(e,t.segmentPrefix),x(e,F(r.toString(16))),O(e,ja);case 6:return x(e,Ta),x(e,t.segmentPrefix),x(e,F(r.toString(16))),O(e,Ma);case 7:return x(e,La),x(e,t.segmentPrefix),x(e,F(r.toString(16))),O(e,Fa);default:throw Error(P(397))}}function Aa(e,t){switch(t.insertionMode){case 0:case 1:return O(e,ga);case 2:return O(e,wa);case 3:return O(e,xa);case 4:return O(e,$a);case 5:return O(e,Ra);case 6:return O(e,Pa);case 7:return O(e,za);default:throw Error(P(397))}}var Da=_('function $RS(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("'),Ba=_('$RS("'),Oa=_('","'),Ha=_('")<\/script>'),Na=_('function $RC(a,b){a=document.getElementById(a);b=document.getElementById(b);b.parentNode.removeChild(b);if(a){a=a.previousSibling;var f=a.parentNode,c=a.nextSibling,e=0;do{if(c&&8===c.nodeType){var d=c.data;if("/$"===d)if(0===e)break;else e--;else"$"!==d&&"$?"!==d&&"$!"!==d||e++}d=c.nextSibling;f.removeChild(c);c=d}while(c);for(;b.firstChild;)f.insertBefore(b.firstChild,c);a.data="$";a._reactRetry&&a._reactRetry()}};$RC("'),Ua=_('$RC("'),Va=_('","'),Wa=_('")<\/script>'),qa=_('function $RX(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};$RX("'),Ga=_('$RX("'),Ka=_('"'),Xa=_(")<\/script>"),sn=_(","),Za=/[<\u2028\u2029]/g;function ln(e){return JSON.stringify(e).replace(Za,function(t){switch(t){case"<":return"\\u003c";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React")}})}var ot=Object.assign,Ja=Symbol.for("react.element"),nu=Symbol.for("react.portal"),ru=Symbol.for("react.fragment"),ou=Symbol.for("react.strict_mode"),uu=Symbol.for("react.profiler"),iu=Symbol.for("react.provider"),au=Symbol.for("react.context"),du=Symbol.for("react.forward_ref"),su=Symbol.for("react.suspense"),lu=Symbol.for("react.suspense_list"),cu=Symbol.for("react.memo"),Gn=Symbol.for("react.lazy"),Ya=Symbol.for("react.scope"),Qa=Symbol.for("react.debug_trace_mode"),ed=Symbol.for("react.legacy_hidden"),td=Symbol.for("react.default_value"),Lr=Symbol.iterator;function kn(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ru:return"Fragment";case nu:return"Portal";case uu:return"Profiler";case ou:return"StrictMode";case su:return"Suspense";case lu:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case au:return(e.displayName||"Context")+".Consumer";case iu:return(e._context.displayName||"Context")+".Provider";case du:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case cu:return t=e.displayName||null,t!==null?t:kn(e.type)||"Memo";case Gn:t=e._payload,e=e._init;try{return kn(e(t))}catch{}}return null}var fu={};function Fr(e,t){if(e=e.contextTypes,!e)return fu;var n={},r;for(r in e)n[r]=t[r];return n}var Ne=null;function Zt(e,t){if(e!==t){e.context._currentValue=e.parentValue,e=e.parent;var n=t.parent;if(e===null){if(n!==null)throw Error(P(401))}else{if(n===null)throw Error(P(401));Zt(e,n)}t.context._currentValue=t.value}}function pu(e){e.context._currentValue=e.parentValue,e=e.parent,e!==null&&pu(e)}function hu(e){var t=e.parent;t!==null&&hu(t),e.context._currentValue=e.value}function mu(e,t){if(e.context._currentValue=e.parentValue,e=e.parent,e===null)throw Error(P(402));e.depth===t.depth?Zt(e,t):mu(e,t)}function vu(e,t){var n=t.parent;if(n===null)throw Error(P(402));e.depth===n.depth?Zt(e,n):vu(e,n),t.context._currentValue=t.value}function zt(e){var t=Ne;t!==e&&(t===null?hu(e):e===null?pu(t):t.depth===e.depth?Zt(t,e):t.depth>e.depth?mu(t,e):vu(t,e),Ne=e)}var zr={isMounted:function(){return!1},enqueueSetState:function(e,t){e=e._reactInternals,e.queue!==null&&e.queue.push(t)},enqueueReplaceState:function(e,t){e=e._reactInternals,e.replace=!0,e.queue=[t]},enqueueForceUpdate:function(){}};function Ir(e,t,n,r){var u=e.state!==void 0?e.state:null;e.updater=zr,e.props=n,e.state=u;var o={queue:[],replace:!1};e._reactInternals=o;var i=t.contextType;if(e.context=typeof i=="object"&&i!==null?i._currentValue:r,i=t.getDerivedStateFromProps,typeof i=="function"&&(i=i(n,u),u=i==null?u:ot({},u,i),e.state=u),typeof t.getDerivedStateFromProps!="function"&&typeof e.getSnapshotBeforeUpdate!="function"&&(typeof e.UNSAFE_componentWillMount=="function"||typeof e.componentWillMount=="function"))if(t=e.state,typeof e.componentWillMount=="function"&&e.componentWillMount(),typeof e.UNSAFE_componentWillMount=="function"&&e.UNSAFE_componentWillMount(),t!==e.state&&zr.enqueueReplaceState(e,e.state,null),o.queue!==null&&0<o.queue.length)if(t=o.queue,i=o.replace,o.queue=null,o.replace=!1,i&&t.length===1)e.state=t[0];else{for(o=i?t[0]:e.state,u=!0,i=i?1:0;i<t.length;i++){var a=t[i];a=typeof a=="function"?a.call(e,o,n,r):a,a!=null&&(u?(u=!1,o=ot({},o,a)):ot(o,a))}e.state=o}else o.queue=null}var nd={id:1,overflow:""};function xn(e,t,n){var r=e.id;e=e.overflow;var u=32-St(r)-1;r&=~(1<<u),n+=1;var o=32-St(t)+u;if(30<o){var i=u-u%5;return o=(r&(1<<i)-1).toString(32),r>>=i,u-=i,{id:1<<32-St(t)+u|n<<u|r,overflow:o+e}}return{id:1<<o|n<<u|r,overflow:e}}var St=Math.clz32?Math.clz32:ud,rd=Math.log,od=Math.LN2;function ud(e){return e>>>=0,e===0?32:31-(rd(e)/od|0)|0}function id(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ad=typeof Object.is=="function"?Object.is:id,$e=null,Kn=null,Ct=null,B=null,tt=!1,It=!1,dt=0,Pe=null,Jt=0;function Oe(){if($e===null)throw Error(P(321));return $e}function Ar(){if(0<Jt)throw Error(P(312));return{memoizedState:null,queue:null,next:null}}function Xn(){return B===null?Ct===null?(tt=!1,Ct=B=Ar()):(tt=!0,B=Ct):B.next===null?(tt=!1,B=B.next=Ar()):(tt=!0,B=B.next),B}function Zn(){Kn=$e=null,It=!1,Ct=null,Jt=0,B=Pe=null}function gu(e,t){return typeof t=="function"?t(e):t}function Dr(e,t,n){if($e=Oe(),B=Xn(),tt){var r=B.queue;if(t=r.dispatch,Pe!==null&&(n=Pe.get(r),n!==void 0)){Pe.delete(r),r=B.memoizedState;do r=e(r,n.action),n=n.next;while(n!==null);return B.memoizedState=r,[r,t]}return[B.memoizedState,t]}return e=e===gu?typeof t=="function"?t():t:n!==void 0?n(t):t,B.memoizedState=e,e=B.queue={last:null,dispatch:null},e=e.dispatch=dd.bind(null,$e,e),[B.memoizedState,e]}function Br(e,t){if($e=Oe(),B=Xn(),t=t===void 0?null:t,B!==null){var n=B.memoizedState;if(n!==null&&t!==null){var r=n[1];e:if(r===null)r=!1;else{for(var u=0;u<r.length&&u<t.length;u++)if(!ad(t[u],r[u])){r=!1;break e}r=!0}if(r)return n[0]}}return e=e(),B.memoizedState=[e,t],e}function dd(e,t,n){if(25<=Jt)throw Error(P(301));if(e===$e)if(It=!0,e={action:n,next:null},Pe===null&&(Pe=new Map),n=Pe.get(t),n===void 0)Pe.set(t,e);else{for(t=n;t.next!==null;)t=t.next;t.next=e}}function sd(){throw Error(P(394))}function pt(){}var Or={readContext:function(e){return e._currentValue},useContext:function(e){return Oe(),e._currentValue},useMemo:Br,useReducer:Dr,useRef:function(e){$e=Oe(),B=Xn();var t=B.memoizedState;return t===null?(e={current:e},B.memoizedState=e):t},useState:function(e){return Dr(gu,e)},useInsertionEffect:pt,useLayoutEffect:function(){},useCallback:function(e,t){return Br(function(){return e},t)},useImperativeHandle:pt,useEffect:pt,useDebugValue:pt,useDeferredValue:function(e){return Oe(),e},useTransition:function(){return Oe(),[!1,sd]},useId:function(){var e=Kn.treeContext,t=e.overflow;e=e.id,e=(e&~(1<<32-St(e)-1)).toString(32)+t;var n=$t;if(n===null)throw Error(P(404));return t=dt++,e=":"+n.idPrefix+"R"+e,0<t&&(e+="H"+t.toString(32)),e+":"},useMutableSource:function(e,t){return Oe(),t(e._source)},useSyncExternalStore:function(e,t,n){if(n===void 0)throw Error(P(407));return n()}},$t=null,cn=Jo.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentDispatcher;function ld(e){return console.error(e),null}function nt(){}function cd(e,t,n,r,u,o,i,a,d){var l=[],s=new Set;return t={destination:null,responseState:t,progressiveChunkSize:r===void 0?12800:r,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:s,pingedTasks:l,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],onError:u===void 0?ld:u,onAllReady:o===void 0?nt:o,onShellReady:i===void 0?nt:i,onShellError:a===void 0?nt:a,onFatalError:d===void 0?nt:d},n=At(t,0,null,n,!1,!1),n.parentFlushed=!0,e=Jn(t,e,null,n,s,fu,null,nd),l.push(e),t}function Jn(e,t,n,r,u,o,i,a){e.allPendingTasks++,n===null?e.pendingRootTasks++:n.pendingTasks++;var d={node:t,ping:function(){var l=e.pingedTasks;l.push(d),l.length===1&&wu(e)},blockedBoundary:n,blockedSegment:r,abortSet:u,legacyContext:o,context:i,treeContext:a};return u.add(d),d}function At(e,t,n,r,u,o){return{status:0,id:-1,index:t,parentFlushed:!1,chunks:[],children:[],formatContext:r,boundary:n,lastPushedText:u,textEmbedded:o}}function st(e,t){if(e=e.onError(t),e!=null&&typeof e!="string")throw Error('onError returned something with a type other than "string". onError should return a string and may return null or undefined but must not return anything else. It received something of type "'+typeof e+'" instead');return e}function Dt(e,t){var n=e.onShellError;n(t),n=e.onFatalError,n(t),e.destination!==null?(e.status=2,Qo(e.destination,t)):(e.status=1,e.fatalError=t)}function Hr(e,t,n,r,u){for($e={},Kn=t,dt=0,e=n(r,u);It;)It=!1,dt=0,Jt+=1,B=null,e=n(r,u);return Zn(),e}function Nr(e,t,n,r){var u=n.render(),o=r.childContextTypes;if(o!=null){var i=t.legacyContext;if(typeof n.getChildContext!="function")r=i;else{n=n.getChildContext();for(var a in n)if(!(a in o))throw Error(P(108,kn(r)||"Unknown",a));r=ot({},i,n)}t.legacyContext=r,se(e,t,u),t.legacyContext=i}else se(e,t,u)}function Ur(e,t){if(e&&e.defaultProps){t=ot({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Sn(e,t,n,r,u){if(typeof n=="function")if(n.prototype&&n.prototype.isReactComponent){u=Fr(n,t.legacyContext);var o=n.contextType;o=new n(r,typeof o=="object"&&o!==null?o._currentValue:u),Ir(o,n,r,u),Nr(e,t,o,n)}else{o=Fr(n,t.legacyContext),u=Hr(e,t,n,r,o);var i=dt!==0;if(typeof u=="object"&&u!==null&&typeof u.render=="function"&&u.$$typeof===void 0)Ir(u,n,r,o),Nr(e,t,u,n);else if(i){r=t.treeContext,t.treeContext=xn(r,1,0);try{se(e,t,u)}finally{t.treeContext=r}}else se(e,t,u)}else if(typeof n=="string"){switch(u=t.blockedSegment,o=ta(u.chunks,n,r,e.responseState,u.formatContext),u.lastPushedText=!1,i=u.formatContext,u.formatContext=Xi(i,n,r),Cn(e,t,o),u.formatContext=i,n){case"area":case"base":case"br":case"col":case"embed":case"hr":case"img":case"input":case"keygen":case"link":case"meta":case"param":case"source":case"track":case"wbr":break;default:u.chunks.push(na,F(n),ra)}u.lastPushedText=!1}else{switch(n){case ed:case Qa:case ou:case uu:case ru:se(e,t,r.children);return;case lu:se(e,t,r.children);return;case Ya:throw Error(P(343));case su:e:{n=t.blockedBoundary,u=t.blockedSegment,o=r.fallback,r=r.children,i=new Set;var a={id:null,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,forceClientRender:!1,completedSegments:[],byteSize:0,fallbackAbortableTasks:i,errorDigest:null},d=At(e,u.chunks.length,a,u.formatContext,!1,!1);u.children.push(d),u.lastPushedText=!1;var l=At(e,0,null,u.formatContext,!1,!1);l.parentFlushed=!0,t.blockedBoundary=a,t.blockedSegment=l;try{if(Cn(e,t,r),l.lastPushedText&&l.textEmbedded&&l.chunks.push(qn),l.status=1,Bt(a,l),a.pendingTasks===0)break e}catch(s){l.status=4,a.forceClientRender=!0,a.errorDigest=st(e,s)}finally{t.blockedBoundary=n,t.blockedSegment=u}t=Jn(e,o,n,d,i,t.legacyContext,t.context,t.treeContext),e.pingedTasks.push(t)}return}if(typeof n=="object"&&n!==null)switch(n.$$typeof){case du:if(r=Hr(e,t,n.render,r,u),dt!==0){n=t.treeContext,t.treeContext=xn(n,1,0);try{se(e,t,r)}finally{t.treeContext=n}}else se(e,t,r);return;case cu:n=n.type,r=Ur(n,r),Sn(e,t,n,r,u);return;case iu:if(u=r.children,n=n._context,r=r.value,o=n._currentValue,n._currentValue=r,i=Ne,Ne=r={parent:i,depth:i===null?0:i.depth+1,context:n,parentValue:o,value:r},t.context=r,se(e,t,u),e=Ne,e===null)throw Error(P(403));r=e.parentValue,e.context._currentValue=r===td?e.context._defaultValue:r,e=Ne=e.parent,t.context=e;return;case au:r=r.children,r=r(n._currentValue),se(e,t,r);return;case Gn:u=n._init,n=u(n._payload),r=Ur(n,r),Sn(e,t,n,r,void 0);return}throw Error(P(130,n==null?n:typeof n,""))}}function se(e,t,n){if(t.node=n,typeof n=="object"&&n!==null){switch(n.$$typeof){case Ja:Sn(e,t,n.type,n.props,n.ref);return;case nu:throw Error(P(257));case Gn:var r=n._init;n=r(n._payload),se(e,t,n);return}if(_n(n)){Vr(e,t,n);return}if(n===null||typeof n!="object"?r=null:(r=Lr&&n[Lr]||n["@@iterator"],r=typeof r=="function"?r:null),r&&(r=r.call(n))){if(n=r.next(),!n.done){var u=[];do u.push(n.value),n=r.next();while(!n.done);Vr(e,t,u)}return}throw e=Object.prototype.toString.call(n),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e))}typeof n=="string"?(r=t.blockedSegment,r.lastPushedText=$r(t.blockedSegment.chunks,n,e.responseState,r.lastPushedText)):typeof n=="number"&&(r=t.blockedSegment,r.lastPushedText=$r(t.blockedSegment.chunks,""+n,e.responseState,r.lastPushedText))}function Vr(e,t,n){for(var r=n.length,u=0;u<r;u++){var o=t.treeContext;t.treeContext=xn(o,r,u);try{Cn(e,t,n[u])}finally{t.treeContext=o}}}function Cn(e,t,n){var r=t.blockedSegment.formatContext,u=t.legacyContext,o=t.context;try{return se(e,t,n)}catch(d){if(Zn(),typeof d=="object"&&d!==null&&typeof d.then=="function"){n=d;var i=t.blockedSegment,a=At(e,i.chunks.length,null,i.formatContext,i.lastPushedText,!0);i.children.push(a),i.lastPushedText=!1,e=Jn(e,t.node,t.blockedBoundary,a,t.abortSet,t.legacyContext,t.context,t.treeContext).ping,n.then(e,e),t.blockedSegment.formatContext=r,t.legacyContext=u,t.context=o,zt(o)}else throw t.blockedSegment.formatContext=r,t.legacyContext=u,t.context=o,zt(o),d}}function fd(e){var t=e.blockedBoundary;e=e.blockedSegment,e.status=3,yu(this,t,e)}function bu(e,t,n){var r=e.blockedBoundary;e.blockedSegment.status=3,r===null?(t.allPendingTasks--,t.status!==2&&(t.status=2,t.destination!==null&&t.destination.close())):(r.pendingTasks--,r.forceClientRender||(r.forceClientRender=!0,e=n===void 0?Error(P(432)):n,r.errorDigest=t.onError(e),r.parentFlushed&&t.clientRenderedBoundaries.push(r)),r.fallbackAbortableTasks.forEach(function(u){return bu(u,t,n)}),r.fallbackAbortableTasks.clear(),t.allPendingTasks--,t.allPendingTasks===0&&(r=t.onAllReady,r()))}function Bt(e,t){if(t.chunks.length===0&&t.children.length===1&&t.children[0].boundary===null){var n=t.children[0];n.id=t.id,n.parentFlushed=!0,n.status===1&&Bt(e,n)}else e.completedSegments.push(t)}function yu(e,t,n){if(t===null){if(n.parentFlushed){if(e.completedRootSegment!==null)throw Error(P(389));e.completedRootSegment=n}e.pendingRootTasks--,e.pendingRootTasks===0&&(e.onShellError=nt,t=e.onShellReady,t())}else t.pendingTasks--,t.forceClientRender||(t.pendingTasks===0?(n.parentFlushed&&n.status===1&&Bt(t,n),t.parentFlushed&&e.completedBoundaries.push(t),t.fallbackAbortableTasks.forEach(fd,e),t.fallbackAbortableTasks.clear()):n.parentFlushed&&n.status===1&&(Bt(t,n),t.completedSegments.length===1&&t.parentFlushed&&e.partialBoundaries.push(t)));e.allPendingTasks--,e.allPendingTasks===0&&(e=e.onAllReady,e())}function wu(e){if(e.status!==2){var t=Ne,n=cn.current;cn.current=Or;var r=$t;$t=e.responseState;try{var u=e.pingedTasks,o;for(o=0;o<u.length;o++){var i=u[o],a=e,d=i.blockedSegment;if(d.status===0){zt(i.context);try{se(a,i,i.node),d.lastPushedText&&d.textEmbedded&&d.chunks.push(qn),i.abortSet.delete(i),d.status=1,yu(a,i.blockedBoundary,d)}catch(p){if(Zn(),typeof p=="object"&&p!==null&&typeof p.then=="function"){var l=i.ping;p.then(l,l)}else{i.abortSet.delete(i),d.status=4;var s=i.blockedBoundary,c=p,f=st(a,c);if(s===null?Dt(a,c):(s.pendingTasks--,s.forceClientRender||(s.forceClientRender=!0,s.errorDigest=f,s.parentFlushed&&a.clientRenderedBoundaries.push(s))),a.allPendingTasks--,a.allPendingTasks===0){var m=a.onAllReady;m()}}}finally{}}}u.splice(0,o),e.destination!==null&&Yn(e,e.destination)}catch(p){st(e,p),Dt(e,p)}finally{$t=r,cn.current=n,n===Or&&zt(t)}}}function ht(e,t,n){switch(n.parentFlushed=!0,n.status){case 0:var r=n.id=e.nextSegmentId++;return n.lastPushedText=!1,n.textEmbedded=!1,e=e.responseState,x(t,oa),x(t,e.placeholderPrefix),e=F(r.toString(16)),x(t,e),O(t,ua);case 1:n.status=2;var u=!0;r=n.chunks;var o=0;n=n.children;for(var i=0;i<n.length;i++){for(u=n[i];o<u.index;o++)x(t,r[o]);u=Yt(e,t,u)}for(;o<r.length-1;o++)x(t,r[o]);return o<r.length&&(u=O(t,r[o])),u;default:throw Error(P(390))}}function Yt(e,t,n){var r=n.boundary;if(r===null)return ht(e,t,n);if(r.parentFlushed=!0,r.forceClientRender)r=r.errorDigest,O(t,sa),x(t,ca),r&&(x(t,pa),x(t,F(G(r))),x(t,fa)),O(t,ha),ht(e,t,n);else if(0<r.pendingTasks){r.rootSegmentID=e.nextSegmentId++,0<r.completedSegments.length&&e.partialBoundaries.push(r);var u=e.responseState,o=u.nextSuspenseID++;u=_(u.boundaryPrefix+o.toString(16)),r=r.id=u,Pr(t,e.responseState,r),ht(e,t,n)}else if(r.byteSize>e.progressiveChunkSize)r.rootSegmentID=e.nextSegmentId++,e.completedBoundaries.push(r),Pr(t,e.responseState,r.id),ht(e,t,n);else{if(O(t,ia),n=r.completedSegments,n.length!==1)throw Error(P(391));Yt(e,t,n[0])}return O(t,la)}function Wr(e,t,n){return Ia(t,e.responseState,n.formatContext,n.id),Yt(e,t,n),Aa(t,n.formatContext)}function qr(e,t,n){for(var r=n.completedSegments,u=0;u<r.length;u++)_u(e,t,n,r[u]);if(r.length=0,e=e.responseState,r=n.id,n=n.rootSegmentID,x(t,e.startInlineScript),e.sentCompleteBoundaryFunction?x(t,Ua):(e.sentCompleteBoundaryFunction=!0,x(t,Na)),r===null)throw Error(P(395));return n=F(n.toString(16)),x(t,r),x(t,Va),x(t,e.segmentPrefix),x(t,n),O(t,Wa)}function _u(e,t,n,r){if(r.status===2)return!0;var u=r.id;if(u===-1){if((r.id=n.rootSegmentID)===-1)throw Error(P(392));return Wr(e,t,r)}return Wr(e,t,r),e=e.responseState,x(t,e.startInlineScript),e.sentCompleteSegmentFunction?x(t,Ba):(e.sentCompleteSegmentFunction=!0,x(t,Da)),x(t,e.segmentPrefix),u=F(u.toString(16)),x(t,u),x(t,Oa),x(t,e.placeholderPrefix),x(t,u),O(t,Ha)}function Yn(e,t){ae=new Uint8Array(512),de=0;try{var n=e.completedRootSegment;if(n!==null&&e.pendingRootTasks===0){Yt(e,t,n),e.completedRootSegment=null;var r=e.responseState.bootstrapChunks;for(n=0;n<r.length-1;n++)x(t,r[n]);n<r.length&&O(t,r[n])}var u=e.clientRenderedBoundaries,o;for(o=0;o<u.length;o++){var i=u[o];r=t;var a=e.responseState,d=i.id,l=i.errorDigest,s=i.errorMessage,c=i.errorComponentStack;if(x(r,a.startInlineScript),a.sentClientRenderFunction?x(r,Ga):(a.sentClientRenderFunction=!0,x(r,qa)),d===null)throw Error(P(395));x(r,d),x(r,Ka),(l||s||c)&&(x(r,sn),x(r,F(ln(l||"")))),(s||c)&&(x(r,sn),x(r,F(ln(s||"")))),c&&(x(r,sn),x(r,F(ln(c)))),O(r,Xa)}u.splice(0,o);var f=e.completedBoundaries;for(o=0;o<f.length;o++)qr(e,t,f[o]);f.splice(0,o),kr(t),ae=new Uint8Array(512),de=0;var m=e.partialBoundaries;for(o=0;o<m.length;o++){var p=m[o];e:{u=e,i=t;var y=p.completedSegments;for(a=0;a<y.length;a++)if(!_u(u,i,p,y[a])){a++,y.splice(0,a);var g=!1;break e}y.splice(0,a),g=!0}if(!g){e.destination=null,o++,m.splice(0,o);return}}m.splice(0,o);var S=e.completedBoundaries;for(o=0;o<S.length;o++)qr(e,t,S[o]);S.splice(0,o)}finally{kr(t),e.allPendingTasks===0&&e.pingedTasks.length===0&&e.clientRenderedBoundaries.length===0&&e.completedBoundaries.length===0&&t.close()}}function Gr(e,t){try{var n=e.abortableTasks;n.forEach(function(r){return bu(r,e,t)}),n.clear(),e.destination!==null&&Yn(e,e.destination)}catch(r){st(e,r),Dt(e,r)}}Un.renderToReadableStream=function(e,t){return new Promise(function(n,r){var u,o,i=new Promise(function(s,c){o=s,u=c}),a=cd(e,Gi(t?t.identifierPrefix:void 0,t?t.nonce:void 0,t?t.bootstrapScriptContent:void 0,t?t.bootstrapScripts:void 0,t?t.bootstrapModules:void 0),Ki(t?t.namespaceURI:void 0),t?t.progressiveChunkSize:void 0,t?t.onError:void 0,o,function(){var s=new ReadableStream({type:"bytes",pull:function(c){if(a.status===1)a.status=2,Qo(c,a.fatalError);else if(a.status!==2&&a.destination===null){a.destination=c;try{Yn(a,c)}catch(f){st(a,f),Dt(a,f)}}},cancel:function(){Gr(a)}},{highWaterMark:0});s.allReady=i,n(s)},function(s){i.catch(function(){}),r(s)},u);if(t&&t.signal){var d=t.signal,l=function(){Gr(a,d.reason),d.removeEventListener("abort",l)};d.addEventListener("abort",l)}wu(a)})};Un.version="18.3.1";var Je,ku;Je=Ze,ku=Un;Je.version;var pd=Je.renderToString;Je.renderToStaticMarkup;Je.renderToNodeStream;Je.renderToStaticNodeStream;ku.renderToReadableStream;function hd(e,t){t===void 0&&(t={});var n=t.insertAt;if(!(!e||typeof document>"u")){var r=document.head||document.getElementsByTagName("head")[0],u=document.createElement("style");u.type="text/css",n==="top"&&r.firstChild?r.insertBefore(u,r.firstChild):r.appendChild(u),u.styleSheet?u.styleSheet.cssText=e:u.appendChild(document.createTextNode(e))}}var md=`.react-input-emoji--container {
  color: #4b4b4b;
  text-rendering: optimizeLegibility;
  background-color: #fff;
  border: 1px solid #fff;
  border-radius: 21px;
  margin: 5px 10px;
  box-sizing: border-box;
  flex: 1 1 auto;
  font-size: 15px;
  font-family: sans-serif;
  font-weight: 400;
  line-height: 20px;
  min-height: 20px;
  min-width: 0;
  outline: none;
  width: inherit;
  will-change: width;
  vertical-align: baseline;
  border: 1px solid #eaeaea;
  margin-right: 0;
}

.react-input-emoji--wrapper {
  display: flex;
  overflow: hidden;
  flex: 1;
  position: relative;
  padding-right: 0;
  vertical-align: baseline;
  outline: none;
  margin: 0;
  padding: 0;
  border: 0;
}

.react-input-emoji--input {
  font-weight: 400;
  max-height: 100px;
  min-height: 20px;
  outline: none;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
  white-space: pre-wrap;
  word-wrap: break-word;
  z-index: 1;
  width: 100%;
  user-select: text;
  padding: 9px 12px 11px;
  text-align: left;
}

.react-input-emoji--input img {
  vertical-align: middle;
  width: 18px !important;
  height: 18px !important;
  display: inline !important;
  margin-left: 1px;
  margin-right: 1px;
}

.react-input-emoji--overlay {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 9;
}

.react-input-emoji--placeholder {
  color: #a0a0a0;
  pointer-events: none;
  position: absolute;
  user-select: none;
  z-index: 2;
  left: 16px;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  width: calc(100% - 22px);
}

.react-input-emoji--button {
  position: relative;
  display: block;
  text-align: center;
  padding: 0 10px;
  overflow: hidden;
  transition: color 0.1s ease-out;
  margin: 0;
  box-shadow: none;
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  flex-shrink: 0;
}

.react-input-emoji--button svg {
  fill: #858585;
}

.react-input-emoji--button__show svg {
  fill: #128b7e;
}

.react-emoji {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}

.react-emoji-picker--container {
  position: absolute;
  top: 0;
  width: 100%;
}

.react-emoji-picker--wrapper {
  position: absolute;
  bottom: 0;
  right: 0;
  height: 435px;
  width: 352px;
  overflow: hidden;
  z-index: 10;
}

.react-emoji-picker {
  position: absolute;
  top: 0;
  left: 0;
  animation: slidein 0.1s ease-in-out;
}

.react-emoji-picker__show {
  top: 0;
}

.react-input-emoji--mention--container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
}

.react-input-emoji--mention--list {
  background-color: #fafafa;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  gap: 5px;
  flex-direction: column;
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
}

.react-input-emoji--mention--item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 10px;
  background-color: transparent;
  width: 100%;
  margin: 0;
  border: 0;
}

.react-input-emoji--mention--item__selected {
  background-color: #eeeeee;
}

.react-input-emoji--mention--item--img {
  width: 34px;
  height: 34px;
  border-radius: 50%;
}

.react-input-emoji--mention--item--name {
  font-size: 16px;
  color: #333;
}

.react-input-emoji--mention--item--name__selected {
  color: green;
}

.react-input-emoji--mention--text {
  color: #039be5;
}

.react-input-emoji--mention--loading {
  background-color: #fafafa;
  border: 1px solid #eaeaea;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}

.react-input-emoji--mention--loading--spinner,
.react-input-emoji--mention--loading--spinner::after {
  border-radius: 50%;
  width: 10em;
  height: 10em;
}

.react-input-emoji--mention--loading--spinner {
  margin: 1px auto;
  font-size: 2px;
  position: relative;
  text-indent: -9999em;
  border-top: 1.1em solid rgba(0, 0, 0, 0.1);
  border-right: 1.1em solid rgba(0, 0, 0, 0.1);
  border-bottom: 1.1em solid rgba(0, 0, 0, 0.1);
  border-left: 1.1em solid rgba(0, 0, 0, 0.4);
  transform: translateZ(0);
  animation: load8 1.1s infinite linear;
}

@keyframes load8 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes slidein {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
`;hd(md);function vd(e,t){var n=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n!=null){var r,u,o,i,a=[],d=!0,l=!1;try{if(o=(n=n.call(e)).next,t===0){if(Object(n)!==n)return;d=!1}else for(;!(d=(r=o.call(n)).done)&&(a.push(r.value),a.length!==t);d=!0);}catch(s){l=!0,u=s}finally{try{if(!d&&n.return!=null&&(i=n.return(),Object(i)!==i))return}finally{if(l)throw u}}return a}}function Kr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(u){return Object.getOwnPropertyDescriptor(e,u).enumerable})),n.push.apply(n,r)}return n}function Xr(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Kr(Object(n),!0).forEach(function(r){gd(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Kr(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function Se(){Se=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,u=Object.defineProperty||function(v,b,w){v[b]=w.value},o=typeof Symbol=="function"?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",d=o.toStringTag||"@@toStringTag";function l(v,b,w){return Object.defineProperty(v,b,{value:w,enumerable:!0,configurable:!0,writable:!0}),v[b]}try{l({},"")}catch{l=function(b,w,j){return b[w]=j}}function s(v,b,w,j){var C=b&&b.prototype instanceof S?b:S,I=Object.create(C.prototype),N=new ze(j||[]);return u(I,"_invoke",{value:Ue(v,w,N)}),I}function c(v,b,w){try{return{type:"normal",arg:v.call(b,w)}}catch(j){return{type:"throw",arg:j}}}t.wrap=s;var f="suspendedStart",m="suspendedYield",p="executing",y="completed",g={};function S(){}function $(){}function R(){}var M={};l(M,i,function(){return this});var A=Object.getPrototypeOf,H=A&&A(A(Ye([])));H&&H!==n&&r.call(H,i)&&(M=H);var le=R.prototype=S.prototype=Object.create(M);function te(v){["next","throw","return"].forEach(function(b){l(v,b,function(w){return this._invoke(b,w)})})}function Ee(v,b){function w(C,I,N,U){var Z=c(v[C],v,I);if(Z.type!=="throw"){var je=Z.arg,Ie=je.value;return Ie&&typeof Ie=="object"&&r.call(Ie,"__await")?b.resolve(Ie.__await).then(function(ce){w("next",ce,N,U)},function(ce){w("throw",ce,N,U)}):b.resolve(Ie).then(function(ce){je.value=ce,N(je)},function(ce){return w("throw",ce,N,U)})}U(Z.arg)}var j;u(this,"_invoke",{value:function(C,I){function N(){return new b(function(U,Z){w(C,I,U,Z)})}return j=j?j.then(N,N):N()}})}function Ue(v,b,w){var j=f;return function(C,I){if(j===p)throw new Error("Generator is already running");if(j===y){if(C==="throw")throw I;return{value:e,done:!0}}for(w.method=C,w.arg=I;;){var N=w.delegate;if(N){var U=V(N,w);if(U){if(U===g)continue;return U}}if(w.method==="next")w.sent=w._sent=w.arg;else if(w.method==="throw"){if(j===f)throw j=y,w.arg;w.dispatchException(w.arg)}else w.method==="return"&&w.abrupt("return",w.arg);j=p;var Z=c(v,b,w);if(Z.type==="normal"){if(j=w.done?y:m,Z.arg===g)continue;return{value:Z.arg,done:w.done}}Z.type==="throw"&&(j=y,w.method="throw",w.arg=Z.arg)}}}function V(v,b){var w=b.method,j=v.iterator[w];if(j===e)return b.delegate=null,w==="throw"&&v.iterator.return&&(b.method="return",b.arg=e,V(v,b),b.method==="throw")||w!=="return"&&(b.method="throw",b.arg=new TypeError("The iterator does not provide a '"+w+"' method")),g;var C=c(j,v.iterator,b.arg);if(C.type==="throw")return b.method="throw",b.arg=C.arg,b.delegate=null,g;var I=C.arg;return I?I.done?(b[v.resultName]=I.value,b.next=v.nextLoc,b.method!=="return"&&(b.method="next",b.arg=e),b.delegate=null,g):I:(b.method="throw",b.arg=new TypeError("iterator result is not an object"),b.delegate=null,g)}function pe(v){var b={tryLoc:v[0]};1 in v&&(b.catchLoc=v[1]),2 in v&&(b.finallyLoc=v[2],b.afterLoc=v[3]),this.tryEntries.push(b)}function he(v){var b=v.completion||{};b.type="normal",delete b.arg,v.completion=b}function ze(v){this.tryEntries=[{tryLoc:"root"}],v.forEach(pe,this),this.reset(!0)}function Ye(v){if(v||v===""){var b=v[i];if(b)return b.call(v);if(typeof v.next=="function")return v;if(!isNaN(v.length)){var w=-1,j=function C(){for(;++w<v.length;)if(r.call(v,w))return C.value=v[w],C.done=!1,C;return C.value=e,C.done=!0,C};return j.next=j}}throw new TypeError(typeof v+" is not iterable")}return $.prototype=R,u(le,"constructor",{value:R,configurable:!0}),u(R,"constructor",{value:$,configurable:!0}),$.displayName=l(R,d,"GeneratorFunction"),t.isGeneratorFunction=function(v){var b=typeof v=="function"&&v.constructor;return!!b&&(b===$||(b.displayName||b.name)==="GeneratorFunction")},t.mark=function(v){return Object.setPrototypeOf?Object.setPrototypeOf(v,R):(v.__proto__=R,l(v,d,"GeneratorFunction")),v.prototype=Object.create(le),v},t.awrap=function(v){return{__await:v}},te(Ee.prototype),l(Ee.prototype,a,function(){return this}),t.AsyncIterator=Ee,t.async=function(v,b,w,j,C){C===void 0&&(C=Promise);var I=new Ee(s(v,b,w,j),C);return t.isGeneratorFunction(b)?I:I.next().then(function(N){return N.done?N.value:I.next()})},te(le),l(le,d,"Generator"),l(le,i,function(){return this}),l(le,"toString",function(){return"[object Generator]"}),t.keys=function(v){var b=Object(v),w=[];for(var j in b)w.push(j);return w.reverse(),function C(){for(;w.length;){var I=w.pop();if(I in b)return C.value=I,C.done=!1,C}return C.done=!0,C}},t.values=Ye,ze.prototype={constructor:ze,reset:function(v){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(he),!v)for(var b in this)b.charAt(0)==="t"&&r.call(this,b)&&!isNaN(+b.slice(1))&&(this[b]=e)},stop:function(){this.done=!0;var v=this.tryEntries[0].completion;if(v.type==="throw")throw v.arg;return this.rval},dispatchException:function(v){if(this.done)throw v;var b=this;function w(Z,je){return I.type="throw",I.arg=v,b.next=Z,je&&(b.method="next",b.arg=e),!!je}for(var j=this.tryEntries.length-1;j>=0;--j){var C=this.tryEntries[j],I=C.completion;if(C.tryLoc==="root")return w("end");if(C.tryLoc<=this.prev){var N=r.call(C,"catchLoc"),U=r.call(C,"finallyLoc");if(N&&U){if(this.prev<C.catchLoc)return w(C.catchLoc,!0);if(this.prev<C.finallyLoc)return w(C.finallyLoc)}else if(N){if(this.prev<C.catchLoc)return w(C.catchLoc,!0)}else{if(!U)throw new Error("try statement without catch or finally");if(this.prev<C.finallyLoc)return w(C.finallyLoc)}}}},abrupt:function(v,b){for(var w=this.tryEntries.length-1;w>=0;--w){var j=this.tryEntries[w];if(j.tryLoc<=this.prev&&r.call(j,"finallyLoc")&&this.prev<j.finallyLoc){var C=j;break}}C&&(v==="break"||v==="continue")&&C.tryLoc<=b&&b<=C.finallyLoc&&(C=null);var I=C?C.completion:{};return I.type=v,I.arg=b,C?(this.method="next",this.next=C.finallyLoc,g):this.complete(I)},complete:function(v,b){if(v.type==="throw")throw v.arg;return v.type==="break"||v.type==="continue"?this.next=v.arg:v.type==="return"?(this.rval=this.arg=v.arg,this.method="return",this.next="end"):v.type==="normal"&&b&&(this.next=b),g},finish:function(v){for(var b=this.tryEntries.length-1;b>=0;--b){var w=this.tryEntries[b];if(w.finallyLoc===v)return this.complete(w.completion,w.afterLoc),he(w),g}},catch:function(v){for(var b=this.tryEntries.length-1;b>=0;--b){var w=this.tryEntries[b];if(w.tryLoc===v){var j=w.completion;if(j.type==="throw"){var C=j.arg;he(w)}return C}}throw new Error("illegal catch attempt")},delegateYield:function(v,b,w){return this.delegate={iterator:Ye(v),resultName:b,nextLoc:w},this.method==="next"&&(this.arg=e),g}},t}function Zr(e,t,n,r,u,o,i){try{var a=e[o](i),d=a.value}catch(l){n(l);return}a.done?t(d):Promise.resolve(d).then(r,u)}function Ot(e){return function(){var t=this,n=arguments;return new Promise(function(r,u){var o=e.apply(t,n);function i(d){Zr(o,r,u,i,a,"next",d)}function a(d){Zr(o,r,u,i,a,"throw",d)}i(void 0)})}}function gd(e,t,n){return t=$d(t),t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function bd(e,t){if(e==null)return{};var n={},r=Object.keys(e),u,o;for(o=0;o<r.length;o++)u=r[o],!(t.indexOf(u)>=0)&&(n[u]=e[u]);return n}function yd(e,t){if(e==null)return{};var n=bd(e,t),r,u;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(u=0;u<o.length;u++)r=o[u],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function ye(e,t){return _d(e)||vd(e,t)||Su(e,t)||Sd()}function xu(e){return wd(e)||kd(e)||Su(e)||xd()}function wd(e){if(Array.isArray(e))return $n(e)}function _d(e){if(Array.isArray(e))return e}function kd(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function Su(e,t){if(e){if(typeof e=="string")return $n(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return $n(e,t)}}function $n(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function xd(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Sd(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Cd(e,t){if(typeof e!="object"||e===null)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function $d(e){var t=Cd(e,"string");return typeof t=="symbol"?t:String(t)}var Ed="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";function Cu(e){var t=jd(e);return t&&(t=xu(new Set(t)),t.forEach(function(n){e=$u(e,n,Eu("",n))})),e}function $u(e,t,n){return e.replace(new RegExp(t,"g"),n)}function jd(e){return e.match(/(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe23\u20d0-\u20f0]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe23\u20d0-\u20f0]|\ud83c[\udffb-\udfff])?)*/g)}function Rd(e){var t,n=document.querySelector("em-emoji-picker");if(!n)return Jr(e.native);var r=n==null||(t=n.shadowRoot)===null||t===void 0?void 0:t.querySelector('[title="'.concat(e.name,'"] > span > span'));if(!r)return Jr(e.native);var u=$u(r.style.cssText,'"',"'");return Eu(u,e.native)}function Eu(e,t){return'<img style="'.concat(e,'; display: inline-block" data-emoji="').concat(t,'" src="').concat(Ed,'" />')}function Jr(e){return'<span class="width: 18px; height: 18px; display: inline-block; margin: 0 1px;">'.concat(e,"</span>")}function Et(e){var t=document.createElement("div");t.innerHTML=e;var n=Array.prototype.slice.call(t.querySelectorAll("img"));return n.forEach(function(r){t.innerHTML=t.innerHTML.replace(r.outerHTML,r.dataset.emoji)}),t.innerHTML}function Td(e){var t=window.getSelection();if(t!==null){for(var n=document.createElement("div"),r=0,u=t.rangeCount;r<u;++r)n.appendChild(t.getRangeAt(r).cloneContents());n=Ld(n),e.clipboardData.setData("text",n.innerText),e.preventDefault()}}var En;function Md(){var e=window.getSelection();if(!(!e.rangeCount||(e==null?void 0:e.anchorNode.className)!=="react-input-emoji--input"&&e.anchorNode.parentNode.className!=="react-input-emoji--input")){var t=e.getRangeAt(0);En=t.cloneRange()}}function Pd(e){var t,n;if(window.getSelection){if(t=window.getSelection(),t===null)return;if(t.getRangeAt&&t.rangeCount){var r;n=(r=En)!==null&&r!==void 0?r:t.getRangeAt(0),n.deleteContents();var u=document.createElement("div");u.innerHTML=e;for(var o=document.createDocumentFragment(),i,a;i=u.firstChild;)a=o.appendChild(i);n.insertNode(o),a&&(n=n.cloneRange(),En=n,n.setStartAfter(a),n.collapse(!0),t.removeAllRanges(),t.addRange(n))}}}function Ld(e){var t=Array.prototype.slice.call(e.querySelectorAll("img"));return t.forEach(function(n){n.outerHTML=n.dataset.emoji}),e}function Yr(e){var t=e.text,n=e.html,r=t.length,u=(n.match(/<img/g)||[]).length;return r+u}function Fd(e){var t=e.innerHTML.replaceAll(/<br\s*\/?>/gi,"[BR]"),n=document.createElement("div");n.innerHTML=t;var r=n.innerText,u=r.replaceAll(/\[BR\]/gi,"</br>");return u}function zd(e){var t=e.startContainer,n=e.startOffset;if(t.nodeType!==Node.TEXT_NODE){for(;t.nodeType!==Node.TEXT_NODE&&(t=t.nextSibling,!!t););if(!t)for(t=e.commonAncestorContainer;t.nodeType!==Node.TEXT_NODE;)t=t.firstChild;n=0}return{node:t,offset:n}}function Id(){var e=window.getSelection(),t=e.getRangeAt(0),n=zd(t);return{selection:e,range:t,selectionStart:n}}function Ad(){var e=Id(),t=e.selection,n=e.range,r=e.selectionStart;if(t.isCollapsed&&r.offset===r.node.textContent.length){var u=document.createElement("br");n.insertNode(u),n.setStartAfter(u),n.setEndAfter(u),t.removeAllRanges(),t.addRange(n);var o=document.createElement("br");n.insertNode(o),n.setStartAfter(o),n.setEndAfter(o),t.removeAllRanges(),t.addRange(n)}else{var i=document.createElement("br");n.insertNode(i),n.setStartAfter(i),n.setEndAfter(i),t.removeAllRanges(),t.addRange(n),r.node.nextSibling&&r.node.nextSibling.nodeType===Node.TEXT_NODE&&(n.setStart(r.node.nextSibling,1),n.setEnd(r.node.nextSibling,1)),t.removeAllRanges(),t.addRange(n)}}function Qr(e,t,n,r){if(typeof e!="string")throw new Error("First param must be a string");if(typeof t!="string"&&!(t instanceof RegExp))throw new Error("Second param must be a string pattern or a regular expression");var u=typeof t=="string"?Dd:Bd;return u(e,t,n,r)}function Dd(e,t,n,r){var u=e.indexOf(t);if(u>=0){var o=[],i=u+t.length;return u>0&&o.push(e.substring(0,u)),o.push(typeof n=="function"?n(e.substring(u,i),u+r,e):n),i<e.length&&o.push(e.substring(i)),o}else return[e]}function Bd(e,t,n,r){var u=[],o=typeof n=="function",i=t.lastIndex;t.lastIndex=0;for(var a,d=0;a=t.exec(e);){var l=a.index;a[0]===""&&t.lastIndex++,l!==d&&u.push(e.substring(d,l));var s=a[0];d=l+s.length;var c=o?n.apply(this,a.concat(l+r,a.input)):n;if(u.push(c),!t.global)break}return d<e.length&&u.push(e.substring(d)),t.lastIndex=i,u}var Od=function(t,n,r){if(typeof t=="string")return Qr(t,n,r,0);if(!Array.isArray(t)||!t[0])throw new TypeError("First argument must be an array or non-empty string");for(var u=t.length,o=[],i=0,a=0;a<u;++a){var d=t[a];typeof d=="string"?(o.push.apply(o,Qr(d,n,r,i)),i+=d.length):o.push(d)}return o},Hd=/(?:\ud83d\udc68\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83e\uddd1\ud83c[\udffc-\udfff]|\ud83e\uddd1\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83e\uddd1\ud83c[\udffb\udffd-\udfff]|\ud83e\uddd1\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83e\uddd1\ud83c[\udffb\udffc\udffe\udfff]|\ud83e\uddd1\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83e\uddd1\ud83c[\udffb-\udffd\udfff]|\ud83e\uddd1\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83e\uddd1\ud83c[\udffb-\udffe]|\ud83d\udc68\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc68\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc68\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc68\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc68\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc68\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffc-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffd-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb\udffc\udffe\udfff]|\ud83d\udc69\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffd\udfff]|\ud83d\udc69\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc68\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83d\udc69\ud83c[\udffb-\udfff]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc68\ud83c[\udffb-\udffe]|\ud83d\udc69\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83d\udc69\ud83c[\udffb-\udffe]|\ud83e\uddd1\ud83c\udffb\u200d\u2764\ufe0f\u200d\ud83e\uddd1\ud83c[\udffc-\udfff]|\ud83e\uddd1\ud83c\udffb\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffc\u200d\u2764\ufe0f\u200d\ud83e\uddd1\ud83c[\udffb\udffd-\udfff]|\ud83e\uddd1\ud83c\udffc\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffd\u200d\u2764\ufe0f\u200d\ud83e\uddd1\ud83c[\udffb\udffc\udffe\udfff]|\ud83e\uddd1\ud83c\udffd\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udffe\u200d\u2764\ufe0f\u200d\ud83e\uddd1\ud83c[\udffb-\udffd\udfff]|\ud83e\uddd1\ud83c\udffe\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83e\uddd1\ud83c\udfff\u200d\u2764\ufe0f\u200d\ud83e\uddd1\ud83c[\udffb-\udffe]|\ud83e\uddd1\ud83c\udfff\u200d\ud83e\udd1d\u200d\ud83e\uddd1\ud83c[\udffb-\udfff]|\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d\udc68|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d\udc8b\u200d\ud83d[\udc68\udc69]|\ud83e\udef1\ud83c\udffb\u200d\ud83e\udef2\ud83c[\udffc-\udfff]|\ud83e\udef1\ud83c\udffc\u200d\ud83e\udef2\ud83c[\udffb\udffd-\udfff]|\ud83e\udef1\ud83c\udffd\u200d\ud83e\udef2\ud83c[\udffb\udffc\udffe\udfff]|\ud83e\udef1\ud83c\udffe\u200d\ud83e\udef2\ud83c[\udffb-\udffd\udfff]|\ud83e\udef1\ud83c\udfff\u200d\ud83e\udef2\ud83c[\udffb-\udffe]|\ud83d\udc68\u200d\u2764\ufe0f\u200d\ud83d\udc68|\ud83d\udc69\u200d\u2764\ufe0f\u200d\ud83d[\udc68\udc69]|\ud83e\uddd1\u200d\ud83e\udd1d\u200d\ud83e\uddd1|\ud83d\udc6b\ud83c[\udffb-\udfff]|\ud83d\udc6c\ud83c[\udffb-\udfff]|\ud83d\udc6d\ud83c[\udffb-\udfff]|\ud83d\udc8f\ud83c[\udffb-\udfff]|\ud83d\udc91\ud83c[\udffb-\udfff]|\ud83e\udd1d\ud83c[\udffb-\udfff]|\ud83d[\udc6b-\udc6d\udc8f\udc91]|\ud83e\udd1d)|(?:\ud83d[\udc68\udc69]|\ud83e\uddd1)(?:\ud83c[\udffb-\udfff])?\u200d(?:\u2695\ufe0f|\u2696\ufe0f|\u2708\ufe0f|\ud83c[\udf3e\udf73\udf7c\udf84\udf93\udfa4\udfa8\udfeb\udfed]|\ud83d[\udcbb\udcbc\udd27\udd2c\ude80\ude92]|\ud83e[\uddaf-\uddb3\uddbc\uddbd])|(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75]|\u26f9)((?:\ud83c[\udffb-\udfff]|\ufe0f)\u200d[\u2640\u2642]\ufe0f)|(?:\ud83c[\udfc3\udfc4\udfca]|\ud83d[\udc6e\udc70\udc71\udc73\udc77\udc81\udc82\udc86\udc87\ude45-\ude47\ude4b\ude4d\ude4e\udea3\udeb4-\udeb6]|\ud83e[\udd26\udd35\udd37-\udd39\udd3d\udd3e\uddb8\uddb9\uddcd-\uddcf\uddd4\uddd6-\udddd])(?:\ud83c[\udffb-\udfff])?\u200d[\u2640\u2642]\ufe0f|(?:\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc68\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc68\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d\udc66\u200d\ud83d\udc66|\ud83d\udc69\u200d\ud83d\udc67\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f|\ud83c\udff3\ufe0f\u200d\ud83c\udf08|\ud83d\ude36\u200d\ud83c\udf2b\ufe0f|\u2764\ufe0f\u200d\ud83d\udd25|\u2764\ufe0f\u200d\ud83e\ude79|\ud83c\udff4\u200d\u2620\ufe0f|\ud83d\udc15\u200d\ud83e\uddba|\ud83d\udc3b\u200d\u2744\ufe0f|\ud83d\udc41\u200d\ud83d\udde8|\ud83d\udc68\u200d\ud83d[\udc66\udc67]|\ud83d\udc69\u200d\ud83d[\udc66\udc67]|\ud83d\udc6f\u200d\u2640\ufe0f|\ud83d\udc6f\u200d\u2642\ufe0f|\ud83d\ude2e\u200d\ud83d\udca8|\ud83d\ude35\u200d\ud83d\udcab|\ud83e\udd3c\u200d\u2640\ufe0f|\ud83e\udd3c\u200d\u2642\ufe0f|\ud83e\uddde\u200d\u2640\ufe0f|\ud83e\uddde\u200d\u2642\ufe0f|\ud83e\udddf\u200d\u2640\ufe0f|\ud83e\udddf\u200d\u2642\ufe0f|\ud83d\udc08\u200d\u2b1b)|[#*0-9]\ufe0f?\u20e3|(?:[©®\u2122\u265f]\ufe0f)|(?:\ud83c[\udc04\udd70\udd71\udd7e\udd7f\ude02\ude1a\ude2f\ude37\udf21\udf24-\udf2c\udf36\udf7d\udf96\udf97\udf99-\udf9b\udf9e\udf9f\udfcd\udfce\udfd4-\udfdf\udff3\udff5\udff7]|\ud83d[\udc3f\udc41\udcfd\udd49\udd4a\udd6f\udd70\udd73\udd76-\udd79\udd87\udd8a-\udd8d\udda5\udda8\uddb1\uddb2\uddbc\uddc2-\uddc4\uddd1-\uddd3\udddc-\uddde\udde1\udde3\udde8\uddef\uddf3\uddfa\udecb\udecd-\udecf\udee0-\udee5\udee9\udef0\udef3]|[\u203c\u2049\u2139\u2194-\u2199\u21a9\u21aa\u231a\u231b\u2328\u23cf\u23ed-\u23ef\u23f1\u23f2\u23f8-\u23fa\u24c2\u25aa\u25ab\u25b6\u25c0\u25fb-\u25fe\u2600-\u2604\u260e\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262a\u262e\u262f\u2638-\u263a\u2640\u2642\u2648-\u2653\u2660\u2663\u2665\u2666\u2668\u267b\u267f\u2692-\u2697\u2699\u269b\u269c\u26a0\u26a1\u26a7\u26aa\u26ab\u26b0\u26b1\u26bd\u26be\u26c4\u26c5\u26c8\u26cf\u26d1\u26d3\u26d4\u26e9\u26ea\u26f0-\u26f5\u26f8\u26fa\u26fd\u2702\u2708\u2709\u270f\u2712\u2714\u2716\u271d\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u2764\u27a1\u2934\u2935\u2b05-\u2b07\u2b1b\u2b1c\u2b50\u2b55\u3030\u303d\u3297\u3299])(?:\ufe0f|(?!\ufe0e))|(?:(?:\ud83c[\udfcb\udfcc]|\ud83d[\udd74\udd75\udd90]|[\u261d\u26f7\u26f9\u270c\u270d])(?:\ufe0f|(?!\ufe0e))|(?:\ud83c[\udf85\udfc2-\udfc4\udfc7\udfca]|\ud83d[\udc42\udc43\udc46-\udc50\udc66-\udc69\udc6e\udc70-\udc78\udc7c\udc81-\udc83\udc85-\udc87\udcaa\udd7a\udd95\udd96\ude45-\ude47\ude4b-\ude4f\udea3\udeb4-\udeb6\udec0\udecc]|\ud83e[\udd0c\udd0f\udd18-\udd1c\udd1e\udd1f\udd26\udd30-\udd39\udd3d\udd3e\udd77\uddb5\uddb6\uddb8\uddb9\uddbb\uddcd-\uddcf\uddd1-\udddd\udec3-\udec5\udef0-\udef6]|[\u270a\u270b]))(?:\ud83c[\udffb-\udfff])?|(?:\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc73\udb40\udc63\udb40\udc74\udb40\udc7f|\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc77\udb40\udc6c\udb40\udc73\udb40\udc7f|\ud83c\udde6\ud83c[\udde8-\uddec\uddee\uddf1\uddf2\uddf4\uddf6-\uddfa\uddfc\uddfd\uddff]|\ud83c\udde7\ud83c[\udde6\udde7\udde9-\uddef\uddf1-\uddf4\uddf6-\uddf9\uddfb\uddfc\uddfe\uddff]|\ud83c\udde8\ud83c[\udde6\udde8\udde9\uddeb-\uddee\uddf0-\uddf5\uddf7\uddfa-\uddff]|\ud83c\udde9\ud83c[\uddea\uddec\uddef\uddf0\uddf2\uddf4\uddff]|\ud83c\uddea\ud83c[\udde6\udde8\uddea\uddec\udded\uddf7-\uddfa]|\ud83c\uddeb\ud83c[\uddee-\uddf0\uddf2\uddf4\uddf7]|\ud83c\uddec\ud83c[\udde6\udde7\udde9-\uddee\uddf1-\uddf3\uddf5-\uddfa\uddfc\uddfe]|\ud83c\udded\ud83c[\uddf0\uddf2\uddf3\uddf7\uddf9\uddfa]|\ud83c\uddee\ud83c[\udde8-\uddea\uddf1-\uddf4\uddf6-\uddf9]|\ud83c\uddef\ud83c[\uddea\uddf2\uddf4\uddf5]|\ud83c\uddf0\ud83c[\uddea\uddec-\uddee\uddf2\uddf3\uddf5\uddf7\uddfc\uddfe\uddff]|\ud83c\uddf1\ud83c[\udde6-\udde8\uddee\uddf0\uddf7-\uddfb\uddfe]|\ud83c\uddf2\ud83c[\udde6\udde8-\udded\uddf0-\uddff]|\ud83c\uddf3\ud83c[\udde6\udde8\uddea-\uddec\uddee\uddf1\uddf4\uddf5\uddf7\uddfa\uddff]|\ud83c\uddf4\ud83c\uddf2|\ud83c\uddf5\ud83c[\udde6\uddea-\udded\uddf0-\uddf3\uddf7-\uddf9\uddfc\uddfe]|\ud83c\uddf6\ud83c\udde6|\ud83c\uddf7\ud83c[\uddea\uddf4\uddf8\uddfa\uddfc]|\ud83c\uddf8\ud83c[\udde6-\uddea\uddec-\uddf4\uddf7-\uddf9\uddfb\uddfd-\uddff]|\ud83c\uddf9\ud83c[\udde6\udde8\udde9\uddeb-\udded\uddef-\uddf4\uddf7\uddf9\uddfb\uddfc\uddff]|\ud83c\uddfa\ud83c[\udde6\uddec\uddf2\uddf3\uddf8\uddfe\uddff]|\ud83c\uddfb\ud83c[\udde6\udde8\uddea\uddec\uddee\uddf3\uddfa]|\ud83c\uddfc\ud83c[\uddeb\uddf8]|\ud83c\uddfd\ud83c\uddf0|\ud83c\uddfe\ud83c[\uddea\uddf9]|\ud83c\uddff\ud83c[\udde6\uddf2\uddfc]|\ud83c[\udccf\udd8e\udd91-\udd9a\udde6-\uddff\ude01\ude32-\ude36\ude38-\ude3a\ude50\ude51\udf00-\udf20\udf2d-\udf35\udf37-\udf7c\udf7e-\udf84\udf86-\udf93\udfa0-\udfc1\udfc5\udfc6\udfc8\udfc9\udfcf-\udfd3\udfe0-\udff0\udff4\udff8-\udfff]|\ud83d[\udc00-\udc3e\udc40\udc44\udc45\udc51-\udc65\udc6a\udc6f\udc79-\udc7b\udc7d-\udc80\udc84\udc88-\udc8e\udc90\udc92-\udca9\udcab-\udcfc\udcff-\udd3d\udd4b-\udd4e\udd50-\udd67\udda4\uddfb-\ude44\ude48-\ude4a\ude80-\udea2\udea4-\udeb3\udeb7-\udebf\udec1-\udec5\uded0-\uded2\uded5-\uded7\udedd-\udedf\udeeb\udeec\udef4-\udefc\udfe0-\udfeb\udff0]|\ud83e[\udd0d\udd0e\udd10-\udd17\udd20-\udd25\udd27-\udd2f\udd3a\udd3c\udd3f-\udd45\udd47-\udd76\udd78-\uddb4\uddb7\uddba\uddbc-\uddcc\uddd0\uddde-\uddff\ude70-\ude74\ude78-\ude7c\ude80-\ude86\ude90-\udeac\udeb0-\udeba\udec0-\udec2\uded0-\uded9\udee0-\udee7]|[\u23e9-\u23ec\u23f0\u23f3\u267e\u26ce\u2705\u2728\u274c\u274e\u2753-\u2755\u2795-\u2797\u27b0\u27bf\ue50a])|\ufe0f/g;function Nd(e,t){for(var n=[],r=0,u=0,o=0;o<e.length;)r=e.charCodeAt(o++),u?(n.push((65536+(u-55296<<10)+(r-56320)).toString(16)),u=0):55296<=r&&r<=56319?u=r:n.push(r.toString(16));return n.join(t||"-")}var Ud=/\uFE0F/g,Vd=String.fromCharCode(8205);function Wd(e){return Nd(e.indexOf(Vd)<0?e.replace(Ud,""):e)}var qd=function(t,n){return Od(t,Hd,function(u,o,i){var a=Wd(u);return n(a,u,i)})},Gd=L.createElement,Kd=typeof location>"u"?"":location.protocol==="https:"?"https:":"http:",Xd={height:"1em",width:"1em",margin:"0 .05em 0 .1em",verticalAlign:"-0.1em"};function Zd(e){return e&&e.length>0&&e.charAt(e.length-1)!==":"?e+":":e}var Jd=function(t){return t=eo({protocol:Kd,baseUrl:"//cdnjs.cloudflare.com/ajax/libs/twemoji/14.0.2/",size:"72x72",ext:".png",props:null},t),function(r,u,o){var i="";return t.baseUrl.indexOf("http")!==0&&(i+=Zd(t.protocol)),i+=t.baseUrl+t.size+"/"+r+t.ext,Gd("img",eo({key:o,alt:u,draggable:!1,src:i,style:Xd},t.props))}};function eo(){for(var e={},t=arguments.length,n=0;n<t;++n){var r=arguments[n];if(r)for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&(e[u]=r[u])}return e}var Yd=qd,Qd=Jd,es=function(t,n){var r=typeof n=="function"?n:Qd(n);return Yd(t,r)},ts=es,ns=new RegExp(/(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe23\u20d0-\u20f0]|\ud83c[\udffb-\udfff])?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe23\u20d0-\u20f0]|\ud83c[\udffb-\udfff])?)*/g);function ju(e,t){var n=k.useRef([]),r=k.useRef(""),u=k.useCallback(function(i){n.current.push(i)},[]),o=k.useCallback(function(i){var a=n.current.reduce(function(d,l){return l(d)},i);return a=rs(a,e),t&&(a=os(a)),r.current=a,a},[e,t]);return{addSanitizeFn:u,sanitize:o,sanitizedTextRef:r}}function rs(e,t){var n=document.createElement("div");n.innerHTML=e;var r;return t?r=Fd(n):r=n.innerText||"",r=r.replace(/\n/gi,""),r}function os(e){return e=us(e),e=pd(ts(e)),e=e.replace(new RegExp("&lt;span class=&quot;message-emoji&quot;&gt;","g"),'<span class="message-emoji">'),e=e.replace(new RegExp("&lt;/span&gt;","g"),"</span>"),e}function us(e){return e.replace(ns,'<span class="message-emoji">$&</span>')}function is(e){var t=e.ref,n=e.textInputRef,r=e.setValue,u=e.emitChange,o=e.shouldConvertEmojiToImage,i=ju(!1,o),a=i.sanitize,d=i.sanitizedTextRef;k.useImperativeHandle(t,function(){return{get value(){return d.current},set value(l){r(l)},focus:function(){n.current!==null&&n.current.focus()},blur:function(){n.current!==null&&a(n.current.html),u()}}})}function as(e,t,n){var r=k.useRef(null),u=k.useRef(n),o=k.useCallback(function(){if(e.current!==null){var a=r.current,d=e.current.size;(!a||a.width!==d.width||a.height!==d.height)&&typeof t=="function"&&t(d),r.current=d}},[t,e]),i=k.useCallback(function(a){typeof u.current=="function"&&u.current(a),typeof t=="function"&&o()},[o,t]);return k.useEffect(function(){e.current&&o()},[o,e]),i}var ds=["placeholder","style","tabIndex","className","onChange"],ss=function(t,n){var r=t.placeholder,u=t.style,o=t.tabIndex,i=t.className,a=t.onChange,d=yd(t,ds);k.useImperativeHandle(n,function(){return{appendContent:function(S){f.current&&f.current.focus(),Pd(S),f.current&&f.current.focus(),f.current&&c.current&&Et(f.current.innerHTML)===""?c.current.style.visibility="visible":c.current&&(c.current.style.visibility="hidden"),f.current&&typeof a=="function"&&a(f.current.innerHTML)},set html(g){if(f.current&&(f.current.innerHTML=g),c.current){var S=Et(g);S===""?c.current.style.visibility="visible":c.current.style.visibility="hidden"}typeof a=="function"&&f.current&&a(f.current.innerHTML)},get html(){return f.current?f.current.innerHTML:""},get text(){return f.current?f.current.innerText:""},get size(){return f.current?{width:f.current.offsetWidth,height:f.current.offsetHeight}:{width:0,height:0}},focus:function(){f.current&&f.current.focus()}}});var l=k.useMemo(function(){var g={};return u.placeholderColor&&(g.color=u.placeholderColor),g},[u==null?void 0:u.placeholderColor]),s=k.useMemo(function(){var g={};return u.color&&(g.color=u.color),g},[u==null?void 0:u.color]),c=k.useRef(null),f=k.useRef(null);function m(g){if(g.key==="Enter"&&(g.shiftKey===!0||g.ctrlKey===!0)&&d.shouldReturn&&(g.preventDefault(),f.current)){Ad();return}g.key==="Enter"?d.onEnter(g):g.key==="ArrowUp"?d.onArrowUp(g):g.key==="ArrowDown"?d.onArrowDown(g):g.key.length===1&&c.current&&(c.current.style.visibility="hidden"),d.onKeyDown(g)}function p(){d.onFocus()}function y(g){d.onKeyUp(g);var S=f.current;if(c.current&&S){var $=Et(S.innerHTML);$===""?c.current.style.visibility="visible":c.current.style.visibility="hidden"}typeof a=="function"&&f.current&&a(f.current.innerHTML)}return L.createElement("div",{className:"react-input-emoji--container",style:u},L.createElement("div",{className:"react-input-emoji--wrapper",onClick:p},L.createElement("div",{ref:c,className:"react-input-emoji--placeholder",style:l},r),L.createElement("div",{ref:f,onKeyDown:m,onKeyUp:y,tabIndex:o,contentEditable:!0,className:"react-input-emoji--input".concat(i?" ".concat(i):""),onBlur:d.onBlur,onCopy:d.onCopy,onPaste:d.onPaste,"data-testid":"react-input-emoji--input",style:s})))},ls=k.forwardRef(ss);function to(e){var t=e.showPicker,n=e.toggleShowPicker,r=e.buttonElement,u=e.buttonRef,o=k.useRef(null),i=k.useState(!1),a=ye(i,2),d=a[0],l=a[1];return k.useEffect(function(){var s,c,f,m;((s=u==null||(c=u.current)===null||c===void 0||(c=c.childNodes)===null||c===void 0?void 0:c.length)!==null&&s!==void 0?s:0)>2?(o.current.appendChild(u.current.childNodes[0]),l(!0)):((f=r==null||(m=r.childNodes)===null||m===void 0?void 0:m.length)!==null&&f!==void 0?f:0)>2&&(o.current.appendChild(r==null?void 0:r.childNodes[0]),l(!0))},[r==null?void 0:r.childNodes]),L.createElement("button",{ref:o,type:"button",className:"react-input-emoji--button".concat(t?" react-input-emoji--button__show":""),onClick:n},!d&&L.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"24",height:"24",className:"react-input-emoji--button--icon"},L.createElement("path",{d:"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"}),L.createElement("path",{d:"M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0"})))}function Ru(e){return e&&e.__esModule?e.default:e}function fe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Qt,E,Tu,ut,Mu,no,Ht={},Pu=[],cs=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function Le(e,t){for(var n in t)e[n]=t[n];return e}function Lu(e){var t=e.parentNode;t&&t.removeChild(e)}function jn(e,t,n){var r,u,o,i={};for(o in t)o=="key"?r=t[o]:o=="ref"?u=t[o]:i[o]=t[o];if(arguments.length>2&&(i.children=arguments.length>3?Qt.call(arguments,2):n),typeof e=="function"&&e.defaultProps!=null)for(o in e.defaultProps)i[o]===void 0&&(i[o]=e.defaultProps[o]);return jt(e,i,r,u,null)}function jt(e,t,n,r,u){var o={type:e,props:t,key:n,ref:r,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:u??++Tu};return u==null&&E.vnode!=null&&E.vnode(o),o}function ke(){return{current:null}}function Ke(e){return e.children}function we(e,t){this.props=e,this.context=t}function Xe(e,t){if(t==null)return e.__?Xe(e.__,e.__.__k.indexOf(e)+1):null;for(var n;t<e.__k.length;t++)if((n=e.__k[t])!=null&&n.__e!=null)return n.__e;return typeof e.type=="function"?Xe(e):null}function Fu(e){var t,n;if((e=e.__)!=null&&e.__c!=null){for(e.__e=e.__c.base=null,t=0;t<e.__k.length;t++)if((n=e.__k[t])!=null&&n.__e!=null){e.__e=e.__c.base=n.__e;break}return Fu(e)}}function ro(e){(!e.__d&&(e.__d=!0)&&ut.push(e)&&!Nt.__r++||no!==E.debounceRendering)&&((no=E.debounceRendering)||Mu)(Nt)}function Nt(){for(var e;Nt.__r=ut.length;)e=ut.sort(function(t,n){return t.__v.__b-n.__v.__b}),ut=[],e.some(function(t){var n,r,u,o,i,a;t.__d&&(i=(o=(n=t).__v).__e,(a=n.__P)&&(r=[],(u=Le({},o)).__v=o.__v+1,Qn(a,o,u,n.__n,a.ownerSVGElement!==void 0,o.__h!=null?[i]:null,r,i??Xe(o),o.__h),Du(r,o),o.__e!=i&&Fu(o)))})}function zu(e,t,n,r,u,o,i,a,d,l){var s,c,f,m,p,y,g,S=r&&r.__k||Pu,$=S.length;for(n.__k=[],s=0;s<t.length;s++)if((m=n.__k[s]=(m=t[s])==null||typeof m=="boolean"?null:typeof m=="string"||typeof m=="number"||typeof m=="bigint"?jt(null,m,null,null,m):Array.isArray(m)?jt(Ke,{children:m},null,null,null):m.__b>0?jt(m.type,m.props,m.key,null,m.__v):m)!=null){if(m.__=n,m.__b=n.__b+1,(f=S[s])===null||f&&m.key==f.key&&m.type===f.type)S[s]=void 0;else for(c=0;c<$;c++){if((f=S[c])&&m.key==f.key&&m.type===f.type){S[c]=void 0;break}f=null}Qn(e,m,f=f||Ht,u,o,i,a,d,l),p=m.__e,(c=m.ref)&&f.ref!=c&&(g||(g=[]),f.ref&&g.push(f.ref,null,m),g.push(c,m.__c||p,m)),p!=null?(y==null&&(y=p),typeof m.type=="function"&&m.__k===f.__k?m.__d=d=Iu(m,d,e):d=Au(e,m,f,S,p,d),typeof n.type=="function"&&(n.__d=d)):d&&f.__e==d&&d.parentNode!=e&&(d=Xe(f))}for(n.__e=y,s=$;s--;)S[s]!=null&&(typeof n.type=="function"&&S[s].__e!=null&&S[s].__e==n.__d&&(n.__d=Xe(r,s+1)),Ou(S[s],S[s]));if(g)for(s=0;s<g.length;s++)Bu(g[s],g[++s],g[++s])}function Iu(e,t,n){for(var r,u=e.__k,o=0;u&&o<u.length;o++)(r=u[o])&&(r.__=e,t=typeof r.type=="function"?Iu(r,t,n):Au(n,r,r,u,r.__e,t));return t}function Ut(e,t){return t=t||[],e==null||typeof e=="boolean"||(Array.isArray(e)?e.some(function(n){Ut(n,t)}):t.push(e)),t}function Au(e,t,n,r,u,o){var i,a,d;if(t.__d!==void 0)i=t.__d,t.__d=void 0;else if(n==null||u!=o||u.parentNode==null)e:if(o==null||o.parentNode!==e)e.appendChild(u),i=null;else{for(a=o,d=0;(a=a.nextSibling)&&d<r.length;d+=2)if(a==u)break e;e.insertBefore(u,o),i=o}return i!==void 0?i:u.nextSibling}function fs(e,t,n,r,u){var o;for(o in n)o==="children"||o==="key"||o in t||Vt(e,o,null,n[o],r);for(o in t)u&&typeof t[o]!="function"||o==="children"||o==="key"||o==="value"||o==="checked"||n[o]===t[o]||Vt(e,o,t[o],n[o],r)}function oo(e,t,n){t[0]==="-"?e.setProperty(t,n):e[t]=n==null?"":typeof n!="number"||cs.test(t)?n:n+"px"}function Vt(e,t,n,r,u){var o;e:if(t==="style")if(typeof n=="string")e.style.cssText=n;else{if(typeof r=="string"&&(e.style.cssText=r=""),r)for(t in r)n&&t in n||oo(e.style,t,"");if(n)for(t in n)r&&n[t]===r[t]||oo(e.style,t,n[t])}else if(t[0]==="o"&&t[1]==="n")o=t!==(t=t.replace(/Capture$/,"")),t=t.toLowerCase()in e?t.toLowerCase().slice(2):t.slice(2),e.l||(e.l={}),e.l[t+o]=n,n?r||e.addEventListener(t,o?io:uo,o):e.removeEventListener(t,o?io:uo,o);else if(t!=="dangerouslySetInnerHTML"){if(u)t=t.replace(/xlink[H:h]/,"h").replace(/sName$/,"s");else if(t!=="href"&&t!=="list"&&t!=="form"&&t!=="tabIndex"&&t!=="download"&&t in e)try{e[t]=n??"";break e}catch{}typeof n=="function"||(n!=null&&(n!==!1||t[0]==="a"&&t[1]==="r")?e.setAttribute(t,n):e.removeAttribute(t))}}function uo(e){this.l[e.type+!1](E.event?E.event(e):e)}function io(e){this.l[e.type+!0](E.event?E.event(e):e)}function Qn(e,t,n,r,u,o,i,a,d){var l,s,c,f,m,p,y,g,S,$,R,M=t.type;if(t.constructor!==void 0)return null;n.__h!=null&&(d=n.__h,a=t.__e=n.__e,t.__h=null,o=[a]),(l=E.__b)&&l(t);try{e:if(typeof M=="function"){if(g=t.props,S=(l=M.contextType)&&r[l.__c],$=l?S?S.props.value:l.__:r,n.__c?y=(s=t.__c=n.__c).__=s.__E:("prototype"in M&&M.prototype.render?t.__c=s=new M(g,$):(t.__c=s=new we(g,$),s.constructor=M,s.render=hs),S&&S.sub(s),s.props=g,s.state||(s.state={}),s.context=$,s.__n=r,c=s.__d=!0,s.__h=[]),s.__s==null&&(s.__s=s.state),M.getDerivedStateFromProps!=null&&(s.__s==s.state&&(s.__s=Le({},s.__s)),Le(s.__s,M.getDerivedStateFromProps(g,s.__s))),f=s.props,m=s.state,c)M.getDerivedStateFromProps==null&&s.componentWillMount!=null&&s.componentWillMount(),s.componentDidMount!=null&&s.__h.push(s.componentDidMount);else{if(M.getDerivedStateFromProps==null&&g!==f&&s.componentWillReceiveProps!=null&&s.componentWillReceiveProps(g,$),!s.__e&&s.shouldComponentUpdate!=null&&s.shouldComponentUpdate(g,s.__s,$)===!1||t.__v===n.__v){s.props=g,s.state=s.__s,t.__v!==n.__v&&(s.__d=!1),s.__v=t,t.__e=n.__e,t.__k=n.__k,t.__k.forEach(function(A){A&&(A.__=t)}),s.__h.length&&i.push(s);break e}s.componentWillUpdate!=null&&s.componentWillUpdate(g,s.__s,$),s.componentDidUpdate!=null&&s.__h.push(function(){s.componentDidUpdate(f,m,p)})}s.context=$,s.props=g,s.state=s.__s,(l=E.__r)&&l(t),s.__d=!1,s.__v=t,s.__P=e,l=s.render(s.props,s.state,s.context),s.state=s.__s,s.getChildContext!=null&&(r=Le(Le({},r),s.getChildContext())),c||s.getSnapshotBeforeUpdate==null||(p=s.getSnapshotBeforeUpdate(f,m)),R=l!=null&&l.type===Ke&&l.key==null?l.props.children:l,zu(e,Array.isArray(R)?R:[R],t,n,r,u,o,i,a,d),s.base=t.__e,t.__h=null,s.__h.length&&i.push(s),y&&(s.__E=s.__=null),s.__e=!1}else o==null&&t.__v===n.__v?(t.__k=n.__k,t.__e=n.__e):t.__e=ps(n.__e,t,n,r,u,o,i,d);(l=E.diffed)&&l(t)}catch(A){t.__v=null,(d||o!=null)&&(t.__e=a,t.__h=!!d,o[o.indexOf(a)]=null),E.__e(A,t,n)}}function Du(e,t){E.__c&&E.__c(t,e),e.some(function(n){try{e=n.__h,n.__h=[],e.some(function(r){r.call(n)})}catch(r){E.__e(r,n.__v)}})}function ps(e,t,n,r,u,o,i,a){var d,l,s,c=n.props,f=t.props,m=t.type,p=0;if(m==="svg"&&(u=!0),o!=null){for(;p<o.length;p++)if((d=o[p])&&"setAttribute"in d==!!m&&(m?d.localName===m:d.nodeType===3)){e=d,o[p]=null;break}}if(e==null){if(m===null)return document.createTextNode(f);e=u?document.createElementNS("http://www.w3.org/2000/svg",m):document.createElement(m,f.is&&f),o=null,a=!1}if(m===null)c===f||a&&e.data===f||(e.data=f);else{if(o=o&&Qt.call(e.childNodes),l=(c=n.props||Ht).dangerouslySetInnerHTML,s=f.dangerouslySetInnerHTML,!a){if(o!=null)for(c={},p=0;p<e.attributes.length;p++)c[e.attributes[p].name]=e.attributes[p].value;(s||l)&&(s&&(l&&s.__html==l.__html||s.__html===e.innerHTML)||(e.innerHTML=s&&s.__html||""))}if(fs(e,f,c,u,a),s)t.__k=[];else if(p=t.props.children,zu(e,Array.isArray(p)?p:[p],t,n,r,u&&m!=="foreignObject",o,i,o?o[0]:n.__k&&Xe(n,0),a),o!=null)for(p=o.length;p--;)o[p]!=null&&Lu(o[p]);a||("value"in f&&(p=f.value)!==void 0&&(p!==c.value||p!==e.value||m==="progress"&&!p)&&Vt(e,"value",p,c.value,!1),"checked"in f&&(p=f.checked)!==void 0&&p!==e.checked&&Vt(e,"checked",p,c.checked,!1))}return e}function Bu(e,t,n){try{typeof e=="function"?e(t):e.current=t}catch(r){E.__e(r,n)}}function Ou(e,t,n){var r,u;if(E.unmount&&E.unmount(e),(r=e.ref)&&(r.current&&r.current!==e.__e||Bu(r,null,t)),(r=e.__c)!=null){if(r.componentWillUnmount)try{r.componentWillUnmount()}catch(o){E.__e(o,t)}r.base=r.__P=null}if(r=e.__k)for(u=0;u<r.length;u++)r[u]&&Ou(r[u],t,typeof e.type!="function");n||e.__e==null||Lu(e.__e),e.__e=e.__d=void 0}function hs(e,t,n){return this.constructor(e,n)}function Hu(e,t,n){var r,u,o;E.__&&E.__(e,t),u=(r=typeof n=="function")?null:n&&n.__k||t.__k,o=[],Qn(t,e=(!r&&n||t).__k=jn(Ke,null,[e]),u||Ht,Ht,t.ownerSVGElement!==void 0,!r&&n?[n]:u?null:t.firstChild?Qt.call(t.childNodes):null,o,!r&&n?n:u?u.__e:t.firstChild,r),Du(o,e)}Qt=Pu.slice,E={__e:function(e,t){for(var n,r,u;t=t.__;)if((n=t.__c)&&!n.__)try{if((r=n.constructor)&&r.getDerivedStateFromError!=null&&(n.setState(r.getDerivedStateFromError(e)),u=n.__d),n.componentDidCatch!=null&&(n.componentDidCatch(e),u=n.__d),u)return n.__E=n}catch(o){e=o}throw e}},Tu=0,we.prototype.setState=function(e,t){var n;n=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=Le({},this.state),typeof e=="function"&&(e=e(Le({},n),this.props)),e&&Le(n,e),e!=null&&this.__v&&(t&&this.__h.push(t),ro(this))},we.prototype.forceUpdate=function(e){this.__v&&(this.__e=!0,e&&this.__h.push(e),ro(this))},we.prototype.render=Ke,ut=[],Mu=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Nt.__r=0;var ms=0;function h(e,t,n,r,u){var o,i,a={};for(i in t)i=="ref"?o=t[i]:a[i]=t[i];var d={type:e,props:a,key:n,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:--ms,__source:r,__self:u};if(typeof e=="function"&&(o=e.defaultProps))for(i in o)a[i]===void 0&&(a[i]=o[i]);return E.vnode&&E.vnode(d),d}function vs(e,t){try{window.localStorage[`emoji-mart.${e}`]=JSON.stringify(t)}catch{}}function gs(e){try{const t=window.localStorage[`emoji-mart.${e}`];if(t)return JSON.parse(t)}catch{}}var Fe={set:vs,get:gs};const fn=new Map,bs=[{v:15,emoji:"🫨"},{v:14,emoji:"🫠"},{v:13.1,emoji:"😶‍🌫️"},{v:13,emoji:"🥸"},{v:12.1,emoji:"🧑‍🦰"},{v:12,emoji:"🥱"},{v:11,emoji:"🥰"},{v:5,emoji:"🤩"},{v:4,emoji:"👱‍♀️"},{v:3,emoji:"🤣"},{v:2,emoji:"👋🏻"},{v:1,emoji:"🙃"}];function ys(){for(const{v:e,emoji:t}of bs)if(Nu(t))return e}function ws(){return!Nu("🇨🇦")}function Nu(e){if(fn.has(e))return fn.get(e);const t=_s(e);return fn.set(e,t),t}const _s=(()=>{let e=null;try{navigator.userAgent.includes("jsdom")||(e=document.createElement("canvas").getContext("2d",{willReadFrequently:!0}))}catch{}if(!e)return()=>!1;const t=25,n=20,r=Math.floor(t/2);return e.font=r+"px Arial, Sans-Serif",e.textBaseline="top",e.canvas.width=n*2,e.canvas.height=t,u=>{e.clearRect(0,0,n*2,t),e.fillStyle="#FF0000",e.fillText(u,0,22),e.fillStyle="#0000FF",e.fillText(u,n,22);const o=e.getImageData(0,0,n,t).data,i=o.length;let a=0;for(;a<i&&!o[a+3];a+=4);if(a>=i)return!1;const d=n+a/4%n,l=Math.floor(a/4/n),s=e.getImageData(d,l,1,1).data;return!(o[a]!==s[0]||o[a+2]!==s[2]||e.measureText(u).width>=n)}})();var ao={latestVersion:ys,noCountryFlags:ws};const Rn=["+1","grinning","kissing_heart","heart_eyes","laughing","stuck_out_tongue_winking_eye","sweat_smile","joy","scream","disappointed","unamused","weary","sob","sunglasses","heart"];let q=null;function ks(e){q||(q=Fe.get("frequently")||{});const t=e.id||e;t&&(q[t]||(q[t]=0),q[t]+=1,Fe.set("last",t),Fe.set("frequently",q))}function xs({maxFrequentRows:e,perLine:t}){if(!e)return[];q||(q=Fe.get("frequently"));let n=[];if(!q){q={};for(let o in Rn.slice(0,t)){const i=Rn[o];q[i]=t-o,n.push(i)}return n}const r=e*t,u=Fe.get("last");for(let o in q)n.push(o);if(n.sort((o,i)=>{const a=q[i],d=q[o];return a==d?o.localeCompare(i):a-d}),n.length>r){const o=n.slice(r);n=n.slice(0,r);for(let i of o)i!=u&&delete q[i];u&&n.indexOf(u)==-1&&(delete q[n[n.length-1]],n.splice(-1,1,u)),Fe.set("frequently",q)}return n}var Uu={add:ks,get:xs,DEFAULTS:Rn},Vu={};Vu=JSON.parse('{"search":"Search","search_no_results_1":"Oh no!","search_no_results_2":"That emoji couldn’t be found","pick":"Pick an emoji…","add_custom":"Add custom emoji","categories":{"activity":"Activity","custom":"Custom","flags":"Flags","foods":"Food & Drink","frequent":"Frequently used","nature":"Animals & Nature","objects":"Objects","people":"Smileys & People","places":"Travel & Places","search":"Search Results","symbols":"Symbols"},"skins":{"1":"Default","2":"Light","3":"Medium-Light","4":"Medium","5":"Medium-Dark","6":"Dark","choose":"Choose default skin tone"}}');var xe={autoFocus:{value:!1},dynamicWidth:{value:!1},emojiButtonColors:{value:null},emojiButtonRadius:{value:"100%"},emojiButtonSize:{value:36},emojiSize:{value:24},emojiVersion:{value:15,choices:[1,2,3,4,5,11,12,12.1,13,13.1,14,15]},exceptEmojis:{value:[]},icons:{value:"auto",choices:["auto","outline","solid"]},locale:{value:"en",choices:["en","ar","be","cs","de","es","fa","fi","fr","hi","it","ja","ko","nl","pl","pt","ru","sa","tr","uk","vi","zh"]},maxFrequentRows:{value:4},navPosition:{value:"top",choices:["top","bottom","none"]},noCountryFlags:{value:!1},noResultsEmoji:{value:null},perLine:{value:9},previewEmoji:{value:null},previewPosition:{value:"bottom",choices:["top","bottom","none"]},searchPosition:{value:"sticky",choices:["sticky","static","none"]},set:{value:"native",choices:["native","apple","facebook","google","twitter"]},skin:{value:1,choices:[1,2,3,4,5,6]},skinTonePosition:{value:"preview",choices:["preview","search","none"]},theme:{value:"auto",choices:["auto","light","dark"]},categories:null,categoryIcons:null,custom:null,data:null,i18n:null,getImageURL:null,getSpritesheetURL:null,onAddCustomEmoji:null,onClickOutside:null,onEmojiSelect:null,stickySearch:{deprecated:!0,value:!0}};let J=null,z=null;const pn={};async function so(e){if(pn[e])return pn[e];const n=await(await fetch(e)).json();return pn[e]=n,n}let hn=null,Wu=null,qu=!1;function en(e,{caller:t}={}){return hn||(hn=new Promise(n=>{Wu=n})),e?Ss(e):t&&!qu&&console.warn(`\`${t}\` requires data to be initialized first. Promise will be pending until \`init\` is called.`),hn}async function Ss(e){qu=!0;let{emojiVersion:t,set:n,locale:r}=e;if(t||(t=xe.emojiVersion.value),n||(n=xe.set.value),r||(r=xe.locale.value),z)z.categories=z.categories.filter(d=>!d.name);else{z=(typeof e.data=="function"?await e.data():e.data)||await so(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/sets/${t}/${n}.json`),z.emoticons={},z.natives={},z.categories.unshift({id:"frequent",emojis:[]});for(const d in z.aliases){const l=z.aliases[d],s=z.emojis[l];s&&(s.aliases||(s.aliases=[]),s.aliases.push(d))}z.originalCategories=z.categories}if(J=(typeof e.i18n=="function"?await e.i18n():e.i18n)||(r=="en"?Ru(Vu):await so(`https://cdn.jsdelivr.net/npm/@emoji-mart/data@latest/i18n/${r}.json`)),e.custom)for(let d in e.custom){d=parseInt(d);const l=e.custom[d],s=e.custom[d-1];if(!(!l.emojis||!l.emojis.length)){l.id||(l.id=`custom_${d+1}`),l.name||(l.name=J.categories.custom),s&&!l.icon&&(l.target=s.target||s),z.categories.push(l);for(const c of l.emojis)z.emojis[c.id]=c}}e.categories&&(z.categories=z.originalCategories.filter(d=>e.categories.indexOf(d.id)!=-1).sort((d,l)=>{const s=e.categories.indexOf(d.id),c=e.categories.indexOf(l.id);return s-c}));let u=null,o=null;n=="native"&&(u=ao.latestVersion(),o=e.noCountryFlags||ao.noCountryFlags());let i=z.categories.length,a=!1;for(;i--;){const d=z.categories[i];if(d.id=="frequent"){let{maxFrequentRows:c,perLine:f}=e;c=c>=0?c:xe.maxFrequentRows.value,f||(f=xe.perLine.value),d.emojis=Uu.get({maxFrequentRows:c,perLine:f})}if(!d.emojis||!d.emojis.length){z.categories.splice(i,1);continue}const{categoryIcons:l}=e;if(l){const c=l[d.id];c&&!d.icon&&(d.icon=c)}let s=d.emojis.length;for(;s--;){const c=d.emojis[s],f=c.id?c:z.emojis[c],m=()=>{d.emojis.splice(s,1)};if(!f||e.exceptEmojis&&e.exceptEmojis.includes(f.id)){m();continue}if(u&&f.version>u){m();continue}if(o&&d.id=="flags"&&!Rs.includes(f.id)){m();continue}if(!f.search){if(a=!0,f.search=","+[[f.id,!1],[f.name,!0],[f.keywords,!1],[f.emoticons,!1]].map(([y,g])=>{if(y)return(Array.isArray(y)?y:[y]).map(S=>(g?S.split(/[-|_|\s]+/):[S]).map($=>$.toLowerCase())).flat()}).flat().filter(y=>y&&y.trim()).join(","),f.emoticons)for(const y of f.emoticons)z.emoticons[y]||(z.emoticons[y]=f.id);let p=0;for(const y of f.skins){if(!y)continue;p++;const{native:g}=y;g&&(z.natives[g]=f.id,f.search+=`,${g}`);const S=p==1?"":`:skin-tone-${p}:`;y.shortcodes=`:${f.id}:${S}`}}}}a&&Ge.reset(),Wu()}function Gu(e,t,n){e||(e={});const r={};for(let u in t)r[u]=Ku(u,e,t,n);return r}function Ku(e,t,n,r){const u=n[e];let o=r&&r.getAttribute(e)||(t[e]!=null&&t[e]!=null?t[e]:null);return u&&(o!=null&&u.value&&typeof u.value!=typeof o&&(typeof u.value=="boolean"?o=o!="false":o=u.value.constructor(o)),u.transform&&o&&(o=u.transform(o)),(o==null||u.choices&&u.choices.indexOf(o)==-1)&&(o=u.value)),o}const Cs=/^(?:\:([^\:]+)\:)(?:\:skin-tone-(\d)\:)?$/;let Tn=null;function $s(e){return e.id?e:z.emojis[e]||z.emojis[z.aliases[e]]||z.emojis[z.natives[e]]}function Es(){Tn=null}async function js(e,{maxResults:t,caller:n}={}){if(!e||!e.trim().length)return null;t||(t=90),await en(null,{caller:n||"SearchIndex.search"});const r=e.toLowerCase().replace(/(\w)-/,"$1 ").split(/[\s|,]+/).filter((a,d,l)=>a.trim()&&l.indexOf(a)==d);if(!r.length)return;let u=Tn||(Tn=Object.values(z.emojis)),o,i;for(const a of r){if(!u.length)break;o=[],i={};for(const d of u){if(!d.search)continue;const l=d.search.indexOf(`,${a}`);l!=-1&&(o.push(d),i[d.id]||(i[d.id]=0),i[d.id]+=d.id==a?0:l+1)}u=o}return o.length<2||(o.sort((a,d)=>{const l=i[a.id],s=i[d.id];return l==s?a.id.localeCompare(d.id):l-s}),o.length>t&&(o=o.slice(0,t))),o}var Ge={search:js,get:$s,reset:Es,SHORTCODES_REGEX:Cs};const Rs=["checkered_flag","crossed_flags","pirate_flag","rainbow-flag","transgender_flag","triangular_flag_on_post","waving_black_flag","waving_white_flag"];function Ts(e,t){return Array.isArray(e)&&Array.isArray(t)&&e.length===t.length&&e.every((n,r)=>n==t[r])}async function Ms(e=1){for(let t in[...Array(e).keys()])await new Promise(requestAnimationFrame)}function Ps(e,{skinIndex:t=0}={}){const n=e.skins[t]||(()=>(t=0,e.skins[t]))(),r={id:e.id,name:e.name,native:n.native,unified:n.unified,keywords:e.keywords,shortcodes:n.shortcodes||e.shortcodes};return e.skins.length>1&&(r.skin=t+1),n.src&&(r.src=n.src),e.aliases&&e.aliases.length&&(r.aliases=e.aliases),e.emoticons&&e.emoticons.length&&(r.emoticons=e.emoticons),r}const Ls={activity:{outline:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:h("path",{d:"M12 0C5.373 0 0 5.372 0 12c0 6.627 5.373 12 12 12 6.628 0 12-5.373 12-12 0-6.628-5.372-12-12-12m9.949 11H17.05c.224-2.527 1.232-4.773 1.968-6.113A9.966 9.966 0 0 1 21.949 11M13 11V2.051a9.945 9.945 0 0 1 4.432 1.564c-.858 1.491-2.156 4.22-2.392 7.385H13zm-2 0H8.961c-.238-3.165-1.536-5.894-2.393-7.385A9.95 9.95 0 0 1 11 2.051V11zm0 2v8.949a9.937 9.937 0 0 1-4.432-1.564c.857-1.492 2.155-4.221 2.393-7.385H11zm4.04 0c.236 3.164 1.534 5.893 2.392 7.385A9.92 9.92 0 0 1 13 21.949V13h2.04zM4.982 4.887C5.718 6.227 6.726 8.473 6.951 11h-4.9a9.977 9.977 0 0 1 2.931-6.113M2.051 13h4.9c-.226 2.527-1.233 4.771-1.969 6.113A9.972 9.972 0 0 1 2.051 13m16.967 6.113c-.735-1.342-1.744-3.586-1.968-6.113h4.899a9.961 9.961 0 0 1-2.931 6.113"})}),solid:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:h("path",{d:"M16.17 337.5c0 44.98 7.565 83.54 13.98 107.9C35.22 464.3 50.46 496 174.9 496c9.566 0 19.59-.4707 29.84-1.271L17.33 307.3C16.53 317.6 16.17 327.7 16.17 337.5zM495.8 174.5c0-44.98-7.565-83.53-13.98-107.9c-4.688-17.54-18.34-31.23-36.04-35.95C435.5 27.91 392.9 16 337 16c-9.564 0-19.59 .4707-29.84 1.271l187.5 187.5C495.5 194.4 495.8 184.3 495.8 174.5zM26.77 248.8l236.3 236.3c142-36.1 203.9-150.4 222.2-221.1L248.9 26.87C106.9 62.96 45.07 177.2 26.77 248.8zM256 335.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L164.7 283.3C161.6 280.2 160 276.1 160 271.1c0-8.529 6.865-16 16-16c4.095 0 8.189 1.562 11.31 4.688l64.01 64C254.4 327.8 256 331.9 256 335.1zM304 287.1c0 9.141-7.474 16-16 16c-4.094 0-8.188-1.564-11.31-4.689L212.7 235.3C209.6 232.2 208 228.1 208 223.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01C302.5 279.8 304 283.9 304 287.1zM256 175.1c0-9.141 7.473-16 16-16c4.094 0 8.188 1.562 11.31 4.688l64.01 64.01c3.125 3.125 4.688 7.219 4.688 11.31c0 9.133-7.468 16-16 16c-4.094 0-8.189-1.562-11.31-4.688l-64.01-64.01C257.6 184.2 256 180.1 256 175.1z"})})},custom:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",children:h("path",{d:"M417.1 368c-5.937 10.27-16.69 16-27.75 16c-5.422 0-10.92-1.375-15.97-4.281L256 311.4V448c0 17.67-14.33 32-31.1 32S192 465.7 192 448V311.4l-118.3 68.29C68.67 382.6 63.17 384 57.75 384c-11.06 0-21.81-5.734-27.75-16c-8.828-15.31-3.594-34.88 11.72-43.72L159.1 256L41.72 187.7C26.41 178.9 21.17 159.3 29.1 144C36.63 132.5 49.26 126.7 61.65 128.2C65.78 128.7 69.88 130.1 73.72 132.3L192 200.6V64c0-17.67 14.33-32 32-32S256 46.33 256 64v136.6l118.3-68.29c3.838-2.213 7.939-3.539 12.07-4.051C398.7 126.7 411.4 132.5 417.1 144c8.828 15.31 3.594 34.88-11.72 43.72L288 256l118.3 68.28C421.6 333.1 426.8 352.7 417.1 368z"})}),flags:{outline:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:h("path",{d:"M0 0l6.084 24H8L1.916 0zM21 5h-4l-1-4H4l3 12h3l1 4h13L21 5zM6.563 3h7.875l2 8H8.563l-2-8zm8.832 10l-2.856 1.904L12.063 13h3.332zM19 13l-1.5-6h1.938l2 8H16l3-2z"})}),solid:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:h("path",{d:"M64 496C64 504.8 56.75 512 48 512h-32C7.25 512 0 504.8 0 496V32c0-17.75 14.25-32 32-32s32 14.25 32 32V496zM476.3 0c-6.365 0-13.01 1.35-19.34 4.233c-45.69 20.86-79.56 27.94-107.8 27.94c-59.96 0-94.81-31.86-163.9-31.87C160.9 .3055 131.6 4.867 96 15.75v350.5c32-9.984 59.87-14.1 84.85-14.1c73.63 0 124.9 31.78 198.6 31.78c31.91 0 68.02-5.971 111.1-23.09C504.1 355.9 512 344.4 512 332.1V30.73C512 11.1 495.3 0 476.3 0z"})})},foods:{outline:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:h("path",{d:"M17 4.978c-1.838 0-2.876.396-3.68.934.513-1.172 1.768-2.934 4.68-2.934a1 1 0 0 0 0-2c-2.921 0-4.629 1.365-5.547 2.512-.064.078-.119.162-.18.244C11.73 1.838 10.798.023 9.207.023 8.579.022 7.85.306 7 .978 5.027 2.54 5.329 3.902 6.492 4.999 3.609 5.222 0 7.352 0 12.969c0 4.582 4.961 11.009 9 11.009 1.975 0 2.371-.486 3-1 .629.514 1.025 1 3 1 4.039 0 9-6.418 9-11 0-5.953-4.055-8-7-8M8.242 2.546c.641-.508.943-.523.965-.523.426.169.975 1.405 1.357 3.055-1.527-.629-2.741-1.352-2.98-1.846.059-.112.241-.356.658-.686M15 21.978c-1.08 0-1.21-.109-1.559-.402l-.176-.146c-.367-.302-.816-.452-1.266-.452s-.898.15-1.266.452l-.176.146c-.347.292-.477.402-1.557.402-2.813 0-7-5.389-7-9.009 0-5.823 4.488-5.991 5-5.991 1.939 0 2.484.471 3.387 1.251l.323.276a1.995 1.995 0 0 0 2.58 0l.323-.276c.902-.78 1.447-1.251 3.387-1.251.512 0 5 .168 5 6 0 3.617-4.187 9-7 9"})}),solid:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:h("path",{d:"M481.9 270.1C490.9 279.1 496 291.3 496 304C496 316.7 490.9 328.9 481.9 337.9C472.9 346.9 460.7 352 448 352H64C51.27 352 39.06 346.9 30.06 337.9C21.06 328.9 16 316.7 16 304C16 291.3 21.06 279.1 30.06 270.1C39.06 261.1 51.27 256 64 256H448C460.7 256 472.9 261.1 481.9 270.1zM475.3 388.7C478.3 391.7 480 395.8 480 400V416C480 432.1 473.3 449.3 461.3 461.3C449.3 473.3 432.1 480 416 480H96C79.03 480 62.75 473.3 50.75 461.3C38.74 449.3 32 432.1 32 416V400C32 395.8 33.69 391.7 36.69 388.7C39.69 385.7 43.76 384 48 384H464C468.2 384 472.3 385.7 475.3 388.7zM50.39 220.8C45.93 218.6 42.03 215.5 38.97 211.6C35.91 207.7 33.79 203.2 32.75 198.4C31.71 193.5 31.8 188.5 32.99 183.7C54.98 97.02 146.5 32 256 32C365.5 32 457 97.02 479 183.7C480.2 188.5 480.3 193.5 479.2 198.4C478.2 203.2 476.1 207.7 473 211.6C469.1 215.5 466.1 218.6 461.6 220.8C457.2 222.9 452.3 224 447.3 224H64.67C59.73 224 54.84 222.9 50.39 220.8zM372.7 116.7C369.7 119.7 368 123.8 368 128C368 131.2 368.9 134.3 370.7 136.9C372.5 139.5 374.1 141.6 377.9 142.8C380.8 143.1 384 144.3 387.1 143.7C390.2 143.1 393.1 141.6 395.3 139.3C397.6 137.1 399.1 134.2 399.7 131.1C400.3 128 399.1 124.8 398.8 121.9C397.6 118.1 395.5 116.5 392.9 114.7C390.3 112.9 387.2 111.1 384 111.1C379.8 111.1 375.7 113.7 372.7 116.7V116.7zM244.7 84.69C241.7 87.69 240 91.76 240 96C240 99.16 240.9 102.3 242.7 104.9C244.5 107.5 246.1 109.6 249.9 110.8C252.8 111.1 256 112.3 259.1 111.7C262.2 111.1 265.1 109.6 267.3 107.3C269.6 105.1 271.1 102.2 271.7 99.12C272.3 96.02 271.1 92.8 270.8 89.88C269.6 86.95 267.5 84.45 264.9 82.7C262.3 80.94 259.2 79.1 256 79.1C251.8 79.1 247.7 81.69 244.7 84.69V84.69zM116.7 116.7C113.7 119.7 112 123.8 112 128C112 131.2 112.9 134.3 114.7 136.9C116.5 139.5 118.1 141.6 121.9 142.8C124.8 143.1 128 144.3 131.1 143.7C134.2 143.1 137.1 141.6 139.3 139.3C141.6 137.1 143.1 134.2 143.7 131.1C144.3 128 143.1 124.8 142.8 121.9C141.6 118.1 139.5 116.5 136.9 114.7C134.3 112.9 131.2 111.1 128 111.1C123.8 111.1 119.7 113.7 116.7 116.7L116.7 116.7z"})})},frequent:{outline:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[h("path",{d:"M13 4h-2l-.001 7H9v2h2v2h2v-2h4v-2h-4z"}),h("path",{d:"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"})]}),solid:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:h("path",{d:"M256 512C114.6 512 0 397.4 0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512zM232 256C232 264 236 271.5 242.7 275.1L338.7 339.1C349.7 347.3 364.6 344.3 371.1 333.3C379.3 322.3 376.3 307.4 365.3 300L280 243.2V120C280 106.7 269.3 96 255.1 96C242.7 96 231.1 106.7 231.1 120L232 256z"})})},nature:{outline:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[h("path",{d:"M15.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 15.5 8M8.5 8a1.5 1.5 0 1 0 .001 3.001A1.5 1.5 0 0 0 8.5 8"}),h("path",{d:"M18.933 0h-.027c-.97 0-2.138.787-3.018 1.497-1.274-.374-2.612-.51-3.887-.51-1.285 0-2.616.133-3.874.517C7.245.79 6.069 0 5.093 0h-.027C3.352 0 .07 2.67.002 7.026c-.039 2.479.276 4.238 1.04 5.013.254.258.882.677 1.295.882.191 3.177.922 5.238 2.536 6.38.897.637 2.187.949 3.2 1.102C8.04 20.6 8 20.795 8 21c0 1.773 2.35 3 4 3 1.648 0 4-1.227 4-3 0-.201-.038-.393-.072-.586 2.573-.385 5.435-1.877 5.925-7.587.396-.22.887-.568 1.104-.788.763-.774 1.079-2.534 1.04-5.013C23.929 2.67 20.646 0 18.933 0M3.223 9.135c-.237.281-.837 1.155-.884 1.238-.15-.41-.368-1.349-.337-3.291.051-3.281 2.478-4.972 3.091-5.031.256.015.731.27 1.265.646-1.11 1.171-2.275 2.915-2.352 5.125-.133.546-.398.858-.783 1.313M12 22c-.901 0-1.954-.693-2-1 0-.654.475-1.236 1-1.602V20a1 1 0 1 0 2 0v-.602c.524.365 1 .947 1 1.602-.046.307-1.099 1-2 1m3-3.48v.02a4.752 4.752 0 0 0-1.262-1.02c1.092-.516 2.239-1.334 2.239-2.217 0-1.842-1.781-2.195-3.977-2.195-2.196 0-3.978.354-3.978 2.195 0 .883 1.148 1.701 2.238 2.217A4.8 4.8 0 0 0 9 18.539v-.025c-1-.076-2.182-.281-2.973-.842-1.301-.92-1.838-3.045-1.853-6.478l.023-.041c.496-.826 1.49-1.45 1.804-3.102 0-2.047 1.357-3.631 2.362-4.522C9.37 3.178 10.555 3 11.948 3c1.447 0 2.685.192 3.733.57 1 .9 2.316 2.465 2.316 4.48.313 1.651 1.307 2.275 1.803 3.102.035.058.068.117.102.178-.059 5.967-1.949 7.01-4.902 7.19m6.628-8.202c-.037-.065-.074-.13-.113-.195a7.587 7.587 0 0 0-.739-.987c-.385-.455-.648-.768-.782-1.313-.076-2.209-1.241-3.954-2.353-5.124.531-.376 1.004-.63 1.261-.647.636.071 3.044 1.764 3.096 5.031.027 1.81-.347 3.218-.37 3.235"})]}),solid:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 576 512",children:h("path",{d:"M332.7 19.85C334.6 8.395 344.5 0 356.1 0C363.6 0 370.6 3.52 375.1 9.502L392 32H444.1C456.8 32 469.1 37.06 478.1 46.06L496 64H552C565.3 64 576 74.75 576 88V112C576 156.2 540.2 192 496 192H426.7L421.6 222.5L309.6 158.5L332.7 19.85zM448 64C439.2 64 432 71.16 432 80C432 88.84 439.2 96 448 96C456.8 96 464 88.84 464 80C464 71.16 456.8 64 448 64zM416 256.1V480C416 497.7 401.7 512 384 512H352C334.3 512 320 497.7 320 480V364.8C295.1 377.1 268.8 384 240 384C211.2 384 184 377.1 160 364.8V480C160 497.7 145.7 512 128 512H96C78.33 512 64 497.7 64 480V249.8C35.23 238.9 12.64 214.5 4.836 183.3L.9558 167.8C-3.331 150.6 7.094 133.2 24.24 128.1C41.38 124.7 58.76 135.1 63.05 152.2L66.93 167.8C70.49 182 83.29 191.1 97.97 191.1H303.8L416 256.1z"})})},objects:{outline:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[h("path",{d:"M12 0a9 9 0 0 0-5 16.482V21s2.035 3 5 3 5-3 5-3v-4.518A9 9 0 0 0 12 0zm0 2c3.86 0 7 3.141 7 7s-3.14 7-7 7-7-3.141-7-7 3.14-7 7-7zM9 17.477c.94.332 1.946.523 3 .523s2.06-.19 3-.523v.834c-.91.436-1.925.689-3 .689a6.924 6.924 0 0 1-3-.69v-.833zm.236 3.07A8.854 8.854 0 0 0 12 21c.965 0 1.888-.167 2.758-.451C14.155 21.173 13.153 22 12 22c-1.102 0-2.117-.789-2.764-1.453z"}),h("path",{d:"M14.745 12.449h-.004c-.852-.024-1.188-.858-1.577-1.824-.421-1.061-.703-1.561-1.182-1.566h-.009c-.481 0-.783.497-1.235 1.537-.436.982-.801 1.811-1.636 1.791l-.276-.043c-.565-.171-.853-.691-1.284-1.794-.125-.313-.202-.632-.27-.913-.051-.213-.127-.53-.195-.634C7.067 9.004 7.039 9 6.99 9A1 1 0 0 1 7 7h.01c1.662.017 2.015 1.373 2.198 2.134.486-.981 1.304-2.058 2.797-2.075 1.531.018 2.28 1.153 2.731 2.141l.002-.008C14.944 8.424 15.327 7 16.979 7h.032A1 1 0 1 1 17 9h-.011c-.149.076-.256.474-.319.709a6.484 6.484 0 0 1-.311.951c-.429.973-.79 1.789-1.614 1.789"})]}),solid:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512",children:h("path",{d:"M112.1 454.3c0 6.297 1.816 12.44 5.284 17.69l17.14 25.69c5.25 7.875 17.17 14.28 26.64 14.28h61.67c9.438 0 21.36-6.401 26.61-14.28l17.08-25.68c2.938-4.438 5.348-12.37 5.348-17.7L272 415.1h-160L112.1 454.3zM191.4 .0132C89.44 .3257 16 82.97 16 175.1c0 44.38 16.44 84.84 43.56 115.8c16.53 18.84 42.34 58.23 52.22 91.45c.0313 .25 .0938 .5166 .125 .7823h160.2c.0313-.2656 .0938-.5166 .125-.7823c9.875-33.22 35.69-72.61 52.22-91.45C351.6 260.8 368 220.4 368 175.1C368 78.61 288.9-.2837 191.4 .0132zM192 96.01c-44.13 0-80 35.89-80 79.1C112 184.8 104.8 192 96 192S80 184.8 80 176c0-61.76 50.25-111.1 112-111.1c8.844 0 16 7.159 16 16S200.8 96.01 192 96.01z"})})},people:{outline:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[h("path",{d:"M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0m0 22C6.486 22 2 17.514 2 12S6.486 2 12 2s10 4.486 10 10-4.486 10-10 10"}),h("path",{d:"M8 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 8 7M16 7a2 2 0 1 0-.001 3.999A2 2 0 0 0 16 7M15.232 15c-.693 1.195-1.87 2-3.349 2-1.477 0-2.655-.805-3.347-2H15m3-2H6a6 6 0 1 0 12 0"})]}),solid:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:h("path",{d:"M0 256C0 114.6 114.6 0 256 0C397.4 0 512 114.6 512 256C512 397.4 397.4 512 256 512C114.6 512 0 397.4 0 256zM256 432C332.1 432 396.2 382 415.2 314.1C419.1 300.4 407.8 288 393.6 288H118.4C104.2 288 92.92 300.4 96.76 314.1C115.8 382 179.9 432 256 432V432zM176.4 160C158.7 160 144.4 174.3 144.4 192C144.4 209.7 158.7 224 176.4 224C194 224 208.4 209.7 208.4 192C208.4 174.3 194 160 176.4 160zM336.4 224C354 224 368.4 209.7 368.4 192C368.4 174.3 354 160 336.4 160C318.7 160 304.4 174.3 304.4 192C304.4 209.7 318.7 224 336.4 224z"})})},places:{outline:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[h("path",{d:"M6.5 12C5.122 12 4 13.121 4 14.5S5.122 17 6.5 17 9 15.879 9 14.5 7.878 12 6.5 12m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5M17.5 12c-1.378 0-2.5 1.121-2.5 2.5s1.122 2.5 2.5 2.5 2.5-1.121 2.5-2.5-1.122-2.5-2.5-2.5m0 3c-.275 0-.5-.225-.5-.5s.225-.5.5-.5.5.225.5.5-.225.5-.5.5"}),h("path",{d:"M22.482 9.494l-1.039-.346L21.4 9h.6c.552 0 1-.439 1-.992 0-.006-.003-.008-.003-.008H23c0-1-.889-2-1.984-2h-.642l-.731-1.717C19.262 3.012 18.091 2 16.764 2H7.236C5.909 2 4.738 3.012 4.357 4.283L3.626 6h-.642C1.889 6 1 7 1 8h.003S1 8.002 1 8.008C1 8.561 1.448 9 2 9h.6l-.043.148-1.039.346a2.001 2.001 0 0 0-1.359 2.097l.751 7.508a1 1 0 0 0 .994.901H3v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h6v1c0 1.103.896 2 2 2h2c1.104 0 2-.897 2-2v-1h1.096a.999.999 0 0 0 .994-.901l.751-7.508a2.001 2.001 0 0 0-1.359-2.097M6.273 4.857C6.402 4.43 6.788 4 7.236 4h9.527c.448 0 .834.43.963.857L19.313 9H4.688l1.585-4.143zM7 21H5v-1h2v1zm12 0h-2v-1h2v1zm2.189-3H2.811l-.662-6.607L3 11h18l.852.393L21.189 18z"})]}),solid:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:h("path",{d:"M39.61 196.8L74.8 96.29C88.27 57.78 124.6 32 165.4 32H346.6C387.4 32 423.7 57.78 437.2 96.29L472.4 196.8C495.6 206.4 512 229.3 512 256V448C512 465.7 497.7 480 480 480H448C430.3 480 416 465.7 416 448V400H96V448C96 465.7 81.67 480 64 480H32C14.33 480 0 465.7 0 448V256C0 229.3 16.36 206.4 39.61 196.8V196.8zM109.1 192H402.9L376.8 117.4C372.3 104.6 360.2 96 346.6 96H165.4C151.8 96 139.7 104.6 135.2 117.4L109.1 192zM96 256C78.33 256 64 270.3 64 288C64 305.7 78.33 320 96 320C113.7 320 128 305.7 128 288C128 270.3 113.7 256 96 256zM416 320C433.7 320 448 305.7 448 288C448 270.3 433.7 256 416 256C398.3 256 384 270.3 384 288C384 305.7 398.3 320 416 320z"})})},symbols:{outline:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:h("path",{d:"M0 0h11v2H0zM4 11h3V6h4V4H0v2h4zM15.5 17c1.381 0 2.5-1.116 2.5-2.493s-1.119-2.493-2.5-2.493S13 13.13 13 14.507 14.119 17 15.5 17m0-2.986c.276 0 .5.222.5.493 0 .272-.224.493-.5.493s-.5-.221-.5-.493.224-.493.5-.493M21.5 19.014c-1.381 0-2.5 1.116-2.5 2.493S20.119 24 21.5 24s2.5-1.116 2.5-2.493-1.119-2.493-2.5-2.493m0 2.986a.497.497 0 0 1-.5-.493c0-.271.224-.493.5-.493s.5.222.5.493a.497.497 0 0 1-.5.493M22 13l-9 9 1.513 1.5 8.99-9.009zM17 11c2.209 0 4-1.119 4-2.5V2s.985-.161 1.498.949C23.01 4.055 23 6 23 6s1-1.119 1-3.135C24-.02 21 0 21 0h-2v6.347A5.853 5.853 0 0 0 17 6c-2.209 0-4 1.119-4 2.5s1.791 2.5 4 2.5M10.297 20.482l-1.475-1.585a47.54 47.54 0 0 1-1.442 1.129c-.307-.288-.989-1.016-2.045-2.183.902-.836 1.479-1.466 1.729-1.892s.376-.871.376-1.336c0-.592-.273-1.178-.818-1.759-.546-.581-1.329-.871-2.349-.871-1.008 0-1.79.293-2.344.879-.556.587-.832 1.181-.832 1.784 0 .813.419 1.748 1.256 2.805-.847.614-1.444 1.208-1.794 1.784a3.465 3.465 0 0 0-.523 1.833c0 .857.308 1.56.924 2.107.616.549 1.423.823 2.42.823 1.173 0 2.444-.379 3.813-1.137L8.235 24h2.819l-2.09-2.383 1.333-1.135zm-6.736-6.389a1.02 1.02 0 0 1 .73-.286c.31 0 .559.085.747.254a.849.849 0 0 1 .283.659c0 .518-.419 1.112-1.257 1.784-.536-.651-.805-1.231-.805-1.742a.901.901 0 0 1 .302-.669M3.74 22c-.427 0-.778-.116-1.057-.349-.279-.232-.418-.487-.418-.766 0-.594.509-1.288 1.527-2.083.968 1.134 1.717 1.946 2.248 2.438-.921.507-1.686.76-2.3.76"})}),solid:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512",children:h("path",{d:"M500.3 7.251C507.7 13.33 512 22.41 512 31.1V175.1C512 202.5 483.3 223.1 447.1 223.1C412.7 223.1 383.1 202.5 383.1 175.1C383.1 149.5 412.7 127.1 447.1 127.1V71.03L351.1 90.23V207.1C351.1 234.5 323.3 255.1 287.1 255.1C252.7 255.1 223.1 234.5 223.1 207.1C223.1 181.5 252.7 159.1 287.1 159.1V63.1C287.1 48.74 298.8 35.61 313.7 32.62L473.7 .6198C483.1-1.261 492.9 1.173 500.3 7.251H500.3zM74.66 303.1L86.5 286.2C92.43 277.3 102.4 271.1 113.1 271.1H174.9C185.6 271.1 195.6 277.3 201.5 286.2L213.3 303.1H239.1C266.5 303.1 287.1 325.5 287.1 351.1V463.1C287.1 490.5 266.5 511.1 239.1 511.1H47.1C21.49 511.1-.0019 490.5-.0019 463.1V351.1C-.0019 325.5 21.49 303.1 47.1 303.1H74.66zM143.1 359.1C117.5 359.1 95.1 381.5 95.1 407.1C95.1 434.5 117.5 455.1 143.1 455.1C170.5 455.1 191.1 434.5 191.1 407.1C191.1 381.5 170.5 359.1 143.1 359.1zM440.3 367.1H496C502.7 367.1 508.6 372.1 510.1 378.4C513.3 384.6 511.6 391.7 506.5 396L378.5 508C372.9 512.1 364.6 513.3 358.6 508.9C352.6 504.6 350.3 496.6 353.3 489.7L391.7 399.1H336C329.3 399.1 323.4 395.9 321 389.6C318.7 383.4 320.4 376.3 325.5 371.1L453.5 259.1C459.1 255 467.4 254.7 473.4 259.1C479.4 263.4 481.6 271.4 478.7 278.3L440.3 367.1zM116.7 219.1L19.85 119.2C-8.112 90.26-6.614 42.31 24.85 15.34C51.82-8.137 93.26-3.642 118.2 21.83L128.2 32.32L137.7 21.83C162.7-3.642 203.6-8.137 231.6 15.34C262.6 42.31 264.1 90.26 236.1 119.2L139.7 219.1C133.2 225.6 122.7 225.6 116.7 219.1H116.7z"})})}},Fs={loupe:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",children:h("path",{d:"M12.9 14.32a8 8 0 1 1 1.41-1.41l5.35 5.33-1.42 1.42-5.33-5.34zM8 14A6 6 0 1 0 8 2a6 6 0 0 0 0 12z"})}),delete:h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",children:h("path",{d:"M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z"})})};var Wt={categories:Ls,search:Fs};function Mn(e){let{id:t,skin:n,emoji:r}=e;if(e.shortcodes){const a=e.shortcodes.match(Ge.SHORTCODES_REGEX);a&&(t=a[1],a[2]&&(n=a[2]))}if(r||(r=Ge.get(t||e.native)),!r)return e.fallback;const u=r.skins[n-1]||r.skins[0],o=u.src||(e.set!="native"&&!e.spritesheet?typeof e.getImageURL=="function"?e.getImageURL(e.set,u.unified):`https://cdn.jsdelivr.net/npm/emoji-datasource-${e.set}@15.0.1/img/${e.set}/64/${u.unified}.png`:void 0),i=typeof e.getSpritesheetURL=="function"?e.getSpritesheetURL(e.set):`https://cdn.jsdelivr.net/npm/emoji-datasource-${e.set}@15.0.1/img/${e.set}/sheets-256/64.png`;return h("span",{class:"emoji-mart-emoji","data-emoji-set":e.set,children:o?h("img",{style:{maxWidth:e.size||"1em",maxHeight:e.size||"1em",display:"inline-block"},alt:u.native||u.shortcodes,src:o}):e.set=="native"?h("span",{style:{fontSize:e.size,fontFamily:'"EmojiMart", "Segoe UI Emoji", "Segoe UI Symbol", "Segoe UI", "Apple Color Emoji", "Twemoji Mozilla", "Noto Color Emoji", "Android Emoji"'},children:u.native}):h("span",{style:{display:"block",width:e.size,height:e.size,backgroundImage:`url(${i})`,backgroundSize:`${100*z.sheet.cols}% ${100*z.sheet.rows}%`,backgroundPosition:`${100/(z.sheet.cols-1)*u.x}% ${100/(z.sheet.rows-1)*u.y}%`}})})}const zs=typeof window<"u"&&window.HTMLElement?window.HTMLElement:Object;class Xu extends zs{static get observedAttributes(){return Object.keys(this.Props)}update(t={}){for(let n in t)this.attributeChangedCallback(n,null,t[n])}attributeChangedCallback(t,n,r){if(!this.component)return;const u=Ku(t,{[t]:r},this.constructor.Props,this);this.component.componentWillReceiveProps?this.component.componentWillReceiveProps({[t]:u}):(this.component.props[t]=u,this.component.forceUpdate())}disconnectedCallback(){this.disconnected=!0,this.component&&this.component.unregister&&this.component.unregister()}constructor(t={}){if(super(),this.props=t,t.parent||t.ref){let n=null;const r=t.parent||(n=t.ref&&t.ref.current);n&&(n.innerHTML=""),r&&r.appendChild(this)}}}class Is extends Xu{setShadow(){this.attachShadow({mode:"open"})}injectStyles(t){if(!t)return;const n=document.createElement("style");n.textContent=t,this.shadowRoot.insertBefore(n,this.shadowRoot.firstChild)}constructor(t,{styles:n}={}){super(t),this.setShadow(),this.injectStyles(n)}}var Zu={fallback:"",id:"",native:"",shortcodes:"",size:{value:"",transform:e=>/\D/.test(e)?e:`${e}px`},set:xe.set,skin:xe.skin};class Ju extends Xu{async connectedCallback(){const t=Gu(this.props,Zu,this);t.element=this,t.ref=n=>{this.component=n},await en(),!this.disconnected&&Hu(h(Mn,{...t}),this)}constructor(t){super(t)}}fe(Ju,"Props",Zu);typeof customElements<"u"&&!customElements.get("em-emoji")&&customElements.define("em-emoji",Ju);var lo,Pn=[],co=E.__b,fo=E.__r,po=E.diffed,ho=E.__c,mo=E.unmount;function As(){var e;for(Pn.sort(function(t,n){return t.__v.__b-n.__v.__b});e=Pn.pop();)if(e.__P)try{e.__H.__h.forEach(Rt),e.__H.__h.forEach(Ln),e.__H.__h=[]}catch(t){e.__H.__h=[],E.__e(t,e.__v)}}E.__b=function(e){co&&co(e)},E.__r=function(e){fo&&fo(e);var t=e.__c.__H;t&&(t.__h.forEach(Rt),t.__h.forEach(Ln),t.__h=[])},E.diffed=function(e){po&&po(e);var t=e.__c;t&&t.__H&&t.__H.__h.length&&(Pn.push(t)!==1&&lo===E.requestAnimationFrame||((lo=E.requestAnimationFrame)||function(n){var r,u=function(){clearTimeout(o),vo&&cancelAnimationFrame(r),setTimeout(n)},o=setTimeout(u,100);vo&&(r=requestAnimationFrame(u))})(As))},E.__c=function(e,t){t.some(function(n){try{n.__h.forEach(Rt),n.__h=n.__h.filter(function(r){return!r.__||Ln(r)})}catch(r){t.some(function(u){u.__h&&(u.__h=[])}),t=[],E.__e(r,n.__v)}}),ho&&ho(e,t)},E.unmount=function(e){mo&&mo(e);var t,n=e.__c;n&&n.__H&&(n.__H.__.forEach(function(r){try{Rt(r)}catch(u){t=u}}),t&&E.__e(t,n.__v))};var vo=typeof requestAnimationFrame=="function";function Rt(e){var t=e.__c;typeof t=="function"&&(e.__c=void 0,t())}function Ln(e){e.__c=e.__()}function Ds(e,t){for(var n in t)e[n]=t[n];return e}function go(e,t){for(var n in e)if(n!=="__source"&&!(n in t))return!0;for(var r in t)if(r!=="__source"&&e[r]!==t[r])return!0;return!1}function qt(e){this.props=e}(qt.prototype=new we).isPureReactComponent=!0,qt.prototype.shouldComponentUpdate=function(e,t){return go(this.props,e)||go(this.state,t)};var bo=E.__b;E.__b=function(e){e.type&&e.type.__f&&e.ref&&(e.props.ref=e.ref,e.ref=null),bo&&bo(e)};var Bs=E.__e;E.__e=function(e,t,n){if(e.then){for(var r,u=t;u=u.__;)if((r=u.__c)&&r.__c)return t.__e==null&&(t.__e=n.__e,t.__k=n.__k),r.__c(e,t)}Bs(e,t,n)};var yo=E.unmount;function mn(){this.__u=0,this.t=null,this.__b=null}function Yu(e){var t=e.__.__c;return t&&t.__e&&t.__e(e)}function mt(){this.u=null,this.o=null}E.unmount=function(e){var t=e.__c;t&&t.__R&&t.__R(),t&&e.__h===!0&&(e.type=null),yo&&yo(e)},(mn.prototype=new we).__c=function(e,t){var n=t.__c,r=this;r.t==null&&(r.t=[]),r.t.push(n);var u=Yu(r.__v),o=!1,i=function(){o||(o=!0,n.__R=null,u?u(a):a())};n.__R=i;var a=function(){if(!--r.__u){if(r.state.__e){var l=r.state.__e;r.__v.__k[0]=function c(f,m,p){return f&&(f.__v=null,f.__k=f.__k&&f.__k.map(function(y){return c(y,m,p)}),f.__c&&f.__c.__P===m&&(f.__e&&p.insertBefore(f.__e,f.__d),f.__c.__e=!0,f.__c.__P=p)),f}(l,l.__c.__P,l.__c.__O)}var s;for(r.setState({__e:r.__b=null});s=r.t.pop();)s.forceUpdate()}},d=t.__h===!0;r.__u++||d||r.setState({__e:r.__b=r.__v.__k[0]}),e.then(i,i)},mn.prototype.componentWillUnmount=function(){this.t=[]},mn.prototype.render=function(e,t){if(this.__b){if(this.__v.__k){var n=document.createElement("div"),r=this.__v.__k[0].__c;this.__v.__k[0]=function o(i,a,d){return i&&(i.__c&&i.__c.__H&&(i.__c.__H.__.forEach(function(l){typeof l.__c=="function"&&l.__c()}),i.__c.__H=null),(i=Ds({},i)).__c!=null&&(i.__c.__P===d&&(i.__c.__P=a),i.__c=null),i.__k=i.__k&&i.__k.map(function(l){return o(l,a,d)})),i}(this.__b,n,r.__O=r.__P)}this.__b=null}var u=t.__e&&jn(Ke,null,e.fallback);return u&&(u.__h=null),[jn(Ke,null,t.__e?null:e.children),u]};var wo=function(e,t,n){if(++n[1]===n[0]&&e.o.delete(t),e.props.revealOrder&&(e.props.revealOrder[0]!=="t"||!e.o.size))for(n=e.u;n;){for(;n.length>3;)n.pop()();if(n[1]<n[0])break;e.u=n=n[2]}};(mt.prototype=new we).__e=function(e){var t=this,n=Yu(t.__v),r=t.o.get(e);return r[0]++,function(u){var o=function(){t.props.revealOrder?(r.push(u),wo(t,e,r)):u()};n?n(o):o()}},mt.prototype.render=function(e){this.u=null,this.o=new Map;var t=Ut(e.children);e.revealOrder&&e.revealOrder[0]==="b"&&t.reverse();for(var n=t.length;n--;)this.o.set(t[n],this.u=[1,0,this.u]);return e.children},mt.prototype.componentDidUpdate=mt.prototype.componentDidMount=function(){var e=this;this.o.forEach(function(t,n){wo(e,n,t)})};var Os=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,Hs=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|marker(?!H|W|U)|overline|paint|stop|strikethrough|stroke|text(?!L)|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,Ns=typeof document<"u",Us=function(e){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/i:/fil|che|ra/i).test(e)};we.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(e){Object.defineProperty(we.prototype,e,{configurable:!0,get:function(){return this["UNSAFE_"+e]},set:function(t){Object.defineProperty(this,e,{configurable:!0,writable:!0,value:t})}})});var _o=E.event;function Vs(){}function Ws(){return this.cancelBubble}function qs(){return this.defaultPrevented}E.event=function(e){return _o&&(e=_o(e)),e.persist=Vs,e.isPropagationStopped=Ws,e.isDefaultPrevented=qs,e.nativeEvent=e};var ko={configurable:!0,get:function(){return this.class}},xo=E.vnode;E.vnode=function(e){var t=e.type,n=e.props,r=n;if(typeof t=="string"){var u=t.indexOf("-")===-1;for(var o in r={},n){var i=n[o];Ns&&o==="children"&&t==="noscript"||o==="value"&&"defaultValue"in n&&i==null||(o==="defaultValue"&&"value"in n&&n.value==null?o="value":o==="download"&&i===!0?i="":/ondoubleclick/i.test(o)?o="ondblclick":/^onchange(textarea|input)/i.test(o+t)&&!Us(n.type)?o="oninput":/^onfocus$/i.test(o)?o="onfocusin":/^onblur$/i.test(o)?o="onfocusout":/^on(Ani|Tra|Tou|BeforeInp)/.test(o)?o=o.toLowerCase():u&&Hs.test(o)?o=o.replace(/[A-Z0-9]/,"-$&").toLowerCase():i===null&&(i=void 0),r[o]=i)}t=="select"&&r.multiple&&Array.isArray(r.value)&&(r.value=Ut(n.children).forEach(function(a){a.props.selected=r.value.indexOf(a.props.value)!=-1})),t=="select"&&r.defaultValue!=null&&(r.value=Ut(n.children).forEach(function(a){a.props.selected=r.multiple?r.defaultValue.indexOf(a.props.value)!=-1:r.defaultValue==a.props.value})),e.props=r,n.class!=n.className&&(ko.enumerable="className"in n,n.className!=null&&(r.class=n.className),Object.defineProperty(r,"className",ko))}e.$$typeof=Os,xo&&xo(e)};var So=E.__r;E.__r=function(e){So&&So(e),e.__c};const Gs={light:"outline",dark:"solid"};class Ks extends qt{renderIcon(t){const{icon:n}=t;if(n){if(n.svg)return h("span",{class:"flex",dangerouslySetInnerHTML:{__html:n.svg}});if(n.src)return h("img",{src:n.src})}const r=Wt.categories[t.id]||Wt.categories.custom,u=this.props.icons=="auto"?Gs[this.props.theme]:this.props.icons;return r[u]||r}render(){let t=null;return h("nav",{id:"nav",class:"padding","data-position":this.props.position,dir:this.props.dir,children:h("div",{class:"flex relative",children:[this.categories.map((n,r)=>{const u=n.name||J.categories[n.id],o=!this.props.unfocused&&n.id==this.state.categoryId;return o&&(t=r),h("button",{"aria-label":u,"aria-selected":o||void 0,title:u,type:"button",class:"flex flex-grow flex-center",onMouseDown:i=>i.preventDefault(),onClick:()=>{this.props.onClick({category:n,i:r})},children:this.renderIcon(n)})}),h("div",{class:"bar",style:{width:`${100/this.categories.length}%`,opacity:t==null?0:1,transform:this.props.dir==="rtl"?`scaleX(-1) translateX(${t*100}%)`:`translateX(${t*100}%)`}})]})})}constructor(){super(),this.categories=z.categories.filter(t=>!t.target),this.state={categoryId:this.categories[0].id}}}class Xs extends qt{shouldComponentUpdate(t){for(let n in t)if(n!="children"&&t[n]!=this.props[n])return!0;return!1}render(){return this.props.children}}const vt={rowsPerRender:10};class Zs extends we{getInitialState(t=this.props){return{skin:Fe.get("skin")||t.skin,theme:this.initTheme(t.theme)}}componentWillMount(){this.dir=J.rtl?"rtl":"ltr",this.refs={menu:ke(),navigation:ke(),scroll:ke(),search:ke(),searchInput:ke(),skinToneButton:ke(),skinToneRadio:ke()},this.initGrid(),this.props.stickySearch==!1&&this.props.searchPosition=="sticky"&&(console.warn("[EmojiMart] Deprecation warning: `stickySearch` has been renamed `searchPosition`."),this.props.searchPosition="static")}componentDidMount(){if(this.register(),this.shadowRoot=this.base.parentNode,this.props.autoFocus){const{searchInput:t}=this.refs;t.current&&t.current.focus()}}componentWillReceiveProps(t){this.nextState||(this.nextState={});for(const n in t)this.nextState[n]=t[n];clearTimeout(this.nextStateTimer),this.nextStateTimer=setTimeout(()=>{let n=!1;for(const u in this.nextState)this.props[u]=this.nextState[u],(u==="custom"||u==="categories")&&(n=!0);delete this.nextState;const r=this.getInitialState();if(n)return this.reset(r);this.setState(r)})}componentWillUnmount(){this.unregister()}async reset(t={}){await en(this.props),this.initGrid(),this.unobserve(),this.setState(t,()=>{this.observeCategories(),this.observeRows()})}register(){document.addEventListener("click",this.handleClickOutside),this.observe()}unregister(){var t;document.removeEventListener("click",this.handleClickOutside),(t=this.darkMedia)==null||t.removeEventListener("change",this.darkMediaCallback),this.unobserve()}observe(){this.observeCategories(),this.observeRows()}unobserve({except:t=[]}={}){Array.isArray(t)||(t=[t]);for(const n of this.observers)t.includes(n)||n.disconnect();this.observers=[].concat(t)}initGrid(){const{categories:t}=z;this.refs.categories=new Map;const n=z.categories.map(u=>u.id).join(",");this.navKey&&this.navKey!=n&&this.refs.scroll.current&&(this.refs.scroll.current.scrollTop=0),this.navKey=n,this.grid=[],this.grid.setsize=0;const r=(u,o)=>{const i=[];i.__categoryId=o.id,i.__index=u.length,this.grid.push(i);const a=this.grid.length-1,d=a%vt.rowsPerRender?{}:ke();return d.index=a,d.posinset=this.grid.setsize+1,u.push(d),i};for(let u of t){const o=[];let i=r(o,u);for(let a of u.emojis)i.length==this.getPerLine()&&(i=r(o,u)),this.grid.setsize+=1,i.push(a);this.refs.categories.set(u.id,{root:ke(),rows:o})}}initTheme(t){if(t!="auto")return t;if(!this.darkMedia){if(this.darkMedia=matchMedia("(prefers-color-scheme: dark)"),this.darkMedia.media.match(/^not/))return"light";this.darkMedia.addEventListener("change",this.darkMediaCallback)}return this.darkMedia.matches?"dark":"light"}initDynamicPerLine(t=this.props){if(!t.dynamicWidth)return;const{element:n,emojiButtonSize:r}=t,u=()=>{const{width:i}=n.getBoundingClientRect();return Math.floor(i/r)},o=new ResizeObserver(()=>{this.unobserve({except:o}),this.setState({perLine:u()},()=>{this.initGrid(),this.forceUpdate(()=>{this.observeCategories(),this.observeRows()})})});return o.observe(n),this.observers.push(o),u()}getPerLine(){return this.state.perLine||this.props.perLine}getEmojiByPos([t,n]){const r=this.state.searchResults||this.grid,u=r[t]&&r[t][n];if(u)return Ge.get(u)}observeCategories(){const t=this.refs.navigation.current;if(!t)return;const n=new Map,r=i=>{i!=t.state.categoryId&&t.setState({categoryId:i})},u={root:this.refs.scroll.current,threshold:[0,1]},o=new IntersectionObserver(i=>{for(const d of i){const l=d.target.dataset.id;n.set(l,d.intersectionRatio)}const a=[...n];for(const[d,l]of a)if(l){r(d);break}},u);for(const{root:i}of this.refs.categories.values())o.observe(i.current);this.observers.push(o)}observeRows(){const t={...this.state.visibleRows},n=new IntersectionObserver(r=>{for(const u of r){const o=parseInt(u.target.dataset.index);u.isIntersecting?t[o]=!0:delete t[o]}this.setState({visibleRows:t})},{root:this.refs.scroll.current,rootMargin:`${this.props.emojiButtonSize*(vt.rowsPerRender+5)}px 0px ${this.props.emojiButtonSize*vt.rowsPerRender}px`});for(const{rows:r}of this.refs.categories.values())for(const u of r)u.current&&n.observe(u.current);this.observers.push(n)}preventDefault(t){t.preventDefault()}unfocusSearch(){const t=this.refs.searchInput.current;t&&t.blur()}navigate({e:t,input:n,left:r,right:u,up:o,down:i}){const a=this.state.searchResults||this.grid;if(!a.length)return;let[d,l]=this.state.pos;const s=(()=>{if(d==0&&l==0&&!t.repeat&&(r||o))return null;if(d==-1)return!t.repeat&&(u||i)&&n.selectionStart==n.value.length?[0,0]:null;if(r||u){let c=a[d];const f=r?-1:1;if(l+=f,!c[l]){if(d+=f,c=a[d],!c)return d=r?0:a.length-1,l=r?0:a[d].length-1,[d,l];l=r?c.length-1:0}return[d,l]}if(o||i){d+=o?-1:1;const c=a[d];return c?(c[l]||(l=c.length-1),[d,l]):(d=o?0:a.length-1,l=o?0:a[d].length-1,[d,l])}})();if(s)t.preventDefault();else{this.state.pos[0]>-1&&this.setState({pos:[-1,-1]});return}this.setState({pos:s,keyboard:!0},()=>{this.scrollTo({row:s[0]})})}scrollTo({categoryId:t,row:n}){const r=this.state.searchResults||this.grid;if(!r.length)return;const u=this.refs.scroll.current,o=u.getBoundingClientRect();let i=0;if(n>=0&&(t=r[n].__categoryId),t&&(i=(this.refs[t]||this.refs.categories.get(t).root).current.getBoundingClientRect().top-(o.top-u.scrollTop)+1),n>=0)if(!n)i=0;else{const a=r[n].__index,d=i+a*this.props.emojiButtonSize,l=d+this.props.emojiButtonSize+this.props.emojiButtonSize*.88;if(d<u.scrollTop)i=d;else if(l>u.scrollTop+o.height)i=l-o.height;else return}this.ignoreMouse(),u.scrollTop=i}ignoreMouse(){this.mouseIsIgnored=!0,clearTimeout(this.ignoreMouseTimer),this.ignoreMouseTimer=setTimeout(()=>{delete this.mouseIsIgnored},100)}handleEmojiOver(t){this.mouseIsIgnored||this.state.showSkins||this.setState({pos:t||[-1,-1],keyboard:!1})}handleEmojiClick({e:t,emoji:n,pos:r}){if(this.props.onEmojiSelect&&(!n&&r&&(n=this.getEmojiByPos(r)),n)){const u=Ps(n,{skinIndex:this.state.skin-1});this.props.maxFrequentRows&&Uu.add(u,this.props),this.props.onEmojiSelect(u,t)}}closeSkins(){this.state.showSkins&&(this.setState({showSkins:null,tempSkin:null}),this.base.removeEventListener("click",this.handleBaseClick),this.base.removeEventListener("keydown",this.handleBaseKeydown))}handleSkinMouseOver(t){this.setState({tempSkin:t})}handleSkinClick(t){this.ignoreMouse(),this.closeSkins(),this.setState({skin:t,tempSkin:null}),Fe.set("skin",t)}renderNav(){return h(Ks,{ref:this.refs.navigation,icons:this.props.icons,theme:this.state.theme,dir:this.dir,unfocused:!!this.state.searchResults,position:this.props.navPosition,onClick:this.handleCategoryClick},this.navKey)}renderPreview(){const t=this.getEmojiByPos(this.state.pos),n=this.state.searchResults&&!this.state.searchResults.length;return h("div",{id:"preview",class:"flex flex-middle",dir:this.dir,"data-position":this.props.previewPosition,children:[h("div",{class:"flex flex-middle flex-grow",children:[h("div",{class:"flex flex-auto flex-middle flex-center",style:{height:this.props.emojiButtonSize,fontSize:this.props.emojiButtonSize},children:h(Mn,{emoji:t,id:n?this.props.noResultsEmoji||"cry":this.props.previewEmoji||(this.props.previewPosition=="top"?"point_down":"point_up"),set:this.props.set,size:this.props.emojiButtonSize,skin:this.state.tempSkin||this.state.skin,spritesheet:!0,getSpritesheetURL:this.props.getSpritesheetURL})}),h("div",{class:`margin-${this.dir[0]}`,children:t||n?h("div",{class:`padding-${this.dir[2]} align-${this.dir[0]}`,children:[h("div",{class:"preview-title ellipsis",children:t?t.name:J.search_no_results_1}),h("div",{class:"preview-subtitle ellipsis color-c",children:t?t.skins[0].shortcodes:J.search_no_results_2})]}):h("div",{class:"preview-placeholder color-c",children:J.pick})})]}),!t&&this.props.skinTonePosition=="preview"&&this.renderSkinToneButton()]})}renderEmojiButton(t,{pos:n,posinset:r,grid:u}){const o=this.props.emojiButtonSize,i=this.state.tempSkin||this.state.skin,d=(t.skins[i-1]||t.skins[0]).native,l=Ts(this.state.pos,n),s=n.concat(t.id).join("");return h(Xs,{selected:l,skin:i,size:o,children:h("button",{"aria-label":d,"aria-selected":l||void 0,"aria-posinset":r,"aria-setsize":u.setsize,"data-keyboard":this.state.keyboard,title:this.props.previewPosition=="none"?t.name:void 0,type:"button",class:"flex flex-center flex-middle",tabindex:"-1",onClick:c=>this.handleEmojiClick({e:c,emoji:t}),onMouseEnter:()=>this.handleEmojiOver(n),onMouseLeave:()=>this.handleEmojiOver(),style:{width:this.props.emojiButtonSize,height:this.props.emojiButtonSize,fontSize:this.props.emojiSize,lineHeight:0},children:[h("div",{"aria-hidden":"true",class:"background",style:{borderRadius:this.props.emojiButtonRadius,backgroundColor:this.props.emojiButtonColors?this.props.emojiButtonColors[(r-1)%this.props.emojiButtonColors.length]:void 0}}),h(Mn,{emoji:t,set:this.props.set,size:this.props.emojiSize,skin:i,spritesheet:!0,getSpritesheetURL:this.props.getSpritesheetURL})]})},s)}renderSearch(){const t=this.props.previewPosition=="none"||this.props.skinTonePosition=="search";return h("div",{children:[h("div",{class:"spacer"}),h("div",{class:"flex flex-middle",children:[h("div",{class:"search relative flex-grow",children:[h("input",{type:"search",ref:this.refs.searchInput,placeholder:J.search,onClick:this.handleSearchClick,onInput:this.handleSearchInput,onKeyDown:this.handleSearchKeyDown,autoComplete:"off"}),h("span",{class:"icon loupe flex",children:Wt.search.loupe}),this.state.searchResults&&h("button",{title:"Clear","aria-label":"Clear",type:"button",class:"icon delete flex",onClick:this.clearSearch,onMouseDown:this.preventDefault,children:Wt.search.delete})]}),t&&this.renderSkinToneButton()]})]})}renderSearchResults(){const{searchResults:t}=this.state;return t?h("div",{class:"category",ref:this.refs.search,children:[h("div",{class:`sticky padding-small align-${this.dir[0]}`,children:J.categories.search}),h("div",{children:t.length?t.map((n,r)=>h("div",{class:"flex",children:n.map((u,o)=>this.renderEmojiButton(u,{pos:[r,o],posinset:r*this.props.perLine+o+1,grid:t}))})):h("div",{class:`padding-small align-${this.dir[0]}`,children:this.props.onAddCustomEmoji&&h("a",{onClick:this.props.onAddCustomEmoji,children:J.add_custom})})})]}):null}renderCategories(){const{categories:t}=z,n=!!this.state.searchResults,r=this.getPerLine();return h("div",{style:{visibility:n?"hidden":void 0,display:n?"none":void 0,height:"100%"},children:t.map(u=>{const{root:o,rows:i}=this.refs.categories.get(u.id);return h("div",{"data-id":u.target?u.target.id:u.id,class:"category",ref:o,children:[h("div",{class:`sticky padding-small align-${this.dir[0]}`,children:u.name||J.categories[u.id]}),h("div",{class:"relative",style:{height:i.length*this.props.emojiButtonSize},children:i.map((a,d)=>{const l=a.index-a.index%vt.rowsPerRender,s=this.state.visibleRows[l],c="current"in a?a:void 0;if(!s&&!c)return null;const f=d*r,m=f+r,p=u.emojis.slice(f,m);return p.length<r&&p.push(...new Array(r-p.length)),h("div",{"data-index":a.index,ref:c,class:"flex row",style:{top:d*this.props.emojiButtonSize},children:s&&p.map((y,g)=>{if(!y)return h("div",{style:{width:this.props.emojiButtonSize,height:this.props.emojiButtonSize}});const S=Ge.get(y);return this.renderEmojiButton(S,{pos:[a.index,g],posinset:a.posinset+g,grid:this.grid})})},a.index)})})]})})})}renderSkinToneButton(){return this.props.skinTonePosition=="none"?null:h("div",{class:"flex flex-auto flex-center flex-middle",style:{position:"relative",width:this.props.emojiButtonSize,height:this.props.emojiButtonSize},children:h("button",{type:"button",ref:this.refs.skinToneButton,class:"skin-tone-button flex flex-auto flex-center flex-middle","aria-selected":this.state.showSkins?"":void 0,"aria-label":J.skins.choose,title:J.skins.choose,onClick:this.openSkins,style:{width:this.props.emojiSize,height:this.props.emojiSize},children:h("span",{class:`skin-tone skin-tone-${this.state.skin}`})})})}renderLiveRegion(){const t=this.getEmojiByPos(this.state.pos),n=t?t.name:"";return h("div",{"aria-live":"polite",class:"sr-only",children:n})}renderSkins(){const n=this.refs.skinToneButton.current.getBoundingClientRect(),r=this.base.getBoundingClientRect(),u={};return this.dir=="ltr"?u.right=r.right-n.right-3:u.left=n.left-r.left-3,this.props.previewPosition=="bottom"&&this.props.skinTonePosition=="preview"?u.bottom=r.bottom-n.top+6:(u.top=n.bottom-r.top+3,u.bottom="auto"),h("div",{ref:this.refs.menu,role:"radiogroup",dir:this.dir,"aria-label":J.skins.choose,class:"menu hidden","data-position":u.top?"top":"bottom",style:u,children:[...Array(6).keys()].map(o=>{const i=o+1,a=this.state.skin==i;return h("div",{children:[h("input",{type:"radio",name:"skin-tone",value:i,"aria-label":J.skins[i],ref:a?this.refs.skinToneRadio:null,defaultChecked:a,onChange:()=>this.handleSkinMouseOver(i),onKeyDown:d=>{(d.code=="Enter"||d.code=="Space"||d.code=="Tab")&&(d.preventDefault(),this.handleSkinClick(i))}}),h("button",{"aria-hidden":"true",tabindex:"-1",onClick:()=>this.handleSkinClick(i),onMouseEnter:()=>this.handleSkinMouseOver(i),onMouseLeave:()=>this.handleSkinMouseOver(),class:"option flex flex-grow flex-middle",children:[h("span",{class:`skin-tone skin-tone-${i}`}),h("span",{class:"margin-small-lr",children:J.skins[i]})]})]})})})}render(){const t=this.props.perLine*this.props.emojiButtonSize;return h("section",{id:"root",class:"flex flex-column",dir:this.dir,style:{width:this.props.dynamicWidth?"100%":`calc(${t}px + (var(--padding) + var(--sidebar-width)))`},"data-emoji-set":this.props.set,"data-theme":this.state.theme,"data-menu":this.state.showSkins?"":void 0,children:[this.props.previewPosition=="top"&&this.renderPreview(),this.props.navPosition=="top"&&this.renderNav(),this.props.searchPosition=="sticky"&&h("div",{class:"padding-lr",children:this.renderSearch()}),h("div",{ref:this.refs.scroll,class:"scroll flex-grow padding-lr",children:h("div",{style:{width:this.props.dynamicWidth?"100%":t,height:"100%"},children:[this.props.searchPosition=="static"&&this.renderSearch(),this.renderSearchResults(),this.renderCategories()]})}),this.props.navPosition=="bottom"&&this.renderNav(),this.props.previewPosition=="bottom"&&this.renderPreview(),this.state.showSkins&&this.renderSkins(),this.renderLiveRegion()]})}constructor(t){super(),fe(this,"darkMediaCallback",()=>{this.props.theme=="auto"&&this.setState({theme:this.darkMedia.matches?"dark":"light"})}),fe(this,"handleClickOutside",n=>{const{element:r}=this.props;n.target!=r&&(this.state.showSkins&&this.closeSkins(),this.props.onClickOutside&&this.props.onClickOutside(n))}),fe(this,"handleBaseClick",n=>{this.state.showSkins&&(n.target.closest(".menu")||(n.preventDefault(),n.stopImmediatePropagation(),this.closeSkins()))}),fe(this,"handleBaseKeydown",n=>{this.state.showSkins&&n.key=="Escape"&&(n.preventDefault(),n.stopImmediatePropagation(),this.closeSkins())}),fe(this,"handleSearchClick",()=>{this.getEmojiByPos(this.state.pos)&&this.setState({pos:[-1,-1]})}),fe(this,"handleSearchInput",async()=>{const n=this.refs.searchInput.current;if(!n)return;const{value:r}=n,u=await Ge.search(r),o=()=>{this.refs.scroll.current&&(this.refs.scroll.current.scrollTop=0)};if(!u)return this.setState({searchResults:u,pos:[-1,-1]},o);const i=n.selectionStart==n.value.length?[0,0]:[-1,-1],a=[];a.setsize=u.length;let d=null;for(let l of u)(!a.length||d.length==this.getPerLine())&&(d=[],d.__categoryId="search",d.__index=a.length,a.push(d)),d.push(l);this.ignoreMouse(),this.setState({searchResults:a,pos:i},o)}),fe(this,"handleSearchKeyDown",n=>{const r=n.currentTarget;switch(n.stopImmediatePropagation(),n.key){case"ArrowLeft":this.navigate({e:n,input:r,left:!0});break;case"ArrowRight":this.navigate({e:n,input:r,right:!0});break;case"ArrowUp":this.navigate({e:n,input:r,up:!0});break;case"ArrowDown":this.navigate({e:n,input:r,down:!0});break;case"Enter":n.preventDefault(),this.handleEmojiClick({e:n,pos:this.state.pos});break;case"Escape":n.preventDefault(),this.state.searchResults?this.clearSearch():this.unfocusSearch();break}}),fe(this,"clearSearch",()=>{const n=this.refs.searchInput.current;n&&(n.value="",n.focus(),this.handleSearchInput())}),fe(this,"handleCategoryClick",({category:n,i:r})=>{this.scrollTo(r==0?{row:-1}:{categoryId:n.id})}),fe(this,"openSkins",n=>{const{currentTarget:r}=n,u=r.getBoundingClientRect();this.setState({showSkins:u},async()=>{await Ms(2);const o=this.refs.menu.current;o&&(o.classList.remove("hidden"),this.refs.skinToneRadio.current.focus(),this.base.addEventListener("click",this.handleBaseClick,!0),this.base.addEventListener("keydown",this.handleBaseKeydown,!0))})}),this.observers=[],this.state={pos:[-1,-1],perLine:this.initDynamicPerLine(t),visibleRows:{0:!0},...this.getInitialState(t)}}}class er extends Is{async connectedCallback(){const t=Gu(this.props,xe,this);t.element=this,t.ref=n=>{this.component=n},await en(t),!this.disconnected&&Hu(h(Zs,{...t}),this.shadowRoot)}constructor(t){super(t,{styles:Ru(Qu)})}}fe(er,"Props",xe);typeof customElements<"u"&&!customElements.get("em-emoji-picker")&&customElements.define("em-emoji-picker",er);var Qu={};Qu=`:host {
  width: min-content;
  height: 435px;
  min-height: 230px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  --border-radius: 10px;
  --category-icon-size: 18px;
  --font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", sans-serif;
  --font-size: 15px;
  --preview-placeholder-size: 21px;
  --preview-title-size: 1.1em;
  --preview-subtitle-size: .9em;
  --shadow-color: 0deg 0% 0%;
  --shadow: .3px .5px 2.7px hsl(var(--shadow-color) / .14), .4px .8px 1px -3.2px hsl(var(--shadow-color) / .14), 1px 2px 2.5px -4.5px hsl(var(--shadow-color) / .14);
  display: flex;
}

[data-theme="light"] {
  --em-rgb-color: var(--rgb-color, 34, 36, 39);
  --em-rgb-accent: var(--rgb-accent, 34, 102, 237);
  --em-rgb-background: var(--rgb-background, 255, 255, 255);
  --em-rgb-input: var(--rgb-input, 255, 255, 255);
  --em-color-border: var(--color-border, rgba(0, 0, 0, .05));
  --em-color-border-over: var(--color-border-over, rgba(0, 0, 0, .1));
}

[data-theme="dark"] {
  --em-rgb-color: var(--rgb-color, 222, 222, 221);
  --em-rgb-accent: var(--rgb-accent, 58, 130, 247);
  --em-rgb-background: var(--rgb-background, 21, 22, 23);
  --em-rgb-input: var(--rgb-input, 0, 0, 0);
  --em-color-border: var(--color-border, rgba(255, 255, 255, .1));
  --em-color-border-over: var(--color-border-over, rgba(255, 255, 255, .2));
}

#root {
  --color-a: rgb(var(--em-rgb-color));
  --color-b: rgba(var(--em-rgb-color), .65);
  --color-c: rgba(var(--em-rgb-color), .45);
  --padding: 12px;
  --padding-small: calc(var(--padding) / 2);
  --sidebar-width: 16px;
  --duration: 225ms;
  --duration-fast: 125ms;
  --duration-instant: 50ms;
  --easing: cubic-bezier(.4, 0, .2, 1);
  width: 100%;
  text-align: left;
  border-radius: var(--border-radius);
  background-color: rgb(var(--em-rgb-background));
  position: relative;
}

@media (prefers-reduced-motion) {
  #root {
    --duration: 0;
    --duration-fast: 0;
    --duration-instant: 0;
  }
}

#root[data-menu] button {
  cursor: auto;
}

#root[data-menu] .menu button {
  cursor: pointer;
}

:host, #root, input, button {
  color: rgb(var(--em-rgb-color));
  font-family: var(--font-family);
  font-size: var(--font-size);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: normal;
}

*, :before, :after {
  box-sizing: border-box;
  min-width: 0;
  margin: 0;
  padding: 0;
}

.relative {
  position: relative;
}

.flex {
  display: flex;
}

.flex-auto {
  flex: none;
}

.flex-center {
  justify-content: center;
}

.flex-column {
  flex-direction: column;
}

.flex-grow {
  flex: auto;
}

.flex-middle {
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.padding {
  padding: var(--padding);
}

.padding-t {
  padding-top: var(--padding);
}

.padding-lr {
  padding-left: var(--padding);
  padding-right: var(--padding);
}

.padding-r {
  padding-right: var(--padding);
}

.padding-small {
  padding: var(--padding-small);
}

.padding-small-b {
  padding-bottom: var(--padding-small);
}

.padding-small-lr {
  padding-left: var(--padding-small);
  padding-right: var(--padding-small);
}

.margin {
  margin: var(--padding);
}

.margin-r {
  margin-right: var(--padding);
}

.margin-l {
  margin-left: var(--padding);
}

.margin-small-l {
  margin-left: var(--padding-small);
}

.margin-small-lr {
  margin-left: var(--padding-small);
  margin-right: var(--padding-small);
}

.align-l {
  text-align: left;
}

.align-r {
  text-align: right;
}

.color-a {
  color: var(--color-a);
}

.color-b {
  color: var(--color-b);
}

.color-c {
  color: var(--color-c);
}

.ellipsis {
  white-space: nowrap;
  max-width: 100%;
  width: auto;
  text-overflow: ellipsis;
  overflow: hidden;
}

.sr-only {
  width: 1px;
  height: 1px;
  position: absolute;
  top: auto;
  left: -10000px;
  overflow: hidden;
}

a {
  cursor: pointer;
  color: rgb(var(--em-rgb-accent));
}

a:hover {
  text-decoration: underline;
}

.spacer {
  height: 10px;
}

[dir="rtl"] .scroll {
  padding-left: 0;
  padding-right: var(--padding);
}

.scroll {
  padding-right: 0;
  overflow-x: hidden;
  overflow-y: auto;
}

.scroll::-webkit-scrollbar {
  width: var(--sidebar-width);
  height: var(--sidebar-width);
}

.scroll::-webkit-scrollbar-track {
  border: 0;
}

.scroll::-webkit-scrollbar-button {
  width: 0;
  height: 0;
  display: none;
}

.scroll::-webkit-scrollbar-corner {
  background-color: rgba(0, 0, 0, 0);
}

.scroll::-webkit-scrollbar-thumb {
  min-height: 20%;
  min-height: 65px;
  border: 4px solid rgb(var(--em-rgb-background));
  border-radius: 8px;
}

.scroll::-webkit-scrollbar-thumb:hover {
  background-color: var(--em-color-border-over) !important;
}

.scroll:hover::-webkit-scrollbar-thumb {
  background-color: var(--em-color-border);
}

.sticky {
  z-index: 1;
  background-color: rgba(var(--em-rgb-background), .9);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  font-weight: 500;
  position: sticky;
  top: -1px;
}

[dir="rtl"] .search input[type="search"] {
  padding: 10px 2.2em 10px 2em;
}

[dir="rtl"] .search .loupe {
  left: auto;
  right: .7em;
}

[dir="rtl"] .search .delete {
  left: .7em;
  right: auto;
}

.search {
  z-index: 2;
  position: relative;
}

.search input, .search button {
  font-size: calc(var(--font-size)  - 1px);
}

.search input[type="search"] {
  width: 100%;
  background-color: var(--em-color-border);
  transition-duration: var(--duration);
  transition-property: background-color, box-shadow;
  transition-timing-function: var(--easing);
  border: 0;
  border-radius: 10px;
  outline: 0;
  padding: 10px 2em 10px 2.2em;
  display: block;
}

.search input[type="search"]::-ms-input-placeholder {
  color: inherit;
  opacity: .6;
}

.search input[type="search"]::placeholder {
  color: inherit;
  opacity: .6;
}

.search input[type="search"], .search input[type="search"]::-webkit-search-decoration, .search input[type="search"]::-webkit-search-cancel-button, .search input[type="search"]::-webkit-search-results-button, .search input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
  -ms-appearance: none;
  appearance: none;
}

.search input[type="search"]:focus {
  background-color: rgb(var(--em-rgb-input));
  box-shadow: inset 0 0 0 1px rgb(var(--em-rgb-accent)), 0 1px 3px rgba(65, 69, 73, .2);
}

.search .icon {
  z-index: 1;
  color: rgba(var(--em-rgb-color), .7);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.search .loupe {
  pointer-events: none;
  left: .7em;
}

.search .delete {
  right: .7em;
}

svg {
  fill: currentColor;
  width: 1em;
  height: 1em;
}

button {
  -webkit-appearance: none;
  -ms-appearance: none;
  appearance: none;
  cursor: pointer;
  color: currentColor;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
}

#nav {
  z-index: 2;
  padding-top: 12px;
  padding-bottom: 12px;
  padding-right: var(--sidebar-width);
  position: relative;
}

#nav button {
  color: var(--color-b);
  transition: color var(--duration) var(--easing);
}

#nav button:hover {
  color: var(--color-a);
}

#nav svg, #nav img {
  width: var(--category-icon-size);
  height: var(--category-icon-size);
}

#nav[dir="rtl"] .bar {
  left: auto;
  right: 0;
}

#nav .bar {
  width: 100%;
  height: 3px;
  background-color: rgb(var(--em-rgb-accent));
  transition: transform var(--duration) var(--easing);
  border-radius: 3px 3px 0 0;
  position: absolute;
  bottom: -12px;
  left: 0;
}

#nav button[aria-selected] {
  color: rgb(var(--em-rgb-accent));
}

#preview {
  z-index: 2;
  padding: calc(var(--padding)  + 4px) var(--padding);
  padding-right: var(--sidebar-width);
  position: relative;
}

#preview .preview-placeholder {
  font-size: var(--preview-placeholder-size);
}

#preview .preview-title {
  font-size: var(--preview-title-size);
}

#preview .preview-subtitle {
  font-size: var(--preview-subtitle-size);
}

#nav:before, #preview:before {
  content: "";
  height: 2px;
  position: absolute;
  left: 0;
  right: 0;
}

#nav[data-position="top"]:before, #preview[data-position="top"]:before {
  background: linear-gradient(to bottom, var(--em-color-border), transparent);
  top: 100%;
}

#nav[data-position="bottom"]:before, #preview[data-position="bottom"]:before {
  background: linear-gradient(to top, var(--em-color-border), transparent);
  bottom: 100%;
}

.category:last-child {
  min-height: calc(100% + 1px);
}

.category button {
  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, sans-serif;
  position: relative;
}

.category button > * {
  position: relative;
}

.category button .background {
  opacity: 0;
  background-color: var(--em-color-border);
  transition: opacity var(--duration-fast) var(--easing) var(--duration-instant);
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.category button:hover .background {
  transition-duration: var(--duration-instant);
  transition-delay: 0s;
}

.category button[aria-selected] .background {
  opacity: 1;
}

.category button[data-keyboard] .background {
  transition: none;
}

.row {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.skin-tone-button {
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 100%;
}

.skin-tone-button:hover {
  border-color: var(--em-color-border);
}

.skin-tone-button:active .skin-tone {
  transform: scale(.85) !important;
}

.skin-tone-button .skin-tone {
  transition: transform var(--duration) var(--easing);
}

.skin-tone-button[aria-selected] {
  background-color: var(--em-color-border);
  border-top-color: rgba(0, 0, 0, .05);
  border-bottom-color: rgba(0, 0, 0, 0);
  border-left-width: 0;
  border-right-width: 0;
}

.skin-tone-button[aria-selected] .skin-tone {
  transform: scale(.9);
}

.menu {
  z-index: 2;
  white-space: nowrap;
  border: 1px solid var(--em-color-border);
  background-color: rgba(var(--em-rgb-background), .9);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  transition-property: opacity, transform;
  transition-duration: var(--duration);
  transition-timing-function: var(--easing);
  border-radius: 10px;
  padding: 4px;
  position: absolute;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, .05);
}

.menu.hidden {
  opacity: 0;
}

.menu[data-position="bottom"] {
  transform-origin: 100% 100%;
}

.menu[data-position="bottom"].hidden {
  transform: scale(.9)rotate(-3deg)translateY(5%);
}

.menu[data-position="top"] {
  transform-origin: 100% 0;
}

.menu[data-position="top"].hidden {
  transform: scale(.9)rotate(3deg)translateY(-5%);
}

.menu input[type="radio"] {
  clip: rect(0 0 0 0);
  width: 1px;
  height: 1px;
  border: 0;
  margin: 0;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.menu input[type="radio"]:checked + .option {
  box-shadow: 0 0 0 2px rgb(var(--em-rgb-accent));
}

.option {
  width: 100%;
  border-radius: 6px;
  padding: 4px 6px;
}

.option:hover {
  color: #fff;
  background-color: rgb(var(--em-rgb-accent));
}

.skin-tone {
  width: 16px;
  height: 16px;
  border-radius: 100%;
  display: inline-block;
  position: relative;
  overflow: hidden;
}

.skin-tone:after {
  content: "";
  mix-blend-mode: overlay;
  background: linear-gradient(rgba(255, 255, 255, .2), rgba(0, 0, 0, 0));
  border: 1px solid rgba(0, 0, 0, .8);
  border-radius: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  box-shadow: inset 0 -2px 3px #000, inset 0 1px 2px #fff;
}

.skin-tone-1 {
  background-color: #ffc93a;
}

.skin-tone-2 {
  background-color: #ffdab7;
}

.skin-tone-3 {
  background-color: #e7b98f;
}

.skin-tone-4 {
  background-color: #c88c61;
}

.skin-tone-5 {
  background-color: #a46134;
}

.skin-tone-6 {
  background-color: #5d4437;
}

[data-index] {
  justify-content: space-between;
}

[data-emoji-set="twitter"] .skin-tone:after {
  box-shadow: none;
  border-color: rgba(0, 0, 0, .5);
}

[data-emoji-set="twitter"] .skin-tone-1 {
  background-color: #fade72;
}

[data-emoji-set="twitter"] .skin-tone-2 {
  background-color: #f3dfd0;
}

[data-emoji-set="twitter"] .skin-tone-3 {
  background-color: #eed3a8;
}

[data-emoji-set="twitter"] .skin-tone-4 {
  background-color: #cfad8d;
}

[data-emoji-set="twitter"] .skin-tone-5 {
  background-color: #a8805d;
}

[data-emoji-set="twitter"] .skin-tone-6 {
  background-color: #765542;
}

[data-emoji-set="google"] .skin-tone:after {
  box-shadow: inset 0 0 2px 2px rgba(0, 0, 0, .4);
}

[data-emoji-set="google"] .skin-tone-1 {
  background-color: #f5c748;
}

[data-emoji-set="google"] .skin-tone-2 {
  background-color: #f1d5aa;
}

[data-emoji-set="google"] .skin-tone-3 {
  background-color: #d4b48d;
}

[data-emoji-set="google"] .skin-tone-4 {
  background-color: #aa876b;
}

[data-emoji-set="google"] .skin-tone-5 {
  background-color: #916544;
}

[data-emoji-set="google"] .skin-tone-6 {
  background-color: #61493f;
}

[data-emoji-set="facebook"] .skin-tone:after {
  border-color: rgba(0, 0, 0, .4);
  box-shadow: inset 0 -2px 3px #000, inset 0 1px 4px #fff;
}

[data-emoji-set="facebook"] .skin-tone-1 {
  background-color: #f5c748;
}

[data-emoji-set="facebook"] .skin-tone-2 {
  background-color: #f1d5aa;
}

[data-emoji-set="facebook"] .skin-tone-3 {
  background-color: #d4b48d;
}

[data-emoji-set="facebook"] .skin-tone-4 {
  background-color: #aa876b;
}

[data-emoji-set="facebook"] .skin-tone-5 {
  background-color: #916544;
}

[data-emoji-set="facebook"] .skin-tone-6 {
  background-color: #61493f;
}

`;function Js(e){const t=k.useRef(null),n=k.useRef(null);return n.current&&n.current.update(e),k.useEffect(()=>(n.current=new er({...e,ref:t}),()=>{n.current=null}),[]),L.createElement("div",{ref:t})}var Co="https://cdn.jsdelivr.net/npm/@emoji-mart/data",We={};function Ys(e){var t=e.theme,n=e.onSelectEmoji,r=e.disableRecent,u=e.customEmojis,o=e.language,i=k.useMemo(function(){var c=[];return r||c.push("frequent"),c=[].concat(xu(c),["people","nature","foods","activity","places","objects","symbols","flags"]),c},[r]),a=k.useState(void 0),d=ye(a,2),l=d[0],s=d[1];return k.useEffect(function(){var c;if(!o){var f;if(We.en){s(We.en);return}fetch("".concat(Co,"/i18n/en.json")).then(function(m){return(f=f||Ot(Se().mark(function p(y){var g;return Se().wrap(function($){for(;;)switch($.prev=$.next){case 0:return $.next=2,y.json();case 2:g=$.sent,s(g),We.en=g;case 5:case"end":return $.stop()}},p)}))).apply(this,arguments)}).catch(function(m){console.error("Failed to load translations:",m)});return}if(We[o]){s(We[o]);return}fetch("".concat(Co,"/i18n/").concat(o,".json")).then(function(m){return(c=c||Ot(Se().mark(function p(y){var g;return Se().wrap(function($){for(;;)switch($.prev=$.next){case 0:return $.next=2,y.json();case 2:g=$.sent,s(g),We[o]=g;case 5:case"end":return $.stop()}},p)}))).apply(this,arguments)}).catch(function(m){console.error("Failed to load translations:",m)})},[o]),l?L.createElement(Js,{data:void 0,theme:t,previewPosition:"none",onEmojiSelect:n,custom:u,categories:i,set:"apple",i18n:l}):null}var Qs=k.memo(Ys);function $o(e){var t=e.showPicker,n=e.theme,r=e.handleSelectEmoji,u=e.disableRecent,o=e.customEmojis,i=e.position,a=e.language;return L.createElement("div",{className:"react-emoji-picker--container"},t&&L.createElement("div",{className:"react-emoji-picker--wrapper",onClick:function(l){return l.stopPropagation()},style:i==="below"?{top:"40px"}:{}},L.createElement("div",{className:"react-emoji-picker"},L.createElement(Qs,{theme:n,onSelectEmoji:r,disableRecent:u,customEmojis:o,language:a}))))}var el=435,tl=function(t){var n=t.theme,r=t.keepOpened,u=t.disableRecent,o=t.customEmojis,i=t.addSanitizeFn,a=t.addPolluteFn,d=t.appendContent,l=t.buttonElement,s=t.buttonRef,c=t.language,f=k.useState(!1),m=ye(f,2),p=m[0],y=m[1],g=k.useState(),S=ye(g,2),$=S[0],R=S[1],M=k.useState(),A=ye(M,2),H=A[0],le=A[1];k.useEffect(function(){p&&Md()},[p]),k.useEffect(function(){i(Et)},[i]),k.useEffect(function(){a(Cu)},[a]),k.useEffect(function(){function V(pe){var he=pe.target;he.classList.contains("react-input-emoji--button")||he.classList.contains("react-input-emoji--button--icon")||y(!1)}return document.addEventListener("click",V),function(){document.removeEventListener("click",V)}},[]);function te(V){V.stopPropagation(),V.preventDefault(),le(Ee(V)),y(function(pe){return!pe})}function Ee(V){var pe=V.currentTarget,he=pe.getBoundingClientRect(),ze=el;return he.top>=ze?"above":"below"}function Ue(V){d(Rd(V)),r||y(function(pe){return!pe})}return k.useEffect(function(){var V;s!=null&&(V=s.current)!==null&&V!==void 0&&V.style?(s.current.style.position="relative",R(s.current)):l!=null&&l.style&&(l.style.position="relative",R(l))},[s,l]),$?oi.createPortal(L.createElement(L.Fragment,null,L.createElement($o,{showPicker:p,theme:n,handleSelectEmoji:Ue,disableRecent:u,customEmojis:o,position:H,language:c}),L.createElement(to,{showPicker:p,toggleShowPicker:te,buttonElement:$,buttonRef:s})),$):L.createElement(L.Fragment,null,L.createElement($o,{showPicker:p,theme:n,handleSelectEmoji:Ue,disableRecent:u,customEmojis:o,position:H,language:c}),L.createElement(to,{showPicker:p,toggleShowPicker:te}))};function nl(){var e=ei();if(!e)return null;var t=e.text.substring(e.begin,e.end);return t||null}function rl(){var e=ei();e&&e.element.deleteData(e.begin,e.end-e.begin)}function ei(){var e=Fn();if(!e)return null;var t=e.element,n=e.caretOffset,r=t.textContent,u=r.lastIndexOf("@");return u===-1||u>=n||u!==0&&r[u-1]!==" "?null:{begin:u,end:n,text:r,element:t}}function Fn(){var e=ol();if(e===null)return null;var t=0;if(typeof window.getSelection<"u"){var n=window.getSelection().getRangeAt(0),r=n.cloneRange();r.selectNodeContents(e),r.setEnd(n.endContainer,n.endOffset),t=r.toString().length}else if(typeof document.selection<"u"&&document.selection.type!="Control"){var u=document.selection.createRange(),o=document.body.createTextRange();o.moveToElementText(e),o.setEndPoint("EndToEnd",u),t=o.text.length}return{element:e,caretOffset:t}}function ol(){var e=document.getSelection().anchorNode;return(e==null?void 0:e.nodeType)==3?e:null}function ul(e){var t,n=k.useState(!1),r=ye(n,2),u=r[0],o=r[1],i=k.useState([]),a=ye(i,2),d=a[0],l=a[1],s=k.useState(null),c=ye(s,2),f=c[0],m=c[1],p=k.useCallback(function(){rl(),l([])},[]),y=k.useCallback(Ot(Se().mark(function $(){var R,M;return Se().wrap(function(H){for(;;)switch(H.prev=H.next){case 0:if(R=nl(),m(R),R!==null){H.next=6;break}l([]),H.next=12;break;case 6:return o(!0),H.next=9,e(R);case 9:M=H.sent,o(!1),l(M);case 12:case"end":return H.stop()}},$)})),[e]),g=k.useCallback(function($){return(t=t||Ot(Se().mark(function R(M){var A,H;return Se().wrap(function(te){for(;;)switch(te.prev=te.next){case 0:if(typeof e=="function"){te.next=2;break}return te.abrupt("return");case 2:M.key==="Backspace"&&(A=Fn())!==null&&A!==void 0&&A.element.parentElement.hasAttribute("data-mention-id")?(H=Fn(),H.element.parentElement.remove()):["ArrowUp","ArrowDown","Esc","Escape"].includes(M.key)||y();case 3:case"end":return te.stop()}},R)}))).apply(this,arguments)},[y,e]),S=k.useCallback(function(){y()},[y]);return{mentionSearchText:f,mentionUsers:d,onKeyUp:g,onFocus:S,onSelectUser:p,loading:u}}var il=function(t,n){var r=t.users,u=t.mentionSearchText,o=t.onSelect,i=t.addEventListener,a=k.useState(0),d=ye(a,2),l=d[0],s=d[1];k.useImperativeHandle(n,function(){return{prevUser:function(){s(function(y){return y===0?0:y-1})},nextUser:function(){s(function(y){return y===r.length-1?r.length-1:y+1})}}}),k.useEffect(function(){s(0)},[r]);function c(p,y){return'<span class="react-input-emoji--mention--item--name__selected" data-testid="metion-selected-word">'.concat(p,"</span>").concat(y)}var f=k.useMemo(function(){var p=u?u.substring(1).toLocaleLowerCase():"";return r.map(function(y){var g=y.name;if(u&&u.length>1)if(y.name.toLowerCase().startsWith(p))g=c(y.name.substring(0,p.length),y.name.substring(p.length));else{var S=y.name.split(" ");g=S.map(function($){return $.toLocaleLowerCase().startsWith(p)?c($.substring(0,p.length),$.substring(p.length)):$}).join(" ")}return Xr(Xr({},y),{},{nameHtml:g})})},[u,r]);function m(p){return function(y){y.stopPropagation(),y.preventDefault(),o(p)}}return k.useEffect(function(){var p=i("enter",function(y){y.stopPropagation(),y.preventDefault(),o(f[l])});return function(){p()}},[i,o,l,f]),L.createElement("ul",{className:"react-input-emoji--mention--list","data-testid":"mention-user-list"},f.map(function(p,y){return L.createElement("li",{key:p.id},L.createElement("button",{type:"button",onClick:m(p),className:"react-input-emoji--mention--item".concat(l===y?" react-input-emoji--mention--item__selected":""),onMouseOver:function(){return s(y)}},L.createElement("img",{className:"react-input-emoji--mention--item--img",src:p.image}),L.createElement("div",{className:"react-input-emoji--mention--item--name",dangerouslySetInnerHTML:{__html:p.nameHtml}})))}))},al=k.forwardRef(il),dl=function(t){var n=t.searchMention,r=t.addEventListener,u=t.appendContent,o=t.addSanitizeFn,i=k.useRef(null),a=k.useState(!1),d=ye(a,2),l=d[0],s=d[1],c=ul(n),f=c.mentionSearchText,m=c.mentionUsers,p=c.loading,y=c.onKeyUp,g=c.onFocus,S=c.onSelectUser;k.useEffect(function(){o(function(R){var M=document.createElement("div");M.innerHTML=R;var A=Array.prototype.slice.call(M.querySelectorAll(".react-input-emoji--mention--text"));return A.forEach(function(H){M.innerHTML=M.innerHTML.replace(H.outerHTML,"@[".concat(H.dataset.mentionName,"](userId:").concat(H.dataset.mentionId,")"))}),M.innerHTML})},[o]),k.useEffect(function(){s(m.length>0)},[m]),k.useEffect(function(){function R(){s(!1)}return document.addEventListener("click",R),function(){document.removeEventListener("click",R)}},[]),k.useEffect(function(){var R=r("keyUp",y);return function(){R()}},[r,y]),k.useEffect(function(){function R(A){switch(A.key){case"Esc":case"Escape":s(!1);break}}var M=r("keyDown",R);return function(){M()}},[r]),k.useEffect(function(){var R=r("focus",g);return function(){R()}},[r,g]),k.useEffect(function(){if(l){var R=r("arrowUp",function(A){A.stopPropagation(),A.preventDefault(),i.current.prevUser()}),M=r("arrowDown",function(A){A.stopPropagation(),A.preventDefault(),i.current.nextUser()});return function(){R(),M()}}},[r,l]);function $(R){S(),u('<span class="react-input-emoji--mention--text" data-mention-id="'.concat(R.id,'" data-mention-name="').concat(R.name,'">@').concat(R.name,"</span> "))}return L.createElement(L.Fragment,null,p?L.createElement("div",{className:"react-input-emoji--mention--container"},L.createElement("div",{className:"react-input-emoji--mention--loading"},L.createElement("div",{className:"react-input-emoji--mention--loading--spinner"},"Loading..."))):l&&L.createElement("div",{className:"react-input-emoji--mention--container",onClick:function(M){return M.stopPropagation()}},L.createElement(al,{ref:i,mentionSearchText:f,users:m,onSelect:$,addEventListener:r})))};function Ae(){var e=[];return{subscribe:function(n){return e.push(n),function(){e=e.filter(function(r){return r!==n})}},publish:function(n){e.forEach(function(r){return r(n)})},get currentListerners(){return e}}}function sl(){var e=k.useMemo(function(){return{keyDown:Ae(),keyUp:Ae(),arrowUp:Ae(),arrowDown:Ae(),enter:Ae(),focus:Ae(),blur:Ae()}},[]),t=k.useCallback(function(n,r){return e[n].subscribe(r)},[e]);return{addEventListener:t,listeners:e}}function ll(){var e=k.useRef([]),t=k.useCallback(function(r){e.current.push(r)},[]),n=k.useCallback(function(r){var u=e.current.reduce(function(o,i){return i(o)},r);return u},[]);return{addPolluteFn:t,pollute:n}}function cl(e,t){var n=e.onChange,r=e.onEnter,u=e.onResize,o=e.onClick,i=e.onFocus,a=e.onBlur,d=e.onKeyDown,l=e.theme,s=e.cleanOnEnter,c=e.placeholder,f=e.maxLength,m=e.keepOpened,p=e.inputClass,y=e.disableRecent,g=e.tabIndex,S=e.value,$=e.customEmojis,R=e.language,M=e.searchMention,A=e.buttonElement,H=e.buttonRef,le=e.shouldReturn,te=e.shouldConvertEmojiToImage,Ee=e.borderRadius,Ue=e.borderColor,V=e.fontSize,pe=e.fontFamily,he=e.background,ze=e.placeholderColor,Ye=e.color,v=k.useRef(null),b=sl(),w=b.addEventListener,j=b.listeners,C=ju(le,te),I=C.addSanitizeFn,N=C.sanitize,U=C.sanitizedTextRef,Z=ll(),je=Z.addPolluteFn,Ie=Z.pollute,ce=k.useCallback(function(){var W=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";v.current!==null&&(v.current.html=Cu(W),U.current=W)},[U]),tn=k.useCallback(function(W){ce(W)},[ce]),lt=as(v,u,n);is({ref:t,setValue:tn,textInputRef:v,emitChange:lt,shouldConvertEmojiToImage:te}),k.useEffect(function(){U.current!==S&&tn(S)},[U,tn,S]),k.useEffect(function(){function W(Ve){if(typeof f<"u"&&Ve.key!=="Backspace"&&v.current!==null&&Yr(v.current)>=f&&Ve.preventDefault(),Ve.key==="Enter"&&v.current){Ve.preventDefault();var ri=N(v.current.html);return lt(U.current),typeof r=="function"&&j.enter.currentListerners.length===0&&r(ri),s&&j.enter.currentListerners.length===0&&ce(""),typeof d=="function"&&d(Ve.nativeEvent),!1}return typeof d=="function"&&d(Ve.nativeEvent),!0}var me=w("keyDown",W);return function(){me()}},[w,s,lt,j.enter.currentListerners.length,f,r,d,N,U,ce]),k.useEffect(function(){function W(){typeof o=="function"&&o(),typeof i=="function"&&i()}var me=w("focus",W);return function(){me()}},[w,o,i]),k.useEffect(function(){function W(){typeof a=="function"&&a()}var me=w("blur",W);return function(){me()}},[w,a]);function ti(W){N(W),S!==U.current&&lt(U.current)}function tr(W){typeof f<"u"&&v.current!==null&&Yr(v.current)>=f||v.current!==null&&v.current.appendContent(W)}function ni(W){W.preventDefault();var me;W.clipboardData&&(me=W.clipboardData.getData("text/plain"),me=Ie(me),document.execCommand("insertHTML",!1,me))}return L.createElement("div",{className:"react-emoji"},L.createElement(dl,{searchMention:M,addEventListener:w,appendContent:tr,addSanitizeFn:I}),L.createElement(ls,{ref:v,onCopy:Td,onPaste:ni,shouldReturn:le,onBlur:j.blur.publish,onFocus:j.focus.publish,onArrowUp:j.arrowUp.publish,onArrowDown:j.arrowDown.publish,onKeyUp:j.keyUp.publish,onKeyDown:j.keyDown.publish,onEnter:j.enter.publish,placeholder:c,style:{borderRadius:Ee,borderColor:Ue,fontSize:V,fontFamily:pe,background:he,placeholderColor:ze,color:Ye},tabIndex:g,className:p,onChange:ti}),L.createElement(tl,{theme:l,keepOpened:m,disableRecent:y,customEmojis:$,addSanitizeFn:I,addPolluteFn:je,appendContent:tr,buttonElement:A,buttonRef:H,language:R}))}var fl=k.forwardRef(cl);fl.defaultProps={theme:"auto",height:30,placeholder:"Type a message",borderRadius:21,borderColor:"#EAEAEA",color:"black",fontSize:15,fontFamily:"sans-serif",background:"white",tabIndex:0,shouldReturn:!1,shouldConvertEmojiToImage:!1,customEmojis:[],language:void 0};export{fl as I};
