import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as A,f as D,r as l,j as G}from"./vendor-851db8c1.js";import{u as U}from"./react-hook-form-687afde5.js";import{o as B}from"./yup-2824f222.js";import{c as L,a as c}from"./yup-54691517.js";import{w as M,M as O,A as H,G as K,t as C,l as V,b as Y,c as z,d as J}from"./index-08a5dc5b.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let r=new O;const Q=({activeId:_,setSidebar:d})=>{var f,j,y,w,E,N;const F=L({subject:c().required(),html:c().required(),tag:c().required()}).required(),{dispatch:p}=A.useContext(H),{dispatch:u}=A.useContext(K),P=D(),[x,$]=l.useState(0),[o,I]=l.useState(""),[g,h]=l.useState(!1),{register:i,handleSubmit:q,setError:b,setValue:m,formState:{errors:a}}=U({resolver:B(F)});G(),l.useEffect(function(){u({type:"SETPATH",payload:{path:"email"}}),async function(){try{r.setTable("email");const t=await r.callRestAPI({id:_},"GET");t.error||(m("subject",t.model.subject),m("html",t.model.html),m("tag",t.model.tag),I(t.model.slug),$(t.model.id))}catch(t){console.log("error",t),C(p,t.message)}}()},[]);const R=async t=>{var v,S;h(!0);try{const s=await r.callRestAPI({id:x,slug:o,subject:t.subject,html:t.html,tag:t.tag},"PUT");if(!s.error)await V(r,{user_id:localStorage.getItem("user"),activity_type:Y.club_ui,action_type:z.UPDATE,data:{email_id:x,slug:o,old_subject:(v=s.model)==null?void 0:v.subject,new_subject:t.subject,old_tag:(S=s.model)==null?void 0:S.tag,new_tag:t.tag},club_id:null,description:`Admin updated email template: ${o}`}),J(u,"Updated"),P("/admin/email");else if(s.validation){const T=Object.keys(s.validation);for(let n=0;n<T.length;n++){const k=T[n];b(k,{type:"manual",message:s.validation[k]})}}}catch(s){console.log("Error",s),b("html",{type:"manual",message:s.message}),C(p,s.message)}h(!1)};return e.jsxs("div",{className:"mx-auto rounded",children:[e.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[e.jsx("div",{className:"flex items-center gap-3",children:e.jsx("span",{className:"text-lg font-semibold",children:"Edit Email"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#F4F4F4]",onClick:()=>d(!1),children:"Cancel"}),e.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await q(R)(),d(!1)},disabled:g,children:g?"Saving...":"Save"})]})]}),e.jsxs("form",{className:"w-full p-4 text-left",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"slug",children:"Email Type"}),e.jsx("input",{type:"text",placeholder:"Email Type",value:o,readOnly:!0,className:"focus:shadow-outline} mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Subject"}),e.jsx("input",{type:"text",placeholder:"subject",...i("subject"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(f=a.subject)!=null&&f.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(j=a.subject)==null?void 0:j.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"tag",children:"Tags"}),e.jsx("input",{type:"text",placeholder:"tag",...i("tag"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(y=a.tag)!=null&&y.message?"border-red-500":""}`}),e.jsx("p",{className:"text-xs italic text-red-500",children:(w=a.tag)==null?void 0:w.message})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"html",children:"Email Body"}),e.jsx("textarea",{placeholder:"Email Body",className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(E=a.html)!=null&&E.message?"border-red-500":""}`,...i("html"),rows:15}),e.jsx("p",{className:"text-xs italic text-red-500",children:(N=a.html)==null?void 0:N.message})]})]})]})},$e=M(Q,"email","You don't have permission to edit emails");export{$e as default};
