import{j as o}from"./@nivo/heatmap-ba1ecfff.js";import{u as l,b0 as d,h as S,aR as g,M as x}from"./index-08a5dc5b.js";import{C as h}from"./ClubUI-719fd0c0.js";import{r as i}from"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./SportList-b91a6971.js";import"./PlusIcon-7e8d14d7.js";import"./PencilIcon-35185602.js";import"./TrashIcon-aaaccaf2.js";import"./InformationCircleIcon-d35f3488.js";import"./SplashScreenPagePreview-0dbde401.js";import"./ContactModal-541f1b35.js";import"./BottomDrawer-eee99403.js";import"./ImageCropModal-266718bc.js";import"./react-image-crop-1f5038af.js";import"./index.esm-09a3a6b8.js";import"./index.esm-3f8dc7b8.js";import"./AuthLayout-3236f682.js";import"./MembershipCard-244db99f.js";import"./SportTypeSelection-5dc32d74.js";import"./SelectionOptionsCard-30c39f7f.js";import"./SelectionOption-658322e6.js";import"./HistoryComponent-999cafaf.js";new x;function ht(){const[p,j]=i.useState(null),{club:t,sports:s,courts:e,fetchClubData:a,pricing:c}=l(),[r,n]=i.useState(null),[u,m]=i.useState(!1);return i.useEffect(()=>{(async()=>{m(!0);const f=await d(t==null?void 0:t.id);n(f),m(!1)})()},[t==null?void 0:t.id]),o.jsxs("div",{children:[u&&o.jsx(S,{}),r&&r.staff.club_ui?o.jsx(h,{selectedClub:{name:""},profileSettings:p,fetchSettings:a,club:t,courts:e,sports:s,pricing:c}):o.jsx(g,{message:"You don't have permission to access the club UI"})]})}export{ht as default};
