import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,f as ze,b as He,L as Ge}from"./vendor-851db8c1.js";import{f as I,M as ae,l as Ee,b as ne,c as ce,G as oe,d as A,h as Ye,a as Ue,u as ve,R as te}from"./index-08a5dc5b.js";import{S as Ke}from"./SportList-b91a6971.js";import{S as ke}from"./SplashScreenPagePreview-0dbde401.js";import{B as fe}from"./BottomDrawer-eee99403.js";import{I as Qe}from"./ImageCropModal-266718bc.js";import{F as Xe}from"./index.esm-09a3a6b8.js";import{B as es,a as ss}from"./index.esm-3f8dc7b8.js";import{A as ts}from"./AuthLayout-3236f682.js";import{M as rs}from"./MembershipCard-244db99f.js";import{S as as}from"./SportTypeSelection-5dc32d74.js";import{H as ls}from"./HistoryComponent-999cafaf.js";let Fe=new ae;function is({onClose:s,fetchSettings:a,club:j,clubUser:t}){const[v,d]=r.useState(!1),[l,x]=r.useState(null),w=localStorage.getItem("user"),B=(()=>{try{return JSON.parse((j==null?void 0:j.court_description)||'{"reservation_description":"","payment_description":""}')}catch{return{reservation_description:"",payment_description:""}}})(),[f,V]=r.useState(B.reservation_description),[m,n]=r.useState(B.payment_description),[S,b]=r.useState(f),[L,P]=r.useState(m),_=localStorage.getItem("role"),H=c=>{x(c)},h=async()=>{try{d(!0);const c={reservation_description:S,payment_description:L},o=await Fe.callRawAPI(_==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${t==null?void 0:t.id}`:`/v3/api/custom/courtmatchup/${_}/profile-edit`,{court_description:JSON.stringify(c)},"POST");await Ee(Fe,{user_id:w,activity_type:ne.club_ui,action_type:ce.UPDATE,data:c,club_id:j==null?void 0:j.id,description:"Updated court booking descriptions"}),V(S),n(L),x(null),d(!1),a(_==="admin"?t==null?void 0:t.id:null)}catch(c){console.log(c),d(!1)}},p=()=>{b(f),P(m),x(null)};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Reservation Description"}),l!=="reservation"&&e.jsx("button",{onClick:()=>H("reservation"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:f?"Edit":"Add Description"})]}),l==="reservation"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:S,onChange:c=>b(c.target.value),placeholder:"Enter description for reservation screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:h,loading:v,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:p,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:f||"No reservation description added yet."})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Payment Description"}),l!=="payment"&&e.jsx("button",{onClick:()=>H("payment"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:m?"Edit":"Add Description"})]}),l==="payment"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:L,onChange:c=>P(c.target.value),placeholder:"Enter description for payment screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:h,loading:v,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:p,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:m||"No payment description added yet."})]})]})]})})}let ns=new ae;function ds({clubUser:s,onClose:a,fetchSettings:j,club:t,sports:v}){const{dispatch:d}=r.useContext(oe),[l,x]=r.useState(!1),[w,B]=r.useState(!1),f=localStorage.getItem("role"),[V,m]=r.useState([]),[n,S]=r.useState([]),b=()=>{if(v&&v.length>0){const h=[];v.forEach(p=>{var o;((o=p.sport_types)==null?void 0:o.some(N=>N.type&&N.type.trim()!==""))?p.sport_types.forEach(N=>{N.type&&N.type.trim()!==""&&(h.push({id:`${p.id}_type_${N.club_sport_type_id||N.type}`,name:`${p.name} - ${N.type}`,sport_name:p.name,sport_id:p.id,type:N.type,club_sport_type_id:N.club_sport_type_id,threshold:0,level:"type"}),N.subtype&&Array.isArray(N.subtype)&&N.subtype.forEach((F,Z)=>{F&&F.trim()!==""&&h.push({id:`${p.id}_subtype_${N.club_sport_type_id||N.type}_${Z}`,name:`${p.name} - ${N.type} - ${F}`,sport_name:p.name,sport_id:p.id,type:N.type,subtype:F,club_sport_type_id:N.club_sport_type_id,threshold:0,level:"subtype"})}))}):h.push({id:p.id,name:p.name,status:p.status,club_id:p.club_id,threshold:0,level:"sport",sport_id:p.id})}),m(h),S(JSON.parse(JSON.stringify(h)))}};r.useEffect(()=>{if(t!=null&&t.custom_request_threshold)try{let h=typeof t.custom_request_threshold=="string"?JSON.parse(t.custom_request_threshold):t.custom_request_threshold;if(!Array.isArray(h)){console.warn("Threshold data is not an array, initializing with sports data"),b();return}h.some(c=>c.sport_types&&Array.isArray(c.sport_types))?(console.log("Converting old threshold format to new format"),b()):(m(h),S(JSON.parse(JSON.stringify(h))))}catch(h){console.error("Error parsing threshold data:",h),b()}else b()},[t,v]);const L=()=>{x(!0)},P=async()=>{B(!0);try{await ns.callRawAPI(f==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${s==null?void 0:s.id}`:`/v3/api/custom/courtmatchup/${f}/profile-edit`,{custom_request_threshold:JSON.stringify(n)},"POST"),m(JSON.parse(JSON.stringify(n))),x(!1),A(d,"Custom request thresholds updated",3e3,"success"),j(f==="admin"?s==null?void 0:s.id:null)}catch(h){console.log(h),h!=null&&h.message?A(d,h==null?void 0:h.message,3e3,"error"):A(d,"Error updating custom request thresholds",3e3,"error")}finally{B(!1)}},_=()=>{S(JSON.parse(JSON.stringify(V))),x(!1)},H=(h,p)=>{try{if(!Array.isArray(n)){console.error("tempThresholdData is not an array");return}const c=n.map(o=>o.id===h?{...o,threshold:parseInt(p)||0}:o);S(c)}catch(c){console.error("Error updating threshold:",c)}};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsx("div",{className:"flex items-center justify-end",children:l?e.jsxs("div",{className:"mt-4 flex gap-4",children:[e.jsx(I,{onClick:P,loading:w,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:_,className:"font-medium text-primaryBlue underline hover:text-blue-800",children:"Cancel"})]}):e.jsx("button",{onClick:L,className:"font-medium text-primaryBlue underline hover:text-blue-800",children:"Edit"})}),e.jsx("div",{className:"mb-4 text-sm text-gray-500",children:"Set the threshold for custom requests for each sport, type, and subtype. This determines how many players are needed before a custom request is created."}),e.jsx("div",{className:"h-full space-y-4 overflow-y-auto pr-2",children:Array.isArray(n)&&n.length>0?(()=>{const h=n.reduce((p,c)=>{const o=c.sport_id||c.id,N=c.sport_name||c.name;return p[o]||(p[o]={sportName:N,items:[]}),p[o].items.push(c),p},{});return Object.entries(h).map(([p,c])=>e.jsxs("div",{className:"space-y-3 rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"border-b border-gray-100 pb-2 font-medium text-gray-800",children:c.sportName}),e.jsx("div",{className:"space-y-2",children:c.items.map(o=>e.jsxs("div",{className:`flex items-center justify-between rounded p-2 ${o.level==="sport"?"border border-blue-200 bg-blue-50":o.level==="type"?"ml-4 border border-green-200 bg-green-50":"ml-8 border border-yellow-200 bg-yellow-50"}`,children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"text-sm font-medium text-gray-700",children:[o.level==="sport"&&"Sport Level",o.level==="type"&&`Type: ${o.type}`,o.level==="subtype"&&`Subtype: ${o.type} - ${o.subtype}`]}),o.level==="sport"&&e.jsx("div",{className:"text-xs text-gray-500",children:"(Only shown when sport has no types/subtypes)"})]}),e.jsx("div",{className:"flex items-center",children:l?e.jsxs(e.Fragment,{children:[e.jsx("label",{className:"mr-2 text-sm text-gray-600",children:"Threshold:"}),e.jsx("input",{type:"number",min:"0",value:o.threshold||0,onChange:N=>H(o.id,N.target.value),className:"w-16 rounded-md border border-gray-300 p-1 text-center"})]}):e.jsxs("div",{className:"text-sm text-gray-600",children:["Threshold:"," ",e.jsx("span",{className:"font-medium",children:o.threshold||0})]})})]},o.id))})]},p))})():e.jsx("div",{className:"py-4 text-center text-gray-500",children:"No sports data available. Please add sports to set thresholds."})})]})})}function cs({clubUser:s,club:a,pricing:j}){return console.log("club user",s),e.jsxs("div",{className:"flex flex-col gap-6",children:[j!=null&&j.length?j==null?void 0:j.map((t,v)=>{var d;return e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:(d=t==null?void 0:t.courts)==null?void 0:d.map((l,x)=>{var w;return e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"font-medium",children:l==null?void 0:l.title}),e.jsx("button",{className:"text-gray-400",children:e.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{d:"M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z"})})})]}),(w=l==null?void 0:l.slots)==null?void 0:w.map((B,f)=>e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-600",children:B.time}),e.jsx("span",{className:"rounded-md bg-green-100 px-2 py-1 text-sm text-green-800",children:B.price})]},f))]},x)})},v)}):e.jsx("div",{className:"text-center text-gray-600",children:"No pricing found"}),e.jsxs("button",{className:"mt-4 flex items-center text-gray-600 hover:text-gray-800",children:[e.jsx("span",{className:"mr-2",children:"+"})," Add Pricing"]})]})}let me=new ae;function os({fetchSettings:s,setShowSplashScreenPreview:a,setPreviewImageList:j,club:t,clubUser:v}){var De,Be,Te,Ve;const[d,l]=r.useState(0),[x,w]=r.useState([]),[B,f]=r.useState(null),[V,m]=r.useState(null),n=r.useMemo(()=>{try{return t!=null&&t.splash_screen?JSON.parse(t.splash_screen):null}catch(i){return console.error("Error parsing splash screen data:",i),null}},[t==null?void 0:t.splash_screen]),[S,b]=r.useState(n==null?void 0:n.club_logo),[L,P]=r.useState(!1),[_,H]=r.useState(!1),[h,p]=r.useState(null),[c,o]=r.useState(null),[N,F]=r.useState(!1),[Z,Q]=r.useState(!1),[C,W]=r.useState((t==null?void 0:t.name)||""),[q,J]=r.useState((t==null?void 0:t.name)||""),z=localStorage.getItem("role"),le=localStorage.getItem("user"),G=r.useRef(null),se=r.useRef(null),X=r.useRef(null),y=r.useMemo(()=>{if(n!=null&&n.images){const i=new Array(9).fill(null);return n.images.forEach((u,g)=>{u&&u.url&&(i[g]={url:u.url,isDefault:!0,id:u.id||`default-${g}`,type:u.type||"image"})}),i}return new Array(9).fill(null)},[n==null?void 0:n.images]),[k,T]=r.useState(y),[D,xe]=r.useState((n==null?void 0:n.slideshow_delay)||6e3),[he,pe]=r.useState(!1),de=r.useRef(),{dispatch:Y}=r.useContext(oe),[Ne,ie]=r.useState(!1);r.useEffect(()=>{n!=null&&n.bio&&de.current&&(de.current.value=n.bio)},[]),r.useEffect(()=>{t!=null&&t.name&&(W(t.name),J(t.name))},[t==null?void 0:t.name]),r.useEffect(()=>{const i=d*3,u=k.slice(i,i+3);w(u)},[d,k]);const ue=async i=>{try{let u=new FormData;u.append("file",i);const g=i.type.startsWith("video/");let M;return M=await me.uploadImage(u),M.url}catch(u){return console.error("Upload error:",u),A(Y,"Failed to upload file. Please try again.",3e3,"error"),null}},E=()=>k.slice(0,3).filter(u=>u!==null).length,R=i=>{const u=Math.floor(i/3),g=i%3;if(u===0)return!0;if(!k.slice(0,3)[g])return!1;const U=E(),K=u*3;return k.slice(K,K+3).filter(je=>je!==null).length<U},re=(i,u)=>{const g=i.target.files[0];if(!g)return;const M=d*3+u;if(!R(M)){const ee=E();A(Y,`You can only upload up to ${ee} image${ee!==1?"s":""} per slide based on your Slide 1 pattern`,3e3,"error");return}if(!["image/jpeg","image/png","image/gif","video/mp4","video/quicktime","video/x-m4v","application/pdf"].includes(g.type)){A(Y,"Please upload a valid file type (JPEG, PNG, GIF, MP4, or PDF)",3e3,"error");return}if(g.size>50*1024*1024){A(Y,"File size must be less than 50MB",3e3,"error");return}const U=[...k],K=URL.createObjectURL(g);let $="image";g.type.startsWith("video/")?$="video":g.type==="application/pdf"&&($="pdf"),U[M]={file:g,url:K,id:Date.now(),isDefault:!1,type:$,previewUrl:K},T(U)},ge=i=>{i.preventDefault(),i.stopPropagation()},Me=i=>{i.preventDefault(),i.stopPropagation()},Se=i=>{i.preventDefault(),i.stopPropagation()},_e=(i,u)=>{i.preventDefault(),i.stopPropagation();const g=i.dataTransfer.files;if(g.length>0){const M=d*3+u;if(!R(M)){const U=E();A(Y,`You can only upload up to ${U} image${U!==1?"s":""} per slide based on your Slide 1 pattern`,3e3,"error");return}const O={target:{files:[g[0]]}};re(O,u)}},Le=i=>{const u=d*3+i,g=[...k],M=g[u];if(M){!M.isDefault&&M.url&&URL.revokeObjectURL(M.url),g[u]=null;const O=Math.floor(u/3),U=u%3;if(O===0)for(let K=1;K<3;K++){const $=K*3+U;g[$]&&(!g[$].isDefault&&g[$].url&&URL.revokeObjectURL(g[$].url),g[$]=null)}else O===1&&U===2&&g[8]&&(!g[8].isDefault&&g[8].url&&URL.revokeObjectURL(g[8].url),g[8]=null);T(g)}},Ae=()=>{l(i=>i+1)},Oe=()=>{l(i=>Math.max(0,i-1))},Pe=()=>x,Ie=async()=>{var i,u,g,M;try{pe(!0);const O=(i=de.current)==null?void 0:i.value;if(!(O!=null&&O.trim())){A(Y,"Please enter a bio",3e3,"error");return}const U=await Promise.all(k.map(async ee=>{if(!ee)return null;if(ee.isDefault)return ee;{const je=await ue(ee.file);return je?(ee.previewUrl&&URL.revokeObjectURL(ee.previewUrl),{url:je,isDefault:!0,id:`default-${Date.now()}-${Math.random()}`,type:ee.file.type.startsWith("video/")?"video":"image"}):null}})),K={bio:O.trim(),images:U,slideshow_delay:D,button_text:((u=G.current)==null?void 0:u.value)||"Let the club know you're interested",phone:((g=se.current)==null?void 0:g.value)||"",email:((M=X.current)==null?void 0:M.value)||""},$={splash_screen:JSON.stringify(K)};C!==(t==null?void 0:t.name)&&($.name=C),S!==(t==null?void 0:t.club_logo)&&($.club_logo=S),z=="admin"||z=="admin_staff"?await me.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile-edit/${v==null?void 0:v.id}`,$,"POST"):await me.callRawAPI(`/v3/api/custom/courtmatchup/${z}/profile-edit`,$,"POST"),me.setTable("activity_logs"),await me.callRestAPI({user_id:le,activity_type:ne.club_ui,action_type:ce.UPDATE,data:JSON.stringify($),club_id:t==null?void 0:t.id,description:"Updated club splash screen"},"POST"),A(Y,"Splash screen updated",3e3,"success"),await s(z==="admin"?v==null?void 0:v.id:null)}catch(O){console.error("Submission failed:",O),A(Y,"Failed to submit. Please try again.",3e3,"error")}finally{pe(!1)}},Ze=i=>{const u=i.target.files[0];if(!u)return;if(u.size>2*1024*1024){alert("File size exceeds 2MB limit. Please choose a smaller file.");return}o(u.type);const g=new FileReader;g.onload=()=>{p(g.result),H(!0)},g.readAsDataURL(u)},$e=async i=>{P(!0),f(URL.createObjectURL(i));const u=c==="image/png",g=await ue(i);b(g),m(new File([i],`cropped_logo.${u?"png":"jpg"}`,{type:u?"image/png":"image/jpeg"})),Ue(URL.createObjectURL(i)),P(!1)};r.useEffect(()=>()=>{B&&URL.revokeObjectURL(B)},[B]);const qe=()=>{J(C||(t==null?void 0:t.name)||""),F(!0)},Je=()=>{W(q),J(q),F(!1)},We=()=>{J(C),F(!1)};return e.jsxs("div",{className:"flex max-w-xl flex-col gap-4 rounded-lg bg-white",children:[L&&e.jsx(Ye,{}),e.jsxs("div",{className:"flex items-center gap-2 rounded-lg border border-gray-200 bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-1 text-sm font-medium text-gray-700",children:"Club Landing Page URL"}),e.jsx("p",{className:"break-all text-sm text-gray-500",children:`${window.location.origin}/club/${t==null?void 0:t.id}`})]}),e.jsxs("button",{onClick:()=>{navigator.clipboard.writeText(`${window.location.origin}/club/${t==null?void 0:t.id}`),A(Y,"URL copied to clipboard!",2e3,"success")},className:"flex items-center gap-2 rounded-lg border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-50",children:[e.jsx(Xe,{className:"h-4 w-4"}),"Copy"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"mb-2 text-lg font-semibold",children:"Tell us about your club"}),e.jsxs("p",{className:"flex items-start gap-2 text-sm text-gray-500",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("span",{children:"This info will be visible to users who want to book your club. You can edit this content into your Club Panel later."})]})]}),e.jsx("div",{}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"relative  h-[100px] w-[100px] overflow-hidden rounded-lg bg-gray-100",children:e.jsx("img",{src:B||S||(t==null?void 0:t.club_logo)||"/logo.png",alt:"Club logo",className:"h-[100px] w-[100px] object-cover"})}),e.jsxs("div",{className:"w-full space-y-1",children:[e.jsx("span",{children:"Club logo"}),e.jsx("div",{className:"flex justify-between py-1 text-xs text-gray-500",children:e.jsx("span",{children:"Min 400x400px, PNG or JPEG"})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(I,{onClick:()=>{f(null),m(null)},className:"rounded-md border border-red-500 bg-white px-2 py-1 text-xs text-red-500",children:"Remove"}),e.jsx("input",{type:"file",id:"logo-upload",className:"hidden",accept:"image/**",onChange:Ze}),e.jsx(I,{onClick:()=>document.getElementById("logo-upload").click(),className:"rounded-md border border-gray-400 bg-white px-2 py-1 text-xs text-gray-600",children:"Change Logo"})]})]})]}),e.jsx("div",{className:"mt-4 flex items-center gap-4 border-y border-gray-200 py-3",children:e.jsxs("div",{className:" w-full",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Club name"}),N?e.jsxs("div",{className:"space-y-2",children:[e.jsx("input",{type:"text",value:q,onChange:i=>J(i.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-lg font-medium focus:border-blue-500 focus:outline-none",placeholder:"Enter club name"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(I,{onClick:Je,loading:Z,className:"rounded-lg bg-primaryBlue px-4 py-1 text-sm text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:We,className:"text-sm text-primaryBlue hover:underline",children:"Cancel"})]})]}):e.jsxs("div",{children:[e.jsx("p",{className:"text-lg font-medium",children:C||"Club name"}),e.jsx("button",{onClick:qe,className:"text-sm text-primaryBlue hover:underline",children:"Edit"})]})]})}),e.jsx("div",{children:e.jsx("button",{className:"underline",onClick:()=>{a(!0),j(k)},children:"Page preview"})}),e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Upload images"}),e.jsx("p",{className:"mb-2 text-sm text-gray-500",children:"JPEG, PNG, PDF, and MP4 formats, up to 50 MB. Drag and drop files or click to browse."}),d>0&&e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2",children:e.jsxs("p",{className:"text-xs text-blue-700",children:[e.jsx("strong",{children:"Note:"})," You can upload up to"," ",E()," image",E()!==1?"s":""," per slide based on your Slide 1 pattern."]})}),e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"grid grid-rows-[1fr_1fr] gap-4",children:[e.jsx("div",{className:"h-[100px] max-h-[100px]",children:(()=>{const i=Pe()[0],u=d*3+0,g=!R(u);return e.jsxs("div",{className:`relative h-full w-full rounded-xl border-2 border-dashed transition-colors ${g?"cursor-not-allowed border-gray-200 bg-gray-100":"border-gray-300 bg-white hover:border-gray-400"}`,onDragOver:g?void 0:ge,onDragEnter:g?void 0:Me,onDragLeave:g?void 0:Se,onDrop:g?void 0:M=>_e(M,0),children:[e.jsx("input",{type:"file",className:"hidden",accept:"image/*,video/*,application/pdf",onChange:M=>re(M,0),id:"file-input-0",disabled:g}),e.jsx("label",{htmlFor:g?void 0:"file-input-0",className:`absolute inset-0 ${g?"cursor-not-allowed":"cursor-pointer"}`,children:i&&i.url?e.jsxs("div",{className:"relative h-full w-full",children:[i.type==="video"?e.jsxs("video",{src:i.url,className:"h-full w-full rounded-lg object-cover",controls:!0,controlsList:"nodownload",preload:"metadata",playsInline:!0,children:[e.jsx("source",{src:i.url,type:"video/mp4"}),"Your browser does not support the video tag."]}):i.type==="pdf"?e.jsx("div",{className:"flex h-full w-full items-center justify-center rounded-lg bg-red-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-2",children:e.jsx("path",{d:"M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z",fill:"#DC2626"})}),e.jsx("p",{className:"text-xs font-medium text-red-600",children:"PDF"})]})}):e.jsx("img",{src:i.url,alt:"Upload 1",className:"h-full w-full rounded-lg object-cover"}),e.jsx("button",{onClick:M=>{M.preventDefault(),M.stopPropagation(),Le(0)},className:"absolute right-2 top-2 rounded-full bg-white p-1.5 shadow-md hover:bg-gray-100",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"black"})})}),i.type==="video"&&e.jsx("div",{className:"absolute left-2 top-2 rounded-full bg-white/80 p-1.5",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5V7.5L16 12L10 16.5Z",fill:"#176448"})})})]}):e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:g?e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-1",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z",fill:"#9CA3AF"})}),e.jsx("p",{className:"text-xs text-gray-400",children:"Disabled"})]}):e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.9998 12.5274L15.8185 16.3452L14.545 17.6187L12.8998 15.9735V21H11.0998V15.9717L9.45461 17.6187L8.18111 16.3452L11.9998 12.5274ZM11.9998 3C13.5451 3.00007 15.0365 3.568 16.1904 4.59581C17.3443 5.62361 18.0803 7.03962 18.2584 8.5746C19.3782 8.87998 20.3552 9.56919 21.0184 10.5218C21.6816 11.4744 21.989 12.6297 21.8869 13.786C21.7847 14.9422 21.2794 16.0257 20.4594 16.8472C19.6394 17.6687 18.5567 18.1759 17.4007 18.2802V16.4676C17.8149 16.4085 18.2131 16.2674 18.5721 16.0527C18.9312 15.8379 19.2439 15.5539 19.4919 15.217C19.74 14.8801 19.9184 14.4972 20.0169 14.0906C20.1153 13.6839 20.1318 13.2618 20.0653 12.8488C19.9989 12.4357 19.8508 12.0401 19.6298 11.6849C19.4087 11.3297 19.1191 11.0221 18.7779 10.78C18.4367 10.538 18.0506 10.3663 17.6424 10.2751C17.2341 10.1838 16.8117 10.1748 16.3999 10.2486C16.5409 9.5924 16.5332 8.91297 16.3776 8.2601C16.222 7.60722 15.9223 6.99743 15.5004 6.47538C15.0786 5.95333 14.5454 5.53225 13.9397 5.24298C13.3341 4.9537 12.6714 4.80357 12.0003 4.80357C11.3291 4.80357 10.6664 4.9537 10.0608 5.24298C9.45515 5.53225 8.92189 5.95333 8.50007 6.47538C8.07825 6.99743 7.77854 7.60722 7.62291 8.2601C7.46728 8.91297 7.45966 9.5924 7.60061 10.2486C6.7795 10.0944 5.93076 10.2727 5.24112 10.7443C4.55147 11.2159 4.0774 11.9421 3.92321 12.7632C3.76901 13.5843 3.94731 14.433 4.41889 15.1227C4.89047 15.8123 5.6167 16.2864 6.43781 16.4406L6.59981 16.4676V18.2802C5.44371 18.1761 4.36097 17.669 3.54083 16.8476C2.72068 16.0261 2.2153 14.9426 2.11301 13.7863C2.01073 12.6301 2.31804 11.4747 2.98124 10.522C3.64444 9.56934 4.62134 8.88005 5.74121 8.5746C5.91914 7.03954 6.65507 5.62342 7.80903 4.59558C8.96298 3.56774 10.4545 2.99988 11.9998 3Z",fill:"#525866"})})})})]},`${d}-0`)})()}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:[1,2].map(i=>{const u=Pe()[i],g=d*3+i,M=!R(g);return e.jsxs("div",{className:`relative h-[100px] max-h-[100px] w-full rounded-xl border-2 border-dashed transition-colors ${M?"cursor-not-allowed border-gray-200 bg-gray-100":"border-gray-300 bg-white hover:border-gray-400"}`,onDragOver:M?void 0:ge,onDragEnter:M?void 0:Me,onDragLeave:M?void 0:Se,onDrop:M?void 0:O=>_e(O,i),children:[e.jsx("input",{type:"file",className:"hidden",accept:"image/*,video/*,application/pdf",onChange:O=>re(O,i),id:`file-input-${i}`,disabled:M}),e.jsx("label",{htmlFor:M?void 0:`file-input-${i}`,className:`absolute inset-0 ${M?"cursor-not-allowed":"cursor-pointer"}`,children:u&&u.url?e.jsxs("div",{className:"relative h-full w-full",children:[u.type==="video"?e.jsxs("video",{src:u.url,className:"h-full w-full rounded-lg object-cover",controls:!0,controlsList:"nodownload",preload:"metadata",playsInline:!0,children:[e.jsx("source",{src:u.url,type:"video/mp4"}),"Your browser does not support the video tag."]}):u.type==="pdf"?e.jsx("div",{className:"flex h-full w-full items-center justify-center rounded-lg bg-red-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-2",children:e.jsx("path",{d:"M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z",fill:"#DC2626"})}),e.jsx("p",{className:"text-xs font-medium text-red-600",children:"PDF"})]})}):e.jsx("img",{src:u.url,alt:`Upload ${i+1}`,className:"h-full w-full rounded-lg object-cover"}),e.jsx("button",{onClick:O=>{O.preventDefault(),O.stopPropagation(),Le(i)},className:"absolute right-2 top-2 rounded-full bg-white p-1.5 shadow-md hover:bg-gray-100",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"black"})})}),u.type==="video"&&e.jsx("div",{className:"absolute left-2 top-2 rounded-full bg-white/80 p-1.5",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5V7.5L16 12L10 16.5Z",fill:"#176448"})})})]}):e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:M?e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-1",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z",fill:"#9CA3AF"})}),e.jsx("p",{className:"text-xs text-gray-400",children:"Disabled"})]}):e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.9998 12.5274L15.8185 16.3452L14.545 17.6187L12.8998 15.9735V21H11.0998V15.9717L9.45461 17.6187L8.18111 16.3452L11.9998 12.5274ZM11.9998 3C13.5451 3.00007 15.0365 3.568 16.1904 4.59581C17.3443 5.62361 18.0803 7.03962 18.2584 8.5746C19.3782 8.87998 20.3552 9.56919 21.0184 10.5218C21.6816 11.4744 21.989 12.6297 21.8869 13.786C21.7847 14.9422 21.2794 16.0257 20.4594 16.8472C19.6394 17.6687 18.5567 18.1759 17.4007 18.2802V16.4676C17.8149 16.4085 18.2131 16.2674 18.5721 16.0527C18.9312 15.8379 19.2439 15.5539 19.4919 15.217C19.74 14.8801 19.9184 14.4972 20.0169 14.0906C20.1153 13.6839 20.1318 13.2618 20.0653 12.8488C19.9989 12.4357 19.8508 12.0401 19.6298 11.6849C19.4087 11.3297 19.1191 11.0221 18.7779 10.78C18.4367 10.538 18.0506 10.3663 17.6424 10.2751C17.2341 10.1838 16.8117 10.1748 16.3999 10.2486C16.5409 9.5924 16.5332 8.91297 16.3776 8.2601C16.222 7.60722 15.9223 6.99743 15.5004 6.47538C15.0786 5.95333 14.5454 5.53225 13.9397 5.24298C13.3341 4.9537 12.6714 4.80357 12.0003 4.80357C11.3291 4.80357 10.6664 4.9537 10.0608 5.24298C9.45515 5.53225 8.92189 5.95333 8.50007 6.47538C8.07825 6.99743 7.77854 7.60722 7.62291 8.2601C7.46728 8.91297 7.45966 9.5924 7.60061 10.2486C6.7795 10.0944 5.93076 10.2727 5.24112 10.7443C4.55147 11.2159 4.0774 11.9421 3.92321 12.7632C3.76901 13.5843 3.94731 14.433 4.41889 15.1227C4.89047 15.8123 5.6167 16.2864 6.43781 16.4406L6.59981 16.4676V18.2802C5.44371 18.1761 4.36097 17.669 3.54083 16.8476C2.72068 16.0261 2.2153 14.9426 2.11301 13.7863C2.01073 12.6301 2.31804 11.4747 2.98124 10.522C3.64444 9.56934 4.62134 8.88005 5.74121 8.5746C5.91914 7.03954 6.65507 5.62342 7.80903 4.59558C8.96298 3.56774 10.4545 2.99988 11.9998 3Z",fill:"#525866"})})})})]},`${d}-${i}`)})})]},d),e.jsxs("div",{className:"mt-4 flex items-center justify-center gap-4",children:[e.jsx("button",{onClick:Oe,disabled:d===0,className:`rounded-full bg-white p-2 shadow-md ${d===0?"cursor-not-allowed opacity-50":"hover:bg-gray-100"}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})}),e.jsxs("span",{className:"text-sm",children:["Slide ",d+1]}),e.jsx("button",{onClick:Ae,disabled:d===2,className:`rounded-full bg-white p-2 shadow-md ${d===2?"cursor-not-allowed opacity-50":"hover:bg-gray-100"}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})})]})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Bio"}),e.jsx("textarea",{ref:de,rows:4,className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Slideshow Delay (seconds)"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"number",min:"1",max:"60",value:D/1e3,onChange:i=>xe(Math.max(1e3,Math.min(6e4,i.target.value*1e3))),className:"w-24 rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"}),e.jsx("span",{className:"text-sm text-gray-500",children:"seconds"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose between 1 and 60 seconds (default: 6 seconds)"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Button Text"}),e.jsx("input",{type:"text",ref:G,defaultValue:(n==null?void 0:n.button_text)||"Let the club know you're interested",className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter button text"})]}),e.jsxs("div",{className:"mb-4 space-y-4",children:[e.jsx("h3",{className:"text-sm font-medium",children:"Contact Information"}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm text-gray-600",children:"Phone Number"}),e.jsx("input",{type:"tel",ref:se,defaultValue:(n==null?void 0:n.phone)||"",className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter phone number"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm text-gray-600",children:"Email Address"}),e.jsx("input",{type:"email",ref:X,defaultValue:(n==null?void 0:n.email)||"",className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter email address"})]})]}),e.jsx(I,{onClick:Ie,loading:he,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Save changes"}),e.jsx(fe,{isOpen:Ne,onClose:()=>ie(!1),title:"Preview",children:e.jsx(ke,{clubName:t==null?void 0:t.name,image:t==null?void 0:t.name,description:(De=de.current)==null?void 0:De.value,imageList:k,clubLogo:S,slideshowDelay:D,buttonText:((Be=G.current)==null?void 0:Be.value)||"Let the club know you're interested",phone:((Te=se.current)==null?void 0:Te.value)||"",email:((Ve=X.current)==null?void 0:Ve.value)||""})}),e.jsx(Qe,{isOpen:_,onClose:()=>H(!1),image:h,onCropComplete:$e})]})}let be=new ae;function xs({clubUser:s,onClose:a,fetchSettings:j}){const[t,v]=r.useState(!1),[d,l]=r.useState(null),{club:x}=ve(),w=localStorage.getItem("user"),B=(()=>{try{return JSON.parse((x==null?void 0:x.lesson_description)||'{"reservation_description":"","payment_description":""}')}catch{return{reservation_description:"",payment_description:""}}})(),[f,V]=r.useState(B.reservation_description),[m,n]=r.useState(B.payment_description),[S,b]=r.useState(f),[L,P]=r.useState(m),_=localStorage.getItem("role"),H=c=>{l(c)},h=async()=>{try{v(!0);const c={reservation_description:S,payment_description:L},o=await be.callRawAPI(_==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${s==null?void 0:s.id}`:"/v3/api/custom/courtmatchup/club/profile-edit",{lesson_description:JSON.stringify(c)},"POST");be.setTable("activity_logs"),await be.callRestAPI({user_id:w,activity_type:ne.club_ui,action_type:ce.UPDATE,data:JSON.stringify(c),club_id:x==null?void 0:x.id,description:"Updated lesson booking descriptions"},"POST"),V(S),n(L),l(null),v(!1),j(_==="admin"?s==null?void 0:s.id:null)}catch(c){console.log(c),v(!1)}},p=()=>{b(f),P(m),l(null)};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Reservation Description"}),d!=="reservation"&&e.jsx("button",{onClick:()=>H("reservation"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:f?"Edit":"Add Description"})]}),d==="reservation"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:S,onChange:c=>b(c.target.value),placeholder:"Enter description for reservation screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:h,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:p,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:f||"No reservation description added yet."})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Payment Description"}),d!=="payment"&&e.jsx("button",{onClick:()=>H("payment"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:m?"Edit":"Add Description"})]}),d==="payment"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:L,onChange:c=>P(c.target.value),placeholder:"Enter description for payment screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:h,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:p,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:m||"No payment description added yet."})]})]})]})})}let we=new ae;function ms({clubUser:s,onClose:a,fetchSettings:j,club:t}){const[v,d]=r.useState(!1),[l,x]=r.useState(null),{dispatch:w}=r.useContext(oe),B=localStorage.getItem("user"),f=(()=>{try{return JSON.parse((t==null?void 0:t.clinic_description)||'{"reservation_description":"","payment_description":"","default_view":"table","custom_filters":[]}')}catch{return{reservation_description:"",payment_description:"",default_view:"table",custom_filters:[]}}})(),[V,m]=r.useState(f.reservation_description),[n,S]=r.useState(f.payment_description),[b,L]=r.useState(f.default_view||"table"),[P,_]=r.useState(f.custom_filters||[]),[H,h]=r.useState(V),[p,c]=r.useState(n),[o,N]=r.useState(b),[F,Z]=r.useState(P),Q=localStorage.getItem("role"),C=y=>{x(y)},W=async()=>{try{d(!0);const y={reservation_description:H,payment_description:p,default_view:o,custom_filters:F},k=await we.callRawAPI(Q==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${s==null?void 0:s.id}`:"/v3/api/custom/courtmatchup/club/profile-edit",{clinic_description:JSON.stringify(y)},"POST");we.setTable("activity_logs"),await we.callRestAPI({user_id:B,activity_type:ne.club_ui,action_type:ce.UPDATE,data:JSON.stringify(y),club_id:t==null?void 0:t.id,description:"Updated clinic booking descriptions"},"POST"),A(w,"Descriptions updated successfully",3e3,"success"),m(H),S(p),L(o),_(F),x(null),d(!1),j(Q==="admin"?s==null?void 0:s.id:null)}catch(y){console.log(y),d(!1)}},q=()=>{h(V),c(n),N(b),Z(P),x(null)},J=y=>{N(y)},z=[{key:"clinic_level",label:"Clinic Level"},{key:"max_participants",label:"Max Participants"},{key:"clinic_cost",label:"Clinic Cost"},{key:"recurring",label:"Recurring"},{key:"sport_name",label:"Sport"},{key:"type",label:"Type"},{key:"sub_type",label:"Sub Type"},{key:"clinic_day",label:"Day of Week"},{key:"time_of_day",label:"Time of Day"}],le=()=>{const y={id:Date.now(),key:"",label:"",enabled:!0};Z([...F,y])},G=(y,k)=>{const T=z.find(D=>D.key===k);Z(F.map(D=>D.id===y?{...D,key:k,label:(T==null?void 0:T.label)||""}:D))},se=y=>{Z(F.filter(k=>k.id!==y))},X=y=>{Z(F.map(k=>k.id===y?{...k,enabled:!k.enabled}:k))};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Reservation Description"}),l!=="reservation"&&e.jsx("button",{onClick:()=>C("reservation"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:V?"Edit":"Add Description"})]}),l==="reservation"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:H,onChange:y=>h(y.target.value),placeholder:"Enter description for reservation screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:W,loading:v,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:q,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:V||"No reservation description added yet."})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Payment Description"}),l!=="payment"&&e.jsx("button",{onClick:()=>C("payment"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:n?"Edit":"Add Description"})]}),l==="payment"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("textarea",{value:p,onChange:y=>c(y.target.value),placeholder:"Enter description for payment screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:W,loading:v,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:q,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:n||"No payment description added yet."})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Default View"}),l!=="default_view"&&e.jsx("button",{onClick:()=>C("default_view"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:"Edit"})]}),l==="default_view"?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"table",checked:o==="table",onChange:()=>J("table"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Table"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"calendar",checked:o==="calendar",onChange:()=>J("calendar"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Calendar"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"weekly",checked:o==="weekly",onChange:()=>J("weekly"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Weekly"})]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:W,loading:v,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:q,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:b==="table"?"Table":b==="calendar"?"Calendar":b==="weekly"?"Weekly":"Table"})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:"Custom Filters"}),l!=="custom_filters"&&e.jsx("button",{onClick:()=>C("custom_filters"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:P.length>0?"Edit Filters":"Add Filters"})]}),l==="custom_filters"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"space-y-4",children:F.map(y=>e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-2",children:[e.jsxs("select",{value:y.key,onChange:k=>G(y.id,k.target.value),className:"flex-1 rounded-lg border border-gray-300 px-3 py-2",children:[e.jsx("option",{value:"",children:"Select a clinic property"}),z.filter(k=>!F.some(T=>T.id!==y.id&&T.key===k.key)).map(k=>e.jsx("option",{value:k.key,children:k.label},k.key))]}),e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:y.enabled,onChange:()=>X(y.id),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-700",children:"Enabled"})]}),e.jsx("button",{onClick:()=>se(y.id),className:"rounded-lg bg-red-500 px-3 py-2 text-white hover:bg-red-600",children:"Delete"})]}),y.key&&e.jsxs("div",{className:"text-sm text-gray-600",children:[e.jsx("strong",{children:"Filter:"})," ",y.label,e.jsx("br",{}),e.jsx("strong",{children:"Property:"})," ",y.key]})]},y.id))}),e.jsx("button",{onClick:le,className:"rounded-lg border border-gray-300 px-4 py-2 text-gray-700 hover:bg-gray-50",children:"+ Add Filter Property"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:W,loading:v,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:q,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("div",{className:"text-gray-700",children:P.length>0?e.jsx("div",{className:"space-y-2",children:P.map(y=>e.jsxs("div",{children:[e.jsxs("strong",{children:[y.label,":"]})," ",e.jsx("span",{className:y.enabled?"text-green-600":"text-red-600",children:y.enabled?"Enabled":"Disabled"}),e.jsx("br",{}),e.jsxs("span",{className:"text-sm text-gray-500",children:["Property: ",y.key]})]},y.id))}):"No custom filters configured yet."})]})]})]})})}let Re=new ae;function hs({clubUser:s,onClose:a,fetchSettings:j}){const[t,v]=r.useState(!1),[d,l]=r.useState(null),{club:x}=ve(),w=r.useContext(oe),B=localStorage.getItem("user"),f=(()=>{try{return JSON.parse((x==null?void 0:x.coach_description)||'{"reservation_description":"","payment_description":""}')}catch{return{reservation_description:"",payment_description:""}}})(),[V,m]=r.useState(f.reservation_description),[n,S]=r.useState(f.payment_description),[b,L]=r.useState(V),[P,_]=r.useState(n),H=localStorage.getItem("role"),h=o=>{l(o)},p=async()=>{try{v(!0);const o={reservation_description:b,payment_description:P},N=await Re.callRawAPI(H==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${s==null?void 0:s.id}`:"/v3/api/custom/courtmatchup/club/profile-edit",{coach_description:JSON.stringify(o)},"POST");await Ee(Re,{user_id:B,activity_type:ne.club_ui,action_type:ce.UPDATE,data:o,club_id:x==null?void 0:x.id,description:"Updated coach booking descriptions"}),A(w,"Description saved successfully",3e3,"success"),m(b),S(P),l(null),v(!1),j(H==="admin"?s==null?void 0:s.id:null)}catch(o){console.log(o),v(!1)}},c=()=>{L(V),_(n),l(null)};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),!d&&e.jsx("button",{onClick:()=>h("reservation"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:V?"Edit":"Add Reservation Description"})]}),d==="reservation"?e.jsxs(e.Fragment,{children:[e.jsx("textarea",{value:b,onChange:o=>L(o.target.value),placeholder:"Enter description for reservation screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:p,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:c,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:V||"No reservation description added yet."}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),!d&&e.jsx("button",{onClick:()=>h("payment"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:n?"Edit":"Add Payment Description"})]}),d==="payment"?e.jsxs(e.Fragment,{children:[e.jsx("textarea",{value:P,onChange:o=>_(o.target.value),placeholder:"Enter description for payment screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:p,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:c,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:n||"No payment description added yet."})]})})}let Ce=new ae;function ps({clubUser:s,onClose:a,fetchSettings:j}){const[t,v]=r.useState(!1),[d,l]=r.useState(null),{dispatch:x}=r.useContext(oe),{club:w}=ve(),B=localStorage.getItem("user"),f=(()=>{try{return JSON.parse((w==null?void 0:w.buddy_description)||'{"reservation_description":"","payment_description":"","default_view":"table"}')}catch{return{reservation_description:"",payment_description:"",default_view:"table"}}})(),[V,m]=r.useState(f.reservation_description),[n,S]=r.useState(f.payment_description),[b,L]=r.useState(f.default_view||"table"),[P,_]=r.useState(V),[H,h]=r.useState(n),[p,c]=r.useState(b),o=localStorage.getItem("role"),N=C=>{l(C)},F=async()=>{try{v(!0);const C={reservation_description:P,payment_description:H,default_view:p},W=await Ce.callRawAPI(o==="admin"?`/v3/api/custom/courtmatchup/admin/profile-edit/${s==null?void 0:s.id}`:"/v3/api/custom/courtmatchup/club/profile-edit",{buddy_description:JSON.stringify(C)},"POST");Ce.setTable("activity_logs"),await Ce.callRestAPI({user_id:B,activity_type:ne.club_ui,action_type:ce.UPDATE,data:JSON.stringify(C),club_id:w==null?void 0:w.id,description:"Updated find a buddy booking descriptions"},"POST"),A(x,"Descriptions updated successfully",3e3,"success"),m(P),S(H),L(p),l(null),v(!1),j(o==="admin"?s==null?void 0:s.id:null)}catch(C){console.log(C),v(!1)}},Z=()=>{_(V),h(n),c(b),l(null)},Q=C=>{c(C)};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Descriptions"}),!d&&e.jsx("button",{onClick:()=>N("reservation"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:V?"Edit":"Add Description"})]}),d==="reservation"?e.jsxs(e.Fragment,{children:[e.jsx("textarea",{value:P,onChange:C=>_(C.target.value),placeholder:"Enter description for reservation screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:F,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:Z,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:V||"No reservation description added yet."}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Payment Description"}),!d&&e.jsx("button",{onClick:()=>N("payment"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:n?"Edit":"Add Description"})]}),d==="payment"?e.jsxs(e.Fragment,{children:[e.jsx("textarea",{value:H,onChange:C=>h(C.target.value),placeholder:"Enter description for payment screen...",className:"min-h-[110px] w-full rounded-xl border border-gray-300 p-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:F,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:Z,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:n||"No payment description added yet."}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Default View"}),!d&&e.jsx("button",{onClick:()=>N("default_view"),className:"font-medium text-primaryBlue underline hover:text-blue-800",children:"Edit"})]}),d==="default_view"?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"table",checked:p==="table",onChange:()=>Q("table"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Table"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"calendar",checked:p==="calendar",onChange:()=>Q("calendar"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Calendar"})]}),e.jsxs("label",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"radio",name:"defaultView",value:"weekly",checked:p==="weekly",onChange:()=>Q("weekly"),className:"h-4 w-4 text-primaryBlue"}),e.jsx("span",{children:"Weekly"})]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(I,{onClick:F,loading:t,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:Z,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"})]})]}):e.jsx("p",{className:"text-gray-700",children:b==="table"?"Table":b==="calendar"?"Calendar":b==="weekly"?"Weekly":"Table"})]})})}function us({clubName:s,description:a,title:j,image:t}){return e.jsxs("div",{className:"flex min-h-screen flex-col bg-white",children:[e.jsxs("header",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-2xl",children:"🎾"}),e.jsx("span",{className:"text-xl font-semibold",children:s})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{className:"flex items-center gap-2 rounded-xl bg-primaryBlue px-4 py-2 text-white",children:[e.jsx("span",{children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M14.375 5.62502V3.12502C14.375 2.66478 14.0019 2.29169 13.5417 2.29169H3.12502C2.66478 2.29169 2.29169 2.66478 2.29169 3.12502V13.5417C2.29169 14.0019 2.66478 14.375 3.12502 14.375H5.62502M7.77191 17.7084C8.15596 15.8035 9.75384 14.375 11.6667 14.375C13.5795 14.375 15.1774 15.8035 15.5615 17.7084M7.77191 17.7084H6.45835C5.99812 17.7084 5.62502 17.3353 5.62502 16.875V6.45835C5.62502 5.99812 5.99812 5.62502 6.45835 5.62502H16.875C17.3353 5.62502 17.7084 5.99812 17.7084 6.45835V16.875C17.7084 17.3353 17.3353 17.7084 16.875 17.7084H15.5615M7.77191 17.7084H15.5615M13.5417 10.4167C13.5417 11.4522 12.7022 12.2917 11.6667 12.2917C10.6312 12.2917 9.79169 11.4522 9.79169 10.4167C9.79169 9.38115 10.6312 8.54169 11.6667 8.54169C12.7022 8.54169 13.5417 9.38115 13.5417 10.4167Z",stroke:"white","stroke-width":"1.5","stroke-linecap":"square","stroke-linejoin":"round"})})}),e.jsx("span",{children:"Sign up"})]}),e.jsxs("button",{className:"flex items-center rounded-xl border border-gray-300 px-4 py-2 text-gray-500",children:[e.jsx("span",{children:"Log in"}),e.jsx("span",{children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12.2917 3.125L16.0417 3.125C16.5019 3.125 16.875 3.4981 16.875 3.95833V16.0417C16.875 16.5019 16.5019 16.875 16.0417 16.875H12.2917M12.5 10H3.125M12.5 10L9.58333 12.9167M12.5 10L9.58333 7.08334",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]})]})]}),e.jsxs("main",{className:"flex flex-col gap-8 p-4 md:flex-row md:p-8",children:[e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"grid w-full",children:e.jsx("div",{className:" aspect-[4/3] overflow-hidden rounded-lg bg-gray-100",children:e.jsx("img",{src:t||"",alt:`Home logged in page preview of ${s}`,className:"h-full w-full object-cover"})})})}),e.jsxs("div",{className:"flex-1 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h1",{className:"text-2xl font-medium",children:j||"Welcome!"}),e.jsxs("button",{className:"flex items-center gap-2 text-gray-600",children:[e.jsx("span",{children:"Visit website"}),e.jsx("svg",{width:"21",height:"20",viewBox:"0 0 21 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.2958 9.99926L7.58331 6.28676L8.64381 5.22626L13.4168 9.99926L8.64381 14.7723L7.58331 13.7118L11.2958 9.99926Z",fill:"#525866"})})]})]}),e.jsx("div",{className:"max-h-[400px] space-y-4 overflow-y-auto",children:e.jsx("p",{className:"text-gray-600",children:a})}),e.jsxs("div",{className:"mt-8 rounded-xl p-4 shadow-5",children:[e.jsx("button",{className:"w-full rounded-xl bg-[#176448] py-2 text-white",children:e.jsxs("span",{className:"flex items-center justify-center gap-2",children:[e.jsx("span",{className:"calendar-icon",children:e.jsxs("svg",{width:"25",height:"25",viewBox:"0 0 25 25",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M16 3.5H9V4.75C9 5.16421 8.66421 5.5 8.25 5.5C7.83579 5.5 7.5 5.16421 7.5 4.75V3.5H5.25C4.2835 3.5 3.5 4.2835 3.5 5.25V8H21.5V5.25C21.5 4.2835 20.7165 3.5 19.75 3.5H17.5V4.75C17.5 5.16421 17.1642 5.5 16.75 5.5C16.3358 5.5 16 5.16421 16 4.75V3.5Z",fill:"white"}),e.jsx("path",{d:"M21.5 9.5H3.5V19.75C3.5 20.7165 4.2835 21.5 5.25 21.5H19.75C20.7165 21.5 21.5 20.7165 21.5 19.75V9.5Z",fill:"white"})]})}),"Reserve your slot"]})}),e.jsx("p",{className:"mt-5 text-center text-sm text-gray-500",children:"TAKES ONLY 2 MINUTES!"})]})]})]}),e.jsx("footer",{className:"p-4 text-center text-sm text-gray-500",children:"Powered by Court Matchup"})]})}let ye=new ae;function gs({fetchSettings:s,profileSettings:a,onSubmit:j,isOpen:t,onClose:v}){var W,q,J,z,le,G,se,X,y,k;const[d,l]=r.useState(!1),{dispatch:x}=r.useContext(oe),[w,B]=r.useState(""),[f,V]=r.useState(""),[m,n]=r.useState(((W=a==null?void 0:a.club)==null?void 0:W.image)||null),[S,b]=r.useState(!1),{club:L}=ve(),P=localStorage.getItem("user"),[_,H]=r.useState({findBuddy:((q=a==null?void 0:a.club)==null?void 0:q.show_buddy)===1,clinic:((J=a==null?void 0:a.club)==null?void 0:J.show_clinic)===1,coach:((z=a==null?void 0:a.club)==null?void 0:z.show_coach)===1,groups:((le=a==null?void 0:a.club)==null?void 0:le.show_groups)===1,court:((G=a==null?void 0:a.club)==null?void 0:G.show_court)===1}),[h,p]=r.useState(!0),[c,o]=r.useState(!1);r.useEffect(()=>{a!=null&&a.features&&H(a.features),a!=null&&a.club&&(B(a.club.title||""),V(a.club.description||""),a.club.home_image&&n({file:null,url:a.club.home_image}))},[a]),r.useEffect(()=>{(a==null?void 0:a.published)!==void 0&&p(a.published)},[a]);const N=T=>{const D=T.target.files[0];if(!D)return;if(!["image/jpeg","image/png","image/gif"].includes(D.type)){A(x,"Please upload a valid image file (JPEG, PNG, or GIF)",3e3,"warning");return}if(D.size>50*1024*1024){A(x,"File size must be less than 50MB",3e3,"warning");return}n({file:D,url:URL.createObjectURL(D)})},F=()=>{m!=null&&m.url&&URL.revokeObjectURL(m.url),n(null)},Z=async T=>{try{let D=new FormData;return D.append("file",T),(await ye.uploadImage(D)).url}catch(D){return console.log(D),null}},Q=async()=>{try{if(l(!0),!(w!=null&&w.trim()))return A(x,"Please enter a title",3e3,"warning"),l(!1),!1;if(!(f!=null&&f.trim()))return A(x,"Please enter content",3e3,"warning"),l(!1),!1;let T=null;m!=null&&m.file&&(T=await Z(m.file));const D={title:w.trim(),description:f.trim(),home_image:T,published:h,show_clinic:_.clinic?1:0,show_coach:_.coach?1:0,show_groups:_.groups?1:0,show_court:_.court?1:0,show_buddy:_.findBuddy?1:0};return await ye.callRawAPI("/v3/api/custom/courtmatchup/club/profile-edit",{...D},"POST"),ye.setTable("activity_logs"),await ye.callRestAPI({user_id:P,activity_type:ne.club_ui,action_type:ce.UPDATE,data:JSON.stringify(D),club_id:L==null?void 0:L.id,description:"Updated club homepage"},"POST"),A(x,"Home content updated",3e3,"success"),s(),!0}catch(T){return console.error("Submission failed:",T),A(x,"Failed to submit. Please try again.",3e3,"warning"),!1}finally{l(!1)}},C=T=>{H(D=>({...D,[T]:!D[T]}))};return e.jsxs("div",{children:[e.jsx(te,{isOpen:t,onClose:v,title:"Home logged in",showFooter:!1,children:e.jsxs("div",{className:"flex flex-col gap-4 rounded-lg bg-white py-5",children:[e.jsx("button",{className:"text-left underline",onClick:()=>b(!0),children:"Page preview"}),e.jsx("div",{className:"mb-4",children:e.jsxs("p",{className:"flex items-start gap-2 text-sm text-gray-500",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("span",{children:"This content will be shown at the top of homepage for logged-in users."})]})}),e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Features shown"}),e.jsx("p",{className:"mb-4 text-sm text-gray-500",children:"You can control which features are shown on the homepage:"}),e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:_.findBuddy,onChange:()=>C("findBuddy"),className:"h-5 w-5 rounded-md"}),e.jsx("span",{children:"Find Buddy"})]}),e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:_.coach,onChange:()=>C("coach"),className:"h-5 w-5 rounded-md"}),e.jsx("span",{children:"Coach"})]}),e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:_.groups,onChange:()=>C("groups"),className:"h-5 w-5 rounded-md"}),e.jsx("span",{children:"Groups"})]}),e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:_.court,onChange:()=>C("court"),className:"h-5 w-5 rounded-md"}),e.jsx("span",{children:"Court"})]}),e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:_.clinic,onChange:()=>C("clinic"),className:"h-5 w-5 rounded-md"}),e.jsx("span",{children:"Clinic"})]})]})]}),e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Notification"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("button",{onClick:()=>p(!h),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out ${h?"bg-blue-500":"bg-gray-200"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out ${h?"translate-x-6":"translate-x-1"}`})}),e.jsx("span",{className:"text-sm",children:h?"Published":"Not published"}),e.jsxs("div",{className:"relative",children:[e.jsx("span",{onMouseEnter:()=>o(!0),onMouseLeave:()=>o(!1),className:"inline-block cursor-help",children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M8.95768 9.16602H9.99934L9.99935 13.541M17.7077 9.99935C17.7077 14.2565 14.2565 17.7077 9.99935 17.7077C5.74215 17.7077 2.29102 14.2565 2.29102 9.99935C2.29102 5.74215 5.74215 2.29102 9.99935 2.29102C14.2565 2.29102 17.7077 5.74215 17.7077 9.99935ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M10.0007 6.125C9.7015 6.125 9.45898 6.36751 9.45898 6.66667C9.45898 6.96582 9.7015 7.20833 10.0007 7.20833C10.2998 7.20833 10.5423 6.96582 10.5423 6.66667C10.5423 6.36751 10.2998 6.125 10.0007 6.125Z",fill:"#868C98",stroke:"#868C98",strokeWidth:"0.25"})]})}),c&&e.jsx("div",{className:"absolute bottom-full left-[-50px] mb-2 w-[230px]",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"rounded-lg bg-white p-3 text-sm text-gray-900 shadow-lg",children:"If button is OFF and save changes is pressed, any changes will be saved as a draft. If button is ON and save changed is pressed, changes will be published to the Home Screen."}),e.jsx("div",{className:"absolute left-[40px] top-full h-2 w-2 -translate-y-1/2 rotate-45 transform bg-white shadow-lg"})]})})]})]})]}),e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Image (optional)"}),e.jsx("p",{className:"mb-2 text-sm text-gray-500",children:"JPEG, PNG formats, up to 50 MB."}),e.jsxs("div",{className:"relative h-[200px] w-full rounded-xl border-2 border-dashed border-gray-300 hover:border-gray-400",children:[e.jsx("input",{type:"file",className:"hidden",accept:"image/*",onChange:N,id:"file-input"}),e.jsx("label",{htmlFor:"file-input",className:"absolute inset-0 cursor-pointer",children:m?e.jsxs("div",{className:"relative h-full w-full",children:[e.jsx("img",{src:m.url,alt:"Upload preview",className:"h-full w-full rounded-lg object-cover"}),e.jsx("button",{onClick:T=>{T.preventDefault(),F()},className:"absolute right-2 top-2 rounded-full bg-white p-1.5 shadow-md hover:bg-gray-100",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627Z",fill:"black"})})})]}):e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.9998 12.5274L15.8185 16.3452L14.545 17.6187L12.8998 15.9735V21H11.0998V15.9717L9.45461 17.6187L8.18111 16.3452L11.9998 12.5274ZM11.9998 3C13.5451 3.00007 15.0365 3.568 16.1904 4.59581C17.3443 5.62361 18.0803 7.03962 18.2584 8.5746C19.3782 8.87998 20.3552 9.56919 21.0184 10.5218C21.6816 11.4744 21.989 12.6297 21.8869 13.786C21.7847 14.9422 21.2794 16.0257 20.4594 16.8472C19.6394 17.6687 18.5567 18.1759 17.4007 18.2802V16.4676C17.8149 16.4085 18.2131 16.2674 18.5721 16.0527C18.9312 15.8379 19.2439 15.5539 19.4919 15.217C19.74 14.8801 19.9184 14.4972 20.0169 14.0906C20.1153 13.6839 20.1318 13.2618 20.0653 12.8488C19.9989 12.4357 19.8508 12.0401 19.6298 11.6849C19.4087 11.3297 19.1191 11.0221 18.7779 10.78C18.4367 10.538 18.0506 10.3663 17.6424 10.2751C17.2341 10.1838 16.8117 10.1748 16.3999 10.2486C16.5409 9.5924 16.5332 8.91297 16.3776 8.2601C16.222 7.60722 15.9223 6.99743 15.5004 6.47538C15.0786 5.95333 14.5454 5.53225 13.9397 5.24298C13.3341 4.9537 12.6714 4.80357 12.0003 4.80357C11.3291 4.80357 10.6664 4.9537 10.0608 5.24298C9.45515 5.53225 8.92189 5.95333 8.50007 6.47538C8.07825 6.99743 7.77854 7.60722 7.62291 8.2601C7.46728 8.91297 7.45966 9.5924 7.60061 10.2486C6.7795 10.0944 5.93076 10.2727 5.24112 10.7443C4.55147 11.2159 4.0774 11.9421 3.92321 12.7632C3.76901 13.5843 3.94731 14.433 4.41889 15.1227C4.89047 15.8123 5.6167 16.2864 6.43781 16.4406L6.59981 16.4676V18.2802C5.44371 18.1761 4.36097 17.669 3.54083 16.8476C2.72068 16.0261 2.2153 14.9426 2.11301 13.7863C2.01073 12.6301 2.31804 11.4747 2.98124 10.522C3.64444 9.56934 4.62134 8.88005 5.74121 8.5746C5.91914 7.03954 6.65507 5.62342 7.80903 4.59558C8.96298 3.56774 10.4545 2.99988 11.9998 3Z",fill:"#525866"})})})})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Title"}),e.jsx("input",{value:w,onChange:T=>B(T.target.value),type:"text",placeholder:"Enter title...",className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Content"}),e.jsx("textarea",{value:f,onChange:T=>V(T.target.value),rows:4,placeholder:"Enter content...",className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"})]}),e.jsx("div",{className:"flex justify-end",children:e.jsx(I,{loading:d,onClick:Q,className:"rounded-lg bg-primaryBlue px-4 py-2 text-white",children:"Save changes"})})]})}),e.jsx(fe,{isOpen:S,onClose:()=>b(!1),title:"Preview",children:e.jsx(us,{clubName:(se=a==null?void 0:a.club)==null?void 0:se.name,image:(X=a==null?void 0:a.club)==null?void 0:X.home_image,description:(y=a==null?void 0:a.club)==null?void 0:y.description,title:(k=a==null?void 0:a.club)==null?void 0:k.title})})]})}function js({club:s}){var d;const[a,j]=r.useState(!1),t=[{value:"first_name",label:"First Name",type:"text",required:!0},{value:"last_name",label:"Last Name",type:"text",required:!0},{value:"email",label:"Email",type:"email",required:!0},{value:"password",label:"Password",type:"password",required:!0},{value:"phone",label:"Phone Number",type:"tel",required:!0},{value:"gender",label:"Gender",type:"select",required:!0,options:["Male","Female","Other"]},{value:"ntrp",label:"NTRP Rating",type:"select",required:!0,options:["2.0","2.5","3.0","3.5","4.0","4.5","5.0","5.5","6.0","6.5","7.0"]}],v=l=>{switch(l.type){case"select":return e.jsxs("select",{className:"w-full rounded-xl border border-zinc-200 px-3 py-2 text-gray-700 focus:outline-none focus:ring-1 focus:ring-blue-500",children:[e.jsxs("option",{value:"",children:["Select ",l.label]}),l.options.map(x=>e.jsx("option",{value:x,children:x},x))]});case"password":return e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:a?"text":"password",className:"w-full rounded-xl border border-zinc-200 px-3 py-2 text-gray-700 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:l.label}),e.jsx("button",{type:"button",className:"absolute right-3 top-1/2 -translate-y-1/2 text-gray-500",onClick:()=>j(!a),children:a?e.jsx(es,{className:"h-5 w-5"}):e.jsx(ss,{className:"h-5 w-5"})})]});default:return e.jsx("input",{type:l.type,className:"w-full rounded-xl border border-zinc-200 px-3 py-2 text-gray-700 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:l.label})}};return e.jsx(ts,{children:e.jsx("div",{className:"space-y-4",children:e.jsx("div",{className:"mx-auto max-w-2xl rounded-xl border border-gray-200 p-6",children:e.jsxs("div",{className:"grid gap-4 md:grid-cols-2",children:[t.map(l=>e.jsxs("div",{className:"space-y-1",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-700",children:[l.label,l.required&&e.jsx("span",{className:"text-red-500",children:"*"})]}),v(l)]},l.value)),(d=JSON.parse(s==null?void 0:s.custom_fields))==null?void 0:d.map(l=>e.jsxs("div",{className:"space-y-1",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-700",children:[l.label,l.required&&e.jsx("span",{className:"text-red-500",children:"*"})]}),v(l)]},l.value))]})})})})}function ys({club:s}){const a=JSON.parse((s==null?void 0:s.membership_settings)||"[]");return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"mb-5 text-center text-2xl font-medium text-gray-900",children:"Membership Plans"}),e.jsx("p",{className:"text-center text-gray-600",children:"Choose a plan that works best for you"})]}),e.jsx("div",{className:"mt-4 grid grid-cols-1 gap-4 md:grid-cols-3",children:a==null?void 0:a.map((j,t)=>e.jsx(rs,{...j,popular:t===1,onSelect:()=>{},isCurrentPlan:!1,isActive:!1},j.plan_name))}),e.jsx("div",{className:"mt-8 rounded-lg bg-gray-50 p-4",children:e.jsx("p",{className:"text-center text-sm text-gray-600",children:"All plans include access to basic facilities. Additional features vary by plan. Contact club management for more details."})})]})}function vs({club:s}){return e.jsxs("div",{className:"mx-auto max-w-7xl space-y-6 p-3 md:p-6",children:[s!=null&&s.home_image?e.jsx("div",{className:"",children:e.jsxs("div",{className:"flex flex-col gap-4 md:flex-row md:gap-10",children:[e.jsx("div",{className:"relative mb-4 flex-1 overflow-hidden rounded-xl shadow-md transition-all duration-300 hover:shadow-lg md:mb-6",children:e.jsx("div",{className:"relative h-[200px] w-full overflow-hidden md:h-[300px]",children:e.jsx("img",{src:s==null?void 0:s.home_image,alt:"Club Home Image",className:"h-full w-full object-cover transition-transform duration-700 hover:scale-105",loading:"lazy"})})}),e.jsxs("div",{className:"flex-1",children:[(s==null?void 0:s.title)&&e.jsx("h1",{className:"mb-2 text-2xl font-bold tracking-tight md:text-3xl",children:s==null?void 0:s.title}),(s==null?void 0:s.description)&&e.jsx("p",{className:"max-w-2xl text-base md:text-lg",children:s==null?void 0:s.description})]})]})}):e.jsxs("div",{className:"to-navy-900 mb-6 rounded-xl p-4 md:p-6",children:[(s==null?void 0:s.title)&&e.jsx("h1",{className:"mb-2 text-2xl font-bold tracking-tight text-white md:text-3xl",children:s==null?void 0:s.title}),(s==null?void 0:s.description)&&e.jsx("p",{className:"max-w-2xl text-base text-gray-200 md:text-lg",children:s==null?void 0:s.description})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsxs("div",{className:"h-fit rounded-2xl bg-white p-6 shadow-sm",children:[e.jsxs("div",{className:"mb-6 flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.75 3C3.7835 3 3 3.7835 3 4.75V19.25C3 20.2165 3.7835 21 4.75 21H19.25C20.2165 21 21 20.2165 21 19.25V4.75C21 3.7835 20.2165 3 19.25 3H4.75ZM8 11C7.44772 11 7 11.4477 7 12C7 12.5523 7.44772 13 8 13C8.55228 13 9 12.5523 9 12C9 11.4477 8.55228 11 8 11ZM8 15C7.44772 15 7 15.4477 7 16C7 16.5523 7.44772 17 8 17C8.55228 17 9 16.5523 9 16C9 15.4477 8.55228 15 8 15ZM11 16C11 15.4477 11.4477 15 12 15C12.5523 15 13 15.4477 13 16C13 16.5523 12.5523 17 12 17C11.4477 17 11 16.5523 11 16ZM12 11C11.4477 11 11 11.4477 11 12C11 12.5523 11.4477 13 12 13C12.5523 13 13 12.5523 13 12C13 11.4477 12.5523 11 12 11ZM15 12C15 11.4477 15.4477 11 16 11C16.5523 11 17 11.4477 17 12C17 12.5523 16.5523 13 16 13C15.4477 13 15 12.5523 15 12ZM4.75 4.5C4.61193 4.5 4.5 4.61193 4.5 4.75V7H19.5V4.75C19.5 4.61193 19.3881 4.5 19.25 4.5H4.75Z",fill:"#176448"})}),e.jsx("h2",{className:"text-lg font-medium",children:"Upcoming reservations"})]}),e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsxs("svg",{width:"60",height:"60",viewBox:"0 0 60 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",fill:"url(#paint0_linear_158_11396)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",stroke:"url(#paint1_linear_158_11396)"}),e.jsxs("g",{filter:"url(#filter0_d_158_11396)",children:[e.jsx("rect",{x:"8",y:"8",width:"44",height:"44",rx:"22",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"43",height:"43",rx:"21.5",stroke:"#E2E4E9"}),e.jsx("path",{d:"M20 30C20 27.2819 21.0844 24.8171 22.8443 23.0146C25.0536 24.5496 26.5 27.1059 26.5 30C26.5 32.8941 25.0536 35.4504 22.8443 36.9854C21.0844 35.1829 20 32.7181 20 30Z",fill:"#868C98"}),e.jsx("path",{d:"M28 30C28 26.7284 26.4289 23.8237 24 21.9993C25.6713 20.7439 27.7488 20 30 20C32.2512 20 34.3287 20.7439 36 21.9993C33.5711 23.8237 32 26.7284 32 30C32 33.2716 33.5711 36.1763 36 38.0007C34.3287 39.2561 32.2512 40 30 40C27.7488 40 25.6713 39.2561 24 38.0007C26.4289 36.1763 28 33.2716 28 30Z",fill:"#868C98"}),e.jsx("path",{d:"M37.1557 23.0146C38.9156 24.8171 40 27.2819 40 30C40 32.7181 38.9156 35.1829 37.1557 36.9854C34.9464 35.4504 33.5 32.8941 33.5 30C33.5 27.1059 34.9464 24.5496 37.1557 23.0146Z",fill:"#868C98"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_158_11396",x:"4",y:"6",width:"52",height:"52",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[e.jsx("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_158_11396"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_158_11396",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_158_11396",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#E4E5E7",stopOpacity:"0.48"}),e.jsx("stop",{offset:"1",stopColor:"#F7F8F8",stopOpacity:"0"}),e.jsx("stop",{offset:"1",stopColor:"#E4E5E7",stopOpacity:"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_158_11396",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#E4E5E7"}),e.jsx("stop",{offset:"0.765625",stopColor:"#E4E5E7",stopOpacity:"0"})]})]})]}),e.jsx("p",{className:"mb-4 text-gray-600",children:"You have no upcoming reservations"}),e.jsx("span",{className:"font-medium text-blue-600 hover:text-blue-700",children:"Reserve a court"})]})]}),e.jsxs("div",{className:"max-h-fit rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("div",{className:"mb-6 flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M12 2C9.51472 2 7.5 4.01472 7.5 6.5C7.5 8.98528 9.51472 11 12 11C14.4853 11 16.5 8.98528 16.5 6.5C16.5 4.01472 14.4853 2 12 2Z",fill:"#176448"}),e.jsx("path",{d:"M16.5081 13.8263C16.1908 14.2141 16.0005 14.7098 16.0005 15.25V16H15.2505C14.0078 16 13.0005 17.0074 13.0005 18.25C13.0005 19.4926 14.0078 20.5 15.2505 20.5H16.0005V21H5.59881C4.60008 21 3.69057 20.1119 3.9402 19.0012C4.7686 15.3152 8.21185 12.5 12.0004 12.5C13.6638 12.5 15.2115 12.9805 16.5081 13.8263Z",fill:"#176448"}),e.jsx("path",{d:"M19 15.25C19 14.8358 18.6642 14.5 18.25 14.5C17.8358 14.5 17.5 14.8358 17.5 15.25V17.5H15.25C14.8358 17.5 14.5 17.8358 14.5 18.25C14.5 18.6642 14.8358 19 15.25 19H17.5V21.25C17.5 21.6642 17.8358 22 18.25 22C18.6642 22 19 21.6642 19 21.25V19H21.25C21.6642 19 22 18.6642 22 18.25C22 17.8358 21.6642 17.5 21.25 17.5H19V15.25Z",fill:"#176448"})]}),e.jsx("h2",{className:"text-lg font-medium",children:"Open requests"})]})}),e.jsxs("div",{className:"flex flex-col items-center justify-center py-8",children:[e.jsxs("svg",{width:"60",height:"60",viewBox:"0 0 60 60",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",fill:"url(#paint0_linear_187_35666)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"59",height:"59",rx:"29.5",stroke:"url(#paint1_linear_187_35666)"}),e.jsxs("g",{filter:"url(#filter0_d_187_35666)",children:[e.jsx("rect",{x:"8",y:"8",width:"44",height:"44",rx:"22",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"43",height:"43",rx:"21.5",stroke:"#E2E4E9"}),e.jsx("path",{d:"M30 20C27.5147 20 25.5 22.0147 25.5 24.5C25.5 26.9853 27.5147 29 30 29C32.4853 29 34.5 26.9853 34.5 24.5C34.5 22.0147 32.4853 20 30 20Z",fill:"#868C98"}),e.jsx("path",{d:"M34.5081 31.8263C34.1908 32.2141 34.0005 32.7098 34.0005 33.25V34H33.2505C32.0078 34 31.0005 35.0074 31.0005 36.25C31.0005 37.4926 32.0078 38.5 33.2505 38.5H34.0005V39H23.5988C22.6001 39 21.6906 38.1119 21.9402 37.0012C22.7686 33.3152 26.2118 30.5 30.0004 30.5C31.6638 30.5 33.2115 30.9805 34.5081 31.8263Z",fill:"#868C98"}),e.jsx("path",{d:"M37 33.25C37 32.8358 36.6642 32.5 36.25 32.5C35.8358 32.5 35.5 32.8358 35.5 33.25V35.5H33.25C32.8358 35.5 32.5 35.8358 32.5 36.25C32.5 36.6642 32.8358 37 33.25 37H35.5V39.25C35.5 39.6642 35.8358 40 36.25 40C36.6642 40 37 39.6642 37 39.25V37H39.25C39.6642 37 40 36.6642 40 36.25C40 35.8358 39.6642 35.5 39.25 35.5H37V33.25Z",fill:"#868C98"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_187_35666",x:"4",y:"6",width:"52",height:"52",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[e.jsx("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_187_35666"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_187_35666",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_187_35666",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#E4E5E7",stopOpacity:"0.48"}),e.jsx("stop",{offset:"1",stopColor:"#F7F8F8",stopOpacity:"0"}),e.jsx("stop",{offset:"1",stopColor:"#E4E5E7",stopOpacity:"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_187_35666",x1:"30",y1:"0",x2:"30",y2:"60",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{stopColor:"#E4E5E7"}),e.jsx("stop",{offset:"0.765625",stopColor:"#E4E5E7",stopOpacity:"0"})]})]})]}),e.jsx("p",{className:"mb-4 text-gray-600",children:"There are no open requests"}),e.jsx("span",{className:"font-medium text-blue-600 hover:text-blue-700",children:"Make request"})]})]})]})]})}function fs({club:s}){const a=s!=null&&s.court_description?JSON.parse(s==null?void 0:s.court_description):{reservation_description:"",payment_description:""};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-blue-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold text-blue-900",children:"User Court Booking Experience"}),e.jsx("p",{className:"text-blue-800",children:"This shows how users experience the court reservation process on your platform."})]}),e.jsx("div",{className:"flex items-center justify-center rounded-lg bg-white p-4 shadow-sm",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white",children:"1"}),e.jsx("span",{className:"ml-2 text-sm font-medium text-gray-900",children:"Select date and time"})]}),e.jsx("div",{className:"h-px w-8 bg-gray-300"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-gray-300 text-sm font-medium text-gray-600",children:"2"}),e.jsx("span",{className:"ml-2 text-sm font-medium text-gray-500",children:"Reservation detail"})]}),e.jsx("div",{className:"h-px w-8 bg-gray-300"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-gray-300 text-sm font-medium text-gray-600",children:"3"}),e.jsx("span",{className:"ml-2 text-sm font-medium text-gray-500",children:"Payment"})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Sport Selection"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"rounded-lg border-2 border-blue-200 bg-blue-50 p-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Tennis"}),e.jsx("div",{className:"h-4 w-4 rounded-full bg-blue-600"})]})}),e.jsx("div",{className:"rounded-lg border border-gray-200 p-3 opacity-60",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Pickleball"}),e.jsx("div",{className:"h-4 w-4 rounded-full border-2 border-gray-300"})]})}),e.jsx("div",{className:"rounded-lg border border-gray-200 p-3 opacity-60",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Squash"}),e.jsx("div",{className:"h-4 w-4 rounded-full border-2 border-gray-300"})]})})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("h5",{className:"mb-2 text-sm font-medium text-gray-700",children:"Type"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"rounded border-2 border-blue-200 bg-blue-50 px-3 py-2",children:e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Singles"})}),e.jsx("div",{className:"rounded border border-gray-200 px-3 py-2 opacity-60",children:e.jsx("span",{className:"text-sm text-gray-600",children:"Doubles"})})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Select Date"}),e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can reserve a court up to 10 days in advance."}),e.jsxs("div",{className:"rounded-lg border p-3",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),e.jsx("span",{className:"text-sm font-medium",children:"December 2024"}),e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),e.jsxs("div",{className:"grid grid-cols-7 gap-1 text-xs",children:[e.jsx("div",{className:"py-1 text-center text-gray-500",children:"S"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"M"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"T"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"W"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"T"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"F"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"S"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"1"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"2"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"3"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"4"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"5"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"6"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"7"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"8"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"9"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"10"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"11"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"12"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"13"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"14"}),e.jsx("div",{className:"rounded bg-blue-600 py-1 text-center text-white",children:"15"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"16"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"17"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"18"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"19"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"20"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"21"})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Available Times"}),e.jsxs("div",{className:"mb-3 rounded-lg bg-blue-50 p-3",children:[e.jsxs("p",{className:"text-sm text-blue-800",children:[e.jsx("span",{className:"font-medium",children:"Minimum booking time:"})," ",e.jsx("span",{className:"font-semibold text-blue-900",children:"1 hour"})]}),e.jsx("p",{className:"mt-1 text-xs text-blue-600",children:"Based on available courts for your selected sport"})]}),e.jsxs("div",{className:"max-h-64 space-y-2 overflow-y-auto",children:[e.jsx("button",{className:"w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50",children:"8:00 AM"}),e.jsx("button",{className:"w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50",children:"8:30 AM"}),e.jsx("button",{className:"w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50",children:"9:00 AM"}),e.jsx("button",{className:"w-full rounded-lg border-2 border-blue-600 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-600",children:"10:00 AM - 11:00 AM"}),e.jsx("button",{className:"w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50",children:"11:00 AM"}),e.jsx("button",{className:"w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50",children:"11:30 AM"}),e.jsx("button",{className:"w-full cursor-not-allowed rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-400 opacity-50",children:"12:00 PM (Unavailable)"})]}),e.jsx("button",{className:"mt-4 w-full rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Next: Players"})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Step 2: Player Selection"}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Reservation Summary"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Sport:"}),e.jsx("span",{className:"font-medium",children:"Tennis - Singles"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Date:"}),e.jsx("span",{className:"font-medium",children:"Dec 15, 2024"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Time:"}),e.jsx("span",{className:"font-medium",children:"10:00 AM - 11:00 AM"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Duration:"}),e.jsx("span",{className:"font-medium",children:"1 hour"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Court:"}),e.jsx("span",{className:"font-medium",children:"Auto-assigned"})]})]})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Select Players"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Primary Player"}),e.jsxs("div",{className:"flex items-center space-x-3 rounded-lg border-2 border-blue-200 bg-blue-50 p-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white",children:"JD"}),e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"John Doe (You)"})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Additional Players"}),e.jsxs("div",{className:"max-h-32 space-y-2 overflow-y-auto",children:[e.jsxs("div",{className:"flex cursor-pointer items-center space-x-3 rounded-lg border border-gray-200 p-2 hover:bg-gray-50",children:[e.jsx("input",{type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-blue-600"}),e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-300 text-xs",children:"JS"}),e.jsx("span",{className:"text-sm",children:"Jane Smith"})]}),e.jsxs("div",{className:"flex cursor-pointer items-center space-x-3 rounded-lg border border-gray-200 p-2 hover:bg-gray-50",children:[e.jsx("input",{type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-blue-600",checked:!0}),e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-green-500 text-xs text-white",children:"MB"}),e.jsx("span",{className:"text-sm",children:"Mike Brown"})]}),e.jsxs("div",{className:"flex cursor-pointer items-center space-x-3 rounded-lg border border-gray-200 p-2 hover:bg-gray-50",children:[e.jsx("input",{type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-blue-600"}),e.jsx("div",{className:"flex h-6 w-6 items-center justify-center rounded-full bg-gray-300 text-xs",children:"LW"}),e.jsx("span",{className:"text-sm",children:"Lisa Wilson"})]})]})]}),e.jsxs("div",{className:"rounded-lg border border-orange-200 bg-orange-50 p-3",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("input",{type:"checkbox",className:"h-4 w-4 rounded border-gray-300 text-orange-600"}),e.jsx("span",{className:"text-sm font-medium text-orange-900",children:"Find a Buddy"})]}),e.jsx("p",{className:"mt-1 text-xs text-orange-700",children:"Let other members join your reservation"})]})]})]}),e.jsxs("div",{className:"mt-6 rounded-lg bg-gray-50 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Pricing"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Court Fee (1 hour):"}),e.jsx("span",{children:"$25.00"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Service Fee:"}),e.jsx("span",{children:"$2.50"})]}),e.jsxs("div",{className:"flex justify-between border-t border-gray-300 pt-2 font-medium",children:[e.jsx("span",{children:"Total:"}),e.jsx("span",{children:"$27.50"})]})]})]}),e.jsxs("div",{className:"mt-4 flex space-x-3",children:[e.jsx("button",{className:"flex-1 rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:"Back"}),e.jsx("button",{className:"flex-1 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Continue to Payment"})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Step 3: Payment"}),e.jsx("div",{className:"mb-4 rounded-xl bg-orange-500 px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"text-sm",children:"Your session is reserved. You have 15 minutes to complete payment."})]})}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Final Summary"}),e.jsxs("div",{className:"space-y-3 text-sm",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:"Tennis - Singles"}),e.jsx("div",{className:"text-gray-600",children:"Dec 15, 2024 • 10:00 AM - 11:00 AM"}),e.jsx("div",{className:"text-gray-600",children:"Court 1 • 1 hour"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:"Players (2)"}),e.jsx("div",{className:"text-gray-600",children:"John Doe, Mike Brown"})]}),e.jsxs("div",{className:"border-t border-gray-200 pt-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Court Fee:"}),e.jsx("span",{children:"$25.00"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Service Fee:"}),e.jsx("span",{children:"$2.50"})]}),e.jsxs("div",{className:"flex justify-between text-lg font-medium",children:[e.jsx("span",{children:"Total:"}),e.jsx("span",{children:"$27.50"})]})]})]})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Payment Details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"rounded-lg border border-gray-300 bg-gray-50 p-3",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Card Number"}),e.jsx("div",{className:"font-mono text-sm",children:"•••• •••• •••• 4242"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("div",{className:"rounded-lg border border-gray-300 bg-gray-50 p-3",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Expiry"}),e.jsx("div",{className:"font-mono text-sm",children:"12/25"})]}),e.jsxs("div",{className:"rounded-lg border border-gray-300 bg-gray-50 p-3",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"CVC"}),e.jsx("div",{className:"font-mono text-sm",children:"•••"})]})]}),e.jsx("button",{className:"w-full rounded-lg bg-green-600 px-4 py-3 text-sm font-medium text-white hover:bg-green-700",children:"Pay $27.50"})]})]})]}),e.jsx("div",{className:"mt-4 space-y-4 text-sm text-gray-500",children:e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx("span",{className:"font-medium underline",children:"Terms and Conditions"})," ","and ",e.jsx("span",{className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]})})]}),a.reservation_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Reservation Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:a.reservation_description})]}),a.payment_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Payment Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:a.payment_description})]})]})}function Ns({club:s}){const a=s!=null&&s.lesson_description?JSON.parse(s==null?void 0:s.lesson_description):{reservation_description:"",payment_description:""};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-purple-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold text-purple-900",children:"User Lesson Booking Experience"}),e.jsx("p",{className:"text-purple-800",children:"This shows how users experience the lesson booking process with three different search methods."})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Lesson Booking Options"}),e.jsxs("div",{className:"flex max-w-fit divide-x overflow-x-auto rounded-xl border text-sm",children:[e.jsx("button",{className:"whitespace-nowrap bg-white px-3 py-2 font-medium",children:"Find by coach"}),e.jsx("button",{className:"whitespace-nowrap bg-gray-100 px-3 py-2 text-gray-600",children:"Find by time"}),e.jsx("button",{className:"whitespace-nowrap bg-gray-100 px-3 py-2 text-gray-600",children:"Custom request"})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Find by Coach"}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Sport Selection"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"rounded-lg border-2 border-purple-200 bg-purple-50 p-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"Tennis"}),e.jsx("div",{className:"h-4 w-4 rounded-full bg-purple-600"})]})}),e.jsx("div",{className:"rounded-lg border border-gray-200 p-3 opacity-60",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Pickleball"}),e.jsx("div",{className:"h-4 w-4 rounded-full border-2 border-gray-300"})]})})]}),e.jsxs("div",{className:"mt-4",children:[e.jsx("h6",{className:"mb-2 text-sm font-medium text-gray-700",children:"Type"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"rounded border-2 border-purple-200 bg-purple-50 px-3 py-2",children:e.jsx("span",{className:"text-sm font-medium text-purple-900",children:"Private"})}),e.jsx("div",{className:"rounded border border-gray-200 px-3 py-2 opacity-60",children:e.jsx("span",{className:"text-sm text-gray-600",children:"Group"})})]})]})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsx("h5",{className:"font-medium text-gray-900",children:"Available Coaches"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search coaches...",className:"w-32 rounded-lg border border-gray-300 px-3 py-1 text-sm"}),e.jsx("svg",{className:"absolute right-2 top-1.5 h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]}),e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-4 w-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"})})})]})]}),e.jsxs("div",{className:"max-h-64 space-y-3 overflow-y-auto",children:[e.jsx("div",{className:"rounded-lg border-2 border-purple-200 bg-purple-50 p-3",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-purple-600 font-medium text-white",children:"JS"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium text-purple-900",children:"John Smith"}),e.jsx("div",{className:"text-sm text-purple-700",children:"Tennis Pro • 5 years exp"}),e.jsx("div",{className:"text-sm font-medium text-purple-900",children:"$75/hour"})]})]})}),e.jsx("div",{className:"cursor-pointer rounded-lg border border-gray-200 p-3 hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-gray-400 font-medium text-white",children:"MB"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium text-gray-900",children:"Maria Brown"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Tennis Coach • 3 years exp"}),e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"$65/hour"})]})]})}),e.jsx("div",{className:"cursor-pointer rounded-lg border border-gray-200 p-3 hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-full bg-gray-400 font-medium text-white",children:"DW"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium text-gray-900",children:"David Wilson"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Tennis Instructor • 2 years exp"}),e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"$55/hour"})]})]})})]})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Coach Profile"}),e.jsxs("div",{className:"mb-4 text-center",children:[e.jsx("div",{className:"mx-auto mb-2 flex h-16 w-16 items-center justify-center rounded-full bg-purple-600 text-xl font-medium text-white",children:"JS"}),e.jsx("div",{className:"font-medium text-gray-900",children:"John Smith"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Tennis Professional"})]}),e.jsxs("div",{className:"space-y-3 text-sm",children:[e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:"Experience"}),e.jsx("div",{className:"text-gray-600",children:"5 years teaching experience"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:"Specialties"}),e.jsx("div",{className:"text-gray-600",children:"Beginner to Advanced, Tournament Prep"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:"Rate"}),e.jsx("div",{className:"text-gray-600",children:"$75/hour"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:"Bio"}),e.jsx("div",{className:"text-xs text-gray-600",children:"Former college player with extensive coaching experience. Specializes in technique improvement and match strategy."})]})]}),e.jsx("button",{className:"mt-4 w-full rounded-lg bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700",children:"Check Availability"})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Find by Time"}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-3",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Sport Selection"}),e.jsx("div",{className:"space-y-2",children:e.jsx("div",{className:"rounded-lg border-2 border-blue-200 bg-blue-50 p-3",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm font-medium text-blue-900",children:"Tennis - Private"}),e.jsx("div",{className:"h-4 w-4 rounded-full bg-blue-600"})]})})})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Select Date"}),e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can book lessons up to 10 days in advance."}),e.jsxs("div",{className:"rounded-lg border p-3",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),e.jsx("span",{className:"text-sm font-medium",children:"December 2024"}),e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),e.jsxs("div",{className:"grid grid-cols-7 gap-1 text-xs",children:[e.jsx("div",{className:"py-1 text-center text-gray-500",children:"S"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"M"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"T"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"W"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"T"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"F"}),e.jsx("div",{className:"py-1 text-center text-gray-500",children:"S"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"1"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"2"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"3"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"4"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"5"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"6"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"7"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"8"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"9"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"10"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"11"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"12"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"13"}),e.jsx("div",{className:"py-1 text-center text-gray-400",children:"14"}),e.jsx("div",{className:"rounded bg-blue-600 py-1 text-center text-white",children:"16"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"17"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"18"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"19"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"20"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"21"}),e.jsx("div",{className:"cursor-pointer py-1 text-center hover:bg-gray-100",children:"22"})]})]})]})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Available Times"}),e.jsxs("div",{className:"max-h-64 space-y-2 overflow-y-auto",children:[e.jsx("button",{className:"w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50",children:"9:00 AM - 10:00 AM"}),e.jsx("button",{className:"w-full rounded-lg border-2 border-blue-600 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-600",children:"10:00 AM - 11:00 AM"}),e.jsx("button",{className:"w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50",children:"11:00 AM - 12:00 PM"}),e.jsx("button",{className:"w-full cursor-not-allowed rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-400 opacity-50",children:"2:00 PM - 3:00 PM (Unavailable)"}),e.jsx("button",{className:"w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50",children:"3:00 PM - 4:00 PM"}),e.jsx("button",{className:"w-full rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500 hover:bg-gray-50",children:"4:00 PM - 5:00 PM"})]}),e.jsx("button",{className:"mt-4 w-full rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Find Coaches"})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Available Coaches"}),e.jsx("div",{className:"mb-2 text-xs text-gray-600",children:"Dec 16, 2024 • 10:00 AM - 11:00 AM"}),e.jsxs("div",{className:"max-h-64 space-y-3 overflow-y-auto",children:[e.jsx("div",{className:"rounded-lg border-2 border-green-200 bg-green-50 p-3",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white",children:"JS"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium text-green-900",children:"John Smith"}),e.jsx("div",{className:"text-sm text-green-700",children:"$75/hour"})]}),e.jsx("button",{className:"rounded bg-green-600 px-2 py-1 text-xs text-white hover:bg-green-700",children:"Book"})]})}),e.jsx("div",{className:"rounded-lg border border-gray-200 p-3",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 text-sm font-medium text-white",children:"MB"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium text-gray-900",children:"Maria Brown"}),e.jsx("div",{className:"text-sm text-gray-600",children:"$65/hour"})]}),e.jsx("button",{className:"rounded bg-blue-600 px-2 py-1 text-xs text-white hover:bg-blue-700",children:"Book"})]})}),e.jsx("div",{className:"rounded-lg border border-gray-200 p-3",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 text-sm font-medium text-white",children:"DW"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"font-medium text-gray-900",children:"David Wilson"}),e.jsx("div",{className:"text-sm text-gray-600",children:"$55/hour"})]}),e.jsx("button",{className:"rounded bg-blue-600 px-2 py-1 text-xs text-white hover:bg-blue-700",children:"Book"})]})})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h4",{className:"text-lg font-medium text-gray-900",children:"Custom Request"}),e.jsx("button",{className:"rounded-lg bg-blue-600 px-4 py-2 text-sm text-white hover:bg-blue-700",children:"Create request"})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Your Requests"}),e.jsxs("div",{className:"max-h-64 space-y-3 overflow-y-auto",children:[e.jsx("div",{className:"rounded-lg border-2 border-green-200 bg-green-50 p-3",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"mb-1 flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm font-medium text-green-900",children:"Tennis • Private • Beginner"}),e.jsx("span",{className:"rounded-full bg-green-200 px-2 py-0.5 text-xs text-green-800",children:"Approved"})]}),e.jsx("div",{className:"text-xs text-green-700",children:"Dec 18, 2024 • 2:00 PM - 3:00 PM"}),e.jsx("div",{className:"mt-1 text-xs text-green-600",children:"Players: John Doe"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm font-medium text-green-900",children:"$65"}),e.jsx("div",{className:"text-xs text-green-700",children:"Maria Brown"})]})]})}),e.jsx("div",{className:"rounded-lg border border-gray-200 p-3",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"mb-1 flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm font-medium text-gray-900",children:"Tennis • Group • Intermediate"}),e.jsx("span",{className:"rounded-full bg-gray-200 px-2 py-0.5 text-xs text-gray-600",children:"Pending"})]}),e.jsx("div",{className:"text-xs text-gray-600",children:"Dec 20, 2024 • 4:00 PM - 5:00 PM"}),e.jsx("div",{className:"mt-1 text-xs text-gray-500",children:"Players: John Doe, Jane Smith"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"$80"}),e.jsx("div",{className:"text-xs text-gray-500",children:"Waiting..."})]})]})}),e.jsx("div",{className:"rounded-lg border border-red-200 bg-red-50 p-3 opacity-75",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"mb-1 flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm font-medium text-red-900",children:"Tennis • Private • Advanced"}),e.jsx("span",{className:"rounded-full bg-red-200 px-2 py-0.5 text-xs text-red-800",children:"Declined"})]}),e.jsx("div",{className:"text-xs text-red-700",children:"Dec 15, 2024 • 6:00 PM - 7:00 PM"}),e.jsx("div",{className:"mt-1 text-xs text-red-600",children:"Players: John Doe"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm font-medium text-red-900",children:"$75"}),e.jsx("div",{className:"text-xs text-red-700",children:"No responses"})]})]})})]})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Request Details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"rounded-lg border border-green-200 bg-green-50 p-3",children:[e.jsxs("div",{className:"mb-2 flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm font-medium text-green-900",children:"Tennis • Private • Beginner"}),e.jsx("span",{className:"rounded-full bg-green-200 px-2 py-0.5 text-xs text-green-800",children:"Approved"})]}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-green-700",children:"Date & Time:"}),e.jsx("span",{className:"font-medium text-green-900",children:"Dec 18, 2024 • 2:00 PM - 3:00 PM"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-green-700",children:"Duration:"}),e.jsx("span",{className:"font-medium text-green-900",children:"1 hour"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-green-700",children:"Players:"}),e.jsx("span",{className:"font-medium text-green-900",children:"John Doe"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-green-700",children:"Budget:"}),e.jsx("span",{className:"font-medium text-green-900",children:"$60-70/hour"})]})]}),e.jsxs("div",{className:"mt-3 border-t border-green-200 pt-3",children:[e.jsx("div",{className:"mb-1 text-xs text-green-700",children:"Special Requirements:"}),e.jsx("div",{className:"text-xs text-green-600",children:"Looking for a patient coach to help with basic strokes and footwork. Beginner level player."})]})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-gray-50 p-3",children:[e.jsxs("div",{className:"mb-2 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white",children:"MB"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:"Maria Brown"}),e.jsx("div",{className:"text-xs text-gray-600",children:"Responded 2 hours ago"})]})]}),e.jsx("div",{className:"mb-2 text-sm text-gray-700",children:`"I'd be happy to help you with your tennis fundamentals. I specialize in working with beginners and focus on proper technique development."`}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Rate:"}),e.jsx("span",{className:"ml-1 font-medium text-gray-900",children:"$65/hour"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{className:"rounded bg-gray-300 px-3 py-1 text-xs text-gray-700 hover:bg-gray-400",children:"Decline"}),e.jsx("button",{className:"rounded bg-green-600 px-3 py-1 text-xs text-white hover:bg-green-700",children:"Accept"})]})]})]})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Lesson Booking Process"}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-3",children:[e.jsxs("div",{className:"rounded-lg border border-purple-200 bg-purple-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-purple-600 text-sm font-medium text-white",children:"1"}),e.jsx("h5",{className:"font-medium text-purple-900",children:"Find by Coach"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-purple-800",children:[e.jsx("p",{children:"• Browse available coaches by sport"}),e.jsx("p",{children:"• View detailed coach profiles and rates"}),e.jsx("p",{children:"• Check real-time availability"}),e.jsx("p",{children:"• Book directly with preferred coach"})]})]}),e.jsxs("div",{className:"rounded-lg border border-blue-200 bg-blue-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white",children:"2"}),e.jsx("h5",{className:"font-medium text-blue-900",children:"Find by Time"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-blue-800",children:[e.jsx("p",{children:"• Select preferred date and time first"}),e.jsx("p",{children:"• System shows available coaches"}),e.jsx("p",{children:"• Compare rates and experience"}),e.jsx("p",{children:"• Book with best match"})]})]}),e.jsxs("div",{className:"rounded-lg border border-green-200 bg-green-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white",children:"3"}),e.jsx("h5",{className:"font-medium text-green-900",children:"Custom Request"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-green-800",children:[e.jsx("p",{children:"• Submit specific requirements"}),e.jsx("p",{children:"• Set budget and preferences"}),e.jsx("p",{children:"• Coaches respond with proposals"}),e.jsx("p",{children:"• Choose from multiple options"})]})]})]})]}),a.reservation_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Reservation Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:a.reservation_description})]}),a.payment_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Payment Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:a.payment_description})]})]})}function bs(){return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-orange-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold text-orange-900",children:"User Find a Buddy Experience"}),e.jsx("p",{className:"text-orange-800",children:"This shows how users experience the find-a-buddy feature to connect with playing partners."})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsxs("div",{className:"mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h4",{className:"mb-4 text-xl font-semibold sm:mb-0",children:"Find a buddy"}),e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsx("button",{className:"rounded-xl border bg-green-900 px-4 py-2 text-sm text-white",children:"Subscriptions"}),e.jsxs("button",{className:"flex items-center rounded-xl border bg-blue-600 px-4 py-2 text-sm text-white",children:[e.jsx("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",className:"mr-2",children:e.jsx("path",{d:"M5.25 5.25V0.75H6.75V5.25H11.25V6.75H6.75V11.25H5.25V6.75H0.75V5.25H5.25Z",fill:"white"})}),"Create a request"]})]})]}),e.jsxs("div",{className:"mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"mb-3 flex sm:mb-0",children:[e.jsx("button",{className:"flex items-center gap-2 whitespace-nowrap border-b-2 border-blue-600 bg-transparent px-3 py-3",children:e.jsx("span",{children:"All requests"})}),e.jsx("button",{className:"flex items-center gap-2 whitespace-nowrap bg-transparent px-3 py-3 text-gray-600",children:e.jsx("span",{children:"My requests"})})]}),e.jsxs("div",{className:"flex max-w-fit divide-x overflow-x-auto rounded-xl border text-sm",children:[e.jsx("button",{className:"whitespace-nowrap bg-white px-3 py-2 font-medium",children:"Table"}),e.jsx("button",{className:"whitespace-nowrap bg-gray-100 px-3 py-2 text-gray-600",children:"Calendar"}),e.jsx("button",{className:"whitespace-nowrap bg-gray-100 px-3 py-2 text-gray-600",children:"Weekly"})]})]}),e.jsxs("div",{className:"mb-6 flex flex-wrap items-center gap-3 rounded-lg bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{className:"h-4 w-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"})}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Filters:"})]}),e.jsxs("select",{className:"rounded border border-gray-300 px-3 py-1 text-sm",children:[e.jsx("option",{children:"Players needed: All"}),e.jsx("option",{children:"1 player"}),e.jsx("option",{children:"2 players"}),e.jsx("option",{children:"3 players"})]}),e.jsxs("select",{className:"rounded border border-gray-300 px-3 py-1 text-sm",children:[e.jsx("option",{children:"NTRP: All"}),e.jsx("option",{children:"2.5"}),e.jsx("option",{children:"3.0"}),e.jsx("option",{children:"3.5"}),e.jsx("option",{children:"4.0"}),e.jsx("option",{children:"4.5"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Sort:"}),e.jsxs("button",{className:"flex items-center gap-1 rounded border border-gray-300 px-3 py-1 text-sm",children:[e.jsx("span",{children:"Newest first"}),e.jsx("svg",{className:"h-3 w-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Available Requests"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"group w-full cursor-pointer rounded-xl bg-gray-50 p-4 transition-all duration-200 hover:scale-[1.01] hover:bg-gray-100 hover:shadow-md",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"w-24 flex-shrink-0",children:e.jsx("div",{className:"flex aspect-square w-full items-center justify-center rounded-full bg-orange-500 text-lg font-medium text-white shadow-sm",children:"JS"})}),e.jsxs("div",{className:"flex min-w-0 flex-1 flex-col",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"min-w-0",children:[e.jsx("p",{className:"mb-2 break-words text-base font-medium",children:"Today (Monday) • 6:00 PM - 7:00 PM"}),e.jsxs("div",{className:"mb-2 flex flex-wrap items-center text-sm text-gray-500",children:[e.jsx("span",{children:"Added 2 hours ago"}),e.jsx("span",{className:"mx-1",children:"by"}),e.jsx("span",{className:"font-medium capitalize text-gray-700",children:"John Smith"})]})]}),e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",className:"flex-shrink-0 text-gray-400 transition-transform duration-200 group-hover:translate-x-1",children:e.jsx("path",{d:"M4.16669 10H15.8334M15.8334 10L10 4.16669M15.8334 10L10 15.8334",stroke:"currentColor",strokeWidth:"1.67",strokeLinecap:"round",strokeLinejoin:"round"})})]}),e.jsxs("div",{className:"mt-auto flex flex-wrap items-center gap-2",children:[e.jsx("span",{className:"rounded-full border bg-white px-3 py-1 text-xs text-gray-600",children:"Tennis"}),e.jsx("span",{className:"rounded-full border bg-white px-3 py-1 text-xs text-gray-600",children:"NTRP: 3.5 - 4.0"}),e.jsxs("span",{className:"flex items-center gap-1 rounded-full border bg-white px-3 py-1 text-xs text-gray-600",children:[e.jsxs("svg",{width:"12",height:"12",viewBox:"0 0 16 16",fill:"none",children:[e.jsx("path",{d:"M7.99998 1.33301C6.34313 1.33301 4.99998 2.67615 4.99998 4.33301C4.99998 5.98986 6.34313 7.33301 7.99998 7.33301C9.65683 7.33301 11 5.98986 11 4.33301C11 2.67615 9.65683 1.33301 7.99998 1.33301Z",fill:"currentColor"}),e.jsx("path",{d:"M2.66669 14.6663C2.66669 12.0889 4.75597 9.99967 7.33335 9.99967H8.66669C11.2441 9.99967 13.3334 12.0889 13.3334 14.6663H2.66669Z",fill:"currentColor"})]}),"Need 1 player"]}),e.jsx("span",{className:"rounded-full border border-orange-200 bg-orange-100 px-3 py-1 text-xs font-medium text-orange-700",children:"Open"})]})]})]})}),e.jsx("div",{className:"group w-full cursor-pointer rounded-xl bg-gray-50 p-4 transition-all duration-200 hover:scale-[1.01] hover:bg-gray-100 hover:shadow-md",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"w-24 flex-shrink-0",children:e.jsx("div",{className:"flex aspect-square w-full items-center justify-center rounded-full bg-blue-500 text-lg font-medium text-white shadow-sm",children:"MB"})}),e.jsxs("div",{className:"flex min-w-0 flex-1 flex-col",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"min-w-0",children:[e.jsx("p",{className:"mb-2 break-words text-base font-medium",children:"Wednesday, Dec 18 • 10:00 AM - 11:00 AM"}),e.jsxs("div",{className:"mb-2 flex flex-wrap items-center text-sm text-gray-500",children:[e.jsx("span",{children:"Added 5 hours ago"}),e.jsx("span",{className:"mx-1",children:"by"}),e.jsx("span",{className:"font-medium capitalize text-gray-700",children:"Maria Brown"})]})]}),e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",className:"flex-shrink-0 text-gray-400 transition-transform duration-200 group-hover:translate-x-1",children:e.jsx("path",{d:"M4.16669 10H15.8334M15.8334 10L10 4.16669M15.8334 10L10 15.8334",stroke:"currentColor",strokeWidth:"1.67",strokeLinecap:"round",strokeLinejoin:"round"})})]}),e.jsxs("div",{className:"mt-auto flex flex-wrap items-center gap-2",children:[e.jsx("span",{className:"rounded-full border bg-white px-3 py-1 text-xs text-gray-600",children:"Pickleball"}),e.jsx("span",{className:"rounded-full border bg-white px-3 py-1 text-xs text-gray-600",children:"NTRP: 3.0"}),e.jsxs("span",{className:"flex items-center gap-1 rounded-full border bg-white px-3 py-1 text-xs text-gray-600",children:[e.jsxs("svg",{width:"12",height:"12",viewBox:"0 0 16 16",fill:"none",children:[e.jsx("path",{d:"M7.99998 1.33301C6.34313 1.33301 4.99998 2.67615 4.99998 4.33301C4.99998 5.98986 6.34313 7.33301 7.99998 7.33301C9.65683 7.33301 11 5.98986 11 4.33301C11 2.67615 9.65683 1.33301 7.99998 1.33301Z",fill:"currentColor"}),e.jsx("path",{d:"M2.66669 14.6663C2.66669 12.0889 4.75597 9.99967 7.33335 9.99967H8.66669C11.2441 9.99967 13.3334 12.0889 13.3334 14.6663H2.66669Z",fill:"currentColor"})]}),"Need 2 players"]}),e.jsx("span",{className:"rounded-full border border-orange-200 bg-orange-100 px-3 py-1 text-xs font-medium text-orange-700",children:"Open"})]})]})]})}),e.jsx("div",{className:"group w-full cursor-pointer rounded-xl border border-green-200 bg-green-50 p-4 transition-all duration-200 hover:scale-[1.01] hover:bg-green-100 hover:shadow-md",children:e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"w-24 flex-shrink-0",children:e.jsx("div",{className:"flex aspect-square w-full items-center justify-center rounded-full bg-green-600 text-lg font-medium text-white shadow-sm",children:"DW"})}),e.jsxs("div",{className:"flex min-w-0 flex-1 flex-col",children:[e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"min-w-0",children:[e.jsx("p",{className:"mb-2 break-words text-base font-medium text-green-900",children:"Friday, Dec 20 • 2:00 PM - 3:00 PM"}),e.jsxs("div",{className:"mb-2 flex flex-wrap items-center text-sm text-green-600",children:[e.jsx("span",{children:"Added 1 day ago"}),e.jsx("span",{className:"mx-1",children:"by"}),e.jsx("span",{className:"font-medium capitalize text-green-700",children:"David Wilson"})]})]}),e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",className:"flex-shrink-0 text-green-600 transition-transform duration-200 group-hover:translate-x-1",children:e.jsx("path",{d:"M4.16669 10H15.8334M15.8334 10L10 4.16669M15.8334 10L10 15.8334",stroke:"currentColor",strokeWidth:"1.67",strokeLinecap:"round",strokeLinejoin:"round"})})]}),e.jsxs("div",{className:"mt-auto flex flex-wrap items-center gap-2",children:[e.jsx("span",{className:"rounded-full border border-green-200 bg-white px-3 py-1 text-xs text-green-700",children:"Tennis"}),e.jsx("span",{className:"rounded-full border border-green-200 bg-white px-3 py-1 text-xs text-green-700",children:"NTRP: 4.0 - 4.5"}),e.jsxs("span",{className:"flex items-center gap-1 rounded-full border border-green-200 bg-white px-3 py-1 text-xs text-green-700",children:[e.jsxs("svg",{width:"12",height:"12",viewBox:"0 0 16 16",fill:"none",children:[e.jsx("path",{d:"M7.99998 1.33301C6.34313 1.33301 4.99998 2.67615 4.99998 4.33301C4.99998 5.98986 6.34313 7.33301 7.99998 7.33301C9.65683 7.33301 11 5.98986 11 4.33301C11 2.67615 9.65683 1.33301 7.99998 1.33301Z",fill:"currentColor"}),e.jsx("path",{d:"M2.66669 14.6663C2.66669 12.0889 4.75597 9.99967 7.33335 9.99967H8.66669C11.2441 9.99967 13.3334 12.0889 13.3334 14.6663H2.66669Z",fill:"currentColor"})]}),"Full (3/3 players)"]}),e.jsx("span",{className:"rounded-full border border-green-300 bg-green-200 px-3 py-1 text-xs font-medium text-green-800",children:"Complete"})]})]})]})})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Request Details View"}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"mb-4 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-orange-500 text-lg font-medium text-white",children:"JS"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:"John Smith"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Tennis Player • NTRP 3.5"})]})]}),e.jsxs("div",{className:"space-y-3 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Sport:"}),e.jsx("span",{className:"font-medium",children:"Tennis - Singles"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Date & Time:"}),e.jsx("span",{className:"font-medium",children:"Today • 6:00 PM - 7:00 PM"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Duration:"}),e.jsx("span",{className:"font-medium",children:"1 hour"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"NTRP Range:"}),e.jsx("span",{className:"font-medium",children:"3.5 - 4.0"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Players Needed:"}),e.jsx("span",{className:"font-medium",children:"1 more player"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Court:"}),e.jsx("span",{className:"font-medium",children:"Auto-assigned"})]})]}),e.jsxs("div",{className:"mt-4 border-t border-gray-200 pt-4",children:[e.jsx("div",{className:"mb-2 text-sm font-medium text-gray-700",children:"Notes:"}),e.jsx("div",{className:"text-sm text-gray-600",children:"Looking for a consistent hitting partner for regular practice sessions. Prefer someone who can play at least once a week."})]})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Join This Request"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Select Players"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-3 rounded border border-blue-200 bg-blue-50 p-2",children:[e.jsx("input",{type:"checkbox",checked:!0,className:"h-4 w-4 text-blue-600"}),e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white",children:"YU"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium",children:"You"}),e.jsx("div",{className:"text-xs text-gray-600",children:"NTRP: 3.5"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3 rounded border border-gray-200 p-2 hover:bg-gray-50",children:[e.jsx("input",{type:"checkbox",className:"h-4 w-4 text-blue-600"}),e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 text-sm font-medium text-white",children:"FM"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium",children:"Family Member"}),e.jsx("div",{className:"text-xs text-gray-600",children:"NTRP: 3.0"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Message (Optional)"}),e.jsx("textarea",{className:"w-full rounded border border-gray-300 px-3 py-2 text-sm",rows:"3",placeholder:"Hi! I'd love to join your tennis session..."})]}),e.jsxs("div",{className:"rounded bg-gray-50 p-3",children:[e.jsx("div",{className:"mb-2 text-sm font-medium text-gray-900",children:"Estimated Cost"}),e.jsxs("div",{className:"space-y-1 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Court Fee (split):"}),e.jsx("span",{children:"$12.50"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Service Fee:"}),e.jsx("span",{children:"$1.25"})]}),e.jsxs("div",{className:"flex justify-between border-t border-gray-300 pt-1 font-medium",children:[e.jsx("span",{children:"Your Total:"}),e.jsx("span",{children:"$13.75"})]})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx("button",{className:"flex-1 rounded bg-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-400",children:"Cancel"}),e.jsx("button",{className:"flex-1 rounded bg-orange-600 px-4 py-2 text-sm font-medium text-white hover:bg-orange-700",children:"Join Request"})]})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Calendar View"}),e.jsxs("div",{className:"rounded-lg border p-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),e.jsx("span",{className:"font-medium",children:"December 2024"}),e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),e.jsxs("div",{className:"grid grid-cols-7 gap-1 text-sm",children:[e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"S"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"M"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"T"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"W"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"T"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"F"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"S"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"1"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"2"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"3"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"4"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"5"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"6"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"7"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"8"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"9"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"10"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"11"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"12"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"13"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"14"}),e.jsxs("div",{className:"relative rounded bg-orange-100 py-2 text-center text-orange-800",children:["15",e.jsx("div",{className:"absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 transform rounded-full bg-orange-600"})]}),e.jsx("div",{className:"cursor-pointer py-2 text-center hover:bg-gray-100",children:"16"}),e.jsx("div",{className:"cursor-pointer py-2 text-center hover:bg-gray-100",children:"17"}),e.jsxs("div",{className:"relative rounded bg-orange-100 py-2 text-center text-orange-800",children:["18",e.jsx("div",{className:"absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 transform rounded-full bg-orange-600"})]}),e.jsx("div",{className:"cursor-pointer py-2 text-center hover:bg-gray-100",children:"19"}),e.jsxs("div",{className:"relative rounded bg-green-100 py-2 text-center text-green-800",children:["20",e.jsx("div",{className:"absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 transform rounded-full bg-green-600"})]}),e.jsx("div",{className:"cursor-pointer py-2 text-center hover:bg-gray-100",children:"21"})]}),e.jsxs("div",{className:"mt-4 flex items-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-orange-600"}),e.jsx("span",{children:"Open requests"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-600"}),e.jsx("span",{children:"Complete"})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Weekly View"}),e.jsxs("div",{className:"rounded-lg border p-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),e.jsx("span",{className:"font-medium",children:"Dec 15 - Dec 21, 2024"}),e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-3 rounded p-2 hover:bg-gray-50",children:[e.jsx("div",{className:"w-16 text-sm font-medium text-gray-700",children:"Mon 16"}),e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"flex h-6 items-center rounded bg-orange-100 px-2 text-xs text-orange-800",children:"6:00 PM - Tennis (John S.)"})})]}),e.jsxs("div",{className:"flex items-center gap-3 rounded p-2 hover:bg-gray-50",children:[e.jsx("div",{className:"w-16 text-sm font-medium text-gray-700",children:"Tue 17"}),e.jsx("div",{className:"flex-1 text-xs text-gray-500",children:"No requests"})]}),e.jsxs("div",{className:"flex items-center gap-3 rounded p-2 hover:bg-gray-50",children:[e.jsx("div",{className:"w-16 text-sm font-medium text-gray-700",children:"Wed 18"}),e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"flex h-6 items-center rounded bg-orange-100 px-2 text-xs text-orange-800",children:"10:00 AM - Pickleball (Maria B.)"})})]}),e.jsxs("div",{className:"flex items-center gap-3 rounded p-2 hover:bg-gray-50",children:[e.jsx("div",{className:"w-16 text-sm font-medium text-gray-700",children:"Thu 19"}),e.jsx("div",{className:"flex-1 text-xs text-gray-500",children:"No requests"})]}),e.jsxs("div",{className:"flex items-center gap-3 rounded p-2 hover:bg-gray-50",children:[e.jsx("div",{className:"w-16 text-sm font-medium text-gray-700",children:"Fri 20"}),e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"flex h-6 items-center rounded bg-green-100 px-2 text-xs text-green-800",children:"2:00 PM - Tennis (David W.) - Complete"})})]})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Find a Buddy Features"}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-3",children:[e.jsxs("div",{className:"rounded-lg border border-orange-200 bg-orange-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-orange-600 text-sm font-medium text-white",children:"1"}),e.jsx("h5",{className:"font-medium text-orange-900",children:"Create Requests"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-orange-800",children:[e.jsx("p",{children:"• Select sport, date, and time preferences"}),e.jsx("p",{children:"• Set NTRP skill level range for matching"}),e.jsx("p",{children:"• Specify number of players needed"}),e.jsx("p",{children:"• Add custom notes and requirements"})]})]}),e.jsxs("div",{className:"rounded-lg border border-blue-200 bg-blue-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white",children:"2"}),e.jsx("h5",{className:"font-medium text-blue-900",children:"Browse & Join"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-blue-800",children:[e.jsx("p",{children:"• View all open requests from other members"}),e.jsx("p",{children:"• Filter by sport, skill level, and availability"}),e.jsx("p",{children:"• Join requests that match your preferences"}),e.jsx("p",{children:"• Multiple view options: Table, Calendar, Weekly"})]})]}),e.jsxs("div",{className:"rounded-lg border border-green-200 bg-green-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white",children:"3"}),e.jsx("h5",{className:"font-medium text-green-900",children:"Play Together"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-green-800",children:[e.jsx("p",{children:"• Automatic court booking when request fills"}),e.jsx("p",{children:"• Split court costs among all participants"}),e.jsx("p",{children:"• Email notifications and confirmations"}),e.jsx("p",{children:"• Subscription options for regular matches"})]})]})]})]})]})}function ws({club:s}){const a=s!=null&&s.clinic_description?JSON.parse(s==null?void 0:s.clinic_description):{reservation_description:"",payment_description:""};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-indigo-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold text-indigo-900",children:"User Clinic & Program Experience"}),e.jsx("p",{className:"text-indigo-800",children:"This shows how users experience browsing and booking group clinics and programs."})]}),e.jsxs("div",{className:"flex flex-col justify-between bg-white px-3 py-3 sm:flex-row sm:items-center sm:px-4 sm:py-4",children:[e.jsx("h1",{className:"mb-3 text-xl font-semibold sm:mb-6 sm:text-2xl",children:"Clinics"}),e.jsxs("div",{className:"mb-4 flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-8 sm:text-sm",children:[e.jsx("button",{className:"bg-white-600 whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2",children:"Table"}),e.jsx("button",{className:"whitespace-nowrap bg-gray-100 px-2 py-1.5 text-gray-600 sm:px-3 sm:py-2",children:"Calendar"}),e.jsx("button",{className:"whitespace-nowrap bg-gray-100 px-2 py-1.5 text-gray-600 sm:px-3 sm:py-2",children:"Weekly"})]})]}),e.jsx("div",{className:"px-2 py-2 sm:px-3 sm:py-3",children:e.jsx("div",{className:"mx-auto max-w-7xl",children:e.jsxs("div",{className:"mx-auto mt-3 max-w-4xl rounded-lg bg-white p-3 shadow-sm sm:mt-5 sm:p-4",children:[e.jsxs("div",{className:"mb-4 flex flex-col justify-between gap-3 sm:mb-6 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex items-center gap-3 sm:gap-4",children:[e.jsxs("button",{className:"flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-3 py-1.5 sm:px-4 sm:py-2",children:[e.jsx("svg",{className:"text-blue-600",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:e.jsx("polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"})}),e.jsx("span",{className:"text-sm text-gray-700 sm:text-base",children:"Filter"}),e.jsx("span",{className:"flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs text-white",children:"2"})]}),e.jsx("button",{className:"text-sm text-gray-500 hover:underline sm:text-base",children:"Clear all"})]}),e.jsxs("div",{className:"mt-3 flex flex-wrap items-center gap-3 sm:mt-0 sm:gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"whitespace-nowrap text-xs text-gray-700 sm:text-sm",children:"Available slot only"}),e.jsx("button",{className:"relative h-5 w-10 rounded-full bg-blue-600 transition-colors duration-200 ease-in-out sm:h-6 sm:w-12",children:e.jsx("div",{className:"absolute top-0.5 h-4 w-4 translate-x-5 transform rounded-full bg-white shadow-md transition-transform duration-200 ease-in-out sm:h-5 sm:w-5 sm:translate-x-6"})})]}),e.jsx("div",{className:"relative border-gray-200 sm:border-l sm:pl-4",children:e.jsxs("button",{className:"flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1.5 text-xs sm:gap-2 sm:px-4 sm:py-2 sm:text-sm",children:[e.jsx("span",{className:"whitespace-nowrap text-gray-700",children:"By date (Latest)"}),e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-gray-400",children:e.jsx("polyline",{points:"6 9 12 15 18 9"})})]})})]})]}),e.jsxs("div",{className:"space-y-3 sm:space-y-4",children:[e.jsx("div",{className:"cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4",children:e.jsxs("div",{className:"flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"flex flex-wrap items-center gap-2",children:e.jsx("h3",{className:"text-base font-medium sm:text-lg",children:"Tennis Fundamentals"})}),e.jsx("p",{className:"mt-1 text-xs text-gray-600 sm:text-sm",children:"December 18, 2024 • 10:00 AM - 11:30 AM"})]}),e.jsx("div",{className:"flex flex-col gap-2 sm:items-end sm:gap-4",children:e.jsx("span",{className:"w-fit rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-xs text-[#176448]",children:"Slots available: 3 (out of 8)"})})]})}),e.jsx("div",{className:"cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4",children:e.jsxs("div",{className:"flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-wrap items-center gap-2",children:[e.jsx("h3",{className:"text-base font-medium sm:text-lg",children:"Advanced Serve Clinic"}),e.jsx("span",{className:"rounded bg-blue-600 px-2 py-1 text-xs text-white",children:"REGISTERED"})]}),e.jsx("p",{className:"mt-1 text-xs text-gray-600 sm:text-sm",children:"December 20, 2024 • 2:00 PM - 3:30 PM"})]}),e.jsx("div",{className:"flex flex-col gap-2 sm:items-end sm:gap-4",children:e.jsx("span",{className:"w-fit rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-xs text-[#176448]",children:"Slots available: 2 (out of 6)"})})]})}),e.jsx("div",{className:"cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4",children:e.jsxs("div",{className:"flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"flex flex-wrap items-center gap-2",children:e.jsx("h3",{className:"text-base font-medium sm:text-lg",children:"Doubles Strategy Workshop"})}),e.jsx("p",{className:"mt-1 text-xs text-gray-600 sm:text-sm",children:"December 22, 2024 • 4:00 PM - 5:30 PM"})]}),e.jsx("div",{className:"flex flex-col gap-2 sm:items-end sm:gap-4",children:e.jsx("span",{className:"w-fit rounded-full border border-red-800 bg-red-50 px-3 py-1 text-xs text-red-800",children:"No slots available"})})]})}),e.jsx("div",{className:"cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4",children:e.jsxs("div",{className:"flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"flex flex-wrap items-center gap-2",children:e.jsx("h3",{className:"text-base font-medium sm:text-lg",children:"Junior Tennis Program"})}),e.jsx("p",{className:"mt-1 text-xs text-gray-600 sm:text-sm",children:"December 23, 2024 - January 15, 2025 • Mondays & Wednesdays • 4:00 PM - 5:00 PM"})]}),e.jsx("div",{className:"flex flex-col gap-2 sm:items-end sm:gap-4",children:e.jsx("span",{className:"w-fit rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-xs text-[#176448]",children:"Slots available: 5 (out of 12)"})})]})})]})]})})}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Clinic Registration View"}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:"Tennis Fundamentals"}),e.jsx("span",{className:"rounded border border-indigo-200 bg-indigo-100 px-2 py-1 text-xs text-indigo-700",children:"Beginner"})]}),e.jsx("p",{className:"text-gray-600",children:"Perfect for players new to tennis or looking to improve basic techniques."})]}),e.jsxs("div",{className:"space-y-3 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Date:"}),e.jsx("span",{className:"font-medium",children:"December 18, 2024"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Time:"}),e.jsx("span",{className:"font-medium",children:"10:00 AM - 11:30 AM"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Duration:"}),e.jsx("span",{className:"font-medium",children:"1.5 hours"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Coach:"}),e.jsx("span",{className:"font-medium",children:"John Smith"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Location:"}),e.jsx("span",{className:"font-medium",children:"Courts 1-2"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Skill Level:"}),e.jsx("span",{className:"font-medium",children:"Beginner (NTRP 2.0-3.0)"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Available Spots:"}),e.jsx("span",{className:"font-medium text-green-600",children:"3 out of 8"})]})]}),e.jsxs("div",{className:"mt-4 border-t border-gray-200 pt-4",children:[e.jsx("div",{className:"mb-2 text-sm font-medium text-gray-700",children:"What You'll Learn:"}),e.jsxs("ul",{className:"space-y-1 text-sm text-gray-600",children:[e.jsx("li",{children:"• Proper grip and stance"}),e.jsx("li",{children:"• Basic forehand and backhand strokes"}),e.jsx("li",{children:"• Serving fundamentals"}),e.jsx("li",{children:"• Court positioning and movement"})]})]}),e.jsxs("div",{className:"mt-4 border-t border-gray-200 pt-4",children:[e.jsx("div",{className:"mb-2 text-sm font-medium text-gray-700",children:"What to Bring:"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Tennis racket (loaners available), comfortable athletic wear, tennis shoes, and water bottle."})]})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 p-4",children:[e.jsx("h5",{className:"mb-3 font-medium text-gray-900",children:"Register for Clinic"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Select Participants"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center space-x-3 rounded border border-indigo-200 bg-indigo-50 p-2",children:[e.jsx("input",{type:"checkbox",checked:!0,className:"h-4 w-4 text-indigo-600"}),e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-indigo-600 text-sm font-medium text-white",children:"YU"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium",children:"You"}),e.jsx("div",{className:"text-xs text-gray-600",children:"NTRP: 2.5"})]})]}),e.jsxs("div",{className:"flex items-center space-x-3 rounded border border-gray-200 p-2 hover:bg-gray-50",children:[e.jsx("input",{type:"checkbox",className:"h-4 w-4 text-indigo-600"}),e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 text-sm font-medium text-white",children:"FM"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium",children:"Family Member"}),e.jsx("div",{className:"text-xs text-gray-600",children:"NTRP: 2.0"})]})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Special Requirements (Optional)"}),e.jsx("textarea",{className:"w-full rounded border border-gray-300 px-3 py-2 text-sm",rows:"3",placeholder:"Any special accommodations or notes..."})]}),e.jsxs("div",{className:"rounded bg-gray-50 p-3",children:[e.jsx("div",{className:"mb-2 text-sm font-medium text-gray-900",children:"Registration Summary"}),e.jsxs("div",{className:"space-y-1 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Clinic Fee (1 participant):"}),e.jsx("span",{children:"$45.00"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Service Fee:"}),e.jsx("span",{children:"$4.50"})]}),e.jsxs("div",{className:"flex justify-between border-t border-gray-300 pt-1 font-medium",children:[e.jsx("span",{children:"Total:"}),e.jsx("span",{children:"$49.50"})]})]})]}),e.jsxs("div",{className:"flex items-start space-x-2",children:[e.jsx("input",{type:"checkbox",className:"mt-0.5 h-4 w-4 text-indigo-600"}),e.jsx("label",{className:"text-xs text-gray-600",children:"I agree to the clinic cancellation policy and understand that refunds are only available 24 hours before the clinic start time."})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx("button",{className:"flex-1 rounded bg-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-400",children:"Cancel"}),e.jsx("button",{className:"flex-1 rounded bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700",children:"Register & Pay"})]})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Calendar View"}),e.jsxs("div",{className:"rounded-lg border p-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),e.jsx("span",{className:"font-medium",children:"December 2024"}),e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),e.jsxs("div",{className:"grid grid-cols-7 gap-1 text-sm",children:[e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"S"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"M"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"T"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"W"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"T"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"F"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"S"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"1"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"2"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"3"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"4"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"5"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"6"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"7"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"8"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"9"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"10"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"11"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"12"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"13"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"14"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"15"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"16"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"17"}),e.jsxs("div",{className:"relative rounded bg-indigo-100 py-2 text-center text-indigo-800",children:["18",e.jsx("div",{className:"absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 transform rounded-full bg-indigo-600"})]}),e.jsx("div",{className:"cursor-pointer py-2 text-center hover:bg-gray-100",children:"19"}),e.jsxs("div",{className:"relative rounded bg-blue-100 py-2 text-center text-blue-800",children:["20",e.jsx("div",{className:"absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 transform rounded-full bg-blue-600"})]}),e.jsx("div",{className:"cursor-pointer py-2 text-center hover:bg-gray-100",children:"21"})]}),e.jsxs("div",{className:"mt-4 flex items-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-indigo-600"}),e.jsx("span",{children:"Available clinics"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-600"}),e.jsx("span",{children:"Registered"})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Weekly View"}),e.jsxs("div",{className:"rounded-lg border p-4",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),e.jsx("span",{className:"font-medium",children:"Dec 15 - Dec 21, 2024"}),e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center gap-3 rounded p-2 hover:bg-gray-50",children:[e.jsx("div",{className:"w-16 text-sm font-medium text-gray-700",children:"Mon 16"}),e.jsx("div",{className:"flex-1 text-xs text-gray-500",children:"No clinics"})]}),e.jsxs("div",{className:"flex items-center gap-3 rounded p-2 hover:bg-gray-50",children:[e.jsx("div",{className:"w-16 text-sm font-medium text-gray-700",children:"Tue 17"}),e.jsx("div",{className:"flex-1 text-xs text-gray-500",children:"No clinics"})]}),e.jsxs("div",{className:"flex items-center gap-3 rounded p-2 hover:bg-gray-50",children:[e.jsx("div",{className:"w-16 text-sm font-medium text-gray-700",children:"Wed 18"}),e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"flex h-6 items-center rounded bg-indigo-100 px-2 text-xs text-indigo-800",children:"10:00 AM - Tennis Fundamentals"})})]}),e.jsxs("div",{className:"flex items-center gap-3 rounded p-2 hover:bg-gray-50",children:[e.jsx("div",{className:"w-16 text-sm font-medium text-gray-700",children:"Thu 19"}),e.jsx("div",{className:"flex-1 text-xs text-gray-500",children:"No clinics"})]}),e.jsxs("div",{className:"flex items-center gap-3 rounded p-2 hover:bg-gray-50",children:[e.jsx("div",{className:"w-16 text-sm font-medium text-gray-700",children:"Fri 20"}),e.jsx("div",{className:"flex-1",children:e.jsx("div",{className:"flex h-6 items-center rounded bg-blue-100 px-2 text-xs text-blue-800",children:"2:00 PM - Advanced Serve (Registered)"})})]})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Clinic & Program Features"}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-3",children:[e.jsxs("div",{className:"rounded-lg border border-indigo-200 bg-indigo-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-indigo-600 text-sm font-medium text-white",children:"1"}),e.jsx("h5",{className:"font-medium text-indigo-900",children:"Browse & Filter"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-indigo-800",children:[e.jsx("p",{children:"• View clinics by sport and skill level"}),e.jsx("p",{children:"• Filter by date, time, and availability"}),e.jsx("p",{children:"• Multiple view options: Table, Calendar, Weekly"}),e.jsx("p",{children:"• Search by category and tags"})]})]}),e.jsxs("div",{className:"rounded-lg border border-green-200 bg-green-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white",children:"2"}),e.jsx("h5",{className:"font-medium text-green-900",children:"Register & Learn"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-green-800",children:[e.jsx("p",{children:"• Detailed clinic information and curriculum"}),e.jsx("p",{children:"• Professional coach profiles and expertise"}),e.jsx("p",{children:"• Multiple participant registration"}),e.jsx("p",{children:"• Clear pricing and cancellation policies"})]})]}),e.jsxs("div",{className:"rounded-lg border border-purple-200 bg-purple-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-purple-600 text-sm font-medium text-white",children:"3"}),e.jsx("h5",{className:"font-medium text-purple-900",children:"Track Progress"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-purple-800",children:[e.jsx("p",{children:"• View registered clinics and programs"}),e.jsx("p",{children:"• Calendar integration and reminders"}),e.jsx("p",{children:"• Multi-session program tracking"}),e.jsx("p",{children:"• Skill development progression"})]})]})]})]}),a.reservation_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Reservation Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:a.reservation_description})]}),a.payment_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Payment Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:a.payment_description})]})]})}function Cs({pricing:s,sports:a}){return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-green-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold text-green-900",children:"Pricing Overview"}),e.jsx("p",{className:"text-green-800",children:"View the pricing structure for courts, lessons, and other services offered by the club. Prices may vary based on membership type, time of day, and sport selection."})]}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-2",children:[e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Court Reservations"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Hourly rates based on sport and time"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Member vs non-member pricing"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Peak and off-peak rates"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),e.jsx("span",{children:"Service fees and taxes included"})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Lessons & Coaching"}),e.jsxs("div",{className:"space-y-3 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-500"}),e.jsx("span",{children:"Coach-specific hourly rates"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-500"}),e.jsx("span",{children:"Group vs private lesson pricing"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-500"}),e.jsx("span",{children:"Package deals and discounts"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"h-2 w-2 rounded-full bg-blue-500"}),e.jsx("span",{children:"Skill level considerations"})]})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-4 shadow-sm",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Pricing Features"}),e.jsxs("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"• Dynamic pricing based on demand"}),e.jsx("p",{children:"• Membership tier discounts"}),e.jsx("p",{children:"• Seasonal rate adjustments"}),e.jsx("p",{children:"• Multi-hour booking discounts"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-600",children:[e.jsx("p",{children:"• Transparent fee breakdown"}),e.jsx("p",{children:"• Advance booking incentives"}),e.jsx("p",{children:"• Family and group rates"}),e.jsx("p",{children:"• Cancellation fee policies"})]})]})]}),s&&s.length>0?e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-3 font-medium text-gray-900",children:"Current Pricing Structure"}),e.jsx("div",{className:"space-y-2",children:s.map((j,t)=>e.jsxs("div",{className:"flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700",children:[j.sport||"General"," - ",j.type||"Standard"]}),e.jsxs("span",{className:"text-sm text-gray-600",children:["$",j.price||"Contact for pricing"]})]},t))})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("p",{className:"text-gray-600",children:"Pricing information will be displayed here when configured by the club."})}),e.jsxs("div",{className:"rounded-lg bg-blue-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-blue-900",children:"Pricing Notes"}),e.jsxs("div",{className:"space-y-2 text-sm text-blue-800",children:[e.jsx("p",{children:"• All prices are subject to applicable taxes and fees"}),e.jsx("p",{children:"• Membership discounts are automatically applied at checkout"}),e.jsx("p",{children:"• Prices may vary during special events or tournaments"}),e.jsx("p",{children:"• Contact the club directly for custom pricing arrangements"})]})]})]})}function ks({club:s}){const a=s!=null&&s.custom_request_description?JSON.parse(s==null?void 0:s.custom_request_description):{reservation_description:"",payment_description:""};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"rounded-lg bg-orange-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-lg font-semibold text-orange-900",children:"User Custom Request Experience"}),e.jsx("p",{className:"text-orange-800",children:"This shows how users experience creating custom requests for group activities and special arrangements."})]}),e.jsx("div",{className:"flex items-center justify-center bg-white p-4 shadow-sm",children:e.jsx("p",{children:"Custom Request"})}),e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"p-4",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"flex items-center justify-center rounded-lg bg-gray-50 p-3 text-center",children:"Summary"}),e.jsxs("div",{className:"space-y-4 p-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Number of players"}),e.jsxs("div",{className:"flex max-w-fit items-center gap-2 rounded-xl border border-gray-300",children:[e.jsx("button",{className:"flex h-8 w-8 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-50",children:e.jsx("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})})}),e.jsx("input",{type:"number",className:"w-8 rounded-lg border-none p-0 text-center text-gray-700",value:"5",min:1,readOnly:!0}),e.jsx("button",{className:"flex h-8 w-8 items-center justify-center rounded-lg text-gray-500 hover:bg-gray-50",children:e.jsx("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})})})]})]}),e.jsxs("div",{className:"flex gap-1",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("p",{className:"text-sm text-gray-600",children:"Custom requests are meant for parties of 5 or move players. The requests made will be checked by the club and the club will respond to you over the registered email address."})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block text-gray-900",children:["Custom request"," ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsx("textarea",{className:"w-full rounded-xl border border-gray-300 p-2 text-sm",placeholder:"Add a note",rows:4,value:"Looking to organize a tennis tournament for our company team building event. We need courts for approximately 16 players and would prefer morning slots.",maxLength:200,readOnly:!0})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Sport"}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 p-2 text-sm",value:"tennis",children:[e.jsx("option",{value:"",children:"Select sport"}),e.jsx("option",{value:"tennis",children:"Tennis"}),e.jsx("option",{value:"pickleball",children:"Pickleball"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Type"}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 p-2 text-sm",value:"singles",children:[e.jsx("option",{value:"",children:"Select type"}),e.jsx("option",{value:"singles",children:"Singles"}),e.jsx("option",{value:"doubles",children:"Doubles"})]})]})]})]})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),e.jsx("span",{className:"font-medium",children:"December 2024"}),e.jsx("button",{className:"rounded p-1 hover:bg-gray-100",children:e.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),e.jsxs("div",{className:"grid grid-cols-7 gap-1 text-sm",children:[e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"S"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"M"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"T"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"W"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"T"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"F"}),e.jsx("div",{className:"py-2 text-center font-medium text-gray-500",children:"S"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"1"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"2"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"3"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"4"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"5"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"6"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"7"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"8"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"9"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"10"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"11"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"12"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"13"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"14"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"15"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"16"}),e.jsx("div",{className:"py-2 text-center text-gray-400",children:"17"}),e.jsx("div",{className:"rounded bg-blue-100 py-2 text-center text-blue-800",children:"18"}),e.jsx("div",{className:"cursor-pointer py-2 text-center hover:bg-gray-100",children:"19"}),e.jsx("div",{className:"cursor-pointer py-2 text-center hover:bg-gray-100",children:"20"}),e.jsx("div",{className:"cursor-pointer py-2 text-center hover:bg-gray-100",children:"21"})]})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[e.jsx("h5",{className:"mb-4 font-medium text-gray-900",children:"Select Time"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"mb-3 text-sm text-gray-600",children:"December 18, 2024"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsx("button",{className:"rounded border border-gray-300 p-2 text-sm hover:bg-gray-50",children:"8:00 AM"}),e.jsx("button",{className:"rounded border border-gray-300 p-2 text-sm hover:bg-gray-50",children:"8:30 AM"}),e.jsx("button",{className:"rounded border border-gray-300 p-2 text-sm hover:bg-gray-50",children:"9:00 AM"}),e.jsx("button",{className:"rounded bg-blue-600 p-2 text-sm text-white",children:"9:30 AM"}),e.jsx("button",{className:"rounded border border-gray-300 p-2 text-sm hover:bg-gray-50",children:"10:00 AM"}),e.jsx("button",{className:"rounded border border-gray-300 p-2 text-sm hover:bg-gray-50",children:"10:30 AM"})]}),e.jsx("div",{className:"mt-4 border-t border-gray-200 pt-4",children:e.jsx("button",{className:"w-full rounded bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Next: Select coach"})})]})]})]})})})})}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Coach Selection View"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"relative max-w-md flex-1",children:[e.jsx("input",{type:"text",placeholder:"Search coaches...",className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 text-sm",value:"John"}),e.jsx("svg",{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]}),e.jsxs("button",{className:"flex items-center gap-2 rounded-lg border border-gray-300 px-3 py-2 text-sm",children:[e.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"})}),"Sort by price"]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"cursor-pointer rounded-lg border border-gray-200 p-4 hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-blue-600 font-medium text-white",children:"JS"}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-gray-900",children:"John Smith"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Tennis Pro • 8 years experience"}),e.jsxs("div",{className:"mt-1 flex items-center gap-1",children:[e.jsxs("div",{className:"flex text-yellow-400",children:[e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current text-gray-300",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})})]}),e.jsx("span",{className:"text-sm text-gray-600",children:"4.8 (24 reviews)"})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-semibold text-gray-900",children:"$85/hour"}),e.jsx("div",{className:"text-sm text-green-600",children:"Available"})]})]})}),e.jsx("div",{className:"rounded-lg border-2 border-blue-600 bg-blue-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-green-600 font-medium text-white",children:"MB"}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-blue-900",children:"Maria Brown"}),e.jsx("p",{className:"text-sm text-blue-700",children:"Tennis Pro • 12 years experience"}),e.jsxs("div",{className:"mt-1 flex items-center gap-1",children:[e.jsxs("div",{className:"flex text-yellow-400",children:[e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})})]}),e.jsx("span",{className:"text-sm text-blue-700",children:"5.0 (18 reviews)"})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-semibold text-blue-900",children:"$95/hour"}),e.jsx("div",{className:"text-sm text-blue-600",children:"Selected"})]})]})}),e.jsx("div",{className:"rounded-lg border border-gray-200 p-4 opacity-60",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-12 w-12 items-center justify-center rounded-full bg-gray-400 font-medium text-white",children:"DW"}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-gray-700",children:"David Wilson"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Tennis Pro • 6 years experience"}),e.jsxs("div",{className:"mt-1 flex items-center gap-1",children:[e.jsxs("div",{className:"flex text-yellow-400",children:[e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.jsx("svg",{className:"h-4 w-4 fill-current text-gray-300",viewBox:"0 0 20 20",children:e.jsx("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})})]}),e.jsx("span",{className:"text-sm text-gray-500",children:"4.6 (31 reviews)"})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-lg font-semibold text-gray-700",children:"$75/hour"}),e.jsx("div",{className:"text-sm text-red-600",children:"Unavailable"})]})]})})]}),e.jsxs("div",{className:"flex gap-3 border-t border-gray-200 pt-4",children:[e.jsx("button",{className:"flex-1 rounded bg-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-400",children:"Back"}),e.jsx("button",{className:"flex-1 rounded bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Continue to Details"})]})]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow-sm",children:[e.jsx("h4",{className:"mb-4 text-lg font-medium text-gray-900",children:"Custom Request Features"}),e.jsxs("div",{className:"grid grid-cols-1 gap-6 md:grid-cols-3",children:[e.jsxs("div",{className:"rounded-lg border border-orange-200 bg-orange-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-orange-600 text-sm font-medium text-white",children:"1"}),e.jsx("h5",{className:"font-medium text-orange-900",children:"Request Details"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-orange-800",children:[e.jsx("p",{children:"• Specify group size and requirements"}),e.jsx("p",{children:"• Select preferred sports and formats"}),e.jsx("p",{children:"• Choose date and time preferences"}),e.jsx("p",{children:"• Add custom notes and special requests"})]})]}),e.jsxs("div",{className:"rounded-lg border border-blue-200 bg-blue-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-blue-600 text-sm font-medium text-white",children:"2"}),e.jsx("h5",{className:"font-medium text-blue-900",children:"Coach Selection"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-blue-800",children:[e.jsx("p",{children:"• Browse available coaches for your time slot"}),e.jsx("p",{children:"• View coach profiles and ratings"}),e.jsx("p",{children:"• Compare pricing and experience levels"}),e.jsx("p",{children:"• Select preferred coach for your group"})]})]}),e.jsxs("div",{className:"rounded-lg border border-green-200 bg-green-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center space-x-3",children:[e.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white",children:"3"}),e.jsx("h5",{className:"font-medium text-green-900",children:"Club Review"})]}),e.jsxs("div",{className:"space-y-2 text-sm text-green-800",children:[e.jsx("p",{children:"• Club reviews your custom request"}),e.jsx("p",{children:"• Confirmation of availability and pricing"}),e.jsx("p",{children:"• Email notification with details"}),e.jsx("p",{children:"• Flexible arrangements for large groups"})]})]})]})]}),a.reservation_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Reservation Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:a.reservation_description})]}),a.payment_description&&e.jsxs("div",{className:"rounded-lg bg-gray-50 p-4",children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Custom Payment Message"}),e.jsx("p",{className:"text-sm text-gray-700",children:a.payment_description})]})]})}function Ms({club:s,sports:a,pricing:j,courts:t,isOpen:v,onClose:d}){const[l,x]=r.useState("Sports"),w=m=>{try{const S=JSON.parse(m||"{}").images||[];let b=new Array(9).fill(null);return S.forEach((L,P)=>{L&&L.url&&(b[P]={url:L.url,isDefault:!0,id:L.id||`default-${P}`,type:L.type||"image"})}),b}catch(n){return console.error("Error parsing splash screen:",n),new Array(9).fill(null)}},B=({sport:m,type:n,subType:S})=>{console.log({sport:m,type:n,subType:S})},f=[{title:"Sports",path:"/sports"},{title:"Prices",path:"/prices"},{title:"Home splash screen",path:"/home-splash"},{title:"Home_Logged-in",path:"/home-logged-in"},{title:"Court booking description",path:"/court-booking"},{title:"Lesson booking description",path:"/lesson-booking"},{title:"Find a Buddy booking description",path:"/find-buddy"},{title:"Clinic description",path:"/clinic"},{title:"Custom request threshold",path:"/custom-request"},{title:"Membership and modules",path:"/membership"},{title:"Custom signup form",path:"/custom-signup-form"}],V=()=>{switch(l){case"Sports":return e.jsx("div",{className:"mx-auto max-w-xl",children:e.jsx(as,{sports:a,onSelectionChange:B,isChildren:!1})});case"Custom signup form":return e.jsx(js,{club:s});case"Membership and modules":return e.jsx(ys,{club:s});case"Home splash screen":const m=JSON.parse((s==null?void 0:s.splash_screen)||"{}");return e.jsx("div",{className:"h-full",children:e.jsx(ke,{clubName:s==null?void 0:s.name,description:m==null?void 0:m.bio,imageList:w(s==null?void 0:s.splash_screen),clubLogo:s==null?void 0:s.club_logo,slideshowDelay:(m==null?void 0:m.slideshow_delay)||5e3})});case"Home_Logged-in":return e.jsx(vs,{club:s});case"Court booking description":return e.jsx(fs,{club:s});case"Lesson booking description":return e.jsx(Ns,{club:s});case"Find a Buddy booking description":return e.jsx(bs,{});case"Clinic description":return e.jsx(ws,{club:s});case"Prices":return e.jsx(Cs,{pricing:j,sports:a});case"Custom request threshold":return e.jsx(ks,{club:s});default:return e.jsxs("div",{children:[e.jsx("h2",{className:"mb-2 text-lg font-semibold",children:l}),e.jsxs("p",{className:"text-gray-600",children:["Content for ",l," will be displayed here"]})]})}};return e.jsx(fe,{isOpen:v,onClose:d,title:"Preview",children:e.jsxs("div",{className:"flex h-full flex-col",children:[e.jsx("h1",{className:"mb-4 text-xl font-bold",children:s==null?void 0:s.name}),e.jsx("div",{className:"relative",children:e.jsx("div",{className:"scrollbar-hide overflow-x-auto",children:e.jsx("div",{className:"flex space-x-4 border-b border-gray-200 px-4",children:f.map(m=>e.jsx("button",{onClick:()=>x(m.title),className:`whitespace-nowrap px-1 py-2 text-sm transition-colors duration-200 ${l===m.title?"border-b-2 border-blue-600 font-medium text-blue-600":"text-gray-500 hover:text-gray-700"}`,children:m.title},m.title))})})}),e.jsx("div",{className:"mt-3 flex-1 overflow-y-auto bg-white p-4",children:V()})]})})}new ae;function Os({selectedClub:s,profileSettings:a,fetchSettings:j,club:t,courts:v,sports:d,pricing:l,clubUser:x}){const w=ze(),[B,f]=r.useState(!1),[V,m]=r.useState(!1),[n,S]=r.useState(!1),[b,L]=r.useState(!1),[P,_]=r.useState(!1),[H,h]=r.useState(!1),[p,c]=r.useState(!1),[o,N]=r.useState(!1),[F,Z]=r.useState(!1),[Q,C]=r.useState(!1),[W,q]=r.useState(!1),[J,z]=r.useState(!1),{dispatch:le}=He.useContext(oe),[G,se]=r.useState(d),[X,y]=r.useState(""),[k,T]=r.useState(null),D=localStorage.getItem("role"),[xe,he]=r.useState([]),pe=[{title:"Sports",path:"/sports"},{title:"Prices",path:"/prices"},{title:"Home splash screen",path:"/home-splash"},{title:"Home logged in",path:"/home-logged-in"},{title:"Court booking",path:"/court-booking"},{title:"Lesson booking",path:"/lesson-booking"},{title:"Find a Buddy booking",path:"/find-buddy"},{title:"Clinic",path:"/clinic"},{title:"Custom request threshold",path:"/custom-request"},{title:"Membership and modules",path:"/membership"},{title:"Custom signup form",path:"/custom-signup-form"}],de=(E,R)=>{E.preventDefault(),R==="/sports"?w(`/${D}/club-ui/sports`,{state:{clubId:s==null?void 0:s.id}}):R==="/prices"?w(`/${D}/club-ui/pricing`,{state:{clubId:s==null?void 0:s.id}}):R==="/surface-types"?setIsSurfaceModalOpen(!0):R==="/court-booking"?h(!0):R==="/custom-request"?c(!0):R==="/home-splash"?S(!0):R==="/lesson-booking"?N(!0):R==="/membership"?w(`/${D}/club-ui/membership`,{state:{clubId:s==null?void 0:s.id}}):R==="/coach-booking"?Z(!0):R==="/clinic"?C(!0):R==="/find-buddy"?q(!0):R==="/home-logged-in"?z(!0):R==="/custom-signup-form"&&w(`/${D}/club-ui/custom-signup-form`,{state:{clubId:s==null?void 0:s.id}})},Y=()=>{X.trim()&&(se([...G,{id:G.length+1,label:`Sport ${G.length+1}`,name:X.trim()}]),y(""))};He.useEffect(()=>{le({type:"SETPATH",payload:{path:"club-ui"}})},[]),r.useState(()=>{if(t!=null&&t.splash_screen)try{const E=JSON.parse(t.splash_screen);if(E.images&&Array.isArray(E.images)){const R=new Array(9).fill(null);return E.images.forEach((re,ge)=>{re&&(R[ge]={url:re.url,isDefault:!0,id:`default-${Date.now()}-${Math.random()}`,type:re.type||"image"})}),R}}catch(E){console.error("Error parsing splash screen data:",E)}return new Array(9).fill(null)});const Ne=r.useCallback(E=>{T(()=>E)},[]),ie=t!=null&&t.splash_screen?JSON.parse(t==null?void 0:t.splash_screen):null,ue=E=>{he(E)};return e.jsxs("div",{className:"h-screen",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("h1",{className:"text-xl font-semibold",children:s?s==null?void 0:s.name:"--"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("button",{onClick:()=>_(!0),className:"flex items-center gap-2 rounded-lg border border-gray-200 p-2",children:[e.jsx("span",{children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M13.1251 5.41602C13.1251 7.14191 11.726 8.54102 10.0001 8.54102C8.27419 8.54102 6.87508 7.14191 6.87508 5.41602C6.87508 3.69013 8.27419 2.29102 10.0001 2.29102C11.726 2.29102 13.1251 3.69013 13.1251 5.41602Z",stroke:"#868C98","stroke-width":"1.5","stroke-linejoin":"round"}),e.jsx("path",{d:"M10.0001 11.041C6.83708 11.041 4.52834 13.1439 3.89304 15.9708C3.78537 16.4499 4.17439 16.8743 4.66543 16.8743H15.3347C15.8258 16.8743 16.2148 16.4499 16.1071 15.9708C15.4718 13.1439 13.1631 11.041 10.0001 11.041Z",stroke:"#868C98","stroke-width":"1.5","stroke-linejoin":"round"})]})}),e.jsx("span",{children:"User perspective"})]}),e.jsx(ls,{title:"Club UI History",emptyMessage:"No club UI history found",activityType:ne.club_ui,club:t})]})]}),e.jsx("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4",children:pe.map((E,R)=>e.jsxs(Ge,{to:E.path,onClick:re=>de(re,E.path),className:"flex items-center justify-between rounded-xl bg-white p-4 shadow-3 transition-shadow duration-200",children:[e.jsx("span",{className:"text-gray-800",children:E.title}),e.jsx("button",{className:"",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M15.2083 12.7077V4.79102M15.2083 4.79102H7.29167M15.2083 4.79102L5 14.9993",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]},R))}),e.jsx(te,{isOpen:B,onClose:()=>f(!1),title:"Sports",showFooter:!1,children:e.jsx(Ke,{onClose:()=>f(!1),fetchSettings:j,sports:d,courts:v,club:t})}),e.jsx(te,{isOpen:H,onClose:()=>h(!1),title:"Court booking",onPrimaryAction:Y,showFooter:!1,children:e.jsx(is,{onClose:()=>h(!1),fetchSettings:j,clubUser:x,club:t})}),e.jsx(te,{isOpen:p,onClose:()=>c(!1),title:"Custom request threshold",onPrimaryAction:Y,showFooter:!1,children:e.jsx(ds,{onClose:()=>c(!1),fetchSettings:j,clubUser:x,club:t,sports:d})}),e.jsx(te,{isOpen:V,onClose:()=>m(!1),title:"Pricing",showFooter:!1,children:e.jsx(cs,{pricing:l,clubUser:x,club:t})}),e.jsx(te,{isOpen:n,onClose:()=>S(!1),title:"Home splash screen",showFooter:!1,children:e.jsx(os,{fetchSettings:j,clubUser:x,setShowSplashScreenPreview:L,onImageListChange:ue,setPreviewImageList:he,club:t})}),e.jsx(te,{isOpen:o,onClose:()=>N(!1),title:"Lesson booking",showFooter:!1,children:e.jsx(xs,{fetchSettings:j,clubUser:x,club:t})}),e.jsx(te,{isOpen:F,onClose:()=>Z(!1),title:"Coach booking",showFooter:!1,children:e.jsx(hs,{fetchSettings:j,clubUser:x,club:t})}),e.jsx(te,{isOpen:Q,onClose:()=>C(!1),title:"Clinic booking",showFooter:!1,children:e.jsx(ms,{fetchSettings:j,xclubUser:x,club:t})}),e.jsx(te,{isOpen:W,onClose:()=>q(!1),title:"Find a buddy booking",showFooter:!1,children:e.jsx(ps,{fetchSettings:j,clubUser:x,club:t})}),e.jsx(gs,{fetchSettings:j,profileSettings:a,onSubmit:Ne,club:t,isOpen:J,onClose:()=>z(!1)}),e.jsx(fe,{isOpen:b,onClose:()=>L(!1),title:"Preview",children:e.jsx(ke,{clubName:t==null?void 0:t.name,description:ie==null?void 0:ie.bio,imageList:xe,clubLogo:t==null?void 0:t.club_logo,slideshowDelay:(ie==null?void 0:ie.slideshow_delay)||5e3,club:t})}),e.jsx(Ms,{club:t,isOpen:P,onClose:()=>_(!1),sports:G,pricing:l,courts:v})]})}export{Os as C};
