import{r as u,a as Rt,b as S,R as Me}from"../vendor-851db8c1.js";function ye(e,n,t){let r=t.initialDeps??[],o;return()=>{var l,i,a,s;let d;t.key&&((l=t.debug)!=null&&l.call(t))&&(d=Date.now());const c=e();if(!(c.length!==r.length||c.some((f,p)=>r[p]!==f)))return o;r=c;let x;if(t.key&&((i=t.debug)!=null&&i.call(t))&&(x=Date.now()),o=n(...c),t.key&&((a=t.debug)!=null&&a.call(t))){const f=Math.round((Date.now()-d)*100)/100,p=Math.round((Date.now()-x)*100)/100,b=p/16,h=(g,O)=>{for(g=String(g);g.length<O;)g=" "+g;return g};console.info(`%c⏱ ${h(p,5)} /${h(f,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*b,120))}deg 100% 31%);`,t==null?void 0:t.key)}return(s=t==null?void 0:t.onChange)==null||s.call(t,o),o}}function Ge(e,n){if(e===void 0)throw new Error(`Unexpected undefined${n?`: ${n}`:""}`);return e}const an=(e,n)=>Math.abs(e-n)<1,sn=(e,n,t)=>{let r;return function(...o){e.clearTimeout(r),r=e.setTimeout(()=>n.apply(this,o),t)}},un=e=>e,cn=e=>{const n=Math.max(e.startIndex-e.overscan,0),t=Math.min(e.endIndex+e.overscan,e.count-1),r=[];for(let o=n;o<=t;o++)r.push(o);return r},dn=(e,n)=>{const t=e.scrollElement;if(!t)return;const r=e.targetWindow;if(!r)return;const o=i=>{const{width:a,height:s}=i;n({width:Math.round(a),height:Math.round(s)})};if(o(t.getBoundingClientRect()),!r.ResizeObserver)return()=>{};const l=new r.ResizeObserver(i=>{const a=i[0];if(a!=null&&a.borderBoxSize){const s=a.borderBoxSize[0];if(s){o({width:s.inlineSize,height:s.blockSize});return}}o(t.getBoundingClientRect())});return l.observe(t,{box:"border-box"}),()=>{l.unobserve(t)}},Et={passive:!0},fn=typeof window>"u"?!0:"onscrollend"in window,pn=(e,n)=>{const t=e.scrollElement;if(!t)return;const r=e.targetWindow;if(!r)return;let o=0;const l=e.options.useScrollendEvent&&fn?()=>{}:sn(r,()=>{n(o,!1)},e.options.isScrollingResetDelay),i=d=>()=>{const{horizontal:c,isRtl:m}=e.options;o=c?t.scrollLeft*(m&&-1||1):t.scrollTop,l(),n(o,d)},a=i(!0),s=i(!1);return s(),t.addEventListener("scroll",a,Et),t.addEventListener("scrollend",s,Et),()=>{t.removeEventListener("scroll",a),t.removeEventListener("scrollend",s)}},mn=(e,n,t)=>{if(n!=null&&n.borderBoxSize){const r=n.borderBoxSize[0];if(r)return Math.round(r[t.options.horizontal?"inlineSize":"blockSize"])}return Math.round(e.getBoundingClientRect()[t.options.horizontal?"width":"height"])},hn=(e,{adjustments:n=0,behavior:t},r)=>{var o,l;const i=e+n;(l=(o=r.scrollElement)==null?void 0:o.scrollTo)==null||l.call(o,{[r.options.horizontal?"left":"top"]:i,behavior:t})};class vn{constructor(n){this.unsubs=[],this.scrollElement=null,this.targetWindow=null,this.isScrolling=!1,this.scrollToIndexTimeoutId=null,this.measurementsCache=[],this.itemSizeCache=new Map,this.pendingMeasuredCacheIndexes=[],this.scrollRect=null,this.scrollOffset=null,this.scrollDirection=null,this.scrollAdjustments=0,this.elementsCache=new Map,this.observer=(()=>{let t=null;const r=()=>t||(!this.targetWindow||!this.targetWindow.ResizeObserver?null:t=new this.targetWindow.ResizeObserver(o=>{o.forEach(l=>{this._measureElement(l.target,l)})}));return{disconnect:()=>{var o;(o=r())==null||o.disconnect(),t=null},observe:o=>{var l;return(l=r())==null?void 0:l.observe(o,{box:"border-box"})},unobserve:o=>{var l;return(l=r())==null?void 0:l.unobserve(o)}}})(),this.range=null,this.setOptions=t=>{Object.entries(t).forEach(([r,o])=>{typeof o>"u"&&delete t[r]}),this.options={debug:!1,initialOffset:0,overscan:1,paddingStart:0,paddingEnd:0,scrollPaddingStart:0,scrollPaddingEnd:0,horizontal:!1,getItemKey:un,rangeExtractor:cn,onChange:()=>{},measureElement:mn,initialRect:{width:0,height:0},scrollMargin:0,gap:0,indexAttribute:"data-index",initialMeasurementsCache:[],lanes:1,isScrollingResetDelay:150,enabled:!0,isRtl:!1,useScrollendEvent:!0,...t}},this.notify=t=>{var r,o;(o=(r=this.options).onChange)==null||o.call(r,this,t)},this.maybeNotify=ye(()=>(this.calculateRange(),[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]),t=>{this.notify(t)},{key:!1,debug:()=>this.options.debug,initialDeps:[this.isScrolling,this.range?this.range.startIndex:null,this.range?this.range.endIndex:null]}),this.cleanup=()=>{this.unsubs.filter(Boolean).forEach(t=>t()),this.unsubs=[],this.observer.disconnect(),this.scrollElement=null,this.targetWindow=null},this._didMount=()=>()=>{this.cleanup()},this._willUpdate=()=>{var t;const r=this.options.enabled?this.options.getScrollElement():null;if(this.scrollElement!==r){if(this.cleanup(),!r){this.maybeNotify();return}this.scrollElement=r,this.scrollElement&&"ownerDocument"in this.scrollElement?this.targetWindow=this.scrollElement.ownerDocument.defaultView:this.targetWindow=((t=this.scrollElement)==null?void 0:t.window)??null,this.elementsCache.forEach(o=>{this.observer.observe(o)}),this._scrollToOffset(this.getScrollOffset(),{adjustments:void 0,behavior:void 0}),this.unsubs.push(this.options.observeElementRect(this,o=>{this.scrollRect=o,this.maybeNotify()})),this.unsubs.push(this.options.observeElementOffset(this,(o,l)=>{this.scrollAdjustments=0,this.scrollDirection=l?this.getScrollOffset()<o?"forward":"backward":null,this.scrollOffset=o,this.isScrolling=l,this.maybeNotify()}))}},this.getSize=()=>this.options.enabled?(this.scrollRect=this.scrollRect??this.options.initialRect,this.scrollRect[this.options.horizontal?"width":"height"]):(this.scrollRect=null,0),this.getScrollOffset=()=>this.options.enabled?(this.scrollOffset=this.scrollOffset??(typeof this.options.initialOffset=="function"?this.options.initialOffset():this.options.initialOffset),this.scrollOffset):(this.scrollOffset=null,0),this.getFurthestMeasurement=(t,r)=>{const o=new Map,l=new Map;for(let i=r-1;i>=0;i--){const a=t[i];if(o.has(a.lane))continue;const s=l.get(a.lane);if(s==null||a.end>s.end?l.set(a.lane,a):a.end<s.end&&o.set(a.lane,!0),o.size===this.options.lanes)break}return l.size===this.options.lanes?Array.from(l.values()).sort((i,a)=>i.end===a.end?i.index-a.index:i.end-a.end)[0]:void 0},this.getMeasurementOptions=ye(()=>[this.options.count,this.options.paddingStart,this.options.scrollMargin,this.options.getItemKey,this.options.enabled],(t,r,o,l,i)=>(this.pendingMeasuredCacheIndexes=[],{count:t,paddingStart:r,scrollMargin:o,getItemKey:l,enabled:i}),{key:!1}),this.getMeasurements=ye(()=>[this.getMeasurementOptions(),this.itemSizeCache],({count:t,paddingStart:r,scrollMargin:o,getItemKey:l,enabled:i},a)=>{if(!i)return this.measurementsCache=[],this.itemSizeCache.clear(),[];this.measurementsCache.length===0&&(this.measurementsCache=this.options.initialMeasurementsCache,this.measurementsCache.forEach(c=>{this.itemSizeCache.set(c.key,c.size)}));const s=this.pendingMeasuredCacheIndexes.length>0?Math.min(...this.pendingMeasuredCacheIndexes):0;this.pendingMeasuredCacheIndexes=[];const d=this.measurementsCache.slice(0,s);for(let c=s;c<t;c++){const m=l(c),x=this.options.lanes===1?d[c-1]:this.getFurthestMeasurement(d,c),f=x?x.end+this.options.gap:r+o,p=a.get(m),b=typeof p=="number"?p:this.options.estimateSize(c),h=f+b,g=x?x.lane:c%this.options.lanes;d[c]={index:c,start:f,size:b,end:h,key:m,lane:g}}return this.measurementsCache=d,d},{key:!1,debug:()=>this.options.debug}),this.calculateRange=ye(()=>[this.getMeasurements(),this.getSize(),this.getScrollOffset()],(t,r,o)=>this.range=t.length>0&&r>0?bn({measurements:t,outerSize:r,scrollOffset:o}):null,{key:!1,debug:()=>this.options.debug}),this.getIndexes=ye(()=>[this.options.rangeExtractor,this.calculateRange(),this.options.overscan,this.options.count],(t,r,o,l)=>r===null?[]:t({startIndex:r.startIndex,endIndex:r.endIndex,overscan:o,count:l}),{key:!1,debug:()=>this.options.debug}),this.indexFromElement=t=>{const r=this.options.indexAttribute,o=t.getAttribute(r);return o?parseInt(o,10):(console.warn(`Missing attribute name '${r}={index}' on measured element.`),-1)},this._measureElement=(t,r)=>{const o=this.indexFromElement(t),l=this.measurementsCache[o];if(!l)return;const i=l.key,a=this.elementsCache.get(i);a!==t&&(a&&this.observer.unobserve(a),this.observer.observe(t),this.elementsCache.set(i,t)),t.isConnected&&this.resizeItem(o,this.options.measureElement(t,r,this))},this.resizeItem=(t,r)=>{const o=this.measurementsCache[t];if(!o)return;const l=this.itemSizeCache.get(o.key)??o.size,i=r-l;i!==0&&((this.shouldAdjustScrollPositionOnItemSizeChange!==void 0?this.shouldAdjustScrollPositionOnItemSizeChange(o,i,this):o.start<this.getScrollOffset()+this.scrollAdjustments)&&this._scrollToOffset(this.getScrollOffset(),{adjustments:this.scrollAdjustments+=i,behavior:void 0}),this.pendingMeasuredCacheIndexes.push(o.index),this.itemSizeCache=new Map(this.itemSizeCache.set(o.key,r)),this.notify(!1))},this.measureElement=t=>{if(!t){this.elementsCache.forEach((r,o)=>{r.isConnected||(this.observer.unobserve(r),this.elementsCache.delete(o))});return}this._measureElement(t,void 0)},this.getVirtualItems=ye(()=>[this.getIndexes(),this.getMeasurements()],(t,r)=>{const o=[];for(let l=0,i=t.length;l<i;l++){const a=t[l],s=r[a];o.push(s)}return o},{key:!1,debug:()=>this.options.debug}),this.getVirtualItemForOffset=t=>{const r=this.getMeasurements();if(r.length!==0)return Ge(r[It(0,r.length-1,o=>Ge(r[o]).start,t)])},this.getOffsetForAlignment=(t,r)=>{const o=this.getSize(),l=this.getScrollOffset();r==="auto"&&(t<=l?r="start":t>=l+o?r="end":r="start"),r==="start"?t=t:r==="end"?t=t-o:r==="center"&&(t=t-o/2);const i=this.options.horizontal?"scrollWidth":"scrollHeight",s=(this.scrollElement?"document"in this.scrollElement?this.scrollElement.document.documentElement[i]:this.scrollElement[i]:0)-o;return Math.max(Math.min(s,t),0)},this.getOffsetForIndex=(t,r="auto")=>{t=Math.max(0,Math.min(t,this.options.count-1));const o=this.measurementsCache[t];if(!o)return;const l=this.getSize(),i=this.getScrollOffset();if(r==="auto")if(o.end>=i+l-this.options.scrollPaddingEnd)r="end";else if(o.start<=i+this.options.scrollPaddingStart)r="start";else return[i,r];const a=r==="end"?o.end+this.options.scrollPaddingEnd:o.start-this.options.scrollPaddingStart;return[this.getOffsetForAlignment(a,r),r]},this.isDynamicMode=()=>this.elementsCache.size>0,this.cancelScrollToIndex=()=>{this.scrollToIndexTimeoutId!==null&&this.targetWindow&&(this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId),this.scrollToIndexTimeoutId=null)},this.scrollToOffset=(t,{align:r="start",behavior:o}={})=>{this.cancelScrollToIndex(),o==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getOffsetForAlignment(t,r),{adjustments:void 0,behavior:o})},this.scrollToIndex=(t,{align:r="auto",behavior:o}={})=>{t=Math.max(0,Math.min(t,this.options.count-1)),this.cancelScrollToIndex(),o==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size.");const l=this.getOffsetForIndex(t,r);if(!l)return;const[i,a]=l;this._scrollToOffset(i,{adjustments:void 0,behavior:o}),o!=="smooth"&&this.isDynamicMode()&&this.targetWindow&&(this.scrollToIndexTimeoutId=this.targetWindow.setTimeout(()=>{if(this.scrollToIndexTimeoutId=null,this.elementsCache.has(this.options.getItemKey(t))){const[d]=Ge(this.getOffsetForIndex(t,a));an(d,this.getScrollOffset())||this.scrollToIndex(t,{align:a,behavior:o})}else this.scrollToIndex(t,{align:a,behavior:o})}))},this.scrollBy=(t,{behavior:r}={})=>{this.cancelScrollToIndex(),r==="smooth"&&this.isDynamicMode()&&console.warn("The `smooth` scroll behavior is not fully supported with dynamic size."),this._scrollToOffset(this.getScrollOffset()+t,{adjustments:void 0,behavior:r})},this.getTotalSize=()=>{var t;const r=this.getMeasurements();let o;return r.length===0?o=this.options.paddingStart:o=this.options.lanes===1?((t=r[r.length-1])==null?void 0:t.end)??0:Math.max(...r.slice(-this.options.lanes).map(l=>l.end)),Math.max(o-this.options.scrollMargin+this.options.paddingEnd,0)},this._scrollToOffset=(t,{adjustments:r,behavior:o})=>{this.options.scrollToFn(t,{behavior:o,adjustments:r},this)},this.measure=()=>{this.itemSizeCache=new Map,this.notify(!1)},this.setOptions(n)}}const It=(e,n,t,r)=>{for(;e<=n;){const o=(e+n)/2|0,l=t(o);if(l<r)e=o+1;else if(l>r)n=o-1;else return o}return e>0?e-1:0};function bn({measurements:e,outerSize:n,scrollOffset:t}){const r=e.length-1,l=It(0,r,a=>e[a].start,t);let i=l;for(;i<r&&e[i].end<t+n;)i++;return{startIndex:l,endIndex:i}}const gn=typeof document<"u"?u.useLayoutEffect:u.useEffect;function xn(e){const n=u.useReducer(()=>({}),{})[1],t={...e,onChange:(o,l)=>{var i;l?Rt.flushSync(n):n(),(i=e.onChange)==null||i.call(e,o,l)}},[r]=u.useState(()=>new vn(t));return r.setOptions(t),u.useEffect(()=>r._didMount(),[]),gn(()=>r._willUpdate()),r}function En(e){return xn({observeElementRect:dn,observeElementOffset:pn,scrollToFn:hn,...e})}var yn=Object.defineProperty,Sn=(e,n,t)=>n in e?yn(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,Xe=(e,n,t)=>(Sn(e,typeof n!="symbol"?n+"":n,t),t);let wn=class{constructor(){Xe(this,"current",this.detect()),Xe(this,"handoffState","pending"),Xe(this,"currentId",0)}set(n){this.current!==n&&(this.handoffState="pending",this.currentId=0,this.current=n)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},ae=new wn,P=(e,n)=>{ae.isServer?u.useEffect(e,n):u.useLayoutEffect(e,n)};function J(e){let n=u.useRef(e);return P(()=>{n.current=e},[e]),n}function ut(e,n){let[t,r]=u.useState(e),o=J(e);return P(()=>r(o.current),[o,r,...n]),t}let E=function(e){let n=J(e);return S.useCallback((...t)=>n.current(...t),[n])};function $t(e,n,t){let[r,o]=u.useState(t),l=e!==void 0,i=u.useRef(l),a=u.useRef(!1),s=u.useRef(!1);return l&&!i.current&&!a.current?(a.current=!0,i.current=l,console.error("A component is changing from uncontrolled to controlled. This may be caused by the value changing from undefined to a defined value, which should not happen.")):!l&&i.current&&!s.current&&(s.current=!0,i.current=l,console.error("A component is changing from controlled to uncontrolled. This may be caused by the value changing from a defined value to undefined, which should not happen.")),[l?e:r,E(d=>(l||o(d),n==null?void 0:n(d)))]}function Le(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(n=>setTimeout(()=>{throw n}))}function se(){let e=[],n={addEventListener(t,r,o,l){return t.addEventListener(r,o,l),n.add(()=>t.removeEventListener(r,o,l))},requestAnimationFrame(...t){let r=requestAnimationFrame(...t);return n.add(()=>cancelAnimationFrame(r))},nextFrame(...t){return n.requestAnimationFrame(()=>n.requestAnimationFrame(...t))},setTimeout(...t){let r=setTimeout(...t);return n.add(()=>clearTimeout(r))},microTask(...t){let r={current:!0};return Le(()=>{r.current&&t[0]()}),n.add(()=>{r.current=!1})},style(t,r,o){let l=t.style.getPropertyValue(r);return Object.assign(t.style,{[r]:o}),this.add(()=>{Object.assign(t.style,{[r]:l})})},group(t){let r=se();return t(r),this.add(()=>r.dispose())},add(t){return e.push(t),()=>{let r=e.indexOf(t);if(r>=0)for(let o of e.splice(r,1))o()}},dispose(){for(let t of e.splice(0))t()}};return n}function ge(){let[e]=u.useState(se);return u.useEffect(()=>()=>e.dispose(),[e]),e}function Tn(){let e=typeof document>"u";return"useSyncExternalStore"in Me?(n=>n.useSyncExternalStore)(Me)(()=>()=>{},()=>!1,()=>!e):!1}function Te(){let e=Tn(),[n,t]=u.useState(ae.isHandoffComplete);return n&&ae.isHandoffComplete===!1&&t(!1),u.useEffect(()=>{n!==!0&&t(!0)},[n]),u.useEffect(()=>ae.handoff(),[]),e?!1:n}var yt;let Q=(yt=S.useId)!=null?yt:function(){let e=Te(),[n,t]=S.useState(e?()=>ae.nextId():null);return P(()=>{n===null&&t(ae.nextId())},[n]),n!=null?""+n:void 0};function D(e,n,...t){if(e in n){let o=n[e];return typeof o=="function"?o(...t):o}let r=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(n).map(o=>`"${o}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,D),r}function Be(e){return ae.isServer?null:e instanceof Node?e.ownerDocument:e!=null&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}let rt=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var K=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(K||{}),Se=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(Se||{}),On=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(On||{});function Cn(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(rt)).sort((n,t)=>Math.sign((n.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var Pt=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(Pt||{});function Rn(e,n=0){var t;return e===((t=Be(e))==null?void 0:t.body)?!1:D(n,{0(){return e.matches(rt)},1(){let r=e;for(;r!==null;){if(r.matches(rt))return!0;r=r.parentElement}return!1}})}var In=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(In||{});typeof window<"u"&&typeof document<"u"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function he(e){e==null||e.focus({preventScroll:!0})}let $n=["textarea","input"].join(",");function Pn(e){var n,t;return(t=(n=e==null?void 0:e.matches)==null?void 0:n.call(e,$n))!=null?t:!1}function pe(e,n=t=>t){return e.slice().sort((t,r)=>{let o=n(t),l=n(r);if(o===null||l===null)return 0;let i=o.compareDocumentPosition(l);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function le(e,n,{sorted:t=!0,relativeTo:r=null,skipElements:o=[]}={}){let l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,i=Array.isArray(e)?t?pe(e):e:Cn(e);o.length>0&&i.length>1&&(i=i.filter(f=>!o.includes(f))),r=r??l.activeElement;let a=(()=>{if(n&5)return 1;if(n&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),s=(()=>{if(n&1)return 0;if(n&2)return Math.max(0,i.indexOf(r))-1;if(n&4)return Math.max(0,i.indexOf(r))+1;if(n&8)return i.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=n&32?{preventScroll:!0}:{},c=0,m=i.length,x;do{if(c>=m||c+m<=0)return 0;let f=s+c;if(n&16)f=(f+m)%m;else{if(f<0)return 3;if(f>=m)return 1}x=i[f],x==null||x.focus(d),c+=a}while(x!==l.activeElement);return n&6&&Pn(x)&&x.select(),2}function Mt(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function Mn(){return/Android/gi.test(window.navigator.userAgent)}function Ft(){return Mt()||Mn()}function Ne(e,n,t){let r=J(n);u.useEffect(()=>{function o(l){r.current(l)}return document.addEventListener(e,o,t),()=>document.removeEventListener(e,o,t)},[e,t])}function Lt(e,n,t){let r=J(n);u.useEffect(()=>{function o(l){r.current(l)}return window.addEventListener(e,o,t),()=>window.removeEventListener(e,o,t)},[e,t])}function Dt(e,n,t=!0){let r=u.useRef(!1);u.useEffect(()=>{requestAnimationFrame(()=>{r.current=t})},[t]);function o(i,a){if(!r.current||i.defaultPrevented)return;let s=a(i);if(s===null||!s.getRootNode().contains(s)||!s.isConnected)return;let d=function c(m){return typeof m=="function"?c(m()):Array.isArray(m)||m instanceof Set?m:[m]}(e);for(let c of d){if(c===null)continue;let m=c instanceof HTMLElement?c:c.current;if(m!=null&&m.contains(s)||i.composed&&i.composedPath().includes(m))return}return!Rn(s,Pt.Loose)&&s.tabIndex!==-1&&i.preventDefault(),n(i,s)}let l=u.useRef(null);Ne("pointerdown",i=>{var a,s;r.current&&(l.current=((s=(a=i.composedPath)==null?void 0:a.call(i))==null?void 0:s[0])||i.target)},!0),Ne("mousedown",i=>{var a,s;r.current&&(l.current=((s=(a=i.composedPath)==null?void 0:a.call(i))==null?void 0:s[0])||i.target)},!0),Ne("click",i=>{Ft()||l.current&&(o(i,()=>l.current),l.current=null)},!0),Ne("touchend",i=>o(i,()=>i.target instanceof HTMLElement?i.target:null),!0),Lt("blur",i=>o(i,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}function Oe(...e){return u.useMemo(()=>Be(...e),[...e])}function St(e){var n;if(e.type)return e.type;let t=(n=e.as)!=null?n:"button";if(typeof t=="string"&&t.toLowerCase()==="button")return"button"}function ct(e,n){let[t,r]=u.useState(()=>St(e));return P(()=>{r(St(e))},[e.type,e.as]),P(()=>{t||n.current&&n.current instanceof HTMLButtonElement&&!n.current.hasAttribute("type")&&r("button")},[t,n]),t}let kt=Symbol();function Fn(e,n=!0){return Object.assign(e,{[kt]:n})}function H(...e){let n=u.useRef(e);u.useEffect(()=>{n.current=e},[e]);let t=E(r=>{for(let o of n.current)o!=null&&(typeof o=="function"?o(r):o.current=r)});return e.every(r=>r==null||(r==null?void 0:r[kt]))?void 0:t}function wt(e){return[e.screenX,e.screenY]}function Ln(){let e=u.useRef([-1,-1]);return{wasMoved(n){let t=wt(n);return e.current[0]===t[0]&&e.current[1]===t[1]?!1:(e.current=t,!0)},update(n){e.current=wt(n)}}}function Dn({container:e,accept:n,walk:t,enabled:r=!0}){let o=u.useRef(n),l=u.useRef(t);u.useEffect(()=>{o.current=n,l.current=t},[n,t]),P(()=>{if(!e||!r)return;let i=Be(e);if(!i)return;let a=o.current,s=l.current,d=Object.assign(m=>a(m),{acceptNode:a}),c=i.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,d,!1);for(;c.nextNode();)s(c.currentNode)},[e,r,o,l])}function Fe(e,n){let t=u.useRef([]),r=E(e);u.useEffect(()=>{let o=[...t.current];for(let[l,i]of n.entries())if(t.current[l]!==i){let a=r(n,o);return t.current=n,a}},[r,...n])}function ze(...e){return Array.from(new Set(e.flatMap(n=>typeof n=="string"?n.split(" "):[]))).filter(Boolean).join(" ")}var de=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(de||{}),ce=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(ce||{});function A({ourProps:e,theirProps:n,slot:t,defaultTag:r,features:o,visible:l=!0,name:i,mergeRefs:a}){a=a??kn;let s=At(n,e);if(l)return _e(s,t,r,i,a);let d=o??0;if(d&2){let{static:c=!1,...m}=s;if(c)return _e(m,t,r,i,a)}if(d&1){let{unmount:c=!0,...m}=s;return D(c?0:1,{0(){return null},1(){return _e({...m,hidden:!0,style:{display:"none"}},t,r,i,a)}})}return _e(s,t,r,i,a)}function _e(e,n={},t,r,o){let{as:l=t,children:i,refName:a="ref",...s}=Je(e,["unmount","static"]),d=e.ref!==void 0?{[a]:e.ref}:{},c=typeof i=="function"?i(n):i;"className"in s&&s.className&&typeof s.className=="function"&&(s.className=s.className(n));let m={};if(n){let x=!1,f=[];for(let[p,b]of Object.entries(n))typeof b=="boolean"&&(x=!0),b===!0&&f.push(p);x&&(m["data-headlessui-state"]=f.join(" "))}if(l===u.Fragment&&Object.keys(je(s)).length>0){if(!u.isValidElement(c)||Array.isArray(c)&&c.length>1)throw new Error(['Passing props on "Fragment"!',"",`The current component <${r} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(s).map(b=>`  - ${b}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(b=>`  - ${b}`).join(`
`)].join(`
`));let x=c.props,f=typeof(x==null?void 0:x.className)=="function"?(...b)=>ze(x==null?void 0:x.className(...b),s.className):ze(x==null?void 0:x.className,s.className),p=f?{className:f}:{};return u.cloneElement(c,Object.assign({},At(c.props,je(Je(s,["ref"]))),m,d,{ref:o(c.ref,d.ref)},p))}return u.createElement(l,Object.assign({},Je(s,["ref"]),l!==u.Fragment&&d,l!==u.Fragment&&m),c)}function kn(...e){return e.every(n=>n==null)?void 0:n=>{for(let t of e)t!=null&&(typeof t=="function"?t(n):t.current=n)}}function At(...e){if(e.length===0)return{};if(e.length===1)return e[0];let n={},t={};for(let r of e)for(let o in r)o.startsWith("on")&&typeof r[o]=="function"?(t[o]!=null||(t[o]=[]),t[o].push(r[o])):n[o]=r[o];if(n.disabled||n["aria-disabled"])return Object.assign(n,Object.fromEntries(Object.keys(t).map(r=>[r,void 0])));for(let r in t)Object.assign(n,{[r](o,...l){let i=t[r];for(let a of i){if((o instanceof Event||(o==null?void 0:o.nativeEvent)instanceof Event)&&o.defaultPrevented)return;a(o,...l)}}});return n}function N(e){var n;return Object.assign(u.forwardRef(e),{displayName:(n=e.displayName)!=null?n:e.name})}function je(e){let n=Object.assign({},e);for(let t in n)n[t]===void 0&&delete n[t];return n}function Je(e,n=[]){let t=Object.assign({},e);for(let r of n)r in t&&delete t[r];return t}let An="div";var ve=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(ve||{});function Nn(e,n){var t;let{features:r=1,...o}=e,l={ref:n,"aria-hidden":(r&2)===2?!0:(t=o["aria-hidden"])!=null?t:void 0,hidden:(r&4)===4?!0:void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...(r&4)===4&&(r&2)!==2&&{display:"none"}}};return A({ourProps:l,theirProps:o,slot:{},defaultTag:An,name:"Hidden"})}let be=N(Nn),dt=u.createContext(null);dt.displayName="OpenClosedContext";var Y=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(Y||{});function Ue(){return u.useContext(dt)}function Nt({value:e,children:n}){return S.createElement(dt.Provider,{value:e},n)}function _n(e){function n(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",n))}typeof window<"u"&&typeof document<"u"&&(document.addEventListener("DOMContentLoaded",n),n())}let ie=[];_n(()=>{function e(n){n.target instanceof HTMLElement&&n.target!==document.body&&ie[0]!==n.target&&(ie.unshift(n.target),ie=ie.filter(t=>t!=null&&t.isConnected),ie.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function ft(e){let n=e.parentElement,t=null;for(;n&&!(n instanceof HTMLFieldSetElement);)n instanceof HTMLLegendElement&&(t=n),n=n.parentElement;let r=(n==null?void 0:n.getAttribute("disabled"))==="";return r&&zn(t)?!1:r}function zn(e){if(!e)return!1;let n=e.previousElementSibling;for(;n!==null;){if(n instanceof HTMLLegendElement)return!1;n=n.previousElementSibling}return!0}function jn(e){throw new Error("Unexpected object: "+e)}var V=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(V||{});function Tt(e,n){let t=n.resolveItems();if(t.length<=0)return null;let r=n.resolveActiveIndex(),o=r??-1;switch(e.focus){case 0:{for(let l=0;l<t.length;++l)if(!n.resolveDisabled(t[l],l,t))return l;return r}case 1:{for(let l=o-1;l>=0;--l)if(!n.resolveDisabled(t[l],l,t))return l;return r}case 2:{for(let l=o+1;l<t.length;++l)if(!n.resolveDisabled(t[l],l,t))return l;return r}case 3:{for(let l=t.length-1;l>=0;--l)if(!n.resolveDisabled(t[l],l,t))return l;return r}case 4:{for(let l=0;l<t.length;++l)if(n.resolveId(t[l],l,t)===e.id)return l;return r}case 5:return null;default:jn(e)}}function _t(e={},n=null,t=[]){for(let[r,o]of Object.entries(e))jt(t,zt(n,r),o);return t}function zt(e,n){return e?e+"["+n+"]":n}function jt(e,n,t){if(Array.isArray(t))for(let[r,o]of t.entries())jt(e,zt(n,r.toString()),o);else t instanceof Date?e.push([n,t.toISOString()]):typeof t=="boolean"?e.push([n,t?"1":"0"]):typeof t=="string"?e.push([n,t]):typeof t=="number"?e.push([n,`${t}`]):t==null?e.push([n,""]):_t(t,n,e)}function Hn(e){var n,t;let r=(n=e==null?void 0:e.form)!=null?n:e.closest("form");if(r){for(let o of r.elements)if(o!==e&&(o.tagName==="INPUT"&&o.type==="submit"||o.tagName==="BUTTON"&&o.type==="submit"||o.nodeName==="INPUT"&&o.type==="image")){o.click();return}(t=r.requestSubmit)==null||t.call(r)}}var k=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(k||{}),Bn=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Bn||{}),Un=(e=>(e[e.Single=0]="Single",e[e.Multi=1]="Multi",e))(Un||{}),Wn=(e=>(e[e.Pointer=0]="Pointer",e[e.Focus=1]="Focus",e[e.Other=2]="Other",e))(Wn||{}),Vn=(e=>(e[e.OpenCombobox=0]="OpenCombobox",e[e.CloseCombobox=1]="CloseCombobox",e[e.GoToOption=2]="GoToOption",e[e.RegisterOption=3]="RegisterOption",e[e.UnregisterOption=4]="UnregisterOption",e[e.RegisterLabel=5]="RegisterLabel",e[e.SetActivationTrigger=6]="SetActivationTrigger",e[e.UpdateVirtualOptions=7]="UpdateVirtualOptions",e))(Vn||{});function Qe(e,n=t=>t){let t=e.activeOptionIndex!==null?e.options[e.activeOptionIndex]:null,r=n(e.options.slice()),o=r.length>0&&r[0].dataRef.current.order!==null?r.sort((i,a)=>i.dataRef.current.order-a.dataRef.current.order):pe(r,i=>i.dataRef.current.domRef.current),l=t?o.indexOf(t):null;return l===-1&&(l=null),{options:o,activeOptionIndex:l}}let qn={1(e){var n;return(n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===1?e:{...e,activeOptionIndex:null,comboboxState:1}},0(e){var n,t;if((n=e.dataRef.current)!=null&&n.disabled||e.comboboxState===0)return e;if((t=e.dataRef.current)!=null&&t.value){let r=e.dataRef.current.calculateIndex(e.dataRef.current.value);if(r!==-1)return{...e,activeOptionIndex:r,comboboxState:0}}return{...e,comboboxState:0}},2(e,n){var t,r,o,l,i;if((t=e.dataRef.current)!=null&&t.disabled||(r=e.dataRef.current)!=null&&r.optionsRef.current&&!((o=e.dataRef.current)!=null&&o.optionsPropsRef.current.static)&&e.comboboxState===1)return e;if(e.virtual){let c=n.focus===V.Specific?n.idx:Tt(n,{resolveItems:()=>e.virtual.options,resolveActiveIndex:()=>{var x,f;return(f=(x=e.activeOptionIndex)!=null?x:e.virtual.options.findIndex(p=>!e.virtual.disabled(p)))!=null?f:null},resolveDisabled:e.virtual.disabled,resolveId(){throw new Error("Function not implemented.")}}),m=(l=n.trigger)!=null?l:2;return e.activeOptionIndex===c&&e.activationTrigger===m?e:{...e,activeOptionIndex:c,activationTrigger:m}}let a=Qe(e);if(a.activeOptionIndex===null){let c=a.options.findIndex(m=>!m.dataRef.current.disabled);c!==-1&&(a.activeOptionIndex=c)}let s=n.focus===V.Specific?n.idx:Tt(n,{resolveItems:()=>a.options,resolveActiveIndex:()=>a.activeOptionIndex,resolveId:c=>c.id,resolveDisabled:c=>c.dataRef.current.disabled}),d=(i=n.trigger)!=null?i:2;return e.activeOptionIndex===s&&e.activationTrigger===d?e:{...e,...a,activeOptionIndex:s,activationTrigger:d}},3:(e,n)=>{var t,r,o;if((t=e.dataRef.current)!=null&&t.virtual)return{...e,options:[...e.options,n.payload]};let l=n.payload,i=Qe(e,s=>(s.push(l),s));e.activeOptionIndex===null&&(r=e.dataRef.current)!=null&&r.isSelected(n.payload.dataRef.current.value)&&(i.activeOptionIndex=i.options.indexOf(l));let a={...e,...i,activationTrigger:2};return(o=e.dataRef.current)!=null&&o.__demoMode&&e.dataRef.current.value===void 0&&(a.activeOptionIndex=0),a},4:(e,n)=>{var t;if((t=e.dataRef.current)!=null&&t.virtual)return{...e,options:e.options.filter(o=>o.id!==n.id)};let r=Qe(e,o=>{let l=o.findIndex(i=>i.id===n.id);return l!==-1&&o.splice(l,1),o});return{...e,...r,activationTrigger:2}},5:(e,n)=>e.labelId===n.id?e:{...e,labelId:n.id},6:(e,n)=>e.activationTrigger===n.trigger?e:{...e,activationTrigger:n.trigger},7:(e,n)=>{var t;if(((t=e.virtual)==null?void 0:t.options)===n.options)return e;let r=e.activeOptionIndex;if(e.activeOptionIndex!==null){let o=n.options.indexOf(e.virtual.options[e.activeOptionIndex]);o!==-1?r=o:r=null}return{...e,activeOptionIndex:r,virtual:Object.assign({},e.virtual,{options:n.options})}}},pt=u.createContext(null);pt.displayName="ComboboxActionsContext";function De(e){let n=u.useContext(pt);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,De),t}return n}let Ht=u.createContext(null);function Kn(e){var n;let t=xe("VirtualProvider"),[r,o]=u.useMemo(()=>{let s=t.optionsRef.current;if(!s)return[0,0];let d=window.getComputedStyle(s);return[parseFloat(d.paddingBlockStart||d.paddingTop),parseFloat(d.paddingBlockEnd||d.paddingBottom)]},[t.optionsRef.current]),l=En({scrollPaddingStart:r,scrollPaddingEnd:o,count:t.virtual.options.length,estimateSize(){return 40},getScrollElement(){var s;return(s=t.optionsRef.current)!=null?s:null},overscan:12}),[i,a]=u.useState(0);return P(()=>{a(s=>s+1)},[(n=t.virtual)==null?void 0:n.options]),S.createElement(Ht.Provider,{value:l},S.createElement("div",{style:{position:"relative",width:"100%",height:`${l.getTotalSize()}px`},ref:s=>{if(s){if(typeof process<"u"&&{}.JEST_WORKER_ID!==void 0||t.activationTrigger===0)return;t.activeOptionIndex!==null&&t.virtual.options.length>t.activeOptionIndex&&l.scrollToIndex(t.activeOptionIndex)}}},l.getVirtualItems().map(s=>{var d;return S.createElement(u.Fragment,{key:s.key},S.cloneElement((d=e.children)==null?void 0:d.call(e,{option:t.virtual.options[s.index],open:t.comboboxState===0}),{key:`${i}-${s.key}`,"data-index":s.index,"aria-setsize":t.virtual.options.length,"aria-posinset":s.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${s.start}px)`,overflowAnchor:"none"}}))})))}let mt=u.createContext(null);mt.displayName="ComboboxDataContext";function xe(e){let n=u.useContext(mt);if(n===null){let t=new Error(`<${e} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,xe),t}return n}function Yn(e,n){return D(n.type,qn,e,n)}let Gn=u.Fragment;function Xn(e,n){let{value:t,defaultValue:r,onChange:o,form:l,name:i,by:a=null,disabled:s=!1,__demoMode:d=!1,nullable:c=!1,multiple:m=!1,immediate:x=!1,virtual:f=null,...p}=e,b=!1,h=null,[g=m?[]:void 0,O]=$t(t,o,r),[w,$]=u.useReducer(Yn,{dataRef:u.createRef(),comboboxState:d?0:1,options:[],virtual:null,activeOptionIndex:null,activationTrigger:2,labelId:null}),F=u.useRef(!1),M=u.useRef({static:!1,hold:!1}),U=u.useRef(null),B=u.useRef(null),T=u.useRef(null),y=u.useRef(null),C=E(typeof a=="string"?(R,L)=>{let I=a;return(R==null?void 0:R[I])===(L==null?void 0:L[I])}:a??((R,L)=>R===L)),z=E(R=>w.options.findIndex(L=>C(L.dataRef.current.value,R))),re=u.useCallback(R=>D(v.mode,{1:()=>g.some(L=>C(L,R)),0:()=>C(g,R)}),[g]),te=E(R=>w.activeOptionIndex===z(R)),v=u.useMemo(()=>({...w,immediate:b,optionsPropsRef:M,labelRef:U,inputRef:B,buttonRef:T,optionsRef:y,value:g,defaultValue:r,disabled:s,mode:m?1:0,virtual:w.virtual,get activeOptionIndex(){if(F.current&&w.activeOptionIndex===null&&w.options.length>0){let R=w.options.findIndex(L=>!L.dataRef.current.disabled);if(R!==-1)return R}return w.activeOptionIndex},calculateIndex:z,compare:C,isSelected:re,isActive:te,nullable:c,__demoMode:d}),[g,r,s,m,c,d,w,h]);P(()=>{},[h,void 0]),P(()=>{w.dataRef.current=v},[v]),Dt([v.buttonRef,v.inputRef,v.optionsRef],()=>Re.closeCombobox(),v.comboboxState===0);let q=u.useMemo(()=>{var R,L,I;return{open:v.comboboxState===0,disabled:s,activeIndex:v.activeOptionIndex,activeOption:v.activeOptionIndex===null?null:v.virtual?v.virtual.options[(R=v.activeOptionIndex)!=null?R:0]:(I=(L=v.options[v.activeOptionIndex])==null?void 0:L.dataRef.current.value)!=null?I:null,value:g}},[v,s,g]),_=E(()=>{if(v.activeOptionIndex!==null){if(v.virtual)fe(v.virtual.options[v.activeOptionIndex]);else{let{dataRef:R}=v.options[v.activeOptionIndex];fe(R.current.value)}Re.goToOption(V.Specific,v.activeOptionIndex)}}),G=E(()=>{$({type:0}),F.current=!0}),j=E(()=>{$({type:1}),F.current=!1}),W=E((R,L,I)=>(F.current=!1,R===V.Specific?$({type:2,focus:V.Specific,idx:L,trigger:I}):$({type:2,focus:R,trigger:I}))),ee=E((R,L)=>($({type:3,payload:{id:R,dataRef:L}}),()=>{v.isActive(L.current.value)&&(F.current=!0),$({type:4,id:R})})),oe=E(R=>($({type:5,id:R}),()=>$({type:5,id:null}))),fe=E(R=>D(v.mode,{0(){return O==null?void 0:O(R)},1(){let L=v.value.slice(),I=L.findIndex(Z=>C(Z,R));return I===-1?L.push(R):L.splice(I,1),O==null?void 0:O(L)}})),ne=E(R=>{$({type:6,trigger:R})}),Re=u.useMemo(()=>({onChange:fe,registerOption:ee,registerLabel:oe,goToOption:W,closeCombobox:j,openCombobox:G,setActivationTrigger:ne,selectActiveOption:_}),[]),Ke=n===null?{}:{ref:n},Ee=u.useRef(null),Ye=ge();return u.useEffect(()=>{Ee.current&&r!==void 0&&Ye.addEventListener(Ee.current,"reset",()=>{O==null||O(r)})},[Ee,O]),S.createElement(pt.Provider,{value:Re},S.createElement(mt.Provider,{value:v},S.createElement(Nt,{value:D(v.comboboxState,{0:Y.Open,1:Y.Closed})},i!=null&&g!=null&&_t({[i]:g}).map(([R,L],I)=>S.createElement(be,{features:ve.Hidden,ref:I===0?Z=>{var X;Ee.current=(X=Z==null?void 0:Z.closest("form"))!=null?X:null}:void 0,...je({key:R,as:"input",type:"hidden",hidden:!0,readOnly:!0,form:l,disabled:s,name:R,value:L})})),A({ourProps:Ke,theirProps:p,slot:q,defaultTag:Gn,name:"Combobox"}))))}let Jn="input";function Qn(e,n){var t,r,o,l,i;let a=Q(),{id:s=`headlessui-combobox-input-${a}`,onChange:d,displayValue:c,type:m="text",...x}=e,f=xe("Combobox.Input"),p=De("Combobox.Input"),b=H(f.inputRef,n),h=Oe(f.inputRef),g=u.useRef(!1),O=ge(),w=E(()=>{p.onChange(null),f.optionsRef.current&&(f.optionsRef.current.scrollTop=0),p.goToOption(V.Nothing)}),$=function(){var v;return typeof c=="function"&&f.value!==void 0?(v=c(f.value))!=null?v:"":typeof f.value=="string"?f.value:""}();Fe(([v,q],[_,G])=>{if(g.current)return;let j=f.inputRef.current;j&&((G===0&&q===1||v!==_)&&(j.value=v),requestAnimationFrame(()=>{if(g.current||!j||(h==null?void 0:h.activeElement)!==j)return;let{selectionStart:W,selectionEnd:ee}=j;Math.abs((ee??0)-(W??0))===0&&W===0&&j.setSelectionRange(j.value.length,j.value.length)}))},[$,f.comboboxState,h]),Fe(([v],[q])=>{if(v===0&&q===1){if(g.current)return;let _=f.inputRef.current;if(!_)return;let G=_.value,{selectionStart:j,selectionEnd:W,selectionDirection:ee}=_;_.value="",_.value=G,ee!==null?_.setSelectionRange(j,W,ee):_.setSelectionRange(j,W)}},[f.comboboxState]);let F=u.useRef(!1),M=E(()=>{F.current=!0}),U=E(()=>{O.nextFrame(()=>{F.current=!1})}),B=E(v=>{switch(g.current=!0,v.key){case k.Enter:if(g.current=!1,f.comboboxState!==0||F.current)return;if(v.preventDefault(),v.stopPropagation(),f.activeOptionIndex===null){p.closeCombobox();return}p.selectActiveOption(),f.mode===0&&p.closeCombobox();break;case k.ArrowDown:return g.current=!1,v.preventDefault(),v.stopPropagation(),D(f.comboboxState,{0:()=>p.goToOption(V.Next),1:()=>p.openCombobox()});case k.ArrowUp:return g.current=!1,v.preventDefault(),v.stopPropagation(),D(f.comboboxState,{0:()=>p.goToOption(V.Previous),1:()=>{p.openCombobox(),O.nextFrame(()=>{f.value||p.goToOption(V.Last)})}});case k.Home:if(v.shiftKey)break;return g.current=!1,v.preventDefault(),v.stopPropagation(),p.goToOption(V.First);case k.PageUp:return g.current=!1,v.preventDefault(),v.stopPropagation(),p.goToOption(V.First);case k.End:if(v.shiftKey)break;return g.current=!1,v.preventDefault(),v.stopPropagation(),p.goToOption(V.Last);case k.PageDown:return g.current=!1,v.preventDefault(),v.stopPropagation(),p.goToOption(V.Last);case k.Escape:return g.current=!1,f.comboboxState!==0?void 0:(v.preventDefault(),f.optionsRef.current&&!f.optionsPropsRef.current.static&&v.stopPropagation(),f.nullable&&f.mode===0&&f.value===null&&w(),p.closeCombobox());case k.Tab:if(g.current=!1,f.comboboxState!==0)return;f.mode===0&&f.activationTrigger!==1&&p.selectActiveOption(),p.closeCombobox();break}}),T=E(v=>{d==null||d(v),f.nullable&&f.mode===0&&v.target.value===""&&w(),p.openCombobox()}),y=E(v=>{var q,_,G;let j=(q=v.relatedTarget)!=null?q:ie.find(W=>W!==v.currentTarget);if(g.current=!1,!((_=f.optionsRef.current)!=null&&_.contains(j))&&!((G=f.buttonRef.current)!=null&&G.contains(j))&&f.comboboxState===0)return v.preventDefault(),f.mode===0&&(f.nullable&&f.value===null?w():f.activationTrigger!==1&&p.selectActiveOption()),p.closeCombobox()}),C=E(v=>{var q,_,G;let j=(q=v.relatedTarget)!=null?q:ie.find(W=>W!==v.currentTarget);(_=f.buttonRef.current)!=null&&_.contains(j)||(G=f.optionsRef.current)!=null&&G.contains(j)||f.disabled||f.immediate&&f.comboboxState!==0&&(p.openCombobox(),O.nextFrame(()=>{p.setActivationTrigger(1)}))}),z=ut(()=>{if(f.labelId)return[f.labelId].join(" ")},[f.labelId]),re=u.useMemo(()=>({open:f.comboboxState===0,disabled:f.disabled}),[f]),te={ref:b,id:s,role:"combobox",type:m,"aria-controls":(t=f.optionsRef.current)==null?void 0:t.id,"aria-expanded":f.comboboxState===0,"aria-activedescendant":f.activeOptionIndex===null?void 0:f.virtual?(r=f.options.find(v=>{var q;return!((q=f.virtual)!=null&&q.disabled(v.dataRef.current.value))&&f.compare(v.dataRef.current.value,f.virtual.options[f.activeOptionIndex])}))==null?void 0:r.id:(o=f.options[f.activeOptionIndex])==null?void 0:o.id,"aria-labelledby":z,"aria-autocomplete":"list",defaultValue:(i=(l=e.defaultValue)!=null?l:f.defaultValue!==void 0?c==null?void 0:c(f.defaultValue):null)!=null?i:f.defaultValue,disabled:f.disabled,onCompositionStart:M,onCompositionEnd:U,onKeyDown:B,onChange:T,onFocus:C,onBlur:y};return A({ourProps:te,theirProps:x,slot:re,defaultTag:Jn,name:"Combobox.Input"})}let Zn="button";function er(e,n){var t;let r=xe("Combobox.Button"),o=De("Combobox.Button"),l=H(r.buttonRef,n),i=Q(),{id:a=`headlessui-combobox-button-${i}`,...s}=e,d=ge(),c=E(b=>{switch(b.key){case k.ArrowDown:return b.preventDefault(),b.stopPropagation(),r.comboboxState===1&&o.openCombobox(),d.nextFrame(()=>{var h;return(h=r.inputRef.current)==null?void 0:h.focus({preventScroll:!0})});case k.ArrowUp:return b.preventDefault(),b.stopPropagation(),r.comboboxState===1&&(o.openCombobox(),d.nextFrame(()=>{r.value||o.goToOption(V.Last)})),d.nextFrame(()=>{var h;return(h=r.inputRef.current)==null?void 0:h.focus({preventScroll:!0})});case k.Escape:return r.comboboxState!==0?void 0:(b.preventDefault(),r.optionsRef.current&&!r.optionsPropsRef.current.static&&b.stopPropagation(),o.closeCombobox(),d.nextFrame(()=>{var h;return(h=r.inputRef.current)==null?void 0:h.focus({preventScroll:!0})}));default:return}}),m=E(b=>{if(ft(b.currentTarget))return b.preventDefault();r.comboboxState===0?o.closeCombobox():(b.preventDefault(),o.openCombobox()),d.nextFrame(()=>{var h;return(h=r.inputRef.current)==null?void 0:h.focus({preventScroll:!0})})}),x=ut(()=>{if(r.labelId)return[r.labelId,a].join(" ")},[r.labelId,a]),f=u.useMemo(()=>({open:r.comboboxState===0,disabled:r.disabled,value:r.value}),[r]),p={ref:l,id:a,type:ct(e,r.buttonRef),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":(t=r.optionsRef.current)==null?void 0:t.id,"aria-expanded":r.comboboxState===0,"aria-labelledby":x,disabled:r.disabled,onClick:m,onKeyDown:c};return A({ourProps:p,theirProps:s,slot:f,defaultTag:Zn,name:"Combobox.Button"})}let tr="label";function nr(e,n){let t=Q(),{id:r=`headlessui-combobox-label-${t}`,...o}=e,l=xe("Combobox.Label"),i=De("Combobox.Label"),a=H(l.labelRef,n);P(()=>i.registerLabel(r),[r]);let s=E(()=>{var c;return(c=l.inputRef.current)==null?void 0:c.focus({preventScroll:!0})}),d=u.useMemo(()=>({open:l.comboboxState===0,disabled:l.disabled}),[l]);return A({ourProps:{ref:a,id:r,onClick:s},theirProps:o,slot:d,defaultTag:tr,name:"Combobox.Label"})}let rr="ul",or=de.RenderStrategy|de.Static;function lr(e,n){let t=Q(),{id:r=`headlessui-combobox-options-${t}`,hold:o=!1,...l}=e,i=xe("Combobox.Options"),a=H(i.optionsRef,n),s=Ue(),d=(()=>s!==null?(s&Y.Open)===Y.Open:i.comboboxState===0)();P(()=>{var f;i.optionsPropsRef.current.static=(f=e.static)!=null?f:!1},[i.optionsPropsRef,e.static]),P(()=>{i.optionsPropsRef.current.hold=o},[i.optionsPropsRef,o]),Dn({container:i.optionsRef.current,enabled:i.comboboxState===0,accept(f){return f.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:f.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(f){f.setAttribute("role","none")}});let c=ut(()=>{var f,p;return(p=i.labelId)!=null?p:(f=i.buttonRef.current)==null?void 0:f.id},[i.labelId,i.buttonRef.current]),m=u.useMemo(()=>({open:i.comboboxState===0,option:void 0}),[i]),x={"aria-labelledby":c,role:"listbox","aria-multiselectable":i.mode===1?!0:void 0,id:r,ref:a};return i.virtual&&i.comboboxState===0&&Object.assign(l,{children:S.createElement(Kn,null,l.children)}),A({ourProps:x,theirProps:l,slot:m,defaultTag:rr,features:or,visible:d,name:"Combobox.Options"})}let ir="li";function ar(e,n){var t;let r=Q(),{id:o=`headlessui-combobox-option-${r}`,disabled:l=!1,value:i,order:a=null,...s}=e,d=xe("Combobox.Option"),c=De("Combobox.Option"),m=d.virtual?d.activeOptionIndex===d.calculateIndex(i):d.activeOptionIndex===null?!1:((t=d.options[d.activeOptionIndex])==null?void 0:t.id)===o,x=d.isSelected(i),f=u.useRef(null),p=J({disabled:l,value:i,domRef:f,order:a}),b=u.useContext(Ht),h=H(n,f,b?b.measureElement:null),g=E(()=>c.onChange(i));P(()=>c.registerOption(o,p),[p,o]);let O=u.useRef(!(d.virtual||d.__demoMode));P(()=>{if(!d.virtual||!d.__demoMode)return;let y=se();return y.requestAnimationFrame(()=>{O.current=!0}),y.dispose},[d.virtual,d.__demoMode]),P(()=>{if(!O.current||d.comboboxState!==0||!m||d.activationTrigger===0)return;let y=se();return y.requestAnimationFrame(()=>{var C,z;(z=(C=f.current)==null?void 0:C.scrollIntoView)==null||z.call(C,{block:"nearest"})}),y.dispose},[f,m,d.comboboxState,d.activationTrigger,d.activeOptionIndex]);let w=E(y=>{var C;if(l||(C=d.virtual)!=null&&C.disabled(i))return y.preventDefault();g(),Ft()||requestAnimationFrame(()=>{var z;return(z=d.inputRef.current)==null?void 0:z.focus({preventScroll:!0})}),d.mode===0&&requestAnimationFrame(()=>c.closeCombobox())}),$=E(()=>{var y;if(l||(y=d.virtual)!=null&&y.disabled(i))return c.goToOption(V.Nothing);let C=d.calculateIndex(i);c.goToOption(V.Specific,C)}),F=Ln(),M=E(y=>F.update(y)),U=E(y=>{var C;if(!F.wasMoved(y)||l||(C=d.virtual)!=null&&C.disabled(i)||m)return;let z=d.calculateIndex(i);c.goToOption(V.Specific,z,0)}),B=E(y=>{var C;F.wasMoved(y)&&(l||(C=d.virtual)!=null&&C.disabled(i)||m&&(d.optionsPropsRef.current.hold||c.goToOption(V.Nothing)))}),T=u.useMemo(()=>({active:m,selected:x,disabled:l}),[m,x,l]);return A({ourProps:{id:o,ref:h,role:"option",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,"aria-selected":x,disabled:void 0,onClick:w,onFocus:$,onPointerEnter:M,onMouseEnter:M,onPointerMove:U,onMouseMove:U,onPointerLeave:B,onMouseLeave:B},theirProps:s,slot:T,defaultTag:ir,name:"Combobox.Option"})}let sr=N(Xn),ur=N(er),cr=N(Qn),dr=N(nr),fr=N(lr),pr=N(ar),Tl=Object.assign(sr,{Input:cr,Button:ur,Label:dr,Options:fr,Option:pr});function Bt(e,n,t,r){let o=J(t);u.useEffect(()=>{e=e??window;function l(i){o.current(i)}return e.addEventListener(n,l,r),()=>e.removeEventListener(n,l,r)},[e,n,r])}function Ce(){let e=u.useRef(!1);return P(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function Ut(e){let n=E(e),t=u.useRef(!1);u.useEffect(()=>(t.current=!1,()=>{t.current=!0,Le(()=>{t.current&&n()})}),[n])}var Pe=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(Pe||{});function mr(){let e=u.useRef(0);return Lt("keydown",n=>{n.key==="Tab"&&(e.current=n.shiftKey?1:0)},!0),e}function Wt(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let n=new Set;for(let t of e.current)t.current instanceof HTMLElement&&n.add(t.current);return n}let hr="div";var Vt=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(Vt||{});function vr(e,n){let t=u.useRef(null),r=H(t,n),{initialFocus:o,containers:l,features:i=30,...a}=e;Te()||(i=1);let s=Oe(t);xr({ownerDocument:s},!!(i&16));let d=Er({ownerDocument:s,container:t,initialFocus:o},!!(i&2));yr({ownerDocument:s,container:t,containers:l,previousActiveElement:d},!!(i&8));let c=mr(),m=E(b=>{let h=t.current;h&&(g=>g())(()=>{D(c.current,{[Pe.Forwards]:()=>{le(h,K.First,{skipElements:[b.relatedTarget]})},[Pe.Backwards]:()=>{le(h,K.Last,{skipElements:[b.relatedTarget]})}})})}),x=ge(),f=u.useRef(!1),p={ref:r,onKeyDown(b){b.key=="Tab"&&(f.current=!0,x.requestAnimationFrame(()=>{f.current=!1}))},onBlur(b){let h=Wt(l);t.current instanceof HTMLElement&&h.add(t.current);let g=b.relatedTarget;g instanceof HTMLElement&&g.dataset.headlessuiFocusGuard!=="true"&&(qt(h,g)||(f.current?le(t.current,D(c.current,{[Pe.Forwards]:()=>K.Next,[Pe.Backwards]:()=>K.Previous})|K.WrapAround,{relativeTo:b.target}):b.target instanceof HTMLElement&&he(b.target)))}};return S.createElement(S.Fragment,null,!!(i&4)&&S.createElement(be,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:m,features:ve.Focusable}),A({ourProps:p,theirProps:a,defaultTag:hr,name:"FocusTrap"}),!!(i&4)&&S.createElement(be,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:m,features:ve.Focusable}))}let br=N(vr),Ie=Object.assign(br,{features:Vt});function gr(e=!0){let n=u.useRef(ie.slice());return Fe(([t],[r])=>{r===!0&&t===!1&&Le(()=>{n.current.splice(0)}),r===!1&&t===!0&&(n.current=ie.slice())},[e,ie,n]),E(()=>{var t;return(t=n.current.find(r=>r!=null&&r.isConnected))!=null?t:null})}function xr({ownerDocument:e},n){let t=gr(n);Fe(()=>{n||(e==null?void 0:e.activeElement)===(e==null?void 0:e.body)&&he(t())},[n]),Ut(()=>{n&&he(t())})}function Er({ownerDocument:e,container:n,initialFocus:t},r){let o=u.useRef(null),l=Ce();return Fe(()=>{if(!r)return;let i=n.current;i&&Le(()=>{if(!l.current)return;let a=e==null?void 0:e.activeElement;if(t!=null&&t.current){if((t==null?void 0:t.current)===a){o.current=a;return}}else if(i.contains(a)){o.current=a;return}t!=null&&t.current?he(t.current):le(i,K.First)===Se.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.current=e==null?void 0:e.activeElement})},[r]),o}function yr({ownerDocument:e,container:n,containers:t,previousActiveElement:r},o){let l=Ce();Bt(e==null?void 0:e.defaultView,"focus",i=>{if(!o||!l.current)return;let a=Wt(t);n.current instanceof HTMLElement&&a.add(n.current);let s=r.current;if(!s)return;let d=i.target;d&&d instanceof HTMLElement?qt(a,d)?(r.current=d,he(d)):(i.preventDefault(),i.stopPropagation(),he(s)):he(r.current)},!0)}function qt(e,n){for(let t of e)if(t.contains(n))return!0;return!1}let Kt=u.createContext(!1);function Sr(){return u.useContext(Kt)}function ot(e){return S.createElement(Kt.Provider,{value:e.force},e.children)}function wr(e){let n=Sr(),t=u.useContext(Yt),r=Oe(e),[o,l]=u.useState(()=>{if(!n&&t!==null||ae.isServer)return null;let i=r==null?void 0:r.getElementById("headlessui-portal-root");if(i)return i;if(r===null)return null;let a=r.createElement("div");return a.setAttribute("id","headlessui-portal-root"),r.body.appendChild(a)});return u.useEffect(()=>{o!==null&&(r!=null&&r.body.contains(o)||r==null||r.body.appendChild(o))},[o,r]),u.useEffect(()=>{n||t!==null&&l(t.current)},[t,l,n]),o}let Tr=u.Fragment;function Or(e,n){let t=e,r=u.useRef(null),o=H(Fn(c=>{r.current=c}),n),l=Oe(r),i=wr(r),[a]=u.useState(()=>{var c;return ae.isServer?null:(c=l==null?void 0:l.createElement("div"))!=null?c:null}),s=u.useContext(lt),d=Te();return P(()=>{!i||!a||i.contains(a)||(a.setAttribute("data-headlessui-portal",""),i.appendChild(a))},[i,a]),P(()=>{if(a&&s)return s.register(a)},[s,a]),Ut(()=>{var c;!i||!a||(a instanceof Node&&i.contains(a)&&i.removeChild(a),i.childNodes.length<=0&&((c=i.parentElement)==null||c.removeChild(i)))}),d?!i||!a?null:Rt.createPortal(A({ourProps:{ref:o},theirProps:t,defaultTag:Tr,name:"Portal"}),a):null}let Cr=u.Fragment,Yt=u.createContext(null);function Rr(e,n){let{target:t,...r}=e,o={ref:H(n)};return S.createElement(Yt.Provider,{value:t},A({ourProps:o,theirProps:r,defaultTag:Cr,name:"Popover.Group"}))}let lt=u.createContext(null);function Ir(){let e=u.useContext(lt),n=u.useRef([]),t=E(l=>(n.current.push(l),e&&e.register(l),()=>r(l))),r=E(l=>{let i=n.current.indexOf(l);i!==-1&&n.current.splice(i,1),e&&e.unregister(l)}),o=u.useMemo(()=>({register:t,unregister:r,portals:n}),[t,r,n]);return[n,u.useMemo(()=>function({children:l}){return S.createElement(lt.Provider,{value:o},l)},[o])]}let $r=N(Or),Pr=N(Rr),it=Object.assign($r,{Group:Pr});function Mr(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}const Fr=typeof Object.is=="function"?Object.is:Mr,{useState:Lr,useEffect:Dr,useLayoutEffect:kr,useDebugValue:Ar}=Me;function Nr(e,n,t){const r=n(),[{inst:o},l]=Lr({inst:{value:r,getSnapshot:n}});return kr(()=>{o.value=r,o.getSnapshot=n,Ze(o)&&l({inst:o})},[e,r,n]),Dr(()=>(Ze(o)&&l({inst:o}),e(()=>{Ze(o)&&l({inst:o})})),[e]),Ar(r),r}function Ze(e){const n=e.getSnapshot,t=e.value;try{const r=n();return!Fr(t,r)}catch{return!0}}function _r(e,n,t){return n()}const zr=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",jr=!zr,Hr=jr?_r:Nr,Br="useSyncExternalStore"in Me?(e=>e.useSyncExternalStore)(Me):Hr;function Ur(e){return Br(e.subscribe,e.getSnapshot,e.getSnapshot)}function Wr(e,n){let t=e(),r=new Set;return{getSnapshot(){return t},subscribe(o){return r.add(o),()=>r.delete(o)},dispatch(o,...l){let i=n[o].call(t,...l);i&&(t=i,r.forEach(a=>a()))}}}function Vr(){let e;return{before({doc:n}){var t;let r=n.documentElement;e=((t=n.defaultView)!=null?t:window).innerWidth-r.clientWidth},after({doc:n,d:t}){let r=n.documentElement,o=r.clientWidth-r.offsetWidth,l=e-o;t.style(r,"paddingRight",`${l}px`)}}}function qr(){return Mt()?{before({doc:e,d:n,meta:t}){function r(o){return t.containers.flatMap(l=>l()).some(l=>l.contains(o))}n.microTask(()=>{var o;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let a=se();a.style(e.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>a.dispose()))}let l=(o=window.scrollY)!=null?o:window.pageYOffset,i=null;n.addEventListener(e,"click",a=>{if(a.target instanceof HTMLElement)try{let s=a.target.closest("a");if(!s)return;let{hash:d}=new URL(s.href),c=e.querySelector(d);c&&!r(c)&&(i=c)}catch{}},!0),n.addEventListener(e,"touchstart",a=>{if(a.target instanceof HTMLElement)if(r(a.target)){let s=a.target;for(;s.parentElement&&r(s.parentElement);)s=s.parentElement;n.style(s,"overscrollBehavior","contain")}else n.style(a.target,"touchAction","none")}),n.addEventListener(e,"touchmove",a=>{if(a.target instanceof HTMLElement)if(r(a.target)){let s=a.target;for(;s.parentElement&&s.dataset.headlessuiPortal!==""&&!(s.scrollHeight>s.clientHeight||s.scrollWidth>s.clientWidth);)s=s.parentElement;s.dataset.headlessuiPortal===""&&a.preventDefault()}else a.preventDefault()},{passive:!1}),n.add(()=>{var a;let s=(a=window.scrollY)!=null?a:window.pageYOffset;l!==s&&window.scrollTo(0,l),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)})})}}:{}}function Kr(){return{before({doc:e,d:n}){n.style(e.documentElement,"overflow","hidden")}}}function Yr(e){let n={};for(let t of e)Object.assign(n,t(n));return n}let me=Wr(()=>new Map,{PUSH(e,n){var t;let r=(t=this.get(e))!=null?t:{doc:e,count:0,d:se(),meta:new Set};return r.count++,r.meta.add(n),this.set(e,r),this},POP(e,n){let t=this.get(e);return t&&(t.count--,t.meta.delete(n)),this},SCROLL_PREVENT({doc:e,d:n,meta:t}){let r={doc:e,d:n,meta:Yr(t)},o=[qr(),Vr(),Kr()];o.forEach(({before:l})=>l==null?void 0:l(r)),o.forEach(({after:l})=>l==null?void 0:l(r))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});me.subscribe(()=>{let e=me.getSnapshot(),n=new Map;for(let[t]of e)n.set(t,t.documentElement.style.overflow);for(let t of e.values()){let r=n.get(t.doc)==="hidden",o=t.count!==0;(o&&!r||!o&&r)&&me.dispatch(t.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",t),t.count===0&&me.dispatch("TEARDOWN",t)}});function Gr(e,n,t){let r=Ur(me),o=e?r.get(e):void 0,l=o?o.count>0:!1;return P(()=>{if(!(!e||!n))return me.dispatch("PUSH",e,t),()=>me.dispatch("POP",e,t)},[n,e]),l}let et=new Map,$e=new Map;function Ot(e,n=!0){P(()=>{var t;if(!n)return;let r=typeof e=="function"?e():e.current;if(!r)return;function o(){var i;if(!r)return;let a=(i=$e.get(r))!=null?i:1;if(a===1?$e.delete(r):$e.set(r,a-1),a!==1)return;let s=et.get(r);s&&(s["aria-hidden"]===null?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",s["aria-hidden"]),r.inert=s.inert,et.delete(r))}let l=(t=$e.get(r))!=null?t:0;return $e.set(r,l+1),l!==0||(et.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0),o},[e,n])}function Xr({defaultContainers:e=[],portals:n,mainTreeNodeRef:t}={}){var r;let o=u.useRef((r=t==null?void 0:t.current)!=null?r:null),l=Oe(o),i=E(()=>{var a,s,d;let c=[];for(let m of e)m!==null&&(m instanceof HTMLElement?c.push(m):"current"in m&&m.current instanceof HTMLElement&&c.push(m.current));if(n!=null&&n.current)for(let m of n.current)c.push(m);for(let m of(a=l==null?void 0:l.querySelectorAll("html > *, body > *"))!=null?a:[])m!==document.body&&m!==document.head&&m instanceof HTMLElement&&m.id!=="headlessui-portal-root"&&(m.contains(o.current)||m.contains((d=(s=o.current)==null?void 0:s.getRootNode())==null?void 0:d.host)||c.some(x=>m.contains(x))||c.push(m));return c});return{resolveContainers:i,contains:E(a=>i().some(s=>s.contains(a))),mainTreeNodeRef:o,MainTreeNode:u.useMemo(()=>function(){return t!=null?null:S.createElement(be,{features:ve.Hidden,ref:o})},[o,t])}}let ht=u.createContext(()=>{});ht.displayName="StackContext";var at=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(at||{});function Jr(){return u.useContext(ht)}function Qr({children:e,onUpdate:n,type:t,element:r,enabled:o}){let l=Jr(),i=E((...a)=>{n==null||n(...a),l(...a)});return P(()=>{let a=o===void 0||o===!0;return a&&i(0,t,r),()=>{a&&i(1,t,r)}},[i,t,r,o]),S.createElement(ht.Provider,{value:i},e)}let Gt=u.createContext(null);function Xt(){let e=u.useContext(Gt);if(e===null){let n=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,Xt),n}return e}function Jt(){let[e,n]=u.useState([]);return[e.length>0?e.join(" "):void 0,u.useMemo(()=>function(t){let r=E(l=>(n(i=>[...i,l]),()=>n(i=>{let a=i.slice(),s=a.indexOf(l);return s!==-1&&a.splice(s,1),a}))),o=u.useMemo(()=>({register:r,slot:t.slot,name:t.name,props:t.props}),[r,t.slot,t.name,t.props]);return S.createElement(Gt.Provider,{value:o},t.children)},[n])]}let Zr="p";function eo(e,n){let t=Q(),{id:r=`headlessui-description-${t}`,...o}=e,l=Xt(),i=H(n);P(()=>l.register(r),[r,l.register]);let a={ref:i,...l.props,id:r};return A({ourProps:a,theirProps:o,slot:l.slot||{},defaultTag:Zr,name:l.name||"Description"})}let to=N(eo),Qt=Object.assign(to,{});var no=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(no||{}),ro=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(ro||{});let oo={0(e,n){return e.titleId===n.id?e:{...e,titleId:n.id}}},He=u.createContext(null);He.displayName="DialogContext";function ke(e){let n=u.useContext(He);if(n===null){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,ke),t}return n}function lo(e,n,t=()=>[document.body]){Gr(e,n,r=>{var o;return{containers:[...(o=r.containers)!=null?o:[],t]}})}function io(e,n){return D(n.type,oo,e,n)}let ao="div",so=de.RenderStrategy|de.Static;function uo(e,n){let t=Q(),{id:r=`headlessui-dialog-${t}`,open:o,onClose:l,initialFocus:i,role:a="dialog",__demoMode:s=!1,...d}=e,[c,m]=u.useState(0),x=u.useRef(!1);a=function(){return a==="dialog"||a==="alertdialog"?a:(x.current||(x.current=!0,console.warn(`Invalid role [${a}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let f=Ue();o===void 0&&f!==null&&(o=(f&Y.Open)===Y.Open);let p=u.useRef(null),b=H(p,n),h=Oe(p),g=e.hasOwnProperty("open")||f!==null,O=e.hasOwnProperty("onClose");if(!g&&!O)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!g)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!O)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(typeof o!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${o}`);if(typeof l!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${l}`);let w=o?0:1,[$,F]=u.useReducer(io,{titleId:null,descriptionId:null,panelRef:u.createRef()}),M=E(()=>l(!1)),U=E(I=>F({type:0,id:I})),B=Te()?s?!1:w===0:!1,T=c>1,y=u.useContext(He)!==null,[C,z]=Ir(),re={get current(){var I;return(I=$.panelRef.current)!=null?I:p.current}},{resolveContainers:te,mainTreeNodeRef:v,MainTreeNode:q}=Xr({portals:C,defaultContainers:[re]}),_=T?"parent":"leaf",G=f!==null?(f&Y.Closing)===Y.Closing:!1,j=(()=>y||G?!1:B)(),W=u.useCallback(()=>{var I,Z;return(Z=Array.from((I=h==null?void 0:h.querySelectorAll("body > *"))!=null?I:[]).find(X=>X.id==="headlessui-portal-root"?!1:X.contains(v.current)&&X instanceof HTMLElement))!=null?Z:null},[v]);Ot(W,j);let ee=(()=>T?!0:B)(),oe=u.useCallback(()=>{var I,Z;return(Z=Array.from((I=h==null?void 0:h.querySelectorAll("[data-headlessui-portal]"))!=null?I:[]).find(X=>X.contains(v.current)&&X instanceof HTMLElement))!=null?Z:null},[v]);Ot(oe,ee);let fe=(()=>!(!B||T))();Dt(te,I=>{I.preventDefault(),M()},fe);let ne=(()=>!(T||w!==0))();Bt(h==null?void 0:h.defaultView,"keydown",I=>{ne&&(I.defaultPrevented||I.key===k.Escape&&(I.preventDefault(),I.stopPropagation(),M()))});let Re=(()=>!(G||w!==0||y))();lo(h,Re,te),u.useEffect(()=>{if(w!==0||!p.current)return;let I=new ResizeObserver(Z=>{for(let X of Z){let Ae=X.target.getBoundingClientRect();Ae.x===0&&Ae.y===0&&Ae.width===0&&Ae.height===0&&M()}});return I.observe(p.current),()=>I.disconnect()},[w,p,M]);let[Ke,Ee]=Jt(),Ye=u.useMemo(()=>[{dialogState:w,close:M,setTitleId:U},$],[w,$,M,U]),R=u.useMemo(()=>({open:w===0}),[w]),L={ref:b,id:r,role:a,"aria-modal":w===0?!0:void 0,"aria-labelledby":$.titleId,"aria-describedby":Ke};return S.createElement(Qr,{type:"Dialog",enabled:w===0,element:p,onUpdate:E((I,Z)=>{Z==="Dialog"&&D(I,{[at.Add]:()=>m(X=>X+1),[at.Remove]:()=>m(X=>X-1)})})},S.createElement(ot,{force:!0},S.createElement(it,null,S.createElement(He.Provider,{value:Ye},S.createElement(it.Group,{target:p},S.createElement(ot,{force:!1},S.createElement(Ee,{slot:R,name:"Dialog.Description"},S.createElement(Ie,{initialFocus:i,containers:te,features:B?D(_,{parent:Ie.features.RestoreFocus,leaf:Ie.features.All&~Ie.features.FocusLock}):Ie.features.None},S.createElement(z,null,A({ourProps:L,theirProps:d,slot:R,defaultTag:ao,features:so,visible:w===0,name:"Dialog"}))))))))),S.createElement(q,null))}let co="div";function fo(e,n){let t=Q(),{id:r=`headlessui-dialog-overlay-${t}`,...o}=e,[{dialogState:l,close:i}]=ke("Dialog.Overlay"),a=H(n),s=E(c=>{if(c.target===c.currentTarget){if(ft(c.currentTarget))return c.preventDefault();c.preventDefault(),c.stopPropagation(),i()}}),d=u.useMemo(()=>({open:l===0}),[l]);return A({ourProps:{ref:a,id:r,"aria-hidden":!0,onClick:s},theirProps:o,slot:d,defaultTag:co,name:"Dialog.Overlay"})}let po="div";function mo(e,n){let t=Q(),{id:r=`headlessui-dialog-backdrop-${t}`,...o}=e,[{dialogState:l},i]=ke("Dialog.Backdrop"),a=H(n);u.useEffect(()=>{if(i.panelRef.current===null)throw new Error("A <Dialog.Backdrop /> component is being used, but a <Dialog.Panel /> component is missing.")},[i.panelRef]);let s=u.useMemo(()=>({open:l===0}),[l]);return S.createElement(ot,{force:!0},S.createElement(it,null,A({ourProps:{ref:a,id:r,"aria-hidden":!0},theirProps:o,slot:s,defaultTag:po,name:"Dialog.Backdrop"})))}let ho="div";function vo(e,n){let t=Q(),{id:r=`headlessui-dialog-panel-${t}`,...o}=e,[{dialogState:l},i]=ke("Dialog.Panel"),a=H(n,i.panelRef),s=u.useMemo(()=>({open:l===0}),[l]),d=E(c=>{c.stopPropagation()});return A({ourProps:{ref:a,id:r,onClick:d},theirProps:o,slot:s,defaultTag:ho,name:"Dialog.Panel"})}let bo="h2";function go(e,n){let t=Q(),{id:r=`headlessui-dialog-title-${t}`,...o}=e,[{dialogState:l,setTitleId:i}]=ke("Dialog.Title"),a=H(n);u.useEffect(()=>(i(r),()=>i(null)),[r,i]);let s=u.useMemo(()=>({open:l===0}),[l]);return A({ourProps:{ref:a,id:r},theirProps:o,slot:s,defaultTag:bo,name:"Dialog.Title"})}let xo=N(uo),Eo=N(mo),yo=N(vo),So=N(fo),wo=N(go),Ol=Object.assign(xo,{Backdrop:Eo,Panel:yo,Overlay:So,Title:wo,Description:Qt}),Zt=u.createContext(null);function en(){let e=u.useContext(Zt);if(e===null){let n=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,en),n}return e}function To(){let[e,n]=u.useState([]);return[e.length>0?e.join(" "):void 0,u.useMemo(()=>function(t){let r=E(l=>(n(i=>[...i,l]),()=>n(i=>{let a=i.slice(),s=a.indexOf(l);return s!==-1&&a.splice(s,1),a}))),o=u.useMemo(()=>({register:r,slot:t.slot,name:t.name,props:t.props}),[r,t.slot,t.name,t.props]);return S.createElement(Zt.Provider,{value:o},t.children)},[n])]}let Oo="label";function Co(e,n){let t=Q(),{id:r=`headlessui-label-${t}`,passive:o=!1,...l}=e,i=en(),a=H(n);P(()=>i.register(r),[r,i.register]);let s={ref:a,...i.props,id:r};return o&&("onClick"in s&&(delete s.htmlFor,delete s.onClick),"onClick"in l&&delete l.onClick),A({ourProps:s,theirProps:l,slot:i.slot||{},defaultTag:Oo,name:i.name||"Label"})}let Ro=N(Co),Io=Object.assign(Ro,{});function $o(e=0){let[n,t]=u.useState(e),r=Ce(),o=u.useCallback(s=>{r.current&&t(d=>d|s)},[n,r]),l=u.useCallback(s=>!!(n&s),[n]),i=u.useCallback(s=>{r.current&&t(d=>d&~s)},[t,r]),a=u.useCallback(s=>{r.current&&t(d=>d^s)},[t]);return{flags:n,addFlag:o,hasFlag:l,removeFlag:i,toggleFlag:a}}let vt=u.createContext(null);vt.displayName="GroupContext";let Po=u.Fragment;function Mo(e){var n;let[t,r]=u.useState(null),[o,l]=To(),[i,a]=Jt(),s=u.useMemo(()=>({switch:t,setSwitch:r,labelledby:o,describedby:i}),[t,r,o,i]),d={},c=e;return S.createElement(a,{name:"Switch.Description"},S.createElement(l,{name:"Switch.Label",props:{htmlFor:(n=s.switch)==null?void 0:n.id,onClick(m){t&&(m.currentTarget.tagName==="LABEL"&&m.preventDefault(),t.click(),t.focus({preventScroll:!0}))}}},S.createElement(vt.Provider,{value:s},A({ourProps:d,theirProps:c,defaultTag:Po,name:"Switch.Group"}))))}let Fo="button";function Lo(e,n){var t;let r=Q(),{id:o=`headlessui-switch-${r}`,checked:l,defaultChecked:i=!1,onChange:a,disabled:s=!1,name:d,value:c,form:m,...x}=e,f=u.useContext(vt),p=u.useRef(null),b=H(p,n,f===null?null:f.setSwitch),[h,g]=$t(l,a,i),O=E(()=>g==null?void 0:g(!h)),w=E(T=>{if(ft(T.currentTarget))return T.preventDefault();T.preventDefault(),O()}),$=E(T=>{T.key===k.Space?(T.preventDefault(),O()):T.key===k.Enter&&Hn(T.currentTarget)}),F=E(T=>T.preventDefault()),M=u.useMemo(()=>({checked:h}),[h]),U={id:o,ref:b,role:"switch",type:ct(e,p),tabIndex:e.tabIndex===-1?0:(t=e.tabIndex)!=null?t:0,"aria-checked":h,"aria-labelledby":f==null?void 0:f.labelledby,"aria-describedby":f==null?void 0:f.describedby,disabled:s,onClick:w,onKeyUp:$,onKeyPress:F},B=ge();return u.useEffect(()=>{var T;let y=(T=p.current)==null?void 0:T.closest("form");y&&i!==void 0&&B.addEventListener(y,"reset",()=>{g(i)})},[p,g]),S.createElement(S.Fragment,null,d!=null&&h&&S.createElement(be,{features:ve.Hidden,...je({as:"input",type:"checkbox",hidden:!0,readOnly:!0,disabled:s,form:m,checked:h,name:d,value:c})}),A({ourProps:U,theirProps:x,slot:M,defaultTag:Fo,name:"Switch"}))}let Do=N(Lo),ko=Mo,Cl=Object.assign(Do,{Group:ko,Label:Io,Description:Qt});function Ao({onFocus:e}){let[n,t]=u.useState(!0),r=Ce();return n?S.createElement(be,{as:"button",type:"button",features:ve.Focusable,onFocus:o=>{o.preventDefault();let l,i=50;function a(){if(i--<=0){l&&cancelAnimationFrame(l);return}if(e()){if(cancelAnimationFrame(l),!r.current)return;t(!1);return}l=requestAnimationFrame(a)}l=requestAnimationFrame(a)}}):null}const tn=u.createContext(null);function No(){return{groups:new Map,get(e,n){var t;let r=this.groups.get(e);r||(r=new Map,this.groups.set(e,r));let o=(t=r.get(n))!=null?t:0;r.set(n,o+1);let l=Array.from(r.keys()).indexOf(n);function i(){let a=r.get(n);a>1?r.set(n,a-1):r.delete(n)}return[l,i]}}}function _o({children:e}){let n=u.useRef(No());return u.createElement(tn.Provider,{value:n},e)}function nn(e){let n=u.useContext(tn);if(!n)throw new Error("You must wrap your component in a <StableCollection>");let t=zo(),[r,o]=n.current.get(e,t);return u.useEffect(()=>o,[]),r}function zo(){var e,n,t;let r=(t=(n=(e=u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)==null?void 0:e.ReactCurrentOwner)==null?void 0:n.current)!=null?t:null;if(!r)return Symbol();let o=[],l=r;for(;l;)o.push(l.index),l=l.return;return"$."+o.join(".")}var jo=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(jo||{}),Ho=(e=>(e[e.Less=-1]="Less",e[e.Equal=0]="Equal",e[e.Greater=1]="Greater",e))(Ho||{}),Bo=(e=>(e[e.SetSelectedIndex=0]="SetSelectedIndex",e[e.RegisterTab=1]="RegisterTab",e[e.UnregisterTab=2]="UnregisterTab",e[e.RegisterPanel=3]="RegisterPanel",e[e.UnregisterPanel=4]="UnregisterPanel",e))(Bo||{});let Uo={0(e,n){var t;let r=pe(e.tabs,c=>c.current),o=pe(e.panels,c=>c.current),l=r.filter(c=>{var m;return!((m=c.current)!=null&&m.hasAttribute("disabled"))}),i={...e,tabs:r,panels:o};if(n.index<0||n.index>r.length-1){let c=D(Math.sign(n.index-e.selectedIndex),{[-1]:()=>1,0:()=>D(Math.sign(n.index),{[-1]:()=>0,0:()=>0,1:()=>1}),1:()=>0});if(l.length===0)return i;let m=D(c,{0:()=>r.indexOf(l[0]),1:()=>r.indexOf(l[l.length-1])});return{...i,selectedIndex:m===-1?e.selectedIndex:m}}let a=r.slice(0,n.index),s=[...r.slice(n.index),...a].find(c=>l.includes(c));if(!s)return i;let d=(t=r.indexOf(s))!=null?t:e.selectedIndex;return d===-1&&(d=e.selectedIndex),{...i,selectedIndex:d}},1(e,n){if(e.tabs.includes(n.tab))return e;let t=e.tabs[e.selectedIndex],r=pe([...e.tabs,n.tab],l=>l.current),o=e.selectedIndex;return e.info.current.isControlled||(o=r.indexOf(t),o===-1&&(o=e.selectedIndex)),{...e,tabs:r,selectedIndex:o}},2(e,n){return{...e,tabs:e.tabs.filter(t=>t!==n.tab)}},3(e,n){return e.panels.includes(n.panel)?e:{...e,panels:pe([...e.panels,n.panel],t=>t.current)}},4(e,n){return{...e,panels:e.panels.filter(t=>t!==n.panel)}}},bt=u.createContext(null);bt.displayName="TabsDataContext";function we(e){let n=u.useContext(bt);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,we),t}return n}let gt=u.createContext(null);gt.displayName="TabsActionsContext";function xt(e){let n=u.useContext(gt);if(n===null){let t=new Error(`<${e} /> is missing a parent <Tab.Group /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,xt),t}return n}function Wo(e,n){return D(n.type,Uo,e,n)}let Vo=u.Fragment;function qo(e,n){let{defaultIndex:t=0,vertical:r=!1,manual:o=!1,onChange:l,selectedIndex:i=null,...a}=e;const s=r?"vertical":"horizontal",d=o?"manual":"auto";let c=i!==null,m=J({isControlled:c}),x=H(n),[f,p]=u.useReducer(Wo,{info:m,selectedIndex:i??t,tabs:[],panels:[]}),b=u.useMemo(()=>({selectedIndex:f.selectedIndex}),[f.selectedIndex]),h=J(l||(()=>{})),g=J(f.tabs),O=u.useMemo(()=>({orientation:s,activation:d,...f}),[s,d,f]),w=E(T=>(p({type:1,tab:T}),()=>p({type:2,tab:T}))),$=E(T=>(p({type:3,panel:T}),()=>p({type:4,panel:T}))),F=E(T=>{M.current!==T&&h.current(T),c||p({type:0,index:T})}),M=J(c?e.selectedIndex:f.selectedIndex),U=u.useMemo(()=>({registerTab:w,registerPanel:$,change:F}),[]);P(()=>{p({type:0,index:i??t})},[i]),P(()=>{if(M.current===void 0||f.tabs.length<=0)return;let T=pe(f.tabs,y=>y.current);T.some((y,C)=>f.tabs[C]!==y)&&F(T.indexOf(f.tabs[M.current]))});let B={ref:x};return S.createElement(_o,null,S.createElement(gt.Provider,{value:U},S.createElement(bt.Provider,{value:O},O.tabs.length<=0&&S.createElement(Ao,{onFocus:()=>{var T,y;for(let C of g.current)if(((T=C.current)==null?void 0:T.tabIndex)===0)return(y=C.current)==null||y.focus(),!0;return!1}}),A({ourProps:B,theirProps:a,slot:b,defaultTag:Vo,name:"Tabs"}))))}let Ko="div";function Yo(e,n){let{orientation:t,selectedIndex:r}=we("Tab.List"),o=H(n);return A({ourProps:{ref:o,role:"tablist","aria-orientation":t},theirProps:e,slot:{selectedIndex:r},defaultTag:Ko,name:"Tabs.List"})}let Go="button";function Xo(e,n){var t,r;let o=Q(),{id:l=`headlessui-tabs-tab-${o}`,...i}=e,{orientation:a,activation:s,selectedIndex:d,tabs:c,panels:m}=we("Tab"),x=xt("Tab"),f=we("Tab"),p=u.useRef(null),b=H(p,n);P(()=>x.registerTab(p),[x,p]);let h=nn("tabs"),g=c.indexOf(p);g===-1&&(g=h);let O=g===d,w=E(y=>{var C;let z=y();if(z===Se.Success&&s==="auto"){let re=(C=Be(p))==null?void 0:C.activeElement,te=f.tabs.findIndex(v=>v.current===re);te!==-1&&x.change(te)}return z}),$=E(y=>{let C=c.map(z=>z.current).filter(Boolean);if(y.key===k.Space||y.key===k.Enter){y.preventDefault(),y.stopPropagation(),x.change(g);return}switch(y.key){case k.Home:case k.PageUp:return y.preventDefault(),y.stopPropagation(),w(()=>le(C,K.First));case k.End:case k.PageDown:return y.preventDefault(),y.stopPropagation(),w(()=>le(C,K.Last))}if(w(()=>D(a,{vertical(){return y.key===k.ArrowUp?le(C,K.Previous|K.WrapAround):y.key===k.ArrowDown?le(C,K.Next|K.WrapAround):Se.Error},horizontal(){return y.key===k.ArrowLeft?le(C,K.Previous|K.WrapAround):y.key===k.ArrowRight?le(C,K.Next|K.WrapAround):Se.Error}}))===Se.Success)return y.preventDefault()}),F=u.useRef(!1),M=E(()=>{var y;F.current||(F.current=!0,(y=p.current)==null||y.focus({preventScroll:!0}),x.change(g),Le(()=>{F.current=!1}))}),U=E(y=>{y.preventDefault()}),B=u.useMemo(()=>{var y;return{selected:O,disabled:(y=e.disabled)!=null?y:!1}},[O,e.disabled]),T={ref:b,onKeyDown:$,onMouseDown:U,onClick:M,id:l,role:"tab",type:ct(e,p),"aria-controls":(r=(t=m[g])==null?void 0:t.current)==null?void 0:r.id,"aria-selected":O,tabIndex:O?0:-1};return A({ourProps:T,theirProps:i,slot:B,defaultTag:Go,name:"Tabs.Tab"})}let Jo="div";function Qo(e,n){let{selectedIndex:t}=we("Tab.Panels"),r=H(n),o=u.useMemo(()=>({selectedIndex:t}),[t]);return A({ourProps:{ref:r},theirProps:e,slot:o,defaultTag:Jo,name:"Tabs.Panels"})}let Zo="div",el=de.RenderStrategy|de.Static;function tl(e,n){var t,r,o,l;let i=Q(),{id:a=`headlessui-tabs-panel-${i}`,tabIndex:s=0,...d}=e,{selectedIndex:c,tabs:m,panels:x}=we("Tab.Panel"),f=xt("Tab.Panel"),p=u.useRef(null),b=H(p,n);P(()=>f.registerPanel(p),[f,p,a]);let h=nn("panels"),g=x.indexOf(p);g===-1&&(g=h);let O=g===c,w=u.useMemo(()=>({selected:O}),[O]),$={ref:b,id:a,role:"tabpanel","aria-labelledby":(r=(t=m[g])==null?void 0:t.current)==null?void 0:r.id,tabIndex:O?s:-1};return!O&&((o=d.unmount)==null||o)&&!((l=d.static)!=null&&l)?S.createElement(be,{as:"span","aria-hidden":"true",...$}):A({ourProps:$,theirProps:d,slot:w,defaultTag:Zo,features:el,visible:O,name:"Tabs.Panel"})}let nl=N(Xo),rl=N(qo),ol=N(Yo),ll=N(Qo),il=N(tl),Rl=Object.assign(nl,{Group:rl,List:ol,Panels:ll,Panel:il});function al(e){let n={called:!1};return(...t)=>{if(!n.called)return n.called=!0,e(...t)}}function tt(e,...n){e&&n.length>0&&e.classList.add(...n)}function nt(e,...n){e&&n.length>0&&e.classList.remove(...n)}function sl(e,n){let t=se();if(!e)return t.dispose;let{transitionDuration:r,transitionDelay:o}=getComputedStyle(e),[l,i]=[r,o].map(s=>{let[d=0]=s.split(",").filter(Boolean).map(c=>c.includes("ms")?parseFloat(c):parseFloat(c)*1e3).sort((c,m)=>m-c);return d}),a=l+i;if(a!==0){t.group(d=>{d.setTimeout(()=>{n(),d.dispose()},a),d.addEventListener(e,"transitionrun",c=>{c.target===c.currentTarget&&d.dispose()})});let s=t.addEventListener(e,"transitionend",d=>{d.target===d.currentTarget&&(n(),s())})}else n();return t.add(()=>n()),t.dispose}function ul(e,n,t,r){let o=t?"enter":"leave",l=se(),i=r!==void 0?al(r):()=>{};o==="enter"&&(e.removeAttribute("hidden"),e.style.display="");let a=D(o,{enter:()=>n.enter,leave:()=>n.leave}),s=D(o,{enter:()=>n.enterTo,leave:()=>n.leaveTo}),d=D(o,{enter:()=>n.enterFrom,leave:()=>n.leaveFrom});return nt(e,...n.base,...n.enter,...n.enterTo,...n.enterFrom,...n.leave,...n.leaveFrom,...n.leaveTo,...n.entered),tt(e,...n.base,...a,...d),l.nextFrame(()=>{nt(e,...n.base,...a,...d),tt(e,...n.base,...a,...s),sl(e,()=>(nt(e,...n.base,...a),tt(e,...n.base,...n.entered),i()))}),l.dispose}function cl({immediate:e,container:n,direction:t,classes:r,onStart:o,onStop:l}){let i=Ce(),a=ge(),s=J(t);P(()=>{e&&(s.current="enter")},[e]),P(()=>{let d=se();a.add(d.dispose);let c=n.current;if(c&&s.current!=="idle"&&i.current)return d.dispose(),o.current(s.current),d.add(ul(c,r.current,s.current==="enter",()=>{d.dispose(),l.current(s.current)})),d.dispose},[t])}function ue(e=""){return e.split(/\s+/).filter(n=>n.length>1)}let We=u.createContext(null);We.displayName="TransitionContext";var dl=(e=>(e.Visible="visible",e.Hidden="hidden",e))(dl||{});function fl(){let e=u.useContext(We);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}function pl(){let e=u.useContext(Ve);if(e===null)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}let Ve=u.createContext(null);Ve.displayName="NestingContext";function qe(e){return"children"in e?qe(e.children):e.current.filter(({el:n})=>n.current!==null).filter(({state:n})=>n==="visible").length>0}function rn(e,n){let t=J(e),r=u.useRef([]),o=Ce(),l=ge(),i=E((f,p=ce.Hidden)=>{let b=r.current.findIndex(({el:h})=>h===f);b!==-1&&(D(p,{[ce.Unmount](){r.current.splice(b,1)},[ce.Hidden](){r.current[b].state="hidden"}}),l.microTask(()=>{var h;!qe(r)&&o.current&&((h=t.current)==null||h.call(t))}))}),a=E(f=>{let p=r.current.find(({el:b})=>b===f);return p?p.state!=="visible"&&(p.state="visible"):r.current.push({el:f,state:"visible"}),()=>i(f,ce.Unmount)}),s=u.useRef([]),d=u.useRef(Promise.resolve()),c=u.useRef({enter:[],leave:[],idle:[]}),m=E((f,p,b)=>{s.current.splice(0),n&&(n.chains.current[p]=n.chains.current[p].filter(([h])=>h!==f)),n==null||n.chains.current[p].push([f,new Promise(h=>{s.current.push(h)})]),n==null||n.chains.current[p].push([f,new Promise(h=>{Promise.all(c.current[p].map(([g,O])=>O)).then(()=>h())})]),p==="enter"?d.current=d.current.then(()=>n==null?void 0:n.wait.current).then(()=>b(p)):b(p)}),x=E((f,p,b)=>{Promise.all(c.current[p].splice(0).map(([h,g])=>g)).then(()=>{var h;(h=s.current.shift())==null||h()}).then(()=>b(p))});return u.useMemo(()=>({children:r,register:a,unregister:i,onStart:m,onStop:x,wait:d,chains:c}),[a,i,r,m,x,c,d])}function ml(){}let hl=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function Ct(e){var n;let t={};for(let r of hl)t[r]=(n=e[r])!=null?n:ml;return t}function vl(e){let n=u.useRef(Ct(e));return u.useEffect(()=>{n.current=Ct(e)},[e]),n}let bl="div",on=de.RenderStrategy;function gl(e,n){var t,r;let{beforeEnter:o,afterEnter:l,beforeLeave:i,afterLeave:a,enter:s,enterFrom:d,enterTo:c,entered:m,leave:x,leaveFrom:f,leaveTo:p,...b}=e,h=u.useRef(null),g=H(h,n),O=(t=b.unmount)==null||t?ce.Unmount:ce.Hidden,{show:w,appear:$,initial:F}=fl(),[M,U]=u.useState(w?"visible":"hidden"),B=pl(),{register:T,unregister:y}=B;u.useEffect(()=>T(h),[T,h]),u.useEffect(()=>{if(O===ce.Hidden&&h.current){if(w&&M!=="visible"){U("visible");return}return D(M,{hidden:()=>y(h),visible:()=>T(h)})}},[M,h,T,y,w,O]);let C=J({base:ue(b.className),enter:ue(s),enterFrom:ue(d),enterTo:ue(c),entered:ue(m),leave:ue(x),leaveFrom:ue(f),leaveTo:ue(p)}),z=vl({beforeEnter:o,afterEnter:l,beforeLeave:i,afterLeave:a}),re=Te();u.useEffect(()=>{if(re&&M==="visible"&&h.current===null)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[h,M,re]);let te=F&&!$,v=$&&w&&F,q=(()=>!re||te?"idle":w?"enter":"leave")(),_=$o(0),G=E(ne=>D(ne,{enter:()=>{_.addFlag(Y.Opening),z.current.beforeEnter()},leave:()=>{_.addFlag(Y.Closing),z.current.beforeLeave()},idle:()=>{}})),j=E(ne=>D(ne,{enter:()=>{_.removeFlag(Y.Opening),z.current.afterEnter()},leave:()=>{_.removeFlag(Y.Closing),z.current.afterLeave()},idle:()=>{}})),W=rn(()=>{U("hidden"),y(h)},B),ee=u.useRef(!1);cl({immediate:v,container:h,classes:C,direction:q,onStart:J(ne=>{ee.current=!0,W.onStart(h,ne,G)}),onStop:J(ne=>{ee.current=!1,W.onStop(h,ne,j),ne==="leave"&&!qe(W)&&(U("hidden"),y(h))})});let oe=b,fe={ref:g};return v?oe={...oe,className:ze(b.className,...C.current.enter,...C.current.enterFrom)}:ee.current&&(oe.className=ze(b.className,(r=h.current)==null?void 0:r.className),oe.className===""&&delete oe.className),S.createElement(Ve.Provider,{value:W},S.createElement(Nt,{value:D(M,{visible:Y.Open,hidden:Y.Closed})|_.flags},A({ourProps:fe,theirProps:oe,defaultTag:bl,features:on,visible:M==="visible",name:"Transition.Child"})))}function xl(e,n){let{show:t,appear:r=!1,unmount:o=!0,...l}=e,i=u.useRef(null),a=H(i,n);Te();let s=Ue();if(t===void 0&&s!==null&&(t=(s&Y.Open)===Y.Open),![!0,!1].includes(t))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[d,c]=u.useState(t?"visible":"hidden"),m=rn(()=>{c("hidden")}),[x,f]=u.useState(!0),p=u.useRef([t]);P(()=>{x!==!1&&p.current[p.current.length-1]!==t&&(p.current.push(t),f(!1))},[p,t]);let b=u.useMemo(()=>({show:t,appear:r,initial:x}),[t,r,x]);u.useEffect(()=>{if(t)c("visible");else if(!qe(m))c("hidden");else{let w=i.current;if(!w)return;let $=w.getBoundingClientRect();$.x===0&&$.y===0&&$.width===0&&$.height===0&&c("hidden")}},[t,m]);let h={unmount:o},g=E(()=>{var w;x&&f(!1),(w=e.beforeEnter)==null||w.call(e)}),O=E(()=>{var w;x&&f(!1),(w=e.beforeLeave)==null||w.call(e)});return S.createElement(Ve.Provider,{value:m},S.createElement(We.Provider,{value:b},A({ourProps:{...h,as:u.Fragment,children:S.createElement(ln,{ref:a,...h,...l,beforeEnter:g,beforeLeave:O})},theirProps:{},defaultTag:u.Fragment,features:on,visible:d==="visible",name:"Transition"})))}function El(e,n){let t=u.useContext(We)!==null,r=Ue()!==null;return S.createElement(S.Fragment,null,!t&&r?S.createElement(st,{ref:n,...e}):S.createElement(ln,{ref:n,...e}))}let st=N(xl),ln=N(gl),yl=N(El),Il=Object.assign(st,{Child:yl,Root:st});export{Rl as $,Ol as _,Tl as a,Cl as b,Il as q};
