import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as i,b as ie}from"./vendor-851db8c1.js";import{G as ce,aC as de,R as me,af as I,D as ue,M as pe,a1 as E,b as D,c as v,d as x}from"./index-08a5dc5b.js";import{P as j}from"./PlusIcon-7e8d14d7.js";import{P as xe}from"./PencilIcon-35185602.js";import{T as he}from"./TrashIcon-aaaccaf2.js";import{I as ye}from"./InformationCircleIcon-d35f3488.js";let c=new pe;function we({fetchSettings:N,sports:m,clubUser:w,courts:S=[]}){const[H,R]=i.useState(!1),[P,G]=i.useState(!1),{dispatch:d}=i.useContext(ce),[u,h]=i.useState("add"),[q,y]=i.useState(null),[C,L]=i.useState(!1),[M,$]=i.useState([]),[B,g]=i.useState(!1),[K,T]=i.useState(!1),[b,k]=i.useState(null),A=localStorage.getItem("role"),U=s=>!S||S.length===0?!1:S.some(t=>{var a;return((a=t.sport_id)==null?void 0:a.toString())===(s==null?void 0:s.toString())}),[l,n]=i.useState({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24});i.useEffect(()=>{if((m==null?void 0:m.length)>0){const s=m==null?void 0:m.map(t=>{var a;return{sport_id:t.id,name:t.name,club_id:t.club_id,types:((a=t.sport_types)==null?void 0:a.map(r=>({name:r.type,sub_type:r.subtype||[],club_sport_type_id:r.club_sport_type_id})))||[],status:t.status||0,allow_cancel_reservation:t.allow_cancel_reservation!==void 0?t.allow_cancel_reservation:1,cancel_hours_before:t.cancel_hours_before||24}});$(s)}else $([])},[m]);const X=async s=>{R(!0);try{c.setTable("sports"),await c.callRestAPI({id:s.sport_id,status:s.status===1?0:1},"PUT"),await E(c,{activity_type:D.court_management,action_type:v.UPDATE,data:{sport_id:s.sport_id,sport_name:s.name,previous_status:s.status,new_status:s.status===1?0:1,action:"status_change"},description:`Sport "${s.name}" ${s.status===1?"deactivated":"activated"}`}),await N()}catch(t){console.error(t),x(d,"Failed to update sport status",3e3,"error")}finally{R(!1)}},Y=s=>{k(s),T(!0)},z=async()=>{if(b)try{G(!0),T(!1);const s=M.find(t=>t.sport_id===b.sport_id);c.setTable("sports"),await c.callRestAPI({id:b.sport_id},"DELETE"),s&&await E(c,{activity_type:D.court_management,action_type:v.DELETE,data:{sport_id:b.sport_id,sport_name:s.name,sport_types:s.types,action:"delete_sport"},description:`Sport "${s.name}" deleted`}),await N(),x(d,"Sport deleted successfully",3e3,"success")}catch(s){console.log(s),x(d,"Failed to delete sport",3e3,"error")}finally{G(!1),k(null)}},J=()=>{T(!1),k(null)},Q=s=>{if(!s){h("add"),y(null),g(!1),n({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24});return}h("edit"),y(s),g(!0),n({name:s.name,sport_id:s.sport_id,types:s.types||[],allow_cancel_reservation:s.allow_cancel_reservation!==void 0?s.allow_cancel_reservation:1,cancel_hours_before:s.cancel_hours_before||24})},O=()=>{n(s=>({...s,types:[...s.types,{name:"",sub_type:[]}]}))},V=s=>{n(t=>({...t,types:t.types.filter((a,r)=>r!==s)}))},W=(s,t)=>{n(a=>({...a,types:a.types.map((r,o)=>o===s?{...r,name:t}:r)}))},Z=s=>{n(t=>({...t,types:t.types.map((a,r)=>r===s?{...a,sub_type:[...a.sub_type,""]}:a)}))},ee=(s,t)=>{n(a=>({...a,types:a.types.map((r,o)=>o===s?{...r,sub_type:r.sub_type.filter((_,f)=>f!==t)}:r)}))},se=(s,t,a)=>{n(r=>({...r,types:r.types.map((o,_)=>_===s?{...o,sub_type:o.sub_type.map((f,p)=>p===t?a:f)}:o)}))},te=async()=>{if(!l.name.trim()){x(d,"Sport name is required",3e3,"error");return}if(l.allow_cancel_reservation===1&&(!l.cancel_hours_before||l.cancel_hours_before<0)){x(d,"Hours before cancellation must be a positive number",3e3,"error");return}try{L(!0);const s={name:l.name,types:l.types.map(t=>({name:t.name,sub_type:t.sub_type,...t.club_sport_type_id&&{club_sport_type_id:t.club_sport_type_id}})),allow_cancel_reservation:l.allow_cancel_reservation,sport_id:l.sport_id,cancel_hours_before:l.allow_cancel_reservation===1?l.cancel_hours_before:0};u==="edit"&&(s.sport_id=q.sport_id),u!=="edit"&&(s.status=1),A=="admin"||A=="admin_staff"?await c.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile-edit/${w==null?void 0:w.id}`,{sports:[s]},"POST"):await c.callRawAPI(`/v3/api/custom/courtmatchup/${A}/profile-edit`,{sports:[s]},"POST"),await E(c,{activity_type:D.court_management,action_type:u==="edit"?v.UPDATE:v.CREATE,data:{sport_id:s.sport_id,sport_name:s.name,sport_types:s.types,allow_cancel_reservation:s.allow_cancel_reservation,cancel_hours_before:s.cancel_hours_before,action:u==="edit"?"update_sport":"create_sport"},description:`Sport "${s.name}" ${u==="edit"?"updated":"created"}`}),await N(),x(d,`Sport ${u==="edit"?"updated":"added"} successfully`,3e3,"success"),g(!1),y(null),h("add"),n({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24})}catch(s){console.error(s),x(d,s.message,3e3,"error")}finally{L(!1)}},ae=[...M].sort((s,t)=>t.id-s.id),re=()=>{g(!1),y(null),h("add"),n({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24})};return ie.useEffect(()=>{d({type:"SETPATH",payload:{path:"club-ui"}})},[]),e.jsx(de,{isLoading:P||C||H,children:e.jsxs("div",{className:"flex flex-col gap-6 p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:"Sports"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Manage your club's sports and their settings"})]}),e.jsxs("button",{onClick:()=>{g(!0),h("add"),y(null),n({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24})},className:"flex items-center gap-2 rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-all hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",children:[e.jsx(j,{className:"h-4 w-4"}),e.jsx("span",{children:"Add Sport"})]})]}),e.jsxs("div",{className:"space-y-4",children:[ae.map(s=>{var a,r,o,_,f;const t=U(s.sport_id);return e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-white shadow-sm transition-shadow hover:shadow-md",children:[e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`flex h-10 w-10 items-center justify-center rounded-lg text-sm font-medium ${s.status===1?"bg-blue-50 text-blue-600":"bg-gray-100 text-gray-400"}`,children:s.name.charAt(0).toUpperCase()}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-base font-medium text-gray-900",children:s.name}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[e.jsxs("span",{children:[((a=s.types)==null?void 0:a.length)||0," ",((r=s.types)==null?void 0:r.length)===1?"type":"types"]}),e.jsx("span",{children:"•"}),e.jsx("span",{className:t?"text-green-600":"text-orange-600",children:t?"Courts assigned":"No court currently assigned to this Sport"})]})]})]})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("label",{className:"relative inline-flex cursor-pointer items-center",children:[e.jsx("input",{type:"checkbox",className:"peer sr-only",checked:s.status===1,onChange:()=>X(s)}),e.jsx("div",{className:"peer h-5 w-9 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-4 after:w-4 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-blue-600 peer-checked:after:translate-x-full peer-checked:after:border-white"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{onClick:()=>Q(s),className:"rounded-md p-2 text-gray-400 transition-colors hover:bg-gray-50 hover:text-gray-600",title:"Edit sport",children:e.jsx(xe,{className:"h-4 w-4"})}),e.jsx("button",{onClick:()=>Y(s),className:"rounded-md p-2 text-gray-400 transition-colors hover:bg-red-50 hover:text-red-600",title:"Delete sport",children:e.jsx(he,{className:"h-4 w-4"})})]})]})]}),s.status===1&&(((o=s.types)==null?void 0:o.length)>0||s.allow_cancel_reservation!==void 0)&&e.jsxs("div",{className:"border-t border-gray-100 bg-gray-50 p-4",children:[((_=s.types)==null?void 0:_.length)>0&&e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"mb-2 text-sm font-medium text-gray-700",children:"Types & Subtypes"}),e.jsx("div",{className:"space-y-2",children:(f=s.types)==null?void 0:f.map((p,le)=>{var F;return e.jsxs("div",{className:"rounded-md border border-gray-200 bg-white p-2",children:[p.name&&e.jsx("p",{className:"text-sm font-medium text-gray-700",children:p.name}),((F=p.sub_type)==null?void 0:F.length)>0&&e.jsx("div",{className:"mt-1 flex flex-wrap gap-1",children:p.sub_type.map((ne,oe)=>e.jsx("span",{className:"inline-flex rounded-full bg-blue-50 px-2 py-1 text-xs text-blue-600",children:ne},oe))})]},p.club_sport_type_id||le)})})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 text-sm font-medium text-gray-700",children:"Reservation Settings"}),e.jsxs("div",{className:"rounded-md border border-gray-200 bg-white p-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`mr-2 h-2 w-2 rounded-full ${s.allow_cancel_reservation===1?"bg-green-500":"bg-red-500"}`}),e.jsxs("p",{className:"text-sm text-gray-700",children:["Cancellation:"," ",e.jsx("span",{className:"font-medium",children:s.allow_cancel_reservation===1?"Allowed":"Not allowed"})]})]}),s.allow_cancel_reservation===1&&e.jsxs("p",{className:"mt-2 text-sm text-gray-700",children:[e.jsxs("span",{className:"inline-block rounded bg-blue-50 px-2 py-1 text-xs font-medium text-blue-600",children:[s.cancel_hours_before," hours"]}),e.jsx("span",{className:"ml-1",children:"before reservation"})]})]})]})]})]},s.sport_id||s.name)}),e.jsx("div",{onClick:()=>{g(!0),h("add"),y(null),n({name:"",types:[],sport_id:null,allow_cancel_reservation:1,cancel_hours_before:24})},className:"flex cursor-pointer items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-white p-6 text-center transition-all hover:border-blue-400 hover:bg-blue-50",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"rounded-lg bg-blue-50 p-2",children:e.jsx(j,{className:"h-5 w-5 text-blue-600"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900",children:"Add New Sport"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Create a new sport with types and subtypes"})]})]})})]}),e.jsx(me,{isOpen:B,onClose:re,title:u==="add"?"Add New Sport":"Edit Sport",showFooter:!0,primaryButtonText:C?"Saving...":"Save Sport",onPrimaryAction:te,submitting:C,children:B&&e.jsx("div",{children:e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsxs("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:["Sport name ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-300 px-4 py-3 text-base shadow-sm focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:l.name,onChange:s=>n(t=>({...t,name:s.target.value})),placeholder:"Enter sport name"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"mb-3 flex items-center justify-between",children:[e.jsxs("label",{className:"text-sm font-medium text-gray-700",children:["Types ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsxs("button",{onClick:O,className:"flex items-center gap-1 rounded-md bg-primaryBlue/10 px-3 py-1 text-sm font-medium text-primaryBlue transition-colors hover:bg-primaryBlue/20",children:[e.jsx(j,{className:"h-4 w-4"}),"Add Type"]})]}),l.types.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-lg border border-dashed border-gray-300 bg-gray-50 p-6 text-center",children:[e.jsx("div",{className:"mb-2 rounded-full bg-gray-100 p-2",children:e.jsx(ye,{className:"h-5 w-5 text-gray-400"})}),e.jsx("p",{className:"text-sm text-gray-500",children:"No types added yet"}),e.jsx("button",{onClick:O,className:"mt-2 text-sm font-medium text-primaryBlue hover:text-primaryBlue/80",children:"Add your first type"})]}):e.jsx("div",{className:"space-y-4",children:l.types.map((s,t)=>e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-gray-50 p-4 shadow-sm",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-2",children:[e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-base shadow-sm focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:s.name,onChange:a=>W(t,a.target.value),placeholder:"Enter type name"}),e.jsx("button",{onClick:()=>V(t),className:"rounded-md p-1 text-gray-400 hover:bg-gray-200 hover:text-gray-600",title:"Remove type",children:e.jsx(I,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm font-medium text-gray-600",children:["Sub-types"," ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsxs("button",{onClick:()=>Z(t),className:"flex items-center gap-1 rounded-md px-2 py-1 text-xs font-medium text-primaryBlue hover:bg-primaryBlue/10",children:[e.jsx(j,{className:"h-3 w-3"}),"Add Sub-type"]})]}),s.sub_type.length===0?e.jsx("p",{className:"text-xs italic text-gray-400",children:"No sub-types added"}):e.jsx("div",{className:"space-y-2",children:s.sub_type.map((a,r)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:a,onChange:o=>se(t,r,o.target.value),placeholder:"Enter sub-type name"}),e.jsx("button",{onClick:()=>ee(t,r),className:"rounded-md p-1 text-gray-400 hover:bg-gray-200 hover:text-gray-600",title:"Remove sub-type",children:e.jsx(I,{className:"h-4 w-4"})})]},r))})]})]},s.club_sport_type_id||t))})]}),e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-gray-50 p-4 shadow-sm",children:[e.jsx("label",{className:"mb-3 block text-sm font-medium text-gray-700",children:"Reservation Cancellation Settings"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",id:"allow-cancel-reservation",className:"h-5 w-5 rounded-md border-gray-300 text-primaryGreen focus:ring-primaryGreen",checked:l.allow_cancel_reservation===1,onChange:s=>n(t=>({...t,allow_cancel_reservation:s.target.checked?1:0}))}),e.jsx("label",{htmlFor:"allow-cancel-reservation",className:"ml-2 text-sm font-medium text-gray-700",children:"Allow users to cancel reservations"})]}),l.allow_cancel_reservation===1&&e.jsxs("div",{className:"ml-7 rounded-lg bg-white p-3",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Hours before reservation when cancel button is clickable"}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"number",min:"1",max:"168",value:l.cancel_hours_before,onChange:s=>{const t=parseInt(s.target.value,10);isNaN(t)||t<1||t>168||n(a=>({...a,cancel_hours_before:t}))},className:"w-24 rounded-lg border border-gray-300 px-3 py-2 text-sm shadow-sm focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen"}),e.jsx("span",{className:"ml-2 text-sm text-gray-500",children:"hours"})]}),e.jsx("p",{className:"mt-2 text-xs text-gray-500",children:"Users will be able to cancel reservations up to this many hours before the scheduled time."})]})]})]})]})})}),e.jsx(ue,{isOpen:K,onClose:J,onDelete:z,title:"Delete Sport",message:b?`Are you sure you want to delete "${b.name}"? This action cannot be undone and will remove all associated sport types and settings.`:"Are you sure you want to delete this sport?",loading:P,buttonText:"Yes, Delete Sport",requireConfirmation:!0})]})})}export{we as S};
