var Wt=(e,t,s)=>{if(!t.has(e))throw TypeError("Cannot "+s)};var r=(e,t,s)=>(Wt(e,t,"read from private field"),s?s.call(e):t.get(e)),h=(e,t,s)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,s)},u=(e,t,s,i)=>(Wt(e,t,"write to private field"),i?i.call(e,s):t.set(e,s),s);var Bt=(e,t,s,i)=>({set _(n){u(e,t,n,s)},get _(){return r(e,t,i)}}),p=(e,t,s)=>(Wt(e,t,"access private method"),s);import{r as k}from"../vendor-851db8c1.js";import{j as Ne}from"../@nivo/heatmap-ba1ecfff.js";var Gt=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},dt=typeof window>"u"||"Deno"in globalThis;function j(){}function Be(e,t){return typeof e=="function"?e(t):e}function Xt(e){return typeof e=="number"&&e>=0&&e!==1/0}function Ie(e,t){return Math.max(e+(t||0)-Date.now(),0)}function pt(e,t){return typeof e=="function"?e(t):e}function _(e,t){return typeof e=="function"?e(t):e}function ye(e,t){const{type:s="all",exact:i,fetchStatus:n,predicate:a,queryKey:o,stale:l}=e;if(o){if(i){if(t.queryHash!==ce(o,t.options))return!1}else if(!Tt(t.queryKey,o))return!1}if(s!=="all"){const d=t.isActive();if(s==="active"&&!d||s==="inactive"&&d)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||n&&n!==t.state.fetchStatus||a&&!a(t))}function pe(e,t){const{exact:s,status:i,predicate:n,mutationKey:a}=e;if(a){if(!t.options.mutationKey)return!1;if(s){if(Mt(t.options.mutationKey)!==Mt(a))return!1}else if(!Tt(t.options.mutationKey,a))return!1}return!(i&&t.state.status!==i||n&&!n(t))}function ce(e,t){return((t==null?void 0:t.queryKeyHashFn)||Mt)(e)}function Mt(e){return JSON.stringify(e,(t,s)=>Zt(s)?Object.keys(s).sort().reduce((i,n)=>(i[n]=s[n],i),{}):s)}function Tt(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(s=>!Tt(e[s],t[s])):!1}function xe(e,t){if(e===t)return e;const s=me(e)&&me(t);if(s||Zt(e)&&Zt(t)){const i=s?e:Object.keys(e),n=i.length,a=s?t:Object.keys(t),o=a.length,l=s?[]:{};let d=0;for(let g=0;g<o;g++){const v=s?g:a[g];(!s&&i.includes(v)||s)&&e[v]===void 0&&t[v]===void 0?(l[v]=void 0,d++):(l[v]=xe(e[v],t[v]),l[v]===e[v]&&e[v]!==void 0&&d++)}return n===o&&d===n?e:l}return t}function Yt(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}function me(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Zt(e){if(!ve(e))return!1;const t=e.constructor;if(t===void 0)return!0;const s=t.prototype;return!(!ve(s)||!s.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function ve(e){return Object.prototype.toString.call(e)==="[object Object]"}function ze(e){return new Promise(t=>{setTimeout(t,e)})}function te(e,t,s){return typeof s.structuralSharing=="function"?s.structuralSharing(e,t):s.structuralSharing!==!1?xe(e,t):t}function Ve(e,t,s=0){const i=[...e,t];return s&&i.length>s?i.slice(1):i}function $e(e,t,s=0){const i=[t,...e];return s&&i.length>s?i.slice(0,-1):i}var le=Symbol();function qe(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===le?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var it,J,mt,Pe,We=(Pe=class extends Gt{constructor(){super();h(this,it,void 0);h(this,J,void 0);h(this,mt,void 0);u(this,mt,t=>{if(!dt&&window.addEventListener){const s=()=>t();return window.addEventListener("visibilitychange",s,!1),()=>{window.removeEventListener("visibilitychange",s)}}})}onSubscribe(){r(this,J)||this.setEventListener(r(this,mt))}onUnsubscribe(){var t;this.hasListeners()||((t=r(this,J))==null||t.call(this),u(this,J,void 0))}setEventListener(t){var s;u(this,mt,t),(s=r(this,J))==null||s.call(this),u(this,J,t(i=>{typeof i=="boolean"?this.setFocused(i):this.onFocus()}))}setFocused(t){r(this,it)!==t&&(u(this,it,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(s=>{s(t)})}isFocused(){var t;return typeof r(this,it)=="boolean"?r(this,it):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},it=new WeakMap,J=new WeakMap,mt=new WeakMap,Pe),de=new We,vt,X,bt,Se,Je=(Se=class extends Gt{constructor(){super();h(this,vt,!0);h(this,X,void 0);h(this,bt,void 0);u(this,bt,t=>{if(!dt&&window.addEventListener){const s=()=>t(!0),i=()=>t(!1);return window.addEventListener("online",s,!1),window.addEventListener("offline",i,!1),()=>{window.removeEventListener("online",s),window.removeEventListener("offline",i)}}})}onSubscribe(){r(this,X)||this.setEventListener(r(this,bt))}onUnsubscribe(){var t;this.hasListeners()||((t=r(this,X))==null||t.call(this),u(this,X,void 0))}setEventListener(t){var s;u(this,bt,t),(s=r(this,X))==null||s.call(this),u(this,X,t(this.setOnline.bind(this)))}setOnline(t){r(this,vt)!==t&&(u(this,vt,t),this.listeners.forEach(i=>{i(t)}))}isOnline(){return r(this,vt)}},vt=new WeakMap,X=new WeakMap,bt=new WeakMap,Se),Vt=new Je;function ee(){let e,t;const s=new Promise((n,a)=>{e=n,t=a});s.status="pending",s.catch(()=>{});function i(n){Object.assign(s,n),delete s.resolve,delete s.reject}return s.resolve=n=>{i({status:"fulfilled",value:n}),e(n)},s.reject=n=>{i({status:"rejected",reason:n}),t(n)},s}function Xe(e){return Math.min(1e3*2**e,3e4)}function Ue(e){return(e??"online")==="online"?Vt.isOnline():!0}var je=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Jt(e){return e instanceof je}function ke(e){let t=!1,s=0,i=!1,n;const a=ee(),o=f=>{var y;i||(w(new je(f)),(y=e.abort)==null||y.call(e))},l=()=>{t=!0},d=()=>{t=!1},g=()=>de.isFocused()&&(e.networkMode==="always"||Vt.isOnline())&&e.canRun(),v=()=>Ue(e.networkMode)&&e.canRun(),c=f=>{var y;i||(i=!0,(y=e.onSuccess)==null||y.call(e,f),n==null||n(),a.resolve(f))},w=f=>{var y;i||(i=!0,(y=e.onError)==null||y.call(e,f),n==null||n(),a.reject(f))},b=()=>new Promise(f=>{var y;n=F=>{(i||g())&&f(F)},(y=e.onPause)==null||y.call(e)}).then(()=>{var f;n=void 0,i||(f=e.onContinue)==null||f.call(e)}),P=()=>{if(i)return;let f;const y=s===0?e.initialPromise:void 0;try{f=y??e.fn()}catch(F){f=Promise.reject(F)}Promise.resolve(f).then(c).catch(F=>{var L;if(i)return;const M=e.retry??(dt?0:3),O=e.retryDelay??Xe,E=typeof O=="function"?O(s,F):O,q=M===!0||typeof M=="number"&&s<M||typeof M=="function"&&M(s,F);if(t||!q){w(F);return}s++,(L=e.onFail)==null||L.call(e,s,F),ze(E).then(()=>g()?void 0:b()).then(()=>{t?w(F):P()})})};return{promise:a,cancel:o,continue:()=>(n==null||n(),a),cancelRetry:l,continueRetry:d,canStart:v,start:()=>(v()?P():b().then(P),a)}}function Ye(){let e=[],t=0,s=l=>{l()},i=l=>{l()},n=l=>setTimeout(l,0);const a=l=>{t?e.push(l):n(()=>{s(l)})},o=()=>{const l=e;e=[],l.length&&n(()=>{i(()=>{l.forEach(d=>{s(d)})})})};return{batch:l=>{let d;t++;try{d=l()}finally{t--,t||o()}return d},batchCalls:l=>(...d)=>{a(()=>{l(...d)})},schedule:a,setNotifyFunction:l=>{s=l},setBatchNotifyFunction:l=>{i=l},setScheduler:l=>{n=l}}}var Q=Ye(),rt,Fe,Le=(Fe=class{constructor(){h(this,rt,void 0)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Xt(this.gcTime)&&u(this,rt,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(dt?1/0:5*60*1e3))}clearGcTimeout(){r(this,rt)&&(clearTimeout(r(this,rt)),u(this,rt,void 0))}},rt=new WeakMap,Fe),gt,wt,U,nt,D,At,at,K,V,Qe,Ze=(Qe=class extends Le{constructor(t){super();h(this,K);h(this,gt,void 0);h(this,wt,void 0);h(this,U,void 0);h(this,nt,void 0);h(this,D,void 0);h(this,At,void 0);h(this,at,void 0);u(this,at,!1),u(this,At,t.defaultOptions),this.setOptions(t.options),this.observers=[],u(this,nt,t.client),u(this,U,r(this,nt).getQueryCache()),this.queryKey=t.queryKey,this.queryHash=t.queryHash,u(this,gt,ts(this.options)),this.state=t.state??r(this,gt),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=r(this,D))==null?void 0:t.promise}setOptions(t){this.options={...r(this,At),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&r(this,U).remove(this)}setData(t,s){const i=te(this.state.data,t,this.options);return p(this,K,V).call(this,{data:i,type:"success",dataUpdatedAt:s==null?void 0:s.updatedAt,manual:s==null?void 0:s.manual}),i}setState(t,s){p(this,K,V).call(this,{type:"setState",state:t,setStateOptions:s})}cancel(t){var i,n;const s=(i=r(this,D))==null?void 0:i.promise;return(n=r(this,D))==null||n.cancel(t),s?s.then(j).catch(j):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(r(this,gt))}isActive(){return this.observers.some(t=>_(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===le||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!Ie(this.state.dataUpdatedAt,t)}onFocus(){var s;const t=this.observers.find(i=>i.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(s=r(this,D))==null||s.continue()}onOnline(){var s;const t=this.observers.find(i=>i.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(s=r(this,D))==null||s.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),r(this,U).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(s=>s!==t),this.observers.length||(r(this,D)&&(r(this,at)?r(this,D).cancel({revert:!0}):r(this,D).cancelRetry()),this.scheduleGc()),r(this,U).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||p(this,K,V).call(this,{type:"invalidate"})}fetch(t,s){var d,g,v;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(s!=null&&s.cancelRefetch))this.cancel({silent:!0});else if(r(this,D))return r(this,D).continueRetry(),r(this,D).promise}if(t&&this.setOptions(t),!this.options.queryFn){const c=this.observers.find(w=>w.options.queryFn);c&&this.setOptions(c.options)}const i=new AbortController,n=c=>{Object.defineProperty(c,"signal",{enumerable:!0,get:()=>(u(this,at,!0),i.signal)})},a=()=>{const c=qe(this.options,s),w={client:r(this,nt),queryKey:this.queryKey,meta:this.meta};return n(w),u(this,at,!1),this.options.persister?this.options.persister(c,w,this):c(w)},o={fetchOptions:s,options:this.options,queryKey:this.queryKey,client:r(this,nt),state:this.state,fetchFn:a};n(o),(d=this.options.behavior)==null||d.onFetch(o,this),u(this,wt,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((g=o.fetchOptions)==null?void 0:g.meta))&&p(this,K,V).call(this,{type:"fetch",meta:(v=o.fetchOptions)==null?void 0:v.meta});const l=c=>{var w,b,P,f;Jt(c)&&c.silent||p(this,K,V).call(this,{type:"error",error:c}),Jt(c)||((b=(w=r(this,U).config).onError)==null||b.call(w,c,this),(f=(P=r(this,U).config).onSettled)==null||f.call(P,this.state.data,c,this)),this.scheduleGc()};return u(this,D,ke({initialPromise:s==null?void 0:s.initialPromise,fn:o.fetchFn,abort:i.abort.bind(i),onSuccess:c=>{var w,b,P,f;if(c===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(c)}catch(y){l(y);return}(b=(w=r(this,U).config).onSuccess)==null||b.call(w,c,this),(f=(P=r(this,U).config).onSettled)==null||f.call(P,c,this.state.error,this),this.scheduleGc()},onError:l,onFail:(c,w)=>{p(this,K,V).call(this,{type:"failed",failureCount:c,error:w})},onPause:()=>{p(this,K,V).call(this,{type:"pause"})},onContinue:()=>{p(this,K,V).call(this,{type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0})),r(this,D).start()}},gt=new WeakMap,wt=new WeakMap,U=new WeakMap,nt=new WeakMap,D=new WeakMap,At=new WeakMap,at=new WeakMap,K=new WeakSet,V=function(t){const s=i=>{switch(t.type){case"failed":return{...i,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...i,fetchStatus:"paused"};case"continue":return{...i,fetchStatus:"fetching"};case"fetch":return{...i,...Ke(i.data,this.options),fetchMeta:t.meta??null};case"success":return{...i,data:t.data,dataUpdateCount:i.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const n=t.error;return Jt(n)&&n.revert&&r(this,wt)?{...r(this,wt),fetchStatus:"idle"}:{...i,error:n,errorUpdateCount:i.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:i.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...i,isInvalidated:!0};case"setState":return{...i,...t.state}}};this.state=s(this.state),Q.batch(()=>{this.observers.forEach(i=>{i.onQueryUpdate()}),r(this,U).notify({query:this,type:"updated",action:t})})},Qe);function Ke(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Ue(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function ts(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,s=t!==void 0,i=s?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:s?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:s?"success":"pending",fetchStatus:"idle"}}var G,Ee,es=(Ee=class extends Gt{constructor(t={}){super();h(this,G,void 0);this.config=t,u(this,G,new Map)}build(t,s,i){const n=s.queryKey,a=s.queryHash??ce(n,s);let o=this.get(a);return o||(o=new Ze({client:t,queryKey:n,queryHash:a,options:t.defaultQueryOptions(s),state:i,defaultOptions:t.getQueryDefaults(n)}),this.add(o)),o}add(t){r(this,G).has(t.queryHash)||(r(this,G).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const s=r(this,G).get(t.queryHash);s&&(t.destroy(),s===t&&r(this,G).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Q.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return r(this,G).get(t)}getAll(){return[...r(this,G).values()]}find(t){const s={exact:!0,...t};return this.getAll().find(i=>ye(s,i))}findAll(t={}){const s=this.getAll();return Object.keys(t).length>0?s.filter(i=>ye(t,i)):s}notify(t){Q.batch(()=>{this.listeners.forEach(s=>{s(t)})})}onFocus(){Q.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Q.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},G=new WeakMap,Ee),N,A,ut,B,W,De,ss=(De=class extends Le{constructor(t){super();h(this,B);h(this,N,void 0);h(this,A,void 0);h(this,ut,void 0);this.mutationId=t.mutationId,u(this,A,t.mutationCache),u(this,N,[]),this.state=t.state||is(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){r(this,N).includes(t)||(r(this,N).push(t),this.clearGcTimeout(),r(this,A).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){u(this,N,r(this,N).filter(s=>s!==t)),this.scheduleGc(),r(this,A).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){r(this,N).length||(this.state.status==="pending"?this.scheduleGc():r(this,A).remove(this))}continue(){var t;return((t=r(this,ut))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var n,a,o,l,d,g,v,c,w,b,P,f,y,F,M,O,E,q,L,T;u(this,ut,ke({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(S,C)=>{p(this,B,W).call(this,{type:"failed",failureCount:S,error:C})},onPause:()=>{p(this,B,W).call(this,{type:"pause"})},onContinue:()=>{p(this,B,W).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>r(this,A).canRun(this)}));const s=this.state.status==="pending",i=!r(this,ut).canStart();try{if(!s){p(this,B,W).call(this,{type:"pending",variables:t,isPaused:i}),await((a=(n=r(this,A).config).onMutate)==null?void 0:a.call(n,t,this));const C=await((l=(o=this.options).onMutate)==null?void 0:l.call(o,t));C!==this.state.context&&p(this,B,W).call(this,{type:"pending",context:C,variables:t,isPaused:i})}const S=await r(this,ut).start();return await((g=(d=r(this,A).config).onSuccess)==null?void 0:g.call(d,S,t,this.state.context,this)),await((c=(v=this.options).onSuccess)==null?void 0:c.call(v,S,t,this.state.context)),await((b=(w=r(this,A).config).onSettled)==null?void 0:b.call(w,S,null,this.state.variables,this.state.context,this)),await((f=(P=this.options).onSettled)==null?void 0:f.call(P,S,null,t,this.state.context)),p(this,B,W).call(this,{type:"success",data:S}),S}catch(S){try{throw await((F=(y=r(this,A).config).onError)==null?void 0:F.call(y,S,t,this.state.context,this)),await((O=(M=this.options).onError)==null?void 0:O.call(M,S,t,this.state.context)),await((q=(E=r(this,A).config).onSettled)==null?void 0:q.call(E,void 0,S,this.state.variables,this.state.context,this)),await((T=(L=this.options).onSettled)==null?void 0:T.call(L,void 0,S,t,this.state.context)),S}finally{p(this,B,W).call(this,{type:"error",error:S})}}finally{r(this,A).runNext(this)}}},N=new WeakMap,A=new WeakMap,ut=new WeakMap,B=new WeakSet,W=function(t){const s=i=>{switch(t.type){case"failed":return{...i,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...i,isPaused:!0};case"continue":return{...i,isPaused:!1};case"pending":return{...i,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...i,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...i,data:void 0,error:t.error,failureCount:i.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=s(this.state),Q.batch(()=>{r(this,N).forEach(i=>{i.onMutationUpdate(t)}),r(this,A).notify({mutation:this,type:"updated",action:t})})},De);function is(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var $,H,It,Me,rs=(Me=class extends Gt{constructor(t={}){super();h(this,$,void 0);h(this,H,void 0);h(this,It,void 0);this.config=t,u(this,$,new Set),u(this,H,new Map),u(this,It,0)}build(t,s,i){const n=new ss({mutationCache:this,mutationId:++Bt(this,It)._,options:t.defaultMutationOptions(s),state:i});return this.add(n),n}add(t){r(this,$).add(t);const s=zt(t);if(typeof s=="string"){const i=r(this,H).get(s);i?i.push(t):r(this,H).set(s,[t])}this.notify({type:"added",mutation:t})}remove(t){if(r(this,$).delete(t)){const s=zt(t);if(typeof s=="string"){const i=r(this,H).get(s);if(i)if(i.length>1){const n=i.indexOf(t);n!==-1&&i.splice(n,1)}else i[0]===t&&r(this,H).delete(s)}}this.notify({type:"removed",mutation:t})}canRun(t){const s=zt(t);if(typeof s=="string"){const i=r(this,H).get(s),n=i==null?void 0:i.find(a=>a.state.status==="pending");return!n||n===t}else return!0}runNext(t){var i;const s=zt(t);if(typeof s=="string"){const n=(i=r(this,H).get(s))==null?void 0:i.find(a=>a!==t&&a.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}else return Promise.resolve()}clear(){Q.batch(()=>{r(this,$).forEach(t=>{this.notify({type:"removed",mutation:t})}),r(this,$).clear(),r(this,H).clear()})}getAll(){return Array.from(r(this,$))}find(t){const s={exact:!0,...t};return this.getAll().find(i=>pe(s,i))}findAll(t={}){return this.getAll().filter(s=>pe(t,s))}notify(t){Q.batch(()=>{this.listeners.forEach(s=>{s(t)})})}resumePausedMutations(){const t=this.getAll().filter(s=>s.state.isPaused);return Q.batch(()=>Promise.all(t.map(s=>s.continue().catch(j))))}},$=new WeakMap,H=new WeakMap,It=new WeakMap,Me);function zt(e){var t;return(t=e.options.scope)==null?void 0:t.id}function be(e){return{onFetch:(t,s)=>{var v,c,w,b,P;const i=t.options,n=(w=(c=(v=t.fetchOptions)==null?void 0:v.meta)==null?void 0:c.fetchMore)==null?void 0:w.direction,a=((b=t.state.data)==null?void 0:b.pages)||[],o=((P=t.state.data)==null?void 0:P.pageParams)||[];let l={pages:[],pageParams:[]},d=0;const g=async()=>{let f=!1;const y=O=>{Object.defineProperty(O,"signal",{enumerable:!0,get:()=>(t.signal.aborted?f=!0:t.signal.addEventListener("abort",()=>{f=!0}),t.signal)})},F=qe(t.options,t.fetchOptions),M=async(O,E,q)=>{if(f)return Promise.reject();if(E==null&&O.pages.length)return Promise.resolve(O);const L={client:t.client,queryKey:t.queryKey,pageParam:E,direction:q?"backward":"forward",meta:t.options.meta};y(L);const T=await F(L),{maxPages:S}=t.options,C=q?$e:Ve;return{pages:C(O.pages,T,S),pageParams:C(O.pageParams,E,S)}};if(n&&a.length){const O=n==="backward",E=O?ns:ge,q={pages:a,pageParams:o},L=E(i,q);l=await M(q,L,O)}else{const O=e??a.length;do{const E=d===0?o[0]??i.initialPageParam:ge(i,l);if(d>0&&E==null)break;l=await M(l,E),d++}while(d<O)}return l};t.options.persister?t.fetchFn=()=>{var f,y;return(y=(f=t.options).persister)==null?void 0:y.call(f,g,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},s)}:t.fetchFn=g}}}function ge(e,{pages:t,pageParams:s}){const i=t.length-1;return t.length>0?e.getNextPageParam(t[i],t,s[i],s):void 0}function ns(e,{pages:t,pageParams:s}){var i;return t.length>0?(i=e.getPreviousPageParam)==null?void 0:i.call(e,t[0],t,s[0],s):void 0}var R,Y,Z,Ct,Ot,tt,Rt,Pt,Te,Ss=(Te=class{constructor(e={}){h(this,R,void 0);h(this,Y,void 0);h(this,Z,void 0);h(this,Ct,void 0);h(this,Ot,void 0);h(this,tt,void 0);h(this,Rt,void 0);h(this,Pt,void 0);u(this,R,e.queryCache||new es),u(this,Y,e.mutationCache||new rs),u(this,Z,e.defaultOptions||{}),u(this,Ct,new Map),u(this,Ot,new Map),u(this,tt,0)}mount(){Bt(this,tt)._++,r(this,tt)===1&&(u(this,Rt,de.subscribe(async e=>{e&&(await this.resumePausedMutations(),r(this,R).onFocus())})),u(this,Pt,Vt.subscribe(async e=>{e&&(await this.resumePausedMutations(),r(this,R).onOnline())})))}unmount(){var e,t;Bt(this,tt)._--,r(this,tt)===0&&((e=r(this,Rt))==null||e.call(this),u(this,Rt,void 0),(t=r(this,Pt))==null||t.call(this),u(this,Pt,void 0))}isFetching(e){return r(this,R).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return r(this,Y).findAll({...e,status:"pending"}).length}getQueryData(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=r(this,R).get(t.queryHash))==null?void 0:s.state.data}ensureQueryData(e){const t=this.defaultQueryOptions(e),s=r(this,R).build(this,t),i=s.state.data;return i===void 0?this.fetchQuery(e):(e.revalidateIfStale&&s.isStaleByTime(pt(t.staleTime,s))&&this.prefetchQuery(t),Promise.resolve(i))}getQueriesData(e){return r(this,R).findAll(e).map(({queryKey:t,state:s})=>{const i=s.data;return[t,i]})}setQueryData(e,t,s){const i=this.defaultQueryOptions({queryKey:e}),n=r(this,R).get(i.queryHash),a=n==null?void 0:n.state.data,o=Be(t,a);if(o!==void 0)return r(this,R).build(this,i).setData(o,{...s,manual:!0})}setQueriesData(e,t,s){return Q.batch(()=>r(this,R).findAll(e).map(({queryKey:i})=>[i,this.setQueryData(i,t,s)]))}getQueryState(e){var s;const t=this.defaultQueryOptions({queryKey:e});return(s=r(this,R).get(t.queryHash))==null?void 0:s.state}removeQueries(e){const t=r(this,R);Q.batch(()=>{t.findAll(e).forEach(s=>{t.remove(s)})})}resetQueries(e,t){const s=r(this,R),i={type:"active",...e};return Q.batch(()=>(s.findAll(e).forEach(n=>{n.reset()}),this.refetchQueries(i,t)))}cancelQueries(e,t={}){const s={revert:!0,...t},i=Q.batch(()=>r(this,R).findAll(e).map(n=>n.cancel(s)));return Promise.all(i).then(j).catch(j)}invalidateQueries(e,t={}){return Q.batch(()=>{if(r(this,R).findAll(e).forEach(i=>{i.invalidate()}),(e==null?void 0:e.refetchType)==="none")return Promise.resolve();const s={...e,type:(e==null?void 0:e.refetchType)??(e==null?void 0:e.type)??"active"};return this.refetchQueries(s,t)})}refetchQueries(e,t={}){const s={...t,cancelRefetch:t.cancelRefetch??!0},i=Q.batch(()=>r(this,R).findAll(e).filter(n=>!n.isDisabled()).map(n=>{let a=n.fetch(void 0,s);return s.throwOnError||(a=a.catch(j)),n.state.fetchStatus==="paused"?Promise.resolve():a}));return Promise.all(i).then(j)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const s=r(this,R).build(this,t);return s.isStaleByTime(pt(t.staleTime,s))?s.fetch(t):Promise.resolve(s.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(j).catch(j)}fetchInfiniteQuery(e){return e.behavior=be(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(j).catch(j)}ensureInfiniteQueryData(e){return e.behavior=be(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Vt.isOnline()?r(this,Y).resumePausedMutations():Promise.resolve()}getQueryCache(){return r(this,R)}getMutationCache(){return r(this,Y)}getDefaultOptions(){return r(this,Z)}setDefaultOptions(e){u(this,Z,e)}setQueryDefaults(e,t){r(this,Ct).set(Mt(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...r(this,Ct).values()],s={};return t.forEach(i=>{Tt(e,i.queryKey)&&Object.assign(s,i.defaultOptions)}),s}setMutationDefaults(e,t){r(this,Ot).set(Mt(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...r(this,Ot).values()];let s={};return t.forEach(i=>{Tt(e,i.mutationKey)&&(s={...s,...i.defaultOptions})}),s}defaultQueryOptions(e){if(e._defaulted)return e;const t={...r(this,Z).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=ce(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===le&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...r(this,Z).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){r(this,R).clear(),r(this,Y).clear()}},R=new WeakMap,Y=new WeakMap,Z=new WeakMap,Ct=new WeakMap,Ot=new WeakMap,tt=new WeakMap,Rt=new WeakMap,Pt=new WeakMap,Te),x,m,xt,I,ot,St,et,z,qt,Ft,Qt,ht,ct,st,Et,lt,Dt,Ut,se,jt,ie,kt,re,Lt,ne,Kt,ae,Ht,ue,_t,oe,$t,He,Ae,as=(Ae=class extends Gt{constructor(t,s){super();h(this,lt);h(this,Ut);h(this,jt);h(this,kt);h(this,Lt);h(this,Kt);h(this,Ht);h(this,_t);h(this,$t);h(this,x,void 0);h(this,m,void 0);h(this,xt,void 0);h(this,I,void 0);h(this,ot,void 0);h(this,St,void 0);h(this,et,void 0);h(this,z,void 0);h(this,qt,void 0);h(this,Ft,void 0);h(this,Qt,void 0);h(this,ht,void 0);h(this,ct,void 0);h(this,st,void 0);h(this,Et,new Set);this.options=s,u(this,x,t),u(this,z,null),u(this,et,ee()),this.options.experimental_prefetchInRender||r(this,et).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(s)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(r(this,m).addObserver(this),we(r(this,m),this.options)?p(this,lt,Dt).call(this):this.updateResult(),p(this,Lt,ne).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return he(r(this,m),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return he(r(this,m),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,p(this,Kt,ae).call(this),p(this,Ht,ue).call(this),r(this,m).removeObserver(this)}setOptions(t,s){const i=this.options,n=r(this,m);if(this.options=r(this,x).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof _(this.options.enabled,r(this,m))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");p(this,_t,oe).call(this),r(this,m).setOptions(this.options),i._defaulted&&!Yt(this.options,i)&&r(this,x).getQueryCache().notify({type:"observerOptionsUpdated",query:r(this,m),observer:this});const a=this.hasListeners();a&&Ce(r(this,m),n,this.options,i)&&p(this,lt,Dt).call(this),this.updateResult(s),a&&(r(this,m)!==n||_(this.options.enabled,r(this,m))!==_(i.enabled,r(this,m))||pt(this.options.staleTime,r(this,m))!==pt(i.staleTime,r(this,m)))&&p(this,Ut,se).call(this);const o=p(this,jt,ie).call(this);a&&(r(this,m)!==n||_(this.options.enabled,r(this,m))!==_(i.enabled,r(this,m))||o!==r(this,st))&&p(this,kt,re).call(this,o)}getOptimisticResult(t){const s=r(this,x).getQueryCache().build(r(this,x),t),i=this.createResult(s,t);return os(this,i)&&(u(this,I,i),u(this,St,this.options),u(this,ot,r(this,m).state)),i}getCurrentResult(){return r(this,I)}trackResult(t,s){const i={};return Object.keys(t).forEach(n=>{Object.defineProperty(i,n,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(n),s==null||s(n),t[n])})}),i}trackProp(t){r(this,Et).add(t)}getCurrentQuery(){return r(this,m)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const s=r(this,x).defaultQueryOptions(t),i=r(this,x).getQueryCache().build(r(this,x),s);return i.fetch().then(()=>this.createResult(i,s))}fetch(t){return p(this,lt,Dt).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),r(this,I)))}createResult(t,s){var S;const i=r(this,m),n=this.options,a=r(this,I),o=r(this,ot),l=r(this,St),g=t!==i?t.state:r(this,xt),{state:v}=t;let c={...v},w=!1,b;if(s._optimisticResults){const C=this.hasListeners(),ft=!C&&we(t,s),yt=C&&Ce(t,i,s,n);(ft||yt)&&(c={...c,...Ke(v.data,t.options)}),s._optimisticResults==="isRestoring"&&(c.fetchStatus="idle")}let{error:P,errorUpdatedAt:f,status:y}=c;if(s.select&&c.data!==void 0)if(a&&c.data===(o==null?void 0:o.data)&&s.select===r(this,qt))b=r(this,Ft);else try{u(this,qt,s.select),b=s.select(c.data),b=te(a==null?void 0:a.data,b,s),u(this,Ft,b),u(this,z,null)}catch(C){u(this,z,C)}else b=c.data;if(s.placeholderData!==void 0&&b===void 0&&y==="pending"){let C;if(a!=null&&a.isPlaceholderData&&s.placeholderData===(l==null?void 0:l.placeholderData))C=a.data;else if(C=typeof s.placeholderData=="function"?s.placeholderData((S=r(this,Qt))==null?void 0:S.state.data,r(this,Qt)):s.placeholderData,s.select&&C!==void 0)try{C=s.select(C),u(this,z,null)}catch(ft){u(this,z,ft)}C!==void 0&&(y="success",b=te(a==null?void 0:a.data,C,s),w=!0)}r(this,z)&&(P=r(this,z),b=r(this,Ft),f=Date.now(),y="error");const F=c.fetchStatus==="fetching",M=y==="pending",O=y==="error",E=M&&F,q=b!==void 0,T={status:y,fetchStatus:c.fetchStatus,isPending:M,isSuccess:y==="success",isError:O,isInitialLoading:E,isLoading:E,data:b,dataUpdatedAt:c.dataUpdatedAt,error:P,errorUpdatedAt:f,failureCount:c.fetchFailureCount,failureReason:c.fetchFailureReason,errorUpdateCount:c.errorUpdateCount,isFetched:c.dataUpdateCount>0||c.errorUpdateCount>0,isFetchedAfterMount:c.dataUpdateCount>g.dataUpdateCount||c.errorUpdateCount>g.errorUpdateCount,isFetching:F,isRefetching:F&&!M,isLoadingError:O&&!q,isPaused:c.fetchStatus==="paused",isPlaceholderData:w,isRefetchError:O&&q,isStale:fe(t,s),refetch:this.refetch,promise:r(this,et)};if(this.options.experimental_prefetchInRender){const C=Nt=>{T.status==="error"?Nt.reject(T.error):T.data!==void 0&&Nt.resolve(T.data)},ft=()=>{const Nt=u(this,et,T.promise=ee());C(Nt)},yt=r(this,et);switch(yt.status){case"pending":t.queryHash===i.queryHash&&C(yt);break;case"fulfilled":(T.status==="error"||T.data!==yt.value)&&ft();break;case"rejected":(T.status!=="error"||T.error!==yt.reason)&&ft();break}}return T}updateResult(t){const s=r(this,I),i=this.createResult(r(this,m),this.options);if(u(this,ot,r(this,m).state),u(this,St,this.options),r(this,ot).data!==void 0&&u(this,Qt,r(this,m)),Yt(i,s))return;u(this,I,i);const n={},a=()=>{if(!s)return!0;const{notifyOnChangeProps:o}=this.options,l=typeof o=="function"?o():o;if(l==="all"||!l&&!r(this,Et).size)return!0;const d=new Set(l??r(this,Et));return this.options.throwOnError&&d.add("error"),Object.keys(r(this,I)).some(g=>{const v=g;return r(this,I)[v]!==s[v]&&d.has(v)})};(t==null?void 0:t.listeners)!==!1&&a()&&(n.listeners=!0),p(this,$t,He).call(this,{...n,...t})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&p(this,Lt,ne).call(this)}},x=new WeakMap,m=new WeakMap,xt=new WeakMap,I=new WeakMap,ot=new WeakMap,St=new WeakMap,et=new WeakMap,z=new WeakMap,qt=new WeakMap,Ft=new WeakMap,Qt=new WeakMap,ht=new WeakMap,ct=new WeakMap,st=new WeakMap,Et=new WeakMap,lt=new WeakSet,Dt=function(t){p(this,_t,oe).call(this);let s=r(this,m).fetch(this.options,t);return t!=null&&t.throwOnError||(s=s.catch(j)),s},Ut=new WeakSet,se=function(){p(this,Kt,ae).call(this);const t=pt(this.options.staleTime,r(this,m));if(dt||r(this,I).isStale||!Xt(t))return;const i=Ie(r(this,I).dataUpdatedAt,t)+1;u(this,ht,setTimeout(()=>{r(this,I).isStale||this.updateResult()},i))},jt=new WeakSet,ie=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(r(this,m)):this.options.refetchInterval)??!1},kt=new WeakSet,re=function(t){p(this,Ht,ue).call(this),u(this,st,t),!(dt||_(this.options.enabled,r(this,m))===!1||!Xt(r(this,st))||r(this,st)===0)&&u(this,ct,setInterval(()=>{(this.options.refetchIntervalInBackground||de.isFocused())&&p(this,lt,Dt).call(this)},r(this,st)))},Lt=new WeakSet,ne=function(){p(this,Ut,se).call(this),p(this,kt,re).call(this,p(this,jt,ie).call(this))},Kt=new WeakSet,ae=function(){r(this,ht)&&(clearTimeout(r(this,ht)),u(this,ht,void 0))},Ht=new WeakSet,ue=function(){r(this,ct)&&(clearInterval(r(this,ct)),u(this,ct,void 0))},_t=new WeakSet,oe=function(){const t=r(this,x).getQueryCache().build(r(this,x),this.options);if(t===r(this,m))return;const s=r(this,m);u(this,m,t),u(this,xt,t.state),this.hasListeners()&&(s==null||s.removeObserver(this),t.addObserver(this))},$t=new WeakSet,He=function(t){Q.batch(()=>{t.listeners&&this.listeners.forEach(s=>{s(r(this,I))}),r(this,x).getQueryCache().notify({query:r(this,m),type:"observerResultsUpdated"})})},Ae);function us(e,t){return _(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function we(e,t){return us(e,t)||e.state.data!==void 0&&he(e,t,t.refetchOnMount)}function he(e,t,s){if(_(t.enabled,e)!==!1){const i=typeof s=="function"?s(e):s;return i==="always"||i!==!1&&fe(e,t)}return!1}function Ce(e,t,s,i){return(e!==t||_(i.enabled,e)===!1)&&(!s.suspense||e.state.status!=="error")&&fe(e,s)}function fe(e,t){return _(t.enabled,e)!==!1&&e.isStaleByTime(pt(t.staleTime,e))}function os(e,t){return!Yt(e.getCurrentResult(),t)}var _e=k.createContext(void 0),hs=e=>{const t=k.useContext(_e);if(e)return e;if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},Fs=({client:e,children:t})=>(k.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),Ne.jsx(_e.Provider,{value:e,children:t})),Ge=k.createContext(!1),cs=()=>k.useContext(Ge);Ge.Provider;function ls(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var ds=k.createContext(ls()),fs=()=>k.useContext(ds);function ys(e,t){return typeof e=="function"?e(...t):!!e}function Oe(){}var ps=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},ms=e=>{k.useEffect(()=>{e.clearReset()},[e])},vs=({result:e,errorResetBoundary:t,throwOnError:s,query:i,suspense:n})=>e.isError&&!t.isReset()&&!e.isFetching&&i&&(n&&e.data===void 0||ys(s,[e.error,i])),bs=e=>{const t=e.staleTime;e.suspense&&(e.staleTime=typeof t=="function"?(...s)=>Math.max(t(...s),1e3):Math.max(t??1e3,1e3),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3)))},gs=(e,t)=>e.isLoading&&e.isFetching&&!t,ws=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,Re=(e,t,s)=>t.fetchOptimistic(e).catch(()=>{s.clearReset()});function Cs(e,t,s){var c,w,b,P,f;const i=hs(s),n=cs(),a=fs(),o=i.defaultQueryOptions(e);(w=(c=i.getDefaultOptions().queries)==null?void 0:c._experimental_beforeQuery)==null||w.call(c,o),o._optimisticResults=n?"isRestoring":"optimistic",bs(o),ps(o,a),ms(a);const l=!i.getQueryCache().get(o.queryHash),[d]=k.useState(()=>new t(i,o)),g=d.getOptimisticResult(o),v=!n&&e.subscribed!==!1;if(k.useSyncExternalStore(k.useCallback(y=>{const F=v?d.subscribe(Q.batchCalls(y)):Oe;return d.updateResult(),F},[d,v]),()=>d.getCurrentResult(),()=>d.getCurrentResult()),k.useEffect(()=>{d.setOptions(o,{listeners:!1})},[o,d]),ws(o,g))throw Re(o,d,a);if(vs({result:g,errorResetBoundary:a,throwOnError:o.throwOnError,query:i.getQueryCache().get(o.queryHash),suspense:o.suspense}))throw g.error;if((P=(b=i.getDefaultOptions().queries)==null?void 0:b._experimental_afterQuery)==null||P.call(b,o,g),o.experimental_prefetchInRender&&!dt&&gs(g,n)){const y=l?Re(o,d,a):(f=i.getQueryCache().get(o.queryHash))==null?void 0:f.promise;y==null||y.catch(Oe).finally(()=>{d.updateResult()})}return o.notifyOnChangeProps?g:d.trackResult(g)}function Qs(e,t){return Cs(e,as,t)}export{Ss as Q,Fs as a,Qs as u};
