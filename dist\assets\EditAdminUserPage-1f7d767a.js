import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as f,f as G,j as q,r as h}from"./vendor-851db8c1.js";import{u as B}from"./react-hook-form-687afde5.js";import{o as L}from"./yup-2824f222.js";import{c as M,a as g}from"./yup-54691517.js";import{w as H,M as I,A as K,G as V,t as T,d as b,l as Y,b as z,c as J}from"./index-08a5dc5b.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let r=new I;const Q=({activeId:i,setSidebar:y})=>{var k,A,S,_;const U=M({email:g().email().required(),password:g(),role:g()}).required(),{dispatch:w}=f.useContext(K),{dispatch:n}=f.useContext(V),R=G();q();const[u,$]=h.useState(""),[W,F]=h.useState(0),[j,v]=h.useState(!1),{register:d,handleSubmit:E,setError:c,setValue:x,formState:{errors:p}}=B({resolver:L(U)}),O=["club","staff","user","coach","admin"],D=[{key:"0",value:"Inactive"},{key:"2",value:"Suspend"},{key:"1",value:"Active"}],N=async e=>{var P,C;v(!0);try{if(u!==e.email){const t=await r.updateEmailByAdmin(e.email,i);if(!t.error)b(n,"Email Updated",1e3);else if(t.validation){const l=Object.keys(t.validation);for(let o=0;o<l.length;o++){const m=l[o];c(m,{type:"manual",message:t.validation[m]})}}}if(e.password.length>0){const t=await r.updatePasswordByAdmin(e.password,i);if(!t.error)b(n,"Password Updated",2e3);else if(t.validation){const l=Object.keys(t.validation);for(let o=0;o<l.length;o++){const m=l[o];c(m,{type:"manual",message:t.validation[m]})}}}r.setTable("user");const a=await r.callRestAPI({activeId:i,email:e.email,role:e.role,status:e.status},"PUT");if(!a.error)await Y(r,{user_id:localStorage.getItem("user"),activity_type:z.user_management,action_type:J.UPDATE,data:{user_id:i,old_email:u,new_email:e.email,old_role:(P=a.model)==null?void 0:P.role,new_role:e.role,old_status:(C=a.model)==null?void 0:C.status,new_status:e.status,password_changed:e.password.length>0,email_changed:u!==e.email},club_id:null,description:`Admin updated user: ${e.email}`}),b(n,"Added",4e3),R("/admin/users");else if(a.validation){const t=Object.keys(a.validation);for(let l=0;l<t.length;l++){const o=t[l];c(o,{type:"manual",message:a.validation[o]})}}}catch(a){console.log("Error",a),c("email",{type:"manual",message:a.message}),T(w,a.message)}v(!1)};return f.useEffect(()=>{n({type:"SETPATH",payload:{path:"users"}}),async function(){try{r.setTable("user");const e=await r.callRestAPI({id:i},"GET");e.error||(x("email",e.model.email),x("role",e.model.role),x("status",e.model.status),$(e.model.email),F(e.model.id))}catch(e){console.log("Error",e),T(w,e.message)}}()},[i]),s.jsxs("div",{className:"mx-auto rounded",children:[s.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[s.jsx("div",{className:"flex items-center gap-3",children:s.jsx("span",{className:"text-lg font-semibold",children:"Edit User"})}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>y(!1),children:"Cancel"}),s.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await E(N)(),y(!1)},disabled:j,children:j?"Saving...":"Save"})]})]}),s.jsxs("form",{className:" w-full p-4 text-left",onSubmit:E(N),children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"email",children:"Email"}),s.jsx("input",{type:"email",...d("email"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(k=p.email)!=null&&k.message?"border-red-500":""}`}),s.jsx("p",{className:"text-xs italic text-red-500",children:(A=p.email)==null?void 0:A.message})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Role"}),s.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...d("role"),children:O.map(e=>s.jsx("option",{name:"role",value:e,children:e},e))})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Status"}),s.jsx("select",{className:"focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...d("status"),children:D.map(e=>s.jsx("option",{name:"status",value:e.key,children:e.value},e.key))})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"password",children:"Password"}),s.jsx("input",{type:"password",placeholder:"******************",...d("password"),className:`focus:shadow-outline mb-3 w-full  appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(S=p.password)!=null&&S.message?"border-red-500":""}`}),s.jsx("p",{className:"text-xs italic text-red-500",children:(_=p.password)==null?void 0:_.message})]})]})]})},$e=H(Q,"user","You don't have permission to edit users");export{$e as default};
