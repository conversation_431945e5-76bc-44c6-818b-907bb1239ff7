import{j as E}from"./@nivo/heatmap-ba1ecfff.js";import{b as k,r}from"./vendor-851db8c1.js";import{S as V}from"./SelectionOptionsCard-30c39f7f.js";const q=k.memo(({sports:m,onSelectionChange:$,isChildren:B=!1,initialSport:y=null,initialType:i=null,initialSubType:h=null,userPermissions:b=null,courts:u=[],filterMode:d="reservation"})=>{const[o,z]=r.useState(y),[n,T]=r.useState(i),[w,g]=r.useState(h),[G,j]=r.useState([]),[W,C]=r.useState([]),[v,H]=r.useState(!1),[N,x]=r.useState(!1),[p,K]=r.useState(!1);console.log("=== SportTypeSelection Debug ==="),console.log("Sports count:",m||0),console.log("Courts count:",u||0),console.log("FilterMode:",d);const A=r.useRef(y),_=r.useRef(i),F=r.useRef(h),L=(e,t)=>{try{if(!e.court_settings)return!0;const l=JSON.parse(e.court_settings);switch(t){case"reservation":return l.allow_reservation!==!1;case"lesson":return l.allow_lesson!==!1;case"clinic":return l.allow_clinic!==!1;case"buddy":return l.allow_buddy!==!1;default:return!0}}catch(l){return console.warn(`Failed to parse court_settings for court ${e.id}:`,l),!1}},O=(e,t=null,l=null)=>{if(!u||!Array.isArray(u)||!d)return!0;const s=u.filter(c=>{var a;return!(((a=c.sport_id)==null?void 0:a.toString())!==(e==null?void 0:e.toString())||t!==null&&c.type!==t||l!==null&&c.sub_type!==l)});return s.length===0?!1:s.some(c=>L(c,d))},f=k.useMemo(()=>{if(!m||!Array.isArray(m))return[];let e=m;return e=e.filter(t=>t.status===1),b!=null&&b.applicable_sports&&Array.isArray(b.applicable_sports)&&(e=e.filter(t=>b.applicable_sports.includes(t.id))),u&&u.length>0&&(e=e.filter(t=>u.some(s=>{var c,a;return((c=s.sport_id)==null?void 0:c.toString())===((a=t.id)==null?void 0:a.toString())}))),d&&u&&u.length>0&&(e=e.filter(t=>O(t.id))),e},[m,b,u,d]),S=f==null?void 0:f.find(e=>e.id===o);return r.useEffect(()=>{z(y),T(i),g(h),A.current=y,_.current=i,F.current=h,K(!0)},[y,i,h]),r.useEffect(()=>{if((f==null?void 0:f.length)>0&&p){const e=f==null?void 0:f.find(t=>t.id===y);if(e){const t=(e==null?void 0:e.sport_types)||[],s=t.filter(a=>a.type&&a.type.trim()!=="").length>0;j(t),H(s);let c=!1;if(i&&s){const a=e.sport_types.find(R=>R.type===i),J=((a==null?void 0:a.subtype)||[]).filter(R=>R&&R.trim()!=="");c=J.length>0,c&&C(J)}x(c),(y!==null||i!==null||h!==null)&&$({sport:y,type:i,subType:h,hasTypes:s,hasSubTypes:c})}}},[f,p,y,i,h]),r.useEffect(()=>{if(S){const e=S.sport_types||[],t=d?e.filter(s=>!s.type||s.type.trim()===""?O(o):O(o,s.type)):e;j(t);const l=t.filter(s=>s.type&&s.type.trim()!=="");H(l.length>0),p&&o!==A.current&&(l.length===0?T(""):T(null),g(null))}else j([]),H(!1),p&&o!==A.current&&(T(null),g(null))},[o,S,p,u,d]),r.useEffect(()=>{if(S&&n!==null){if(!v||n===""){C([]),x(!1),p&&n!==_.current&&g("");return}const e=S.sport_types.find(s=>s.type===n),t=(e==null?void 0:e.subtype)||[],l=d?t.filter(s=>!s||s.trim()===""?!1:O(o,n,s)):t.filter(s=>s&&s.trim()!=="");C(l),x(l.length>0),p&&n!==_.current&&(l.length===0?g(""):g(null))}else(!S||n===null)&&(C([]),x(!1),p&&n!==_.current&&g(null))},[n,S,v,p,o,u,d]),r.useEffect(()=>{if(p){const e=o!==A.current,t=n!==_.current,l=w!==F.current;(e||t||l)&&($({sport:o,type:n,subType:w,hasTypes:v,hasSubTypes:N}),A.current=o,_.current=n,F.current=w)}},[o,n,w,v,N,p]),E.jsxs("div",{className:`h-fit w-full space-y-6 rounded-lg bg-white ${B?"p-0 shadow-none":"p-4 shadow-5"}`,children:[E.jsx(V,{title:"Sports",options:f||[],selectedOption:o,onOptionSelect:z,emptyMessage:"No sports found",optionType:"sport"}),v&&E.jsx(V,{title:"Type",options:G,selectedOption:n,onOptionSelect:T,showPlaceholder:!o,optionType:"type"}),v&&n&&N&&W.length>0&&E.jsx(V,{title:"Sub-Type",options:W,selectedOption:w,onOptionSelect:e=>{console.log("Subtype selected:",e),g(e)},showPlaceholder:!n,optionType:"subtype"})]})});q.displayName="SportTypeSelection";const Z=q;export{Z as S};
