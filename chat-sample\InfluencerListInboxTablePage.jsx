import { LoadingIndicator } from "Components/LoadingIndicator";
import { AuthContext, tokenExpireError } from "Context/Auth";
import { GlobalContext, showToast } from "Context/Global";
import React, { useEffect, useState, useContext, useRef } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { v4 as uuidv4 } from "uuid"; // Add this import for generating unique IDs
import { format, parseISO } from "date-fns"; // Add this import for date formatting
import { InteractiveButton } from "Components/InteractiveButton";
import { useChat } from "Hooks/useChat";
import moment from "moment"; // Add moment import
let sdk = new MkdSDK();

export default function Inbox() {
  const [uploadLoading, setUploadLoading] = useState(false);
  const { dispatch, state: authState } = React.useContext(AuthContext);
  const { dispatch: globalDispatch } = React.useContext(GlobalContext);
  const [selectedRoomId, setSelectedRoomId] = useState(null);
  const [activeScreen, setActiveScreen] = useState("users");

  const [messages, setMessages] = useState([]);
  const [message, setMessage] = useState("");
  const [rooms, setRooms] = useState([]);
  const [activeRoom, setActiveRoom] = useState({});
  const [unreadCount, setUnreadCount] = useState(0);
  const [openingRoom, setOpeningRoom] = useState(false);
  const [sendLoading, setSendLoading] = useState({ status: "", id: "" });
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const { state, dispatch: authDispatch } = useContext(AuthContext);
  const searchParams = new URLSearchParams(window.location.search);
  const user_id = JSON.parse(localStorage.getItem("user"));
  const [fullScreenImage, setFullScreenImage] = useState(null);
  const fullScreenRef = useRef(null);
  const [showLinkInput, setShowLinkInput] = useState(false);
  const [linkUrl, setLinkUrl] = useState("");
  const [users, setUsers] = useState([]);
  const {
    state: chatState,
    dispatch: chatDispatch,
    getRooms,
    sortOrder: chatSortOrder,
    setSortOrder: setChatSortOrder,
  } = useChat();
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [reloading, setReloading] = useState(false);

  const [attachments, setAttachments] = useState([]); // New state for all types of attachments

  // Add this ref
  const messagesEndRef = useRef(null);

  // Add this function to scroll to the bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };
  const [brands, setBrands] = useState([]);
  const [brandsLoading, setBrandsLoading] = useState(false);
  const [campaigns, setCampaigns] = useState([]);

  const returnCampaign = (id) => {
    const campaign = campaigns.find((c) => c.id === id);
    return campaign || {};
  };
  const returnBrands = (id) => {
    const brand = brands.find((c) => c.id === id);
    return brand || {};
  };

  React.useEffect(() => {
    (async () => {
      try {
        setBrandsLoading(true);
        sdk.setTable("user");
        const result = await sdk.callRestAPI(
          {
            role: "brand",
          },
          "GETALL"
        );
        if (!result.error) {
          setBrands(result.list);
        }
        sdk.setTable("campaign");
        const result2 = await sdk.callRestAPI({}, "GETALL");
        if (!result2.error) {
          setCampaigns(result2.list);
        }
        setBrandsLoading(false);
      } catch (error) {
        setBrandsLoading(false);
        console.log("error", error);
      }
    })();
  }, []);

  React.useEffect(() => {
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "inbox",
      },
    });
  }, []);

  const getCampaignTitle = (room) => {
    try {
      if (campaigns && room?.campaign_id) {
        const campaign = campaigns.find((c) => c.id === room.campaign_id);
        return campaign?.title || room.campaign_title || "Untitled Campaign";
      }
      return room.campaign_title || "Untitled Campaign";
    } catch (error) {
      console.error("Error getting campaign title:", error);
      return "Untitled Campaign";
    }
  };

  async function getMessages(room_id) {
    if (!room_id) return [];

    try {
      const result = await sdk.getChats(room_id);
      if (!result || !result.model) {
        console.log("No messages found for room:", room_id);
        return [];
      }

      const data = result.model;
      return data
        .map((msg) => ({
          ...msg,
          room_id: room_id,
          chat: typeof msg.chat === "string" ? JSON.parse(msg.chat) : msg.chat,
          timestamp: new Date(msg.chat.timestamp).getTime(),
        }))
        .sort((a, b) => {
          // Convert Unix timestamp if needed
          const getTime = (timestamp) => {
            if (typeof timestamp === "number" && timestamp > 1000000000) {
              // Unix timestamp (seconds)
              return timestamp * 1000;
            }
            return new Date(timestamp).getTime();
          };

          const timeA = getTime(a.chat.timestamp);
          const timeB = getTime(b.chat.timestamp);
          return timeA - timeB; // Ascending order (old to new)
        });
    } catch (err) {
      console.error("Error fetching messages:", err);
      tokenExpireError(dispatch, err.message);
      return [];
    }
  }
  const handleSendMessage = (e) => {
    e.preventDefault();
    sendMessage();
  };

  const handleSelectRoom = async (room) => {
    try {
      setActiveScreen("chat");
      setOpeningRoom(true);
      setSelectedRoom(null);
      setMessages([]);

      const currentUrl = new URL(window.location.href);
      const userToOpen = user_id;

      // Safely get campaign title
      const campaignTitle = getCampaignTitle(room);
      // Similar updates for influencer flow...

      if (room) {
        const roomId = room.id;

        // Set user with room_id
        setSelectedRoom({
          ...room,
          room_id: roomId,
        });

        setSelectedRoomId(roomId);
        setActiveRoom(room);

        currentUrl.searchParams.set("room_id", roomId);
        currentUrl.searchParams.set("campaign_id", room.campaign_id);
        currentUrl.searchParams.set("influencer_id", userToOpen);
        window.history.pushState({}, "", currentUrl);

        const messages = await getMessages(Number(roomId));
        if (messages && messages.length > 0) {
          setMessages(messages);
          // Mark all unread messages as read when entering chat
          const unread = messages
            .filter((msg) => {
              try {
                const chatData =
                  typeof msg.chat === "string"
                    ? JSON.parse(msg.chat)
                    : msg.chat;
                // Only mark messages from brands as read
                return msg.unread === 1 && chatData.user_id != user_id;
              } catch (error) {
                console.error("Error parsing message chat:", error);
                return false;
              }
            })
            .map((msg) => msg.id);

          if (unread.length > 0) {
            await markMessagesAsRead(Number(roomId), unread);
          }
        }
      }

      setOpeningRoom(false);
    } catch (error) {
      console.error("Error in handleSelectRoom:", error);
      showToast(globalDispatch, error.message, 3000, "error");
      setOpeningRoom(false);
    }
  };

  async function markMessagesAsRead(room_id, messageIds) {
    try {
      sdk.setTable("chat");
      await Promise.all(
        messageIds.map((id) => sdk.callRestAPI({ id, unread: 0 }, "PUT"))
      );

      // Update messages state
      setMessages((prev) =>
        prev.map((msg) => ({
          ...msg,
          unread: messageIds.includes(msg.id) ? 0 : msg.unread,
        }))
      );

      // Immediately update the unread count for the current user in the list
      chatDispatch({
        type: "SET_ROOMS",
        payload: chatState.rooms.map((room) => {
          if (room.id === room_id) {
            return {
              ...room,
              unreadCount: 0,
            };
          }
          return room;
        }),
      });

      // Update global unread count
      setUnreadCount((prev) => {
        const newCount = Math.max(0, prev - messageIds.length);
        globalDispatch({
          type: "SET_UNREAD_MESSAGES_COUNT",
          payload: newCount,
        });
        return newCount;
      });

      // Refresh the rooms to update all unread counts
      getRooms();
    } catch (err) {
      console.error("Error marking messages as read:", err);
      tokenExpireError(dispatch, err.message);
      showToast(
        globalDispatch,
        "Error marking messages as read",
        5000,
        "error"
      );
    }
  }

  async function sendMessage() {
    if (message === "" && attachments.length === 0) return;
    setSendLoading({ status: 1, id: "" });

    try {
      const messageData = {
        room_id: activeRoom.id,
        user_id: Number(user_id),
        chat: JSON.stringify({
          message: attachments.length > 0 ? attachments : message,
          user_id: Number(user_id),
          other_user_id: activeRoom?.other_user_id,
          is_attachment: attachments.length > 0,
          timestamp: new Date().toISOString(),
        }),
        unread: 0, // Set unread to 0 for sent messages
      };

      // Handle new room creation if needed
      if (activeRoom?.is_new) {
        const createRoomResponse = await sdk.callRawAPI(
          "/v3/api/custom/saba/create_room",
          {
            campaign_id: activeRoom.campaign_id,
            other_user_id: activeRoom.other_user_id,
            message: message,
            is_image: attachments.length > 0 ? 1 : 0,
          },
          "POST"
        );

        if (!createRoomResponse.error) {
          messageData.room_id = createRoomResponse.room;

          // Update states after successful creation
          setSelectedRoomId(createRoomResponse.room);
          setActiveRoom({
            ...activeRoom,
            id: createRoomResponse.room,
            is_new: false,
          });

          // Message is already sent via the create_room API, no need for additional chat table call
        }
      } else {
        // Normal message sending for existing room using custom API
        await sdk.callRawAPI(
          "/v3/api/custom/saba/send_message",
          {
            room_id: activeRoom.id,
            message: attachments.length > 0 ? attachments : message,
            is_image: attachments.length > 0 ? 1 : 0,
          },
          "POST"
        );
      }
      await getRooms();

      // After successful message send, fetch all messages again to ensure proper ordering
      const updatedMessages = await getMessages(messageData.room_id);
      setMessages(updatedMessages);

      // Clear input fields
      setMessage("");
      setAttachments([]);
      setSendLoading({ status: 0, id: "" });

      // Scroll to bottom after sending
      setTimeout(scrollToBottom, 100);
    } catch (err) {
      console.error("Error sending message:", err);
      showToast(globalDispatch, err.message, 4000, "error");
    }
    setSendLoading({ status: 0, id: "" });
  }

  // Update the sortUsersByRecentChat function
  const sortUsersByRecentChat = (users, messages) => {
    // Create a map to store the latest message timestamp for each user/room combination
    const latestMessageMap = new Map();

    // Process all messages to find the latest message for each user
    messages.forEach((msg) => {
      const chatData =
        typeof msg.chat === "string" ? JSON.parse(msg.chat) : msg.chat;
      const roomId = msg.room_id;
      const currentTimestamp = new Date(msg.update_at).getTime();

      // Create a unique key for each room
      const key = roomId;

      // Update the map if this message is more recent
      if (
        !latestMessageMap.has(key) ||
        currentTimestamp > latestMessageMap.get(key)
      ) {
        latestMessageMap.set(key, currentTimestamp);
      }
    });

    // Sort users based on their latest message timestamp
    return [...users].sort((a, b) => {
      const timestampA =
        latestMessageMap.get(a.room_id) || new Date(a.update_at).getTime() || 0;
      const timestampB =
        latestMessageMap.get(b.room_id) || new Date(b.update_at).getTime() || 0;
      return timestampB - timestampA; // Sort in descending order (most recent first)
    });
  };

  // Modify the handleImageUpload function to handle all types of files
  const handleFileUpload = async (e) => {
    setUploadLoading(true);
    const files = Array.from(e.target.files);
    try {
      const uploadPromises = files.map(async (file) => {
        const formData = new FormData();
        formData.append("file", file);
        const result = await sdk.uploadImage(formData); // Assume sdk.uploadFile can handle various file types
        return {
          id: uuidv4(),
          type: file.type.split("/")[0], // 'image', 'application', etc.
          url: result.url,
          name: file.name,
        };
      });

      const uploadedFiles = await Promise.all(uploadPromises);
      setAttachments((prev) => [...prev, ...uploadedFiles]);
      setUploadLoading(false);
    } catch (error) {
      console.error("Error uploading files:", error);
      setUploadLoading(false);
      showToast(globalDispatch, error.message, 5000, "error");
    }
  };

  const handleRemoveAttachment = (id) => {
    setAttachments((prev) => prev.filter((item) => item.id !== id));
  };

  const handleOpenPreviousUser = () => {
    const index = chatState.rooms.findIndex(
      (room) =>
        room.other_user_id === selectedRoom?.other_user_id &&
        room.campaign_id === selectedRoom?.campaign_id
    );
    if (index > 0) {
      handleSelectRoom(chatState.rooms[index - 1]);
    }
  };

  const handleOpenNextUser = () => {
    const index = chatState.rooms.findIndex(
      (room) =>
        room.other_user_id === selectedRoom?.other_user_id &&
        room.campaign_id === selectedRoom?.campaign_id
    );
    if (index < chatState.rooms.length - 1) {
      handleSelectRoom(chatState.rooms[index + 1]);
    }
  };

  // Update the polling useEffect
  useEffect(() => {
    const pollMessages = async () => {
      try {
        const poll = await sdk.startPolling(localStorage.getItem("user"));
        if (poll.message && selectedRoomId) {
          // Get current messages
          const currentMessages = new Set(messages.map((msg) => msg.id));

          // Fetch latest messages
          const updatedMessages = await getMessages(selectedRoomId);

          // Only update if there are new messages
          const hasNewMessages = updatedMessages.some(
            (msg) => !currentMessages.has(msg.id)
          );

          if (hasNewMessages) {
            setMessages(updatedMessages);

            // Mark new messages as read if chat is open
            const newUnreadMessages = updatedMessages
              .filter(
                (msg) =>
                  // Only count as unread if:
                  // 1. The message is new (not in current messages)
                  // 2. The message has unread status
                  // 3. The message was NOT sent by the current logged-in user
                  !currentMessages.has(msg.id) &&
                  msg.unread === 1 &&
                  msg.chat.user_id !== user_id
              )
              .map((msg) => msg.id);

            if (newUnreadMessages.length > 0) {
              await markMessagesAsRead(selectedRoomId, newUnreadMessages);
            }

            scrollToBottom();
          }
        }
      } catch (error) {
        console.error("Polling error:", error);
      }
    };

    let pollInterval;
    if (selectedRoomId) {
      // Initial fetch
      getMessages(selectedRoomId).then((msgs) => {
        setMessages(msgs);
        // Mark all unread messages as read when chat is opened
        const unreadMessages = msgs
          .filter((msg) => {
            // Only count as unread if:
            // 1. The message has unread status AND
            // 2. The message was NOT sent by the current logged-in user
            return msg.unread === 1 && msg.chat.user_id !== user_id;
          })
          .map((msg) => msg.id);

        if (unreadMessages.length > 0) {
          markMessagesAsRead(selectedRoomId, unreadMessages);
        }
        scrollToBottom();
      });

      // Start polling
      pollInterval = setInterval(pollMessages, 5000);
    }

    return () => {
      if (pollInterval) {
        clearInterval(pollInterval);
      }
    };
  }, [selectedRoomId]);
  const openFullScreenImage = (imageUrl) => {
    setFullScreenImage(imageUrl);
  };

  const closeFullScreenImage = () => {
    setFullScreenImage(null);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        fullScreenRef.current &&
        !fullScreenRef.current.contains(event.target)
      ) {
        closeFullScreenImage();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleLinkSubmit = (e) => {
    e.preventDefault();
    if (linkUrl) {
      setMessage(linkUrl);
      setLinkUrl("");
      setShowLinkInput(false);
      sendMessage();
    }
  };

  // Add this effect to scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Add cleanup effect when component unmounts
  useEffect(() => {
    return () => {
      setSelectedRoom(null);
      setActiveRoom({});
      setMessages([]);
      setSelectedRoomId(null);
    };
  }, []);

  // Add effect to persist selected user when URL changes
  useEffect(() => {
    const room_id = searchParams.get("room_id");
    const campaign_id = searchParams.get("campaign_id");
    const influencer_id = searchParams.get("influencer_id");

    if (room_id && campaign_id && influencer_id && chatState.rooms.length > 0) {
      const room = chatState.rooms.find(
        (r) => r.user_id == Number(influencer_id)
      );
      console.log("room", room);
      if (room) {
        // setSelectedUser(room);
        handleSelectRoom(room);
      }
    }
  }, [chatState.rooms.length]);

  // Add this effect near your other useEffects
  useEffect(() => {
    if (selectedRoomId) {
      getMessages(selectedRoomId).then((messages) => {
        setMessages(messages);
        scrollToBottom();
      });
    }
  }, [selectedRoomId]);
  console.log("chatState", chatState);
  console.log("selectedRoom", selectedRoom);

  const handleReload = async () => {
    setReloading(true);
    await getRooms();
    setReloading(false);
  };

  return (
    <>
      {activeScreen === "chat" && (
        <button
          onClick={() => {
            setActiveScreen("users");
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.delete("room_id");
            newUrl.searchParams.delete("campaign_id");
            newUrl.searchParams.delete("influencer_id");
            window.history.pushState({}, "", newUrl);
          }}
          className="mb-4 flex items-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:bg-gray-50 lg:hidden"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="1em"
            height="1em"
            viewBox="0 0 16 16"
            className="text-gray-500"
          >
            <path
              fill="currentColor"
              fillRule="evenodd"
              d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8"
            />
          </svg>
          <span>Back to Conversations</span>
        </button>
      )}
      <div className="flex h-[85vh] overflow-hidden rounded-3xl border border-gray-200 bg-white shadow-sm md:h-[80vh]">
        {/* Left sidebar */}
        <div
          className={`${
            activeScreen === "users" ? "block" : "hidden lg:block"
          } w-full overflow-y-auto border-r border-gray-200 bg-gray-50 lg:max-w-[350px]`}
        >
          <div className="sticky top-0 z-10 border-b border-gray-200 bg-gray-50 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <button
                  onClick={handleReload}
                  title="Reload"
                  className="rounded-lg p-2 transition-colors duration-200 hover:bg-gray-200"
                >
                  <svg
                    className={`${reloading ? "animate-spin" : ""}`}
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M10 8H5V3"
                      stroke="#373A4B"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M5.29102 16.3571C6.22284 17.792 7.59014 18.8904 9.19218 19.4909C10.7942 20.0915 12.5466 20.1627 14.1921 19.6939C15.8375 19.2252 17.2893 18.2413 18.3344 16.8867C19.3795 15.5321 19.9628 13.8781 19.9986 12.1675C20.0345 10.457 19.521 8.78004 18.5335 7.38284C17.5461 5.98564 16.1367 4.94181 14.5123 4.4046C12.8879 3.86738 11.1341 3.86509 9.50833 4.39805C7.88255 4.93101 6.47045 5.97114 5.47936 7.36575"
                      stroke="#373A4B"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </button>
              </div>
              <div>
                <p
                  style={{ color: "rgba(32, 40, 48, 0.70)" }}
                  className="font-inter text-[11px] font-medium"
                >
                  Sort by:{" "}
                  <button
                    className="font-inter text-[11px] font-bold text-[#373A4B]"
                    onClick={async () => {
                      const newSortOrder =
                        chatSortOrder === "desc" ? "asc" : "desc";
                      setChatSortOrder(newSortOrder);
                      await getRooms(true);
                    }}
                  >
                    Date {chatSortOrder === "desc" ? "↓" : "↑"}
                  </button>
                </p>
              </div>
            </div>
          </div>
          {chatState?.rooms?.length > 0 ? (
            chatState?.rooms.map((room, index) => (
              <div
                key={index}
                className={`flex cursor-pointer items-center p-4 transition-colors duration-200 hover:bg-gray-100 ${
                  selectedRoom?.other_user_id === room?.other_user_id &&
                  selectedRoom?.campaign_id === room?.campaign_id
                    ? "border-l-brandPrimary border-l-4 bg-gray-100"
                    : ""
                }`}
                onClick={() => handleSelectRoom(room)}
              >
                <div className="mr-3 flex h-12 w-12 items-center justify-center rounded-full bg-gray-200 font-medium text-gray-600">
                  {room?.photo ? (
                    <img
                      src={room?.photo}
                      className="h-full w-full rounded-full object-cover"
                      alt="avatar"
                    />
                  ) : (
                    <span className="text-lg">
                      {room?.first_name?.[0]?.toUpperCase() || "P"}
                      {room?.last_name?.[0]?.toUpperCase() || "T"}
                    </span>
                  )}
                </div>
                <div className="flex min-w-0 grow flex-col">
                  <div className="flex items-center justify-between">
                    <span className="font-inter truncate text-[14px] font-medium text-[#373A4B]">
                      {!room?.first_name ? "--" : room.first_name}{" "}
                      {!room?.last_name ? "--" : room.last_name}
                    </span>
                    <span className="font-inter ml-2 text-[10px] font-medium text-[#373A4B]">
                      {room?.update_at
                        ? moment(room?.update_at).format("DD/MM/YYYY")
                        : "--"}
                    </span>
                  </div>
                  {room?.unreadCount > 0 && (
                    <div
                      style={{ color: "rgba(32, 40, 48, 0.70)" }}
                      className="font-inter mt-1 flex items-center gap-1 text-[10px] font-medium text-[#373A4B]"
                    >
                      <span className="">{room?.unreadCount}</span>
                      <span className="">new messages</span>
                    </div>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="flex h-full items-center justify-center p-8">
              <p className="text-center text-gray-500">
                No brands have sent you messages yet.
              </p>
            </div>
          )}
        </div>

        {/* Chat area */}
        {openingRoom ? (
          <div className="flex h-full w-full items-center justify-center">
            <LoadingIndicator />
          </div>
        ) : (
          <div
            className={`${
              activeScreen === "chat" ? "flex" : "hidden lg:flex"
            } w-full flex-1 flex-col bg-white`}
          >
            {selectedRoom ? (
              <>
                <div className="sticky top-0 z-10 border-b border-gray-200 bg-white p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="relative h-12 w-12 overflow-hidden rounded-full">
                        {selectedRoom?.photo ? (
                          <img
                            src={selectedRoom?.photo}
                            alt={`${selectedRoom?.first_name} ${selectedRoom?.last_name}`}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="flex h-full w-full items-center justify-center bg-gray-200 text-lg font-medium text-gray-600">
                            {selectedRoom?.first_name?.[0]}
                            {selectedRoom?.last_name?.[0]}
                          </div>
                        )}
                      </div>
                      <div>
                        <h2 className="text-lg font-semibold">
                          {selectedRoom?.first_name} {selectedRoom?.last_name}
                        </h2>
                        <div className="flex flex-col gap-0.5">
                          {selectedRoom?.email && (
                            <p className="text-sm text-gray-600">
                              {selectedRoom?.email}
                            </p>
                          )}
                          {selectedRoom?.campaign_id && (
                            <p className="text-brandPrimary text-sm font-medium">
                              {getCampaignTitle(selectedRoom)}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center gap-3">
                        <button
                          title="Previous User"
                          disabled={
                            chatState.rooms.findIndex(
                              (room) =>
                                room.id === selectedRoom?.id &&
                                room.campaign_id === selectedRoom?.campaign_id
                            ) === 0
                          }
                          onClick={handleOpenPreviousUser}
                          className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white transition-colors hover:bg-gray-50 disabled:opacity-50 xl:h-12 xl:w-12"
                        >
                          <svg
                            width="8"
                            height="14"
                            viewBox="0 0 8 14"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M6.86667 12.7667L1 6.88333L6.86667 1"
                              stroke="#292D32"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </button>
                        <button
                          title="Next User"
                          disabled={
                            chatState.rooms.findIndex(
                              (room) =>
                                room.id === selectedRoom?.id &&
                                room.campaign_id === selectedRoom?.campaign_id
                            ) ===
                            chatState.rooms.length - 1
                          }
                          onClick={handleOpenNextUser}
                          className="flex h-8 w-8 items-center justify-center rounded-full border border-gray-200 bg-white transition-colors hover:bg-gray-50 disabled:opacity-50 xl:h-12 xl:w-12"
                        >
                          <svg
                            width="8"
                            height="14"
                            viewBox="0 0 8 14"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M1.13333 12.7667L7 6.88333L1.13333 1"
                              stroke="#292D32"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className={`${
                    Array.isArray(message?.chat?.message)
                      ? "grid grid-cols-2 gap-3 p-4 sm:grid-cols-3 md:grid-cols-4"
                      : "flex-1 space-y-4 overflow-y-auto p-4"
                  }`}
                >
                  {messages?.length > 0 &&
                    messages?.map((message, index) => (
                      <div
                        key={index}
                        className={`flex ${
                          message?.user_id == Number(user_id)
                            ? "justify-end"
                            : "justify-start"
                        }`}
                      >
                        <div
                          className={`max-w-[70%] rounded-2xl p-4 ${
                            message?.user_id == Number(user_id)
                              ? `${
                                  Array.isArray(message?.chat?.message)
                                    ? "bg-transparent"
                                    : "bg-brandPrimary text-white"
                                }`
                              : "bg-gray-100 text-gray-800"
                          }`}
                        >
                          <div className="flex items-end gap-2">
                            {typeof message?.chat?.message === "string" ? (
                              message.chat.is_link ? (
                                <a
                                  href={message.chat.message}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-300 underline"
                                >
                                  {message.chat.message}
                                </a>
                              ) : (
                                <span>{message.chat.message}</span>
                              )
                            ) : Array.isArray(message?.chat?.message) ? (
                              message.chat.message.map((item, imgIndex) => (
                                <img
                                  key={imgIndex}
                                  src={item?.url}
                                  className="h-20 w-20 cursor-pointer rounded-xl object-cover transition-transform hover:scale-105"
                                  alt="Chat image"
                                  onClick={() => openFullScreenImage(item?.url)}
                                />
                              ))
                            ) : null}
                            <span className="ml-2">
                              {sendLoading?.id == message.create_at &&
                              sendLoading?.status == 1 ? (
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="0.8em"
                                  height="0.8em"
                                  viewBox="0 0 24 24"
                                  className="animate-spin"
                                >
                                  <g
                                    fill="none"
                                    stroke="currentColor"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="1.5"
                                  >
                                    <path d="M12 6v6l4 2" />
                                    <path d="M3 9.4c0-2.24 0-3.36.436-4.216a4 4 0 0 1 1.748-1.748C6.04 3 7.16 3 9.4 3h5.2c2.24 0 3.36 0 4.216.436a4 4 0 0 1 1.748 1.748C21 6.04 21 7.16 21 9.4v5.2c0 2.24 0 3.36-.436 4.216a4 4 0 0 1-1.748 1.748C17.96 21 16.84 21 14.6 21H9.4c-2.24 0-3.36 0-4.216-.436a4 4 0 0 1-1.748-1.748C3 17.96 3 16.84 3 14.6z" />
                                  </g>
                                </svg>
                              ) : (
                                <span
                                  className={`${
                                    message?.user_id == Number(user_id)
                                      ? "block"
                                      : "hidden"
                                  }`}
                                >
                                  {message.unread === 0 ? (
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="1em"
                                      height="1em"
                                      viewBox="0 0 24 24"
                                      className="text-blue-500"
                                    >
                                      <path
                                        fill="currentColor"
                                        d="m2.5 8.86l1.5-1.6l2.5 2.67l6.5-6.93l1.5 1.6l-8 8.53zm7.5 6.67l-2.5-2.67l-1.5 1.6l4 4.27l8-8.53l-1.5-1.6z"
                                      />
                                    </svg>
                                  ) : (
                                    <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="0.8em"
                                      height="0.8em"
                                      viewBox="0 0 15 15"
                                    >
                                      <path
                                        fill="currentColor"
                                        fillRule="evenodd"
                                        d="M14.707 3L5.5 12.207L.293 7L1 6.293l4.5 4.5l8.5-8.5z"
                                        clipRule="evenodd"
                                      />
                                    </svg>
                                  )}
                                </span>
                              )}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  <div ref={messagesEndRef} />
                </div>
                <form
                  onSubmit={handleSendMessage}
                  className="sticky bottom-0 border-t border-gray-200 bg-white p-4"
                >
                  <div className="flex flex-col gap-3 rounded-2xl border border-gray-200 bg-gray-50">
                    <textarea
                      type="text"
                      value={message}
                      rows={3}
                      cols={50}
                      onChange={(e) => setMessage(e.target.value)}
                      className={`${
                        attachments.length > 0 ? "hidden" : "block"
                      } flex-1 rounded-t-2xl border-0 bg-transparent px-4 py-2 text-gray-900 placeholder-gray-500 focus:border-0 focus:outline-none focus:ring-0`}
                      placeholder="Type a message..."
                    />
                    <div>
                      <div className="grid grid-cols-2 gap-3 px-2 pt-3 sm:grid-cols-3 md:grid-cols-4">
                        {attachments.map((item) => (
                          <div
                            key={item.id}
                            className="relative h-16 w-16 overflow-hidden rounded-xl"
                          >
                            <button
                              onClick={() => handleRemoveAttachment(item.id)}
                              className="absolute right-0 top-0 z-10 rounded-bl-lg bg-black/50 p-1 text-white transition-colors hover:bg-black/70"
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1.5em"
                                height="1.5em"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  fill="currentColor"
                                  d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10s10-4.47 10-10S17.53 2 12 2m4.3 14.3a.996.996 0 0 1-1.41 0L12 13.41L9.11 16.3a.996.996 0 1 1-1.41-1.41L10.59 12L7.7 9.11A.996.996 0 1 1 9.11 7.7L12 10.59l2.89-2.89a.996.996 0 1 1 1.41 1.41L13.41 12l2.89 2.89c.38.38.38 1.02 0 1.41"
                                />
                              </svg>
                            </button>
                            {item.type === "image" ? (
                              <img
                                src={item.url}
                                className="h-full w-full object-cover"
                                alt="attachment"
                              />
                            ) : (
                              <div className="flex h-full w-full items-center justify-center bg-gray-200">
                                <span className="text-xs">{item.name}</span>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                      <div className="flex items-center justify-between p-2">
                        <div className="flex items-center gap-3">
                          <button
                            title="Attach file"
                            className="relative flex cursor-pointer items-center rounded-lg p-2 text-gray-600 transition-colors hover:bg-gray-200"
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M12.2009 11.8002L10.7908 13.2102C10.0108 13.9902 10.0108 15.2602 10.7908 16.0402C11.5708 16.8202 12.8408 16.8202 13.6208 16.0402L15.8409 13.8202C17.4009 12.2602 17.4009 9.73023 15.8409 8.16023C14.2809 6.60023 11.7508 6.60023 10.1808 8.16023L7.76086 10.5802C6.42086 11.9202 6.42086 14.0902 7.76086 15.4302"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                            <input
                              type="file"
                              multiple
                              accept="*/*"
                              title="Attach file"
                              className="absolute left-0 top-0 z-10 h-full w-full cursor-pointer opacity-0"
                              onChange={handleFileUpload}
                            />
                          </button>
                          <button
                            title="Link"
                            className="flex items-center rounded-lg p-2 text-gray-600 transition-colors hover:bg-gray-200"
                            onClick={() => setShowLinkInput(!showLinkInput)}
                          >
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M14.9902 17.5H16.5002C19.5202 17.5 22.0002 15.03 22.0002 12C22.0002 8.98 19.5302 6.5 16.5002 6.5H14.9902"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M9 6.5H7.5C4.47 6.5 2 8.97 2 12C2 15.02 4.47 17.5 7.5 17.5H9"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M8 12H16"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </button>
                        </div>
                        <InteractiveButton
                          loading={sendLoading.status === 1}
                          type="submit"
                          className="flex items-center gap-2 rounded-full bg-black px-6 py-2.5 text-white transition-colors hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2"
                        >
                          <span>
                            <svg
                              width="20"
                              height="20"
                              viewBox="0 0 20 20"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M5.2825 4.20297L13.7725 1.37297C17.5825 0.102967 19.6525 2.18297 18.3925 5.99297L15.5625 14.483C13.6625 20.193 10.5425 20.193 8.6425 14.483L7.8025 11.963L5.2825 11.123C-0.4275 9.22297 -0.4275 6.11297 5.2825 4.20297Z"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M7.99219 11.5314L11.5722 7.94141"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </span>
                          <span>Send</span>
                        </InteractiveButton>
                      </div>
                    </div>
                  </div>
                </form>
                {showLinkInput && (
                  <form
                    onSubmit={handleLinkSubmit}
                    className="flex items-center gap-2 border-t border-gray-200 bg-white p-4"
                  >
                    <input
                      type="url"
                      value={linkUrl}
                      onChange={(e) => setLinkUrl(e.target.value)}
                      placeholder="Enter link URL"
                      className="focus:border-brandPrimary focus:ring-brandPrimary flex-1 rounded-xl border border-gray-200 px-4 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-1"
                      required
                    />
                    <button
                      type="submit"
                      className="rounded-xl bg-black px-4 py-2 text-white transition-colors hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-black focus:ring-offset-2"
                    >
                      Send Link
                    </button>
                  </form>
                )}
              </>
            ) : (
              <div className="flex flex-1 items-center justify-center">
                {openingRoom ? (
                  <div className="flex items-center justify-center">
                    <LoadingIndicator />
                  </div>
                ) : (
                  <p className="text-center text-gray-500">
                    {!chatState.rooms?.length > 0
                      ? "Your inbox is empty"
                      : "Select a user to start chatting"}
                  </p>
                )}
              </div>
            )}
          </div>
        )}
        {fullScreenImage && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/75 backdrop-blur-sm">
            <div
              ref={fullScreenRef}
              className="relative max-h-[90vh] max-w-[90vw]"
            >
              <img
                src={fullScreenImage}
                alt="Full screen"
                className="max-h-[90vh] max-w-[90vw] rounded-2xl object-contain"
              />
              <button
                onClick={closeFullScreenImage}
                className="absolute right-4 top-4 rounded-full bg-white/10 p-2 text-white backdrop-blur-sm transition-colors hover:bg-white/20"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>
          </div>
        )}
        {uploadLoading && (
          <div className="fixed inset-0 z-[999] flex items-center justify-center bg-black/30 backdrop-blur-sm">
            <div className="flex items-center justify-center space-x-2 rounded-lg bg-white/90 px-6 py-4 shadow-lg backdrop-blur-sm">
              <div className="h-4 w-4 animate-bounce rounded-full bg-black"></div>
              <div
                className="h-4 w-4 animate-bounce rounded-full bg-black"
                style={{ animationDelay: "0.1s" }}
              ></div>
              <div
                className="h-4 w-4 animate-bounce rounded-full bg-black"
                style={{ animationDelay: "0.2s" }}
              ></div>
              <span className="ml-2 text-sm font-medium text-gray-700">
                Uploading...
              </span>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
