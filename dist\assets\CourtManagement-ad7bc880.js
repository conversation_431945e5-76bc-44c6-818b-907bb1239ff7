import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as s,b as he}from"./vendor-851db8c1.js";import{f as Ye,M as xe,aB as ft,R as Te,G as Le,h as Oe,aC as xt,d as B,D as qe,ah as He,l as ce,c as oe,b as ue}from"./index-08a5dc5b.js";import{u as pt}from"./react-hook-form-687afde5.js";import{S as We}from"./react-select-c8303602.js";import{B as Fe}from"./BottomDrawer-eee99403.js";import{H as gt}from"./HistoryComponent-999cafaf.js";import{T as Ke}from"./TimeSlotGrid-640f0461.js";import{P as yt}from"./PencilIcon-35185602.js";import{T as wt}from"./TrashIcon-7d213648.js";import{b as Se}from"./@headlessui/react-a5400090.js";function vt({title:o,titleId:l,...r},n){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":l},r),o?s.createElement("title",{id:l},o):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"}))}const jt=s.forwardRef(vt),bt=jt;function Ie({isOpen:o,onClose:l,onConfirm:r,eventCount:n,affectedReservations:u=[],isSubmitting:v,type:_="hours"}){if(!o)return null;const S=N=>{if(!N)return"";const[H,G]=N.split(":"),L=parseInt(H),h=L>=12?"PM":"AM";return`${L%12||12}:${G} ${h}`},P=N=>N?new Date(N).toLocaleDateString("en-US",{weekday:"short",year:"numeric",month:"short",day:"numeric"}):"";return e.jsxs("div",{className:"fixed inset-0 z-[99999] flex items-center justify-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-2xl rounded-3xl bg-white p-6",children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Confirm Changes"}),e.jsx("p",{className:"mb-4 text-gray-600",children:`Changing ${_==="court"?"this court":`these ${_}`} will delete ${n} existing reservation${n!==1?"s":""}.`}),u.length>0&&e.jsxs("div",{className:"mb-6 max-h-64 overflow-y-auto rounded-lg border border-gray-200",children:[e.jsx("div",{className:"bg-gray-50 px-4 py-2",children:e.jsx("h3",{className:"text-sm font-medium text-gray-700",children:"Affected Reservations:"})}),e.jsx("div",{className:"divide-y divide-gray-200",children:u.map((N,H)=>e.jsx("div",{className:"px-4 py-3 text-sm",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"font-medium text-gray-900",children:N.sport_name}),e.jsx("p",{className:"text-gray-600",children:N.first_name&&N.last_name?`${N.first_name} ${N.last_name}`:N.email})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-gray-900",children:P(N.date)}),e.jsxs("p",{className:"text-gray-600",children:[S(N.start_time)," -"," ",S(N.end_time)]})]})]})},H))})]}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Are you sure you want to continue?"}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:l,className:"rounded-lg border border-gray-200 px-6 py-2",disabled:v,children:"Cancel"}),e.jsx(Ye,{onClick:r,className:"rounded-lg bg-primaryBlue px-6 py-2 text-white",loading:v,children:"Confirm"})]})]})]})}const Xe=he.forwardRef(({onSubmit:o=()=>{},initialData:l={},club:r={},isOpen:n=!1,onClose:u=()=>{},title:v="Edit club settings",onPrimaryAction:_=()=>{},submitting:S=!1},P)=>{const N=new xe,{handleSubmit:H}=pt(),G=c=>{if(!c)return c;const[g,t]=c.split(":").map(Number),a=g*60+t,m=Math.round(a/30)*30,E=Math.floor(m/60)%24,C=m%60;return`${E.toString().padStart(2,"0")}:${C.toString().padStart(2,"0")}:00`},[L,h]=s.useState([{from:"",until:""}]),[J,Y]=s.useState(!1),[q,$]=s.useState(0),[A,b]=s.useState([]),[U,W]=s.useState(!1),[d,j]=s.useState(!1),[i,p]=s.useState(null);he.useEffect(()=>{if(console.log("Club data or modal state changed:",{clubTimes:r==null?void 0:r.times,isOpen:n,clubExists:!!r}),r!=null&&r.times)try{const c=JSON.parse(r.times);if(console.log("Parsed times from club:",c),c.length>0){console.log("Club data changed, updating time slots:",c);const g=c.map(t=>({from:G(t.from),until:G(t.until)}));console.log("Rounded times to 30-minute intervals:",g),h(g)}else console.log("Parsed times array is empty, setting default slot"),h([{from:"",until:""}])}catch(c){console.error("Error parsing club times:",c),h([{from:"",until:""}])}else console.log("No club times data, setting default slot"),h([{from:"",until:""}])},[r==null?void 0:r.times,n]),he.useEffect(()=>{console.log("Current time slots:",L)},[L]);const y=(c,g,t)=>{h(a=>{const m=[...a];return m[c]={...m[c],[g]:t?t.value:""},m})},w=async c=>{try{j(!0);const g=await N.callRawAPI("/v3/api/custom/courtmatchup/club/courts/affected-reservations",{times:c.times,days_off:c.days_off},"POST");return g&&!g.error&&g.total_affected>0?($(g.total_affected),b(g.affected_reservations||[]),p(c),Y(!0),!0):!1}catch(g){return console.error("Error checking for affected events:",g),!1}finally{j(!1)}},M=async()=>{W(!0);try{const c={...i,delete_affected_events:!0};await o(c),Y(!1),p(null)}catch(c){console.error("Error saving changes:",c)}finally{W(!1)}},Z=()=>{Y(!1),p(null),$(0),b([])};he.useImperativeHandle(P,()=>({submit:async()=>new Promise(c=>{H(async g=>{const t=Object.entries(O.daysOff).filter(([C,K])=>K).map(([C])=>C),a=O.dailyBreaks?[{start:O.breakStartTime,end:O.breakEndTime}]:[],m={times:L,daily_breaks:a,days_off:t};await w(m)||await o(m),c(m)})()})}));const[O,V]=s.useState({times:L,daysOff:{Monday:!1,Tuesday:!1,Wednesday:!1,Thursday:!1,Friday:!1,Saturday:!1,Sunday:!1,...r!=null&&r.days_off?JSON.parse(r.days_off).reduce((c,g)=>({...c,[g]:!0}),{}):{}},dailyBreaks:!1,breakStartTime:"09:00:00",breakEndTime:"10:00:00",...l});he.useEffect(()=>{if(r&&(V(c=>({...c,daysOff:{Monday:!1,Tuesday:!1,Wednesday:!1,Thursday:!1,Friday:!1,Saturday:!1,Sunday:!1,...r!=null&&r.days_off?JSON.parse(r.days_off).reduce((g,t)=>({...g,[t]:!0}),{}):{}}})),r!=null&&r.daily_breaks))try{const c=JSON.parse(r.daily_breaks);c&&c.length>0&&V(g=>({...g,dailyBreaks:!0,breakStartTime:c[0].start||g.breakStartTime,breakEndTime:c[0].end||g.breakEndTime}))}catch(c){console.error("Error parsing daily breaks:",c)}},[r]);const z=c=>{V(g=>({...g,daysOff:{...g.daysOff,[c]:!g.daysOff[c]}}))},T=ft().map(c=>({...c,value:c.value+":00"}));console.log("Time options generated:",T.length,"options"),console.log("First few time options:",T.slice(0,5));const se=()=>{h(c=>[...c,{from:"",until:""}])},ae=c=>{h(g=>g.filter((t,a)=>a!==c))};return e.jsxs(e.Fragment,{children:[e.jsx(Te,{isOpen:n,onClose:u,title:v,onPrimaryAction:_,submitting:S||d,children:e.jsxs("div",{className:"flex flex-col gap-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"General opening hours"}),(L.length>0?L:[{from:"",until:""}]).map((c,g)=>e.jsxs("div",{className:"mb-5 flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(We,{classNamePrefix:"select",options:T,components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null},value:(()=>{const t=T.find(a=>a.value===c.from);return console.log(`Finding option for from=${c.from}:`,t),t||null})(),onChange:t=>y(g,"from",t),placeholder:"Select time",isClearable:!0})}),e.jsx("div",{className:"flex-1",children:e.jsx(We,{classNamePrefix:"select",components:{DropdownIndicator:()=>null,IndicatorSeparator:()=>null},options:c.from?T.filter(t=>t.value>c.from):T,value:(()=>{const t=T.find(a=>a.value===c.until);return console.log(`Finding option for until=${c.until}:`,t),t||null})(),onChange:t=>y(g,"until",t),placeholder:"Select time",isDisabled:!c.from,isClearable:!0})})]}),L.length>1&&e.jsx("button",{onClick:()=>ae(g),className:" text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]},g))]}),e.jsx("button",{onClick:se,className:"mb-5 text-primaryBlue underline hover:text-primaryBlue/80",children:"+Add another time slot"}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"Days off"}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:Object.keys(O.daysOff).map(c=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:O.daysOff[c],onChange:()=>z(c),className:"h-4 w-4 rounded border-gray-300"}),e.jsx("span",{className:"text-sm text-gray-600",children:c})]},c))})]})]})}),e.jsx(Ie,{isOpen:J,onClose:Z,onConfirm:M,eventCount:q,affectedReservations:A,isSubmitting:U,type:"hours"})]})});Xe.displayName="ClubSettingsEditForm";const Nt=Xe;function Ct({initialData:o={},onSubmit:l,setExceptionName:r,exceptionName:n}){return e.jsx("div",{className:"flex flex-col gap-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"mb-4 text-sm font-medium text-gray-500",children:"Exception details"}),e.jsxs("div",{className:"mt-1",children:[e.jsx("label",{className:"mb-2 block text-sm text-gray-600",children:"Name"}),e.jsx("input",{type:"text",value:n,onChange:u=>r(u.target.value),placeholder:"Enter exception name",className:"w-full rounded-xl border border-gray-200 px-3 py-2 text-sm shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue"})]})]})})}const St=new xe;function Ue({onSubmit:o,initialData:l,onClose:r,isOpen:n,isEdit:u,club:v,sports:_=[],courts:S,title:P,primaryButtonText:N,submitting:H,showFooter:G=!1,onPrimaryAction:L}){var ne,pe,ge;const[h,J]=s.useState({name:(l==null?void 0:l.name)||"",sport_id:Number(l==null?void 0:l.sport_id)||null,type:(l==null?void 0:l.type)||"",sub_type:(l==null?void 0:l.sub_type)||""}),[Y,q]=s.useState(!1),[$,A]=s.useState(!1),[b,U]=s.useState(!1),[W,d]=s.useState(null),[j,i]=s.useState(null),[p,y]=s.useState(!1),[w,M]=s.useState(0),[Z,O]=s.useState(null),{dispatch:V}=s.useContext(Le),z=()=>{J({name:"",sport_id:null,type:"",sub_type:""})};s.useEffect(()=>{l?J({name:l.name||"",sport_id:Number(l.sport_id)||null,type:l.type||"",sub_type:l.sub_type||""}):u||z()},[l,u]);const T=s.useCallback(x=>{const{name:R,value:X}=x.target;J(le=>{const ie=R==="sport_id"?Number(X):X;if(le[R]===ie)return le;const ee={...le};return ee[R]=ie,R==="sport_id"&&(console.log("handleChange - resetting type and sub_type due to sport change"),ee.type="",ee.sub_type=""),R==="type"&&(console.log("handleChange - resetting sub_type due to type change"),ee.sub_type=""),ee})},[]),se=async()=>{U(!1),A(!0);try{console.log("handleSportChangeConfirm - formData:",h),console.log("handleSportChangeConfirm - tempSportId:",W);const x={...h,sport_id:W,sub_type:h.sub_type||""},R={court_id:l.id,...x,sport_change_option:j};if(console.log("handleSportChangeConfirm - submitData:",R),await c(R)){A(!1);return}await o(R),u||z()}catch(x){console.error(x)}finally{A(!1)}},ae=()=>{d(null),i(null),U(!1)},c=async x=>{try{await St.callRawAPI("/v3/api/custom/courtmatchup/club/check-affected-events",{court_id:x.court_id,sport_id:x.sport_id,type:x.type||"",sub_type:x.sub_type||""},"POST");const R={affected_events_count:Math.floor(Math.random()*5)};return R.affected_events_count>0?(M(R.affected_events_count),O(x),y(!0),!0):!1}catch(R){return console.error("Error checking for affected events:",R),!1}},g=async()=>{A(!0);try{const x={...Z,sub_type:Z.sub_type||""};await o(x),y(!1),u||z()}catch(x){console.error("Error saving changes:",x)}finally{A(!1)}},t=async x=>{if(x.preventDefault(),E.length>0&&!h.type){B(V,"Please select a court type",3e3,"error");return}if(C.length>0&&!h.sub_type){B(V,"Please select a surface type",3e3,"error");return}if(u&&(l!=null&&l.sport_id)&&h.sport_id!==l.sport_id){console.log("handleFormSubmit - sport change detected"),console.log("handleFormSubmit - formData:",h),console.log("handleFormSubmit - initialData.sport_id:",l.sport_id),d(h.sport_id),U(!0);return}A(!0);try{const R={...h,sub_type:h.sub_type||""},X=u?{court_id:l.id,...R}:R;if(u&&await c(X)){A(!1);return}await o(X),u||z()}catch(R){console.error(R)}finally{A(!1)}},a=W||h.sport_id,m=_.find(x=>x.id===a),E=((ne=m==null?void 0:m.sport_types)==null?void 0:ne.filter(x=>x.type))||[],C=((ge=(pe=m==null?void 0:m.sport_types)==null?void 0:pe.find(x=>x.type===h.type))==null?void 0:ge.subtype)||[],K=()=>{var ye,de;const[x,R]=s.useState(""),X=f=>{R(f.target.value)},le=()=>{i(Number(x)),se()},ie=((ye=_.find(f=>f.id===(l==null?void 0:l.sport_id)))==null?void 0:ye.name)||"previous sport",ee=((de=_.find(f=>f.id===W))==null?void 0:de.name)||"new sport";return e.jsxs("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center",children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-md rounded-lg bg-white p-6",children:[e.jsx("h3",{className:"mb-4 text-lg font-medium",children:"Change Sport"}),e.jsxs("p",{className:"mb-4 text-gray-600",children:["You are changing the sport for this space from"," ",e.jsx("strong",{children:ie})," to ",e.jsx("strong",{children:ee}),". However, any events for this space will still be listed under the previous sport, including in the daily scheduler."]}),e.jsx("p",{className:"mb-4 text-gray-600",children:"Would you like to:"}),e.jsxs("div",{className:"mb-6 space-y-3",children:[e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"1",checked:x==="1",onChange:X,className:"mt-1 h-4 w-4"}),e.jsxs("span",{children:["A: Keep these events (events will remain under ",ie,")"]})]}),e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"2",checked:x==="2",onChange:X,className:"mt-1 h-4 w-4"}),e.jsx("span",{children:"B: Delete these events"})]}),e.jsxs("label",{className:"flex items-start gap-2",children:[e.jsx("input",{type:"radio",name:"sportChangeOption",value:"3",checked:x==="3",onChange:X,className:"mt-1 h-4 w-4"}),e.jsxs("span",{children:["C: Change the sport listed for these events to ",ee]})]})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:ae,className:"rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50",children:"Cancel"}),e.jsx("button",{onClick:le,disabled:!x,className:`rounded-lg px-4 py-2 text-white ${x?"bg-primaryBlue hover:bg-blue-700":"cursor-not-allowed bg-gray-400"}`,children:"Confirm"})]})]})]})};return e.jsxs("div",{children:[Y&&e.jsx(Oe,{}),e.jsx(Te,{isOpen:n!==void 0?n:!0,onClose:r,title:P||(u?"Edit court":"Add new court"),showFooter:G,primaryButtonText:N||(u?"Save changes":"Add court"),onPrimaryAction:L,submitting:H||$,children:e.jsx(xt,{isLoading:Y,children:e.jsxs("form",{onSubmit:t,className:"flex h-full flex-col",children:[e.jsxs("div",{className:"h-full flex-1 space-y-8",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("label",{className:"mb-3 block text-sm font-medium text-gray-700",children:["Court Name ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("input",{type:"text",name:"name",value:h.name,onChange:T,className:"block w-full rounded-lg border border-gray-300 px-4 py-3 text-sm transition-colors duration-200 focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue focus:ring-opacity-20",placeholder:"Enter court name",required:!0})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("label",{className:"mb-3 block text-sm font-medium text-gray-700",children:"Sport"}),e.jsx("div",{className:"grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3",children:_.filter(x=>x.status===1).map(x=>e.jsxs("label",{className:"flex cursor-pointer items-center rounded-lg border border-gray-200 p-3 transition-colors duration-200 hover:bg-gray-50",children:[e.jsx("input",{type:"radio",name:"sport_id",value:x.id,checked:h.sport_id===x.id,onChange:T,className:"h-4 w-4 border-gray-300 text-primaryBlue focus:ring-2 focus:ring-primaryBlue"}),e.jsx("span",{className:"ml-3 text-sm font-medium text-gray-900",children:x.name})]},x.id))})]}),E.length>0&&e.jsxs("div",{className:"space-y-1",children:[e.jsxs("label",{className:"mb-3 block text-sm font-medium text-gray-700",children:["Type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3",children:E.map(x=>e.jsxs("label",{className:"flex cursor-pointer items-center rounded-lg border border-gray-200 p-3 transition-colors duration-200 hover:bg-gray-50",children:[e.jsx("input",{type:"radio",name:"type",value:x.type,checked:h.type===x.type,onChange:T,className:"h-4 w-4 border-gray-300 text-primaryBlue focus:ring-2 focus:ring-primaryBlue",required:!0}),e.jsx("span",{className:"ml-3 text-sm font-medium text-gray-900",children:x.type})]},x.type))})]}),h.type&&C.length>0&&e.jsxs("div",{className:"space-y-1",children:[e.jsxs("label",{className:"mb-3 block text-sm font-medium text-gray-700",children:["Sub Type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3",children:C.map(x=>e.jsxs("label",{className:"flex cursor-pointer items-center rounded-lg border border-gray-200 p-3 transition-colors duration-200 hover:bg-gray-50",children:[e.jsx("input",{type:"radio",name:"sub_type",value:x,checked:h.sub_type===x,onChange:T,className:"h-4 w-4 border-gray-300 text-primaryBlue focus:ring-2 focus:ring-primaryBlue",required:!0}),e.jsx("span",{className:"ml-3 text-sm font-medium text-gray-900",children:x})]},x))})]})]}),e.jsxs("div",{className:"flex flex-shrink-0 justify-end gap-4 border-t border-gray-200 px-4 py-4",children:[e.jsx("button",{type:"button",className:"flex-1 rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:r,children:"Cancel"}),e.jsx(Ye,{type:"submit",loading:$,className:"flex-1 rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:u?"Save changes":"Add court"})]})]})})}),b&&e.jsx(K,{}),e.jsx(Ie,{isOpen:p,onClose:()=>y(!1),onConfirm:g,eventCount:w,isSubmitting:$,type:"court"})]})}function Ve({showTimesAvailableModal:o,setShowTimesAvailableModal:l,selectedCourt:r,setSelectedTimes:n,isSubmitting:u,selectedTimes:v,onSave:_,minTime:S=0,maxTime:P=23,allowMultipleSlots:N=!0,title:H="Times available"}){const[G,L]=s.useState(null),[h,J]=s.useState(!1),[Y,q]=s.useState(null),[$,A]=s.useState(null),[b,U]=s.useState({start:null,current:null}),W=s.useRef(new Map),d=s.useRef(null),j=s.useRef(new Set),i=["monday","tuesday","wednesday","thursday","friday","saturday","sunday"],y=(()=>{const t=[];for(let a=0;a<=23;a++)t.push(`${a.toString().padStart(2,"0")}:00:00`),t.push(`${a.toString().padStart(2,"0")}:30:00`);return t})(),w=(t,a)=>{n(m=>m.some(C=>C.time===t&&C.day===a)?m.filter(C=>!(C.time===t&&C.day===a)):[...m,{time:t,day:a}])},M=(t,a)=>{L(null)},Z=(t,a)=>{n(m=>m.filter(E=>!(E.time===t&&E.day===a))),L(null)},O=()=>{const t={};return v.forEach(a=>{const m=a.day.toLowerCase();t[m]||(t[m]=[]),t[m].push(a.time)}),Object.entries(t).map(([a,m])=>({day:a,timeslots:[...new Set(m)].sort()}))},V=t=>{const[a,m]=t.split(":"),E=parseInt(a),C=E>=12?"PM":"AM";let K=E%12;return K===0&&(K=12),`${K}:${m} ${C}`},z=s.useCallback((t,a)=>{if(!t||!a.start||!a.current)return!1;const m=t.getBoundingClientRect(),E=Math.min(a.start.x,a.current.x),C=Math.max(a.start.x,a.current.x),K=Math.min(a.start.y,a.current.y),ne=Math.max(a.start.y,a.current.y);return!(m.right<E||m.left>C||m.bottom<K||m.top>ne)},[]),T=s.useCallback(t=>{d.current&&cancelAnimationFrame(d.current),d.current=requestAnimationFrame(()=>{W.current.forEach((a,m)=>{if(z(a,t)){const[E,C]=m.split("|");v.some(ne=>ne.time===C&&ne.day===E)||w(C,E)}})})},[z,v]),se=(t,a,m)=>{J(!0),q(t),A(a);const E={start:{x:m.clientX,y:m.clientY},current:{x:m.clientX,y:m.clientY}};U(E),j.current.clear(),w(t,a)},ae=s.useCallback(t=>{if(h){t.preventDefault();const a={...b,current:{x:t.clientX,y:t.clientY}};U(a),T(a)}},[h,b,T]),c=s.useCallback(()=>{J(!1),q(null),A(null),U({start:null,current:null}),j.current.clear(),d.current&&cancelAnimationFrame(d.current)},[]),g=(t,a)=>{h&&(v.some(E=>E.time===t&&E.day===a)||w(t,a))};return s.useEffect(()=>{if(h)return window.addEventListener("mousemove",ae),window.addEventListener("mouseup",c),()=>{window.removeEventListener("mousemove",ae),window.removeEventListener("mouseup",c)}},[h,ae,c]),e.jsx(Fe,{isOpen:o,onClose:()=>l(!1),title:H,onDiscard:()=>{n([]),l(!1)},discardLabel:"Discard",showActions:!0,isSubmitting:u,saveLabel:"Save changes",leftElement:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M3.33268 12.5L9.41008 6.42259C9.73552 6.09715 10.2632 6.09715 10.5886 6.42259L16.666 12.5",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-2",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M16.6673 7.5L10.5899 13.5774C10.2645 13.9028 9.73685 13.9028 9.41141 13.5774L3.33398 7.5",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]}),e.jsx("p",{className:"text-xl",children:r==null?void 0:r.name})]}),onSave:()=>{const t=O();_==null||_(t),l(!1)},children:e.jsxs("div",{className:"w-full select-none",children:[h&&b.start&&b.current&&e.jsx("div",{style:{position:"fixed",left:Math.min(b.start.x,b.current.x),top:Math.min(b.start.y,b.current.y),width:Math.abs(b.current.x-b.start.x),height:Math.abs(b.current.y-b.start.y),backgroundColor:"rgba(59, 130, 246, 0.2)",border:"2px solid rgb(59, 130, 246)",pointerEvents:"none",zIndex:1e3}}),e.jsx("div",{className:"grid grid-cols-7 gap-5",children:i.map(t=>e.jsxs("div",{className:"rounded-md bg-white p-2 text-center",children:[e.jsx("div",{className:"mb-2 rounded-md bg-[#F6F8FA] px-3 py-2 font-medium capitalize",children:t}),e.jsx("div",{className:"space-y-2",children:y.map(a=>{const m=v.some(C=>C.time===a&&C.day===t),E=`${t}-${a}`;return e.jsxs("div",{className:"relative",children:[e.jsx("button",{ref:C=>{C&&W.current.set(`${t}|${a}`,C)},className:`w-full rounded-md border-2 border-gray-100 px-3 py-2 text-sm font-medium text-gray-500 ${m?"border-2 border-primaryBlue bg-[#EBF1FF] text-primaryBlue":"border-gray-300 hover:border-gray-400"}`,onMouseDown:C=>se(a,t,C),onMouseEnter:()=>g(a,t),onMouseUp:c,onClick:()=>{m?Z(a,t):w(a,t)},children:V(a)}),G===E&&e.jsx("div",{className:"absolute right-0 z-10 mt-1 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsxs("div",{className:"py-1",children:[e.jsxs("button",{onClick:()=>M(),className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})}),"Edit"]}),e.jsxs("button",{onClick:()=>Z(a,t),className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})]})})]},`${t}-${a}`)})})]},t))})]})})}const _t=({exceptions:o,setExceptions:l,selectedException:r,setSelectedException:n,showExceptionTimeSelector:u,setShowExceptionTimeSelector:v,selectedExceptionTimes:_,setSelectedExceptionTimes:S,showDeleteExceptionModal:P,setShowDeleteExceptionModal:N,selectedExceptionIndex:H,setSelectedExceptionIndex:G,deletingException:L,deleteException:h,setShowExceptionEditModal:J,showHowItWorksModal:Y,setShowHowItWorksModal:q,timesSelectorIsSubmitting:$,setTimesSelectorIsSubmitting:A,userRole:b,profileSettings:U,globalDispatch:W,handleSearchException:d})=>{s.useContext(Le);const j=new xe;s.useState(null);const[i,p]=s.useState(!1),[y,w]=s.useState((r==null?void 0:r.name)||""),M=s.useRef(null),[Z,O]=s.useState(!1),[V,z]=s.useState(0),[T,se]=s.useState([]),[ae,c]=s.useState(!1),[g,t]=s.useState(null),[a,m]=s.useState(()=>(o==null?void 0:o.findIndex(f=>f.name===(r==null?void 0:r.name)))||0),E=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];s.useEffect(()=>{w((r==null?void 0:r.name)||"")},[r]),s.useEffect(()=>{i&&M.current&&M.current.focus()},[i]),s.useEffect(()=>{var f;if(r){const F=((f=r.days)==null?void 0:f.flatMap(D=>D.timeslots.map(Q=>({day:D.day.charAt(0).toUpperCase()+D.day.slice(1),time:Q}))))||[];S(F)}},[r,S]);const C=()=>{if(a>0){const f=a-1;m(f),ne(o[f])}},K=()=>{if(a<o.length-1){const f=a+1;m(f),ne(o[f])}},ne=f=>{var D;n(f);const F=((D=f.days)==null?void 0:D.flatMap(Q=>Q.timeslots.map(re=>({day:Q.day.charAt(0).toUpperCase()+Q.day.slice(1),time:re}))))||[];S(F)},pe=(f,F)=>_.some(D=>D.time===f&&D.day===F),ge=(f,F)=>{S(D=>[...D,{time:f,day:F}])},x=(f,F)=>{S(D=>D.filter(Q=>!(Q.time===f&&Q.day===F)))},R=f=>{w(f.target.value)},X=()=>{y.trim()&&(r.name=y.trim(),p(!1))},le=f=>{f.key==="Enter"?X():f.key==="Escape"&&(w((r==null?void 0:r.name)||""),p(!1))},ie=async f=>{try{c(!0);const F=await j.callRawAPI("/v3/api/custom/courtmatchup/club/courts/affected-reservations",{exceptions:f},"POST");return F&&!F.error&&F.total_affected>0?(z(F.total_affected),se(F.affected_reservations||[]),t(f),O(!0),!0):!1}catch(F){return console.error("Error checking for affected events:",F),!1}finally{c(!1)}},ee=async()=>{try{await de({exceptions:g,delete_affected_events:!0}),O(!1),t(null),v(!1),S([])}catch(f){console.error("Error saving changes:",f)}},ye=()=>{O(!1),t(null),z(0),se([])},de=async f=>{var F;A(!0);try{b==="club"?await j.callRawAPI(`/v3/api/custom/courtmatchup/${b}/profile-edit`,f,"POST"):await j.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile-edit/${(F=U==null?void 0:U.user)==null?void 0:F.id}`,f,"POST");const D=Array.isArray(f)?f:f.exceptions;l(D),B(W,"Times updated successfully",3e3,"success"),v(!1),S([])}catch(D){console.error(D),B(W,"Error updating times",3e3,"error")}finally{A(!1)}};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mt-8 max-w-4xl",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Exceptions"}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsx("button",{className:"rounded-xl bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",onClick:()=>{n(null),J(!0)},children:"+ Add new"})})]}),e.jsxs("div",{className:"mb-6 flex justify-between",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"search",onChange:f=>d(f.target.value),className:"w-full rounded-md border border-gray-300 py-2 pl-10 pr-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"})]}),e.jsxs("button",{className:"flex items-center gap-2 rounded-md border border-gray-300 px-3 py-2 text-sm text-gray-600",onClick:()=>q(!0),children:[e.jsxs("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M7.16732 7.33398H8.00065L8.00065 10.834M14.1673 8.00065C14.1673 11.4064 11.4064 14.1673 8.00065 14.1673C4.5949 14.1673 1.83398 11.4064 1.83398 8.00065C1.83398 4.5949 4.5949 1.83398 8.00065 1.83398C11.4064 1.83398 14.1673 4.5949 14.1673 8.00065Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M7.54102 5.33333C7.54102 5.58646 7.74622 5.79167 7.99935 5.79167C8.25248 5.79167 8.45768 5.58646 8.45768 5.33333C8.45768 5.0802 8.25248 4.875 7.99935 4.875C7.74622 4.875 7.54102 5.0802 7.54102 5.33333Z",fill:"#868C98",stroke:"#868C98",strokeWidth:"0.25"})]}),"How it works"]})]}),e.jsxs("div",{className:"rounded-lg bg-white p-2 shadow",children:[e.jsx("div",{className:"px-6 py-3",children:e.jsxs("div",{className:"grid grid-cols-2",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Exception name"}),e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"Days applicable"})]})}),e.jsx("ul",{className:"pb-5",children:Array.isArray(o)&&o.length>0?o.map((f,F)=>{var D;return e.jsx("li",{className:"mx-2 my-3 rounded-xl bg-gray-100 px-6 py-3",children:e.jsxs("div",{className:"grid grid-cols-2 items-center",children:[e.jsx("span",{className:"text-sm text-gray-900",children:f.name}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:(D=f.days)==null?void 0:D.map(Q=>Q.day.slice(0,3).toUpperCase()).join(", ")}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("button",{className:"text-gray-400 hover:text-gray-500",onClick:()=>{var re;n(f);const Q=((re=f.days)==null?void 0:re.flatMap(fe=>fe.timeslots.map(we=>({day:fe.day.charAt(0).toUpperCase()+fe.day.slice(1),time:we}))))||[];S(Q),v(!0)},children:e.jsx(yt,{className:"h-5 w-5"})}),e.jsx("button",{onClick:()=>{G(F),N(!0)},className:"text-gray-400 hover:text-gray-500",children:e.jsx(wt,{className:"h-5 w-5"})})]})]})]})},F)}):e.jsx("div",{className:"flex w-full items-center justify-center text-center text-sm text-gray-500",children:"No exceptions found"})})]})]}),e.jsxs(Fe,{isOpen:u,onClose:()=>v(!1),title:"Exception times",onDiscard:()=>{S([]),v(!1)},discardLabel:"Discard",showActions:!0,saveLabel:"Save changes",leftElement:e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3",children:[e.jsxs("div",{className:"flex gap-1 sm:gap-3",children:[e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-1.5 sm:p-2",onClick:C,disabled:a===0,children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-5 sm:w-5",children:e.jsx("path",{d:"M3.33268 12.5L9.41008 6.42259C9.73552 6.09715 10.2632 6.09715 10.5886 6.42259L16.666 12.5",stroke:a===0?"#D1D5DB":"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),e.jsx("button",{className:"rounded-lg bg-[#F6F8FA] p-1.5 sm:p-2",onClick:K,disabled:a===(o==null?void 0:o.length)-1,children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-5 sm:w-5",children:e.jsx("path",{d:"M16.6673 7.5L10.5899 13.5774C10.2645 13.9028 9.73685 13.9028 9.41141 13.5774L3.33398 7.5",stroke:a===(o==null?void 0:o.length)-1?"#D1D5DB":"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})})]}),e.jsx("div",{className:"flex items-center gap-1 sm:gap-2",children:i?e.jsx("input",{ref:M,type:"text",value:y,onChange:R,onBlur:X,onKeyDown:le,className:"w-32 rounded-lg border border-primaryBlue bg-white px-2 py-1 text-base outline-none sm:w-48 sm:px-3 sm:text-xl",placeholder:"Enter exception name"}):e.jsxs("div",{className:"flex items-center gap-1 sm:gap-2",children:[e.jsx("p",{className:"text-base font-medium sm:text-xl",children:y}),e.jsx("button",{onClick:()=>p(!0),className:"rounded-lg p-1 hover:bg-gray-100",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"sm:h-5 sm:w-5",children:e.jsx("path",{d:"M11.7167 7.51667L12.4833 8.28333L4.93333 15.8333H4.16667V15.0667L11.7167 7.51667ZM14.7167 2.5C14.5083 2.5 14.2917 2.58333 14.1333 2.74167L12.6083 4.26667L15.7333 7.39167L17.2583 5.86667C17.5833 5.54167 17.5833 5.01667 17.2583 4.69167L15.3083 2.74167C15.1417 2.575 14.9333 2.5 14.7167 2.5ZM11.7167 5.15833L2.5 14.375V17.5H5.625L14.8417 8.28333L11.7167 5.15833Z",fill:"#868C98"})})})]})})]}),onSave:async()=>{const F=(()=>{const re=E.map(fe=>{const we=_.filter(ve=>ve.day===fe).map(ve=>ve.time).sort();return we.length>0?{day:fe.toLowerCase(),timeslots:we}:null}).filter(Boolean);return{name:r==null?void 0:r.name,days:re}})(),D=o.map(re=>re.name===r.name?F:re);await ie(D)||await de(D)},isSubmitting:$||ae,children:[e.jsx("div",{className:"w-full",children:e.jsx(Ke,{days:E,isSelected:pe,handleTimeSelect:ge,handleDeleteTime:x,disableTimeSlot:null,renderTimeSlotContent:null})}),e.jsx(Ie,{isOpen:Z,onClose:ye,onConfirm:ee,eventCount:V,affectedReservations:T,isSubmitting:$,type:"exception"})]}),e.jsx(qe,{onClose:()=>N(!1),isOpen:P,onDelete:()=>h(H),message:"Are you sure you want to delete this exception?",loading:L})]})},ze=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Ae=(o,l)=>{if(!l||l.length===0)return!0;const[r,n]=o.split(":"),u=parseInt(r)*60+parseInt(n);return l.some(v=>{const[_,S]=v.from.split(":"),[P,N]=v.until.split(":"),H=parseInt(_)*60+parseInt(S),G=parseInt(P)*60+parseInt(N);return u>=H&&u<G})},Me=(o,l)=>!l||l.length===0?!1:l.includes(o),Ze=(o,l)=>{if(!l||l.length===0)return[];const r=o.toLowerCase();return l.reduce((n,u)=>{const v=u.days.find(_=>_.day===r);return v&&n.push({name:u.name,timeslots:v.timeslots}),n},[])},Je=(o,l)=>{if(!l||l.length===0)return!1;const r=o.includes(":00",5)?o:`${o}:00`;return l.some(n=>n.timeslots.includes(r))},Ge=(o,l)=>{if(!l||l.length===0)return null;const r=o.includes(":00",5)?o:`${o}:00`,n=l.find(u=>u.timeslots.includes(r));return n?n.name:null},Qe=({isOpen:o,onClose:l,court:r,club:n,globalDispatch:u,onSave:v,edit_api:_})=>{const[S,P]=s.useState([]),[N,H]=s.useState(null),[G,L]=s.useState(!1),[h,J]=s.useState({times:[],daysOff:[],exceptions:[]}),Y=new xe;s.useEffect(()=>{o&&r&&q()},[o,r]),s.useEffect(()=>{if(n){const i=n.times?JSON.parse(n.times):[],p=n.days_off?JSON.parse(n.days_off):[],y=n.exceptions?JSON.parse(n.exceptions):[];J({times:i,daysOff:p,exceptions:y})}},[n]);const q=()=>{try{let i=[];r.availability&&(i=JSON.parse(r.availability));const p=ze.map(y=>({day:y.toLowerCase(),timeslots:[]}));i&&Array.isArray(i)&&i.length>0&&i.forEach(y=>{const w=p.findIndex(M=>M.day===y.day.toLowerCase());w!==-1&&(p[w].timeslots=y.timeslots)}),P(p),H(i||[])}catch(i){console.error("Error parsing court availability:",i),B(u,"Error loading court availability",3e3,"error")}},$=()=>{if(!S||!N)return!1;const i=A(),p=Array.isArray(N)?N.filter(w=>w.timeslots&&w.timeslots.length>0):[];if(i.length===0&&p.length>0||i.length>0&&p.length===0||i.length!==p.length)return!0;const y={};p.forEach(w=>{y[w.day.toLowerCase()]=[...w.timeslots].sort()});for(const w of i){const M=w.day.toLowerCase(),Z=y[M];if(!Z||w.timeslots.length!==Z.length)return!0;const O=[...w.timeslots].sort();for(let V=0;V<O.length;V++){const z=O[V].replace(/:00$/,""),T=Z[V].replace(/:00$/,"");if(z!==T)return!0}}return!1},A=()=>S?S.filter(i=>i.timeslots.length>0):[],b=(i,p)=>{if(Me(p,h.daysOff)){B(u,`${p} is a club day off`,3e3,"error");return}if(!Ae(i,h.times)){B(u,"This time is outside club hours",3e3,"error");return}const y=Ze(p,h.exceptions);if(Je(i,y)){const w=Ge(i,y);B(u,`This time is marked as "${w||"Exception"}"`,3e3,"warning")}P(w=>{const M=w.map(O=>{if(O.day===p.toLowerCase()){const V=i.replace(":00","");if(!O.timeslots.some(T=>T===i||T===V))return{...O,timeslots:[...O.timeslots,i].sort()}}return O});return M.some(O=>O.day===p.toLowerCase())||M.push({day:p.toLowerCase(),timeslots:[i]}),M})},U=(i,p)=>{P(y=>y.map(w=>{if(w.day===p.toLowerCase()){const M=w.timeslots.filter(Z=>Z!==i&&Z!==i.replace(":00",""));return M.length===0?null:{...w,timeslots:M}}return w}).filter(Boolean))},W=(i,p)=>{const y=S==null?void 0:S.find(M=>M.day===p.toLowerCase());if(!y)return!1;const w=i.replace(":00","");return y.timeslots.some(M=>M===i||M.replace(":00","")===w)},d=async()=>{try{L(!0);const i=A();await Y.callRawAPI(_,{courts:[{court_id:r.id,availability:i}]},"POST"),B(u,"Court availability updated successfully",3e3,"success"),H(i),v&&v(),l()}catch(i){console.error("Error saving court availability:",i),B(u,"Failed to update court availability",3e3,"error")}finally{L(!1)}},j=()=>{q(),l()};return r?e.jsx(Fe,{isOpen:o,onClose:l,title:`Court Availability - ${r.name}`,onDiscard:j,discardLabel:"Discard",showActions:$(),saveLabel:"Save changes",onSave:d,isSubmitting:G,children:e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Set the available time slots for this court. Users will only be able to book during these times."})}),e.jsx("div",{className:"overflow-x-auto pb-4",children:e.jsx(Ke,{days:ze,isSelected:W,handleTimeSelect:b,handleDeleteTime:U,renderTimeSlotContent:(i,p)=>{if(Me(p,h.daysOff))return e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});if(!Ae(i.value,h.times))return e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});const y=Ze(p,h.exceptions);if(Je(i.value,y)){const w=Ge(i.value,y);return e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-orange-50 text-xs font-medium text-orange-500",children:e.jsx("span",{className:"truncate",children:w||"Exception"})})}return null},disableTimeSlot:(i,p)=>!!(Me(p,h.daysOff)||!Ae(i.value,h.times))})})]})}):null},kt=({club:o,courts:l,exceptions:r,setShowEditModal:n,searchQuery:u,setSearchQuery:v,filteredCourts:_,activeDropdown:S,setActiveDropdown:P,dropdownPosition:N,setDropdownPosition:H,setSelectedCourtForEdit:G,setShowEditCourtModal:L,setSelectedCourtForDelete:h,setShowDeleteCourtModal:J,setShowAddCourtModal:Y,sports:q,globalDispatch:$,edit_api:A,fetchSettings:b})=>{var c,g;const[U,W]=s.useState((o==null?void 0:o.allow_user_court_selection)===1),[d,j]=s.useState(!1),[i,p]=s.useState([]),[y,w]=s.useState(!1),[M,Z]=s.useState(!1),[O,V]=s.useState(null),z=new xe,T=localStorage.getItem("user");s.useEffect(()=>{if(l&&l.length>0){const t=l.map(a=>({id:a.id,name:a.name,sport_id:a.sport_id,type:a.type||"",sub_type:a.sub_type||"",allow_reservation:a.allow_reservation!==!1,allow_lesson:a.allow_lesson!==!1,allow_clinic:a.allow_clinic!==!1,allow_buddy:a.allow_buddy!==!1}));W((o==null?void 0:o.allow_user_court_selection)===1),p(t)}},[l]);const se=async()=>{const t=!U;j(!0);try{await z.callRawAPI(A,{allow_user_court_selection:t?1:0},"POST"),await ce(z,{user_id:T,activity_type:oe.court_management,action_type:oe.UPDATE,data:{allow_user_court_selection:t?1:0},club_id:o==null?void 0:o.id,description:`${t?"Enabled":"Disabled"} user court selection`}),W(t),B($,`User court selection ${t?"enabled":"disabled"} successfully`,3e3,"success"),b&&b()}catch(a){console.error("Error updating user court selection setting:",a),B($,"Error updating setting",3e3,"error")}finally{j(!1)}},ae=async()=>{w(!0);try{const t=i.map(a=>({id:a.id,allow_reservation:a.allow_reservation,allow_lesson:a.allow_lesson,allow_clinic:a.allow_clinic,allow_buddy:a.allow_buddy}));await z.callRawAPI(A,{court_settings:t},"POST"),await ce(z,{user_id:T,activity_type:oe.court_management,action_type:oe.UPDATE,data:t,club_id:o==null?void 0:o.id,description:"Updated court usage settings"}),B($,"Court settings saved successfully",3e3,"success"),b&&b()}catch(t){console.error("Error saving court settings:",t),B($,"Error saving court settings",3e3,"error")}finally{w(!1)}};return e.jsxs("div",{className:"mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between rounded-lg bg-[#F6F8FA] px-6 py-3",children:[e.jsx("h2",{className:"text-lg font-medium",children:"Club settings"}),e.jsx("button",{className:"text-sm font-medium text-gray-600",onClick:()=>n(!0),children:"Edit"})]}),e.jsxs("div",{className:"flex flex-col divide-y divide-gray-200 p-6",children:[e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"GENERAL OPENING HOURS"}),e.jsx("p",{className:"mt-1",children:o!=null&&o.times&&JSON.parse(o==null?void 0:o.times).length>0?(c=JSON.parse(o==null?void 0:o.times))==null?void 0:c.map(t=>e.jsxs("div",{children:[He(t.from)," -"," ",He(t.until)]},t.from)):"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"DAYS OFF"}),e.jsx("p",{className:"mt-1",children:o!=null&&o.days_off?(g=JSON.parse(o==null?void 0:o.days_off))==null?void 0:g.join(", "):"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"TOTAL COURTS"}),e.jsx("p",{className:"mt-1",children:l!=null&&l.length?l==null?void 0:l.length:"N/A"})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"SCHEDULED EXCEPTIONS"}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("p",{className:"mt-1",children:r==null?void 0:r.length})})]}),e.jsxs("div",{className:"py-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-500",children:"ALLOW USERS TO SELECT COURT"}),e.jsx("button",{onClick:se,disabled:d,className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${U?"bg-primaryBlue":"bg-gray-200"} ${d?"cursor-not-allowed opacity-50":""}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${U?"translate-x-6":"translate-x-1"}`})})]}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"When enabled, users can select their preferred court during reservation."})]})]})]}),e.jsxs("div",{className:"h-fit rounded-lg bg-white p-6 shadow",children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsx("h2",{className:"text-lg font-medium",children:"Courts"}),e.jsx("button",{className:"rounded-xl bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:bg-gray-400",onClick:ae,disabled:y,children:y?"Saving...":"Save Court Settings"})]}),e.jsx("div",{className:"mb-4 flex gap-4",children:e.jsxs("div",{className:"relative w-96",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.25 2.5C12.976 2.5 16 5.524 16 9.25C16 12.976 12.976 16 9.25 16C5.524 16 2.5 12.976 2.5 9.25C2.5 5.524 5.524 2.5 9.25 2.5ZM9.25 14.5C12.1502 14.5 14.5 12.1502 14.5 9.25C14.5 6.349 12.1502 4 9.25 4C6.349 4 4 6.349 4 9.25C4 12.1502 6.349 14.5 9.25 14.5ZM15.6137 14.5532L17.7355 16.6742L16.6742 17.7355L14.5532 15.6137L15.6137 14.5532Z",fill:"#525866"})})}),e.jsx("input",{type:"text",placeholder:"Search by name, sport, type, sub-type...",className:"w-full rounded-lg border border-gray-300 px-4 py-2 pl-10 text-sm focus:border-blue-500 focus:outline-none",value:u,onChange:t=>v(t.target.value)})]})}),e.jsx("div",{className:"overflow-x-auto overflow-y-hidden",children:e.jsxs("table",{className:"min-w-full border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"text-left text-sm text-gray-500",children:[e.jsx("th",{className:"px-4 pb-4",children:"Name"}),e.jsx("th",{className:"px-4 pb-4",children:"Sport"}),e.jsx("th",{className:"px-4 pb-4",children:"Type"}),e.jsx("th",{className:"px-4 pb-4",children:"Sub-type"}),e.jsx("th",{className:"px-4 pb-4"})]})}),(_==null?void 0:_.length)>0?_==null?void 0:_.map(t=>{var a;return e.jsx("tbody",{children:e.jsxs("tr",{className:"overflow-hidden",children:[e.jsx("td",{className:"rounded-l-xl bg-gray-100 px-4 py-3",children:t.name?t.name:"N/A"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:t.sport_id?(a=q.find(m=>m.id==t.sport_id))==null?void 0:a.name:"N/A"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:t.type||"--"}),e.jsx("td",{className:"bg-gray-100 px-4 py-3",children:t.sub_type||"--"}),e.jsx("td",{className:"rounded-r-xl bg-gray-100 px-4 py-3",children:e.jsxs("div",{className:"relative",children:[e.jsx("button",{onClick:m=>{const E=m.currentTarget.getBoundingClientRect(),C=m.currentTarget.closest("table").getBoundingClientRect(),K=E.bottom>C.bottom-100;H(K?"top":"bottom"),P(S===t.id?null:t.id)},className:"text-gray-400 hover:text-gray-500",children:e.jsx(bt,{className:"h-5 w-5"})}),S===t.id&&e.jsx("div",{className:`absolute right-0 z-10 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 ${N==="top"?"bottom-full mb-1":"top-full mt-1"}`,children:e.jsxs("div",{className:"py-1",role:"menu",children:[e.jsxs("button",{className:"flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100",onClick:()=>{G(t),L(!0),P(null)},children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.041 5.20756L13.5768 2.67181C13.9022 2.34638 14.4298 2.34637 14.7553 2.67181L17.3268 5.2433C17.6522 5.56874 17.6522 6.09638 17.3268 6.42181L14.791 8.95756M11.041 5.20756L2.53509 13.7135C2.37881 13.8698 2.29102 14.0817 2.29102 14.3027V17.7076H5.69584C5.91685 17.7076 6.12881 17.6198 6.28509 17.4635L14.791 8.95756M11.041 5.20756L14.791 8.95756",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Edit"]}),e.jsxs("button",{className:"flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100",onClick:()=>{V(t),Z(!0),P(null)},children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.66667 1.66667V4.16667M13.3333 1.66667V4.16667M2.5 6.66667H17.5M4.16667 3.33333H15.8333C16.7538 3.33333 17.5 4.07953 17.5 5V16.6667C17.5 17.5871 16.7538 18.3333 15.8333 18.3333H4.16667C3.24619 18.3333 2.5 17.5871 2.5 16.6667V5C2.5 4.07953 3.24619 3.33333 4.16667 3.33333Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Availability"]}),e.jsxs("button",{className:"flex w-full items-center gap-1 px-4 py-2 text-sm text-gray-600 hover:bg-gray-100",onClick:()=>{h(t.id),J(!0),P(null)},children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Delete"]})]})})]})})]})},t.id)}):e.jsx("div",{className:"flex w-full items-center justify-center text-center text-sm text-gray-500",children:"No courts found"})]})}),e.jsx("button",{className:"mt-6 rounded-md px-4 py-2 text-sm font-medium text-black underline",onClick:()=>Y(!0),children:"+ Add court"})]}),e.jsx(Qe,{isOpen:M,onClose:()=>{Z(!1),V(null)},court:O,club:o,globalDispatch:$,edit_api:A,onSave:()=>{b&&b()}})]})},Et=({courts:o=[],sports:l=[],edit_api:r,globalDispatch:n,fetchSettings:u,club:v})=>{const[_]=s.useState(!1),[S,P]=s.useState([]),[N,H]=s.useState(!1),[G,L]=s.useState(!1),[h,J]=s.useState(!1),[Y,q]=s.useState(null),$=new xe,A=localStorage.getItem("user");s.useEffect(()=>{if(o&&o.length>0){const d=o.map(j=>{const i=j.court_settings||{};return{id:j.id,name:j.name,sport_id:j.sport_id,type:j.type||"",sub_type:j.sub_type||"",min_booking_time:i.min_booking_time||30,allow_reservation:i.allow_reservation!==!1,allow_lesson:i.allow_lesson!==!1,allow_clinic:i.allow_clinic!==!1,allow_buddy:i.allow_buddy!==!1,court_settings:i}});P(d)}},[o]);const b=(d,j,i)=>{P(p=>p.map(y=>y.id===d?{...y,[j]:i}:y)),L(!0)},U=d=>{const j=o.find(i=>i.id===d);console.log("Opening availability for court:",j),q(j),J(!0)},W=async()=>{H(!0);try{const d=S.map(j=>({court_id:j.id,court_settings:{min_booking_time:j.min_booking_time,allow_reservation:j.allow_reservation,allow_lesson:j.allow_lesson,allow_clinic:j.allow_clinic,allow_buddy:j.allow_buddy}}));await $.callRawAPI(r,{courts:d},"POST"),await ce($,{user_id:A,activity_type:ue.court_management,action_type:oe.UPDATE,data:d,club_id:v==null?void 0:v.id,description:"Updated court settings"}),B(n,"Court settings saved successfully",3e3,"success"),L(!1),u&&u()}catch(d){console.error("Error saving court settings:",d),B(n,"Error saving court settings",3e3,"error")}finally{H(!1)}};return e.jsxs("div",{className:"mt-8",children:[_&&e.jsx(Oe,{}),e.jsx("div",{className:"mb-6 flex items-center justify-between",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Court Settings"})}),e.jsxs("div",{className:"rounded-lg bg-white p-6 shadow",children:[e.jsx("div",{className:"mb-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:'Configure minimum booking time and allowed activities for each court. Make your changes and click "Save Changes" to apply them.'})}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Court Name"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Sport"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500",children:"Min. Booking Time"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Reservation"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Lesson"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Clinic"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Allow Find-a-Buddy"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-center text-xs font-medium uppercase tracking-wider text-gray-500",children:"Availability"})]})}),e.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:S.length>0?S.map(d=>{var j;return e.jsxs("tr",{children:[e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"text-sm font-medium text-gray-900",children:d.name})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"text-sm text-gray-500",children:((j=l.find(i=>i.id===d.sport_id))==null?void 0:j.name)||"N/A"})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"number",min:"30",max:"1440",step:"30",value:d.min_booking_time,onChange:i=>{const p=parseInt(i.target.value,10);isNaN(p)||p<30||p>1440||b(d.id,"min_booking_time",p)},className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"}),e.jsx("span",{className:"ml-2 text-sm text-gray-500",children:"minutes"})]})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Se,{checked:d.allow_reservation,onChange:i=>{b(d.id,"allow_reservation",i)},className:`${d.allow_reservation?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${d.allow_reservation?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Se,{checked:d.allow_lesson,onChange:i=>{b(d.id,"allow_lesson",i)},className:`${d.allow_lesson?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${d.allow_lesson?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Se,{checked:d.allow_clinic,onChange:i=>{b(d.id,"allow_clinic",i)},className:`${d.allow_clinic?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${d.allow_clinic?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsx(Se,{checked:d.allow_buddy,onChange:i=>{b(d.id,"allow_buddy",i)},className:`${d.allow_buddy?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${d.allow_buddy?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})})}),e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:e.jsx("div",{className:"flex justify-center",children:e.jsxs("button",{onClick:()=>U(d.id),className:"flex items-center gap-1 rounded-md bg-blue-50 px-3 py-2 text-sm font-medium text-blue-600 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",title:"Manage court availability",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M6.66667 1.66667V4.16667M13.3333 1.66667V4.16667M2.5 6.66667H17.5M4.16667 3.33333H15.8333C16.7538 3.33333 17.5 4.07953 17.5 5V16.6667C17.5 17.5871 16.7538 18.3333 15.8333 18.3333H4.16667C3.24619 18.3333 2.5 17.5871 2.5 16.6667V5C2.5 4.07953 3.24619 3.33333 4.16667 3.33333Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})}),"Availability"]})})})]},d.id)}):e.jsx("tr",{children:e.jsx("td",{colSpan:"8",className:"px-6 py-4 text-center text-sm text-gray-500",children:"No courts found. Please add courts in the General Settings tab."})})})]})}),S.length>0&&e.jsxs("div",{className:"mt-6 flex items-center justify-between",children:[e.jsx("p",{className:"text-xs text-gray-500",children:"* Minimum booking time can be set between 30 minutes and 24 hours (1440 minutes)."}),e.jsx("button",{onClick:W,disabled:N,className:"inline-flex items-center rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:N?e.jsxs(e.Fragment,{children:[e.jsxs("svg",{className:"-ml-1 mr-2 h-4 w-4 animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Saving..."]}):"Save Changes"})]})]}),e.jsx(Qe,{isOpen:h,onClose:()=>{J(!1),q(null)},court:Y,club:v,globalDispatch:n,edit_api:r,onSave:()=>{u&&u()}})]})},te=new xe;function Dt({profileSettings:o,fetchSettings:l,sports:r=[],club:n,courts:u,edit_api:v}){const[_,S]=s.useState("general-settings"),[P,N]=s.useState(null),[H,G]=s.useState("bottom"),[L,h]=s.useState(!1),[J,Y]=s.useState(!1),[q,$]=s.useState(!1),[A,b]=s.useState(null),[U,W]=s.useState(!1),{dispatch:d}=he.useContext(Le),[j,i]=s.useState(!1),[p,y]=s.useState(!1),[w,M]=s.useState(!1),[Z,O]=s.useState(null),[V,z]=s.useState([]),[T,se]=s.useState(""),[ae,c]=s.useState(!1),[g,t]=s.useState(!1),[a,m]=s.useState(null),[E,C]=s.useState(!1),[K,ne]=s.useState(!1),[pe,ge]=s.useState([]),[x,R]=s.useState(!1),[X,le]=s.useState(""),[ie,ee]=s.useState(!1),[ye,de]=s.useState(null),[f,F]=s.useState(!1),[D,Q]=s.useState(!1),[re,fe]=s.useState(null),[we,ve]=s.useState(!1),[et,_e]=s.useState(!1),[Be,ke]=s.useState(null),[tt]=s.useState(!1),st=localStorage.getItem("role"),[je,be]=s.useState(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[]),Ne=localStorage.getItem("user");console.log("courts",u),s.useEffect(()=>{const k=I=>{P&&!I.target.closest(".relative")&&N(null)};return document.addEventListener("mousedown",k),()=>{document.removeEventListener("mousedown",k)}},[P]),s.useEffect(()=>{be(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[])},[n==null?void 0:n.exceptions]),he.useEffect(()=>{d({type:"SETPATH",payload:{path:"court-management"}})},[]);const at=async k=>{C(!0);const I=je.filter((me,Ce)=>Ce!==k);try{await te.callRawAPI(v,{exceptions:I},"POST"),await ce(te,{user_id:Ne,activity_type:ue.court_management,action_type:oe.DELETE,data:I,club_id:n==null?void 0:n.id,description:"Deleted court exception"}),be(I),t(!1),C(!1),B(d,"Exception deleted successfully")}catch{C(!1)}},nt=k=>{if(k===""){be(n!=null&&n.exceptions?JSON.parse(n==null?void 0:n.exceptions):[]);return}const I=je.filter(me=>me.name.toLowerCase().includes(k.toLowerCase()));be(I)},rt=async()=>{ve(!0);try{te.setTable("club_court"),await te.callRestAPI({id:Be},"DELETE"),await ce(te,{user_id:Ne,activity_type:ue.court_management,action_type:oe.DELETE,data:Be,club_id:n==null?void 0:n.id,description:"Deleted court"})}catch(k){console.log(k)}finally{ve(!1),ke(null),_e(!1),l()}};console.log(je);const ot=()=>{switch(_){case"exceptions":return e.jsx(_t,{exceptions:je,setExceptions:be,selectedException:A,setSelectedException:b,showExceptionTimeSelector:K,setShowExceptionTimeSelector:ne,selectedExceptionTimes:pe,setSelectedExceptionTimes:ge,showDeleteExceptionModal:g,setShowDeleteExceptionModal:t,selectedExceptionIndex:a,setSelectedExceptionIndex:m,deletingException:E,deleteException:at,setShowExceptionEditModal:$,showHowItWorksModal:L,setShowHowItWorksModal:h,timesSelectorIsSubmitting:x,setTimesSelectorIsSubmitting:R,userRole:st,profileSettings:o,globalDispatch:d,handleSearchException:nt});case"general-settings":return e.jsx(kt,{club:n,courts:u,exceptions:je,setShowEditModal:Y,searchQuery:X,setSearchQuery:le,filteredCourts:lt,activeDropdown:P,setActiveDropdown:N,dropdownPosition:H,setDropdownPosition:G,setSelectedCourtForEdit:de,setShowEditCourtModal:ee,setSelectedCourtForDelete:ke,setShowDeleteCourtModal:_e,setShowAddCourtModal:W,sports:r,globalDispatch:d,edit_api:v,fetchSettings:l});case"court-settings":return e.jsx(Et,{courts:u,sports:r,edit_api:v,globalDispatch:d,fetchSettings:l,club:n});default:return null}},lt=u==null?void 0:u.filter(k=>{var me,Ce,$e,Re,De;const I=X.toLowerCase();return((me=k.name)==null?void 0:me.toLowerCase().includes(I))||(($e=(Ce=r.find(ht=>ht.id==k.sport_id))==null?void 0:Ce.name)==null?void 0:$e.toLowerCase().includes(I))||((Re=k.type)==null?void 0:Re.toLowerCase().includes(I))||((De=k.sub_type)==null?void 0:De.toLowerCase().includes(I))}),it=()=>L?e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsxs("div",{className:"w-full max-w-lg rounded-2xl bg-white",children:[e.jsxs("div",{className:"p-6",children:[e.jsx("div",{className:"mb-4",children:e.jsx("h2",{className:"text-lg font-semibold",children:"How it works"})}),e.jsx("div",{className:"mb-6 space-y-4 text-sm text-gray-600",children:e.jsxs("p",{children:[e.jsxs("ol",{className:"list-inside list-decimal space-y-2",children:[e.jsx("li",{children:"You can define which hours the exceptions take place here and the exception will automatically be added everywhere in the schedule where you defined the hours for it."}),e.jsx("li",{children:"When adding an event in the daily scheduler, the club can specify these schedule exception names and define the date/time/repeating details."})]}),e.jsx("p",{className:"mt-2",children:'Separately, you can also create custom event types in the daily scheduler by using the event type "other" and defining the name of the event.'})]})})]}),e.jsx("div",{className:"flex justify-end border-t border-gray-200 p-6",children:e.jsx("button",{onClick:()=>h(!1),className:"rounded-xl bg-[#2B5F2B] px-5 py-3 text-sm text-white",children:"Close"})})]})}):null,Ee=he.useRef(null),ct=async()=>{Ee.current&&await Ee.current.submit()},dt=async k=>{try{i(!0),await te.callRawAPI(v,k,"POST"),await ce(te,{user_id:Ne,activity_type:ue.court_management,action_type:oe.UPDATE,data:k,club_id:n==null?void 0:n.id,description:"Updated club settings"}),Y(!1),B(d,"Settings saved successfully",3e3,"success"),l()}catch(I){console.error("Error saving settings:",I),B(d,"Error saving settings",3e3,"error")}finally{i(!1)}},Pe=async()=>{if(c(!0),!T.length){B(d,"Please enter a name",3e3,"error");return}const k={name:T,days:[]},I=[...je,k];try{await te.callRawAPI(v,{exceptions:I},"POST"),await ce(te,{user_id:Ne,activity_type:ue.court_management,action_type:oe.CREATE,data:k,club_id:n==null?void 0:n.id,description:"Added new court exception"}),be(I),B(d,"Exception added successfully",3e3,"success"),$(!1),b(k),ne(!0),se("")}catch(me){console.error(me),B(d,"Error adding exception",3e3,"error")}finally{c(!1)}},mt=async k=>{y(!0);try{const I={courts:[k]};await te.callRawAPI(v,I,"POST"),await ce(te,{user_id:Ne,activity_type:ue.court_management,action_type:oe.CREATE,data:k,club_id:n==null?void 0:n.id,description:"Added new court"}),W(!1),B(d,"Court added successfully",3e3,"success"),l()}catch(I){console.error(I)}finally{y(!1)}},ut=async k=>{F(!0);try{await te.callRawAPI(v,{courts:[k]},"POST"),await ce(te,{user_id:Ne,activity_type:ue.court_management,action_type:oe.UPDATE,data:k,club_id:n==null?void 0:n.id,description:"Updated court"}),B(d,"Court updated successfully",3e3,"success"),ee(!1),l()}catch(I){console.error(I),B(d,"Error updating court",3e3,"error")}finally{F(!1)}};return e.jsxs("div",{className:"",children:[tt&&e.jsx(Oe,{}),e.jsxs("div",{className:"flex flex-col justify-between gap-2 py-3 md:flex-row",children:[e.jsx("nav",{className:"-mb-px flex space-x-8 border-b border-gray-200",children:["General settings","Court settings","Exceptions"].map(k=>e.jsx("button",{className:`
                whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium
                ${_===k.toLowerCase().replace(" ","-")?"border-primaryBlue text-primaryBlue":"border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"}
              `,onClick:()=>S(k.toLowerCase().replace(" ","-")),children:k},k))}),e.jsx(gt,{title:"Court Management History",emptyMessage:"No court management history found",activityType:ue.court_management,club:n})]}),ot(),e.jsx(it,{}),e.jsx(Nt,{ref:Ee,club:n,onSubmit:dt,isOpen:J,onClose:()=>Y(!1),title:"Edit club settings",onPrimaryAction:ct,submitting:j}),e.jsx(Te,{isOpen:q,onClose:()=>{$(!1),b(null)},title:A?"Edit exception":"Add exception",onPrimaryAction:Pe,primaryButtonText:A?"Save changes":"Add exception",submitting:ae,children:e.jsx(Ct,{initialData:A,onSubmit:Pe,setExceptionName:se,exceptionName:T})}),e.jsx(Ue,{profileSettings:o,club:n,sports:r,courts:u,onSubmit:mt,onClose:()=>W(!1),isEdit:!1,isOpen:U,title:"Add new court",showFooter:!1}),e.jsx(Ve,{selectedCourt:Z,selectedTimes:V,setSelectedTimes:z,showTimesAvailableModal:w,setShowTimesAvailableModal:M}),e.jsx(Ue,{onSubmit:ut,isEdit:!0,initialData:ye,sports:r,courts:u,profileSettings:o,onClose:()=>{ee(!1),de(null)},isOpen:ie,title:"Edit court",primaryButtonText:"Save changes",submitting:f,showFooter:!1}),e.jsx(Ve,{showTimesAvailableModal:D,setShowTimesAvailableModal:Q,selectedCourt:re,selectedTimes:V,setSelectedTimes:z,onSave:async k=>{try{const I={courts:[{court_id:re.id,availability:k}]};await te.callRawAPI(v,I,"POST"),B(d,"Times updated successfully",3e3,"success"),l()}catch(I){console.error(I),B(d,"Error updating times",3e3,"error")}}}),e.jsx(qe,{isOpen:et,onClose:()=>{_e(!1),ke(null)},title:"Delete court",loading:we,onDelete:rt,message:"Are you sure you want to delete this court?"})]})}export{Dt as C};
