import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{u as a,f as c,L as t}from"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const u=()=>{const n=a(),r=c(),o=s=>{const i=document.getElementById(s);i?i.scrollIntoView({behavior:"smooth"}):n.pathname!=="/"&&r("/#"+s)};return e.jsxs("footer",{className:"bg-gray-50",children:[e.jsx("div",{className:"container mx-auto max-w-7xl px-6 py-16",children:e.jsxs("div",{className:"grid grid-cols-1 gap-12 md:grid-cols-4",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"mr-3 flex h-12 w-12 items-center justify-center rounded-full bg-white p-2",children:e.jsx("span",{className:"font-ubuntu text-lg font-bold text-[#005954]",children:"CM"})}),e.jsx("span",{className:"font-ubuntu text-xl font-medium",children:"Court Matchup"})]}),e.jsx("p",{className:"font-inter ",children:"There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some formed humour ."})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"font-ubuntu text-lg font-medium",children:"Contact Info"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"font-inter flex items-start ",children:e.jsxs("span",{children:["123 Main Street",e.jsx("br",{}),"Anytown, CA 12345, USA"]})}),e.jsx("p",{className:"font-inter flex items-center ",children:"<EMAIL>"}),e.jsx("p",{className:"font-inter flex items-center ",children:"(000) 000-0000"})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"font-ubuntu text-lg font-medium",children:"Quick Links"}),e.jsxs("ul",{className:"space-y-3",children:[e.jsx("li",{children:e.jsx(t,{to:"/",className:"font-inter  transition-colors hover:text-white",children:"Home"})}),e.jsx("li",{children:e.jsx(t,{to:"/about",className:"font-inter  transition-colors hover:text-white",children:"About Us"})}),e.jsx("li",{children:e.jsx("button",{onClick:()=>o("faq"),className:"font-inter text-left transition-colors hover:text-white",children:"FAQ"})}),e.jsx("li",{children:e.jsx(t,{to:"/services",className:"font-inter  transition-colors hover:text-white",children:"Services"})}),e.jsx("li",{children:e.jsx(t,{to:"/contact",className:"font-inter  transition-colors hover:text-white",children:"Contact"})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h3",{className:"font-ubuntu text-lg font-medium",children:"Ready to Start?"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"font-inter mb-5",children:"Experience the power of Court Matchup for your club."}),e.jsx(t,{to:"/contact",children:e.jsx("button",{className:"font-ubuntu w-full rounded-full bg-[#005954] px-6 py-3 text-white transition-all hover:bg-[#004a45]",children:"Book a Demo"})})]})]})]})}),e.jsx("div",{className:"border-t border-white/10",children:e.jsx("div",{className:"container mx-auto px-6 py-6",children:e.jsxs("div",{className:"flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0",children:[e.jsx("p",{className:"font-inter text-center text-sm  md:text-left",children:"Copyright © 2024 Court Matchup. All rights reserved."}),e.jsxs("div",{className:"flex flex-wrap justify-center space-x-6 md:justify-end",children:[e.jsx(t,{to:"/terms-and-conditions",className:"font-inter text-sm  transition-colors hover:text-white",children:"Terms & Conditions"}),e.jsx(t,{to:"/privacy-policy",className:"font-inter text-sm  transition-colors hover:text-white",children:"Privacy Policy"}),e.jsx(t,{to:"/security-policy",className:"font-inter text-sm  transition-colors hover:text-white",children:"Security"})]})]})})})]})};export{u as PublicFooter,u as default};
