import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as Le,r as s,b as be,k as Ye,L as Oe}from"./vendor-851db8c1.js";import{G as Se,A as Ve,u as Ne,aH as Ge,d as W,aD as as,ad as we,W as V,f as Pe,aS as Ue,M as Ce,T as De,t as ns,aI as qe,b as Je,c as Ze,e as Qe,aq as rs,h as We,aQ as is,aR as ls}from"./index-08a5dc5b.js";import{c as ze,e as os}from"./index.esm-3f8dc7b8.js";import{B as Me}from"./BottomDrawer-eee99403.js";import{A as cs}from"./AddPlayers-29016151.js";import{B as Ke}from"./BackButton-11ba52b2.js";import{L as Ee}from"./ReservationSummary-9dabecd7.js";import{g as Xe}from"./customThresholdUtils-f40b07d5.js";import{f as ye}from"./date-fns-cca0f4f7.js";import{T as es}from"./TimeSlots-6883d5e7.js";import{C as ss}from"./Calendar-35bce269.js";import{S as ts}from"./SportTypeSelection-5dc32d74.js";import{C as He}from"./CalendarIcon-b3488133.js";import{h as ds}from"./moment-a9aaa855.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./index.esm-09a3a6b8.js";import"./react-tooltip-7a26650a.js";import"./ChevronLeftIcon-e5eecf9c.js";import"./ChevronRightIcon-efb4c46c.js";import"./SelectionOptionsCard-30c39f7f.js";import"./SelectionOption-658322e6.js";let Re=new Ce,ms=new De;function us({isOpen:h,onClose:x,selectedDate:m,players:$,sports:I,selectedSport:P,groups:M,coach:b,selectedTimes:d,selectedType:E,selectedSubType:g,userProfile:j}){var ve,N;Le();const[T,L]=s.useState(""),[C,t]=s.useState([]),[J,S]=s.useState(!1),[B,i]=s.useState(!1),[w,k]=s.useState(4),[Z,G]=s.useState(null),[f,O]=s.useState(1),[n,z]=s.useState([]),[H,K]=s.useState(null),[pe,ie]=s.useState(!1),[ae,de]=s.useState(null),{dispatch:me}=s.useContext(Se);s.useContext(Ve);const[ee,ue]=s.useState(null),[le,oe]=s.useState(null),[xe,v]=s.useState(null),[r,y]=s.useState(null),[R,c]=s.useState(!1),[o,A]=s.useState({isOpen:!1,title:"",message:"",actionButtonText:"",actionButtonLink:"",type:"warning"}),{user_subscription:U,user_permissions:Q,club:F}=Ne(),ne=localStorage.getItem("user"),re=d.reduce((l,u)=>{const _=new Date(`2000/01/01 ${u.from}`),je=(new Date(`2000/01/01 ${u.until}`)-_)/(1e3*60*60);return l+je},0),p=Xe(F==null?void 0:F.custom_request_threshold,P,E,g,4,I),q=Ge({hourlyRate:b==null?void 0:b.hourly_rate,hours:re,playerCount:C.length,feeSettings:F==null?void 0:F.fee_settings});be.useEffect(()=>{C.length>p&&(console.log(`Clearing selected players: current ${C.length} exceeds new threshold ${p}`),t([]),k(1),W(me,`Player selection cleared. New maximum is ${p} players. Please select players again.`,4e3,"warning"))},[p]);const _e=()=>{k(l=>Math.min(l+1,p))},fe=()=>{k(l=>Math.max(l-1,0))};async function a(){var l;try{ie(!0);const{data:u,limit:_,error:Y,message:je}=await Re.getCustomerStripeCards();if(Y&&W(me,je,5e3,"error"),!u)return;const ce=(l=u==null?void 0:u.data)==null?void 0:l.find(ke=>{var Be,Fe;return ke.id===((Fe=(Be=u==null?void 0:u.data[0])==null?void 0:Be.customer)==null?void 0:Fe.default_source)});de(ce)}catch(u){console.error("ERROR",u),W(me,u.message,5e3,"error"),ns(dispatch,u.code)}finally{ie(!1)}}async function D(){try{const l=await Re.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:q.total},"POST");if(l.error)throw new Error(l.message||"Failed to create payment intent");v(l.client_secret),y(l.payment_intent)}catch(l){console.error(l),A({isOpen:!0,title:"Payment Error",message:l.message==="Something went wrong"?"Unable to process payment at this time. Please try again.":l.message||"Error creating payment intent",type:"error"})}finally{c(!1)}}const X=async()=>{if(!(U!=null&&U.planId)){A({isOpen:!0,title:"Subscription Required",message:"Please subscribe to a membership plan to book lessons with coaches",actionButtonText:"View Membership Plans",actionButtonLink:"/user/membership/buy",type:"warning"});return}if(!(Q!=null&&Q.allowCoach)){A({isOpen:!0,title:"Plan Upgrade Required",message:`Your current plan (${Q==null?void 0:Q.planName}) does not include coach lessons. Please upgrade your plan.`,actionButtonText:"Upgrade Plan",actionButtonLink:"/user/membership/buy",type:"error"});return}if(!C.length){A({isOpen:!0,title:"Players Required",message:"Please select at least one player",type:"warning"});return}const{duration:l,start_time:u,end_time:_}=we(d);S(!0);try{const Y=ye(new Date(m),"yyyy-MM-dd"),je={sport_id:P,type:E,sub_type:g,date:Y,start_time:u,end_time:_,duration:l,court_id:1,price:q.total,coach_fee:q.coachFee,service_fee:q.serviceFee,reservation_type:qe.lesson,player_ids:C.map(ke=>ke.id),primary_player_id:(H==null?void 0:H.id)||(j==null?void 0:j.id),buddy_details:null,payment_status:0,payment_intent:null,coach_id:b==null?void 0:b.id},ce=await Re.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",je,"POST");Re.setTable("activity_logs"),await Re.callRestAPI({user_id:ne,activity_type:Je.lesson,action_type:Ze.CREATE,data:JSON.stringify(je),club_id:F==null?void 0:F.id,description:"Created a lesson reservation"},"POST"),ce.error||(oe(ce.reservation_id),ue(ce.booking_id),await D(),O(2))}catch(Y){console.error("ERROR",Y),A({isOpen:!0,title:"Reservation Error",message:Y.message||"Error creating lesson reservation",type:"error"})}finally{S(!1)}},se=async()=>{try{const l=parseInt(ne);if(!ne||isNaN(l)){console.error("Invalid user_id for fetching family members:",ne);return}const u=await ms.getList("user",{filter:[`guardian,eq,${l}`,"role,cs,user"]});z(u.list)}catch(l){console.error("Error fetching family members:",l)}},te=l=>{const u=l.value||l;(u==null?void 0:u.id)!==(H==null?void 0:H.id)&&(K(u),t(_=>{const Y=_.filter(ce=>ce.id!==(H==null?void 0:H.id));if(Y.some(ce=>ce.id===u.id)){const ce=Y.filter(ke=>ke.id!==u.id);return[u,...ce]}else return[u,...Y]}))},ge=l=>{t(u=>u.some(Y=>Y.id===l.id)?u.filter(Y=>Y.id!==l.id):[...u,l])};be.useEffect(()=>{a(),se()},[]),be.useEffect(()=>{j&&!H&&K(j)},[j,H]);const he=F!=null&&F.lesson_description?JSON.parse(F==null?void 0:F.lesson_description):{reservation_description:"",payment_description:""};return e.jsxs(e.Fragment,{children:[e.jsx(as,{isOpen:o.isOpen,onClose:()=>A({...o,isOpen:!1}),title:o.title,message:o.message,actionButtonText:o.actionButtonText,actionButtonLink:o.actionButtonLink,type:o.type}),e.jsx(Me,{isOpen:h,onClose:x,title:"Reservation detail",children:e.jsxs("div",{className:"mx-auto max-w-7xl space-y-6",children:[e.jsx(Ke,{onBack:()=>{f===2?O(1):x()}}),f===1&&e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx(Ee,{selectedSport:P,sports:I,selectedType:E,selectedSubType:g,selectedDate:m,selectedTimes:d,playersNeeded:w,selectedCoach:b,timeRange:we(d)}),e.jsx("div",{className:"space-y-4",children:e.jsx(cs,{searchQuery:T,setSearchQuery:L,selectedPlayers:C,setSelectedPlayers:t,onPlayerToggle:ge,players:$,groups:M,selectedGroup:Z,isFindBuddyEnabled:B,setIsFindBuddyEnabled:i,playersNeeded:w,handleIncrement:_e,handleDecrement:fe,showPlayersNeeded:!1,showAddReservationToFindBuddy:!1,maximumPlayers:p,familyMembers:n,currentUser:H,onCurrentUserChange:te,userProfile:j})}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-2  ",children:[e.jsxs("div",{className:"divide-y",children:[e.jsxs("div",{className:"py-3",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["PLAYERS (",C.length,")"]}),e.jsx("div",{className:"mt-1",children:C.map(l=>e.jsxs("div",{className:"text-sm",children:[l.first_name," ",l.last_name]},l.id))})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"COACH"}),e.jsx("div",{className:"mt-1",children:e.jsxs("div",{className:"text-sm",children:[(ve=b==null?void 0:b.user)==null?void 0:ve.first_name," ",(N=b==null?void 0:b.user)==null?void 0:N.last_name]})})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-1",children:["Coach fee",e.jsxs("span",{className:"text-xs text-gray-500",children:["(",V(b==null?void 0:b.hourly_rate),"/hr ×"," ",re,"hr × ",C.length," ","players)"]})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{children:V(q.coachFee)})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Service fee"}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsx("span",{children:V(q.serviceFee)})})]})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:V(q.total)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 8 2.33325C8.10005 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(Pe,{loading:J,onClick:X,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:he.reservation_description}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:"(You will not be charged yet)"})]})})]})]}),f===2&&e.jsx("div",{children:e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx(Ee,{selectedSport:P,sports:I,selectedType:E,selectedSubType:g,selectedDate:m,selectedTimes:d,playersNeeded:w,timeRange:we(d),selectedCoach:b}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("span",{className:"flex items-center gap-1",children:["Coach fee",e.jsxs("span",{className:"text-xs text-gray-500",children:["(",V(b==null?void 0:b.hourly_rate),"/hr ×"," ",re,"hr × ",C.length," ","players)"]})]}),e.jsx("span",{children:V(q.coachFee)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:V(q.serviceFee)})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:V(q.total)})]}),e.jsxs("div",{children:[e.jsx(Ue,{user:j,bookingId:ee,reservationId:le,clientSecret:xe,paymentIntent:r,navigateRoute:`/user/payment-success/${le}?type=lesson`}),e.jsx("p",{className:"text-sm text-gray-500",children:he.payment_description})]}),e.jsx("p",{className:"text-sm text-gray-500",children:"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Curabitur erat nisi, porta a ipsum eu, accumsan dapibus enim. Donec ultrices congue libero in convallis. Cras condimentum felis eget dignissim tincidunt."})]})]})})]})]})})]})})]})}function xs({isOpen:h,onClose:x,coach:m,players:$,selectedLocation:I,selectedSport:P,sports:M,groups:b,club:d,selectedType:E,selectedSubType:g,userProfile:j,coachAvailability:T}){var ee,ue,le,oe,xe;const[L,C]=s.useState(new Date),[t,J]=s.useState(new Date),[S,B]=s.useState({from:null,until:null}),[i,w]=s.useState(!1),[k,Z]=s.useState([]),{dispatch:G}=s.useContext(Se),{club_membership:f,user_subscription:O}=Ne(),n=s.useMemo(()=>!(O!=null&&O.planId)||!(f!=null&&f.length)?null:f.find(v=>v.plan_id===O.planId),[O,f]),z=s.useMemo(()=>{var R,c;if(((R=n==null?void 0:n.advance_booking_enabled)==null?void 0:R.lesson)===!1){const o=new Date;return o.setFullYear(o.getFullYear()+10),o}const v=((c=n==null?void 0:n.advance_booking_days)==null?void 0:c.lesson)||10,r=new Date,y=new Date;return y.setDate(r.getDate()+v),y},[n]),H=v=>{if(!(m!=null&&m.availability)||!L)return!1;const r=ye(L,"EEEE").toLowerCase(),R=(Array.isArray(m.availability)?m.availability:Object.entries(m.availability).map(([o,A])=>({day:o,timeslots:A}))).find(o=>o.day===r);if(!R)return!1;const c=`${v.time24}:00`;return R.timeslots.includes(c)},K=()=>{J(new Date(t.setMonth(t.getMonth()-1)))},pe=()=>{J(new Date(t.setMonth(t.getMonth()+1)))},ie=()=>{var v,r;if(L>z&&((v=n==null?void 0:n.advance_booking_enabled)==null?void 0:v.lesson)!==!1){const y=((r=n==null?void 0:n.advance_booking_days)==null?void 0:r.lesson)||10;W(G,`Your membership plan only allows booking ${y} days in advance`,3e3,"warning");return}w(!0)},ae=()=>{w(!1)},de=v=>{console.log("Selected players:",v.selectedPlayers)},me=v=>{Z([{from:v.from,until:v.until}])};return e.jsxs(e.Fragment,{children:[e.jsx(Me,{isOpen:h&&!i,onClose:x,title:`${(ee=m==null?void 0:m.user)==null?void 0:ee.first_name}'s availability`,children:e.jsx("div",{className:"mx-auto max-w-3xl space-y-6 overflow-hidden",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsxs("div",{children:[((ue=n==null?void 0:n.advance_booking_enabled)==null?void 0:ue.lesson)===!1?e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:"You can reserve a lesson for any future date."}):e.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["Your ",(n==null?void 0:n.plan_name)||"current"," membership plan allows you to book lessons up to"," ",((le=n==null?void 0:n.advance_booking_days)==null?void 0:le.lesson)||10," ","days in advance."]}),e.jsx(ss,{currentMonth:t,selectedDate:L,onDateSelect:v=>{var r,y;if(v>z&&((r=n==null?void 0:n.advance_booking_enabled)==null?void 0:r.lesson)!==!1){const R=((y=n==null?void 0:n.advance_booking_days)==null?void 0:y.lesson)||10;W(G,`Your membership plan only allows booking ${R} days in advance`,3e3,"warning");return}C(v)},onPreviousMonth:K,onNextMonth:pe,daysOff:d!=null&&d.days_off?JSON.parse(d.days_off):[],coachAvailability:T,allowPastDates:!1,minDate:new Date,maxDate:z,disabledDateMessage:((oe=n==null?void 0:n.advance_booking_enabled)==null?void 0:oe.lesson)===!1?"You can book for any future date":`Your membership plan only allows booking ${((xe=n==null?void 0:n.advance_booking_days)==null?void 0:xe.lesson)||10} days in advance`})]})}),e.jsx(es,{selectedDate:L,onTimeClick:me,onNext:ie,nextButtonText:"Next: Players",startHour:0,endHour:24,interval:30,clubExceptions:d!=null&&d.exceptions?JSON.parse(d.exceptions):[],isTimeSlotAvailable:H,timeRange:k,clubTimes:d!=null&&d.times?JSON.parse(d.times):[],coachAvailability:T,height:"h-full"})]})})}),e.jsx(us,{players:$,isOpen:i,onClose:ae,selectedDate:L,timeRange:S,onNext:de,selectedSport:P,selectedLocation:I,sports:M,groups:b,coach:m,club:d,selectedTimes:k,selectedType:E,selectedSubType:g,userProfile:j})]})}function ps({selectedCoach:h,onCheckAvailability:x,loadingAvailability:m,coachPricing:$,selectedSport:I,selectedType:P,selectedSubType:M}){var g,j,T,L,C;if(!h)return null;const d=((t,J,S,B)=>{if(!$||$.length===0)return null;const i=$.filter(n=>n.coach_id===t);if(i.length===0)return null;const w=parseInt(J),k=S||"",Z=B||"";let G=i.find(n=>parseInt(n.sport_id)===w&&(n.type||"")===k&&(n.sub_type||"")===Z);if(G)return G.price;if(k){let n=i.find(z=>parseInt(z.sport_id)===w&&(z.type||"")===k&&(!z.sub_type||z.sub_type===""));if(n)return n.price}let f=i.find(n=>parseInt(n.sport_id)===w&&(!n.type||n.type==="")&&(!n.sub_type||n.sub_type===""));if(f)return f.price;let O=i.find(n=>(!n.sport_id||n.sport_id===0)&&(!n.type||n.type==="")&&(!n.sub_type||n.sub_type===""));return O?O.price:null})(h.id,I,P,M),E=d!==null?d:h.hourly_rate;return e.jsx("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Coach profile"}),e.jsx(Pe,{onClick:x,className:"rounded-xl bg-blue-900 px-3 py-2 text-sm text-white hover:bg-blue-800",loading:m,children:m?"Checking...":"Check availability"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:h.photo||((g=h.user)==null?void 0:g.photo)||"/default-avatar.png",alt:`${(j=h.user)==null?void 0:j.first_name} ${(T=h.user)==null?void 0:T.last_name}`,className:"h-12 w-12 rounded-lg object-cover"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium capitalize",children:[(L=h.user)==null?void 0:L.first_name," ",(C=h.user)==null?void 0:C.last_name]}),e.jsxs("p",{className:"text-sm text-gray-600",children:[V(E),"/h"]})]})]}),h.bio&&e.jsx("div",{className:"space-y-2",children:e.jsx("p",{className:"text-gray-600",children:h.bio})})]})})}let Ie=new Ce;function hs({sports:h,coaches:x,players:m,groups:$,club:I,userProfile:P}){var le,oe,xe,v;const[M,b]=s.useState("Tennis"),[d,E]=s.useState("Indoors"),[g,j]=s.useState(null),[T,L]=s.useState(""),[C,t]=s.useState(!0),[J,S]=s.useState(!1),[B,i]=s.useState(null),[w,k]=s.useState(null),[Z,G]=s.useState(null),[f,O]=s.useState(!1),[n,z]=s.useState(null),[H]=Ye(),K=H.get("coach"),{user_permissions:pe,courts:ie}=Ne(),ae=h==null?void 0:h.find(r=>r.id===M),de=async()=>{O(!0);try{const r=await Ie.callRawAPI(`/v3/api/custom/courtmatchup/user/coach/availability/${g.id}`,{},"GET");z(r.availability),S(!0),console.log(r)}catch(r){console.log(r)}finally{O(!1)}};async function me(r){try{Ie.setTable("coach_sports");const y=await Ie.callRestAPI({filter:[`coach_id,eq,${r}`],join:["sports|sport_id"]},"GETALL");console.log("coach pricing",y),G(y.list||[])}catch(y){console.log(y),G([])}}const ee=(r,y,R,c)=>{if(!Z||Z.length===0)return null;const o=Z.filter(p=>p.coach_id===r);if(o.length===0)return null;const A=parseInt(y),U=R||"",Q=c||"";let F=o.find(p=>parseInt(p.sport_id)===A&&(p.type||"")===U&&(p.sub_type||"")===Q);if(F)return F.price;if(U){let p=o.find(q=>parseInt(q.sport_id)===A&&(q.type||"")===U&&(!q.sub_type||q.sub_type===""));if(p)return p.price}let ne=o.find(p=>parseInt(p.sport_id)===A&&(!p.type||p.type==="")&&(!p.sub_type||p.sub_type===""));if(ne)return ne.price;let re=o.find(p=>(!p.sport_id||p.sport_id===0)&&(!p.type||p.type==="")&&(!p.sub_type||p.sub_type===""));return re?re.price:null};s.useEffect(()=>{if(K){const r=x.find(y=>y.id===parseInt(K));j(r)}},[K,x]);const ue=r=>{j(r),me(r.id)};return s.useEffect(()=>{const r=async()=>{if(x.length>0)try{const y=x.map(c=>c.id);Ie.setTable("coach_sports");const R=await Ie.callRestAPI({filter:[`coach_id,in,${y.join(",")}`],join:["sports|sport_id"]},"GETALL");G(R.list||[])}catch(y){console.log(y),G([])}};M&&x.length>0&&r()},[M,B,w,x]),e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(ts,{sports:h,userPermissions:pe,courts:ie,filterMode:"lesson",onSelectionChange:({sport:r,type:y,subType:R})=>{b(r),i(y),k(R)}}),M&&(!((le=ae==null?void 0:ae.sport_types)!=null&&le.length)||B!==null&&(w!==null||!((v=(xe=(oe=ae==null?void 0:ae.sport_types)==null?void 0:oe.find(r=>r.type===B))==null?void 0:xe.subtype)!=null&&v.length)))?e.jsxs("div",{className:"max-h-fit space-y-6 rounded-xl bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search by name",value:T,onChange:r=>L(r.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"}),e.jsx(Qe,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("button",{onClick:()=>t(!C),className:"absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"A-Z"}),e.jsx(ze,{className:`text-xs transition-transform ${C?"":"rotate-180"}`})]})})]}),e.jsxs("div",{className:"max-h-[400px] space-y-4 overflow-y-auto",children:[x.length>0&&x.filter(r=>{var R,c;return`${(R=r.user)==null?void 0:R.first_name} ${(c=r.user)==null?void 0:c.last_name}`.toLowerCase().includes(T.toLowerCase())}).sort((r,y)=>{var o,A,U,Q;const R=`${(o=r.user)==null?void 0:o.first_name} ${(A=r.user)==null?void 0:A.last_name}`.toLowerCase(),c=`${(U=y.user)==null?void 0:U.first_name} ${(Q=y.user)==null?void 0:Q.last_name}`.toLowerCase();return C?R.localeCompare(c):c.localeCompare(R)}).map(r=>{var y,R,c,o,A;return e.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${(g==null?void 0:g.id)===r.id?"border-primaryBlue bg-blue-50":"border-gray-100 hover:bg-gray-50"}`,onClick:()=>ue(r),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:((y=r.user)==null?void 0:y.photo)||(r==null?void 0:r.photo)||"/default-avatar.png",alt:`${(R=r.user)==null?void 0:R.first_name} ${(c=r.user)==null?void 0:c.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("span",{className:"font-medium capitalize",children:[(o=r.user)==null?void 0:o.first_name," ",(A=r.user)==null?void 0:A.last_name]})})]}),e.jsx("div",{className:"flex flex-col items-end",children:(()=>{const U=ee(r.id,M,B,w);return U!==null?e.jsxs("span",{className:"text-gray-600",children:[V(U),"/h"]}):e.jsxs("span",{className:"text-gray-600",children:[V(r.hourly_rate),"/h"]})})()})]},r.id)}),x.length===0&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No coaches found"})]})]}):e.jsx("div",{className:"col-span-2 flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Please select a sport"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose a sport, type, and sub-type to view available coaches"})]})}),e.jsx(ps,{selectedCoach:g,onCheckAvailability:de,loadingAvailability:f,coachPricing:Z,selectedSport:M,selectedType:B,selectedSubType:w})]})})}),e.jsx(xs,{isOpen:J,coachAvailability:n,onClose:()=>S(!1),selectedSport:M,selectedLocation:d,sports:h,groups:$,club:I,players:m,selectedType:B,selectedSubType:w,userProfile:P,coach:{...g,availability:JSON.parse((g==null?void 0:g.availability)||"{}")}})]})}let gs=new Ce;function ys({userProfile:h}){const[x,m]=s.useState(null),[$,I]=s.useState(!1),[P,M]=s.useState([]),{dispatch:b}=s.useContext(Se),{dispatch:d}=s.useContext(Ve),[E,g]=s.useState([]),[j,T]=s.useState(!1),{sports:L}=Ne();s.useEffect(()=>{const i=()=>{T(window.innerWidth<768)};return i(),window.addEventListener("resize",i),()=>window.removeEventListener("resize",i)},[]);async function C(){try{I(!0);const i=await gs.callRawAPI("/v3/api/custom/courtmatchup/user/reservations?custom_request=1",{},"GET");M(i.list)}catch(i){console.error(i)}finally{I(!1)}}s.useEffect(()=>{C()},[]),s.useEffect(()=>{P.length>0&&!x&&(m(P[0]),B(P[0]))},[P]);const t=i=>{switch(i){case 0:return"border-gray-300 border bg-gray-50 text-gray-600";case 1:return"border-green-300 border bg-green-50 text-green-600";case 2:return"border-red-300 border bg-red-50 text-red-600";default:return"border-gray-300 border bg-gray-50 text-gray-600"}},J=i=>{switch(i){case 0:return"Pending";case 1:return"Approved";case 2:return"Declined";default:return"Pending"}},S=(i,w,k)=>{const Z=L.find(f=>f.id===i);return`${Z?Z.name:"Unknown Sport"} • ${w} • ${k}`},B=async i=>{I(!0);try{const w=JSON.parse(i.player_ids),k=await rs(b,d,"user",w);m(i),g(k.list)}catch(w){console.error(w),W(b,w.message,5e3,"error")}finally{I(!1)}};return e.jsxs("div",{className:"relative flex h-full flex-col gap-3 p-2 md:flex-row",children:[$&&e.jsx(We,{}),e.jsx("div",{className:`${j?"w-full":"w-full md:w-1/3"} max-h-[300px] space-y-4 overflow-y-auto p-2 md:max-h-[600px]`,children:P.length>0?P.map(i=>e.jsxs("div",{onClick:()=>B(i),className:`cursor-pointer rounded-lg bg-white p-4 shadow-sm transition-shadow hover:shadow-md ${(x==null?void 0:x.reservation_id)===i.reservation_id?"ring-2 ring-blue-500":""}`,children:[e.jsx("div",{className:"mb-2 flex items-center justify-between",children:e.jsx("span",{className:`rounded-full px-3 py-1 text-xs font-medium ${t(i.booking_status)}`,children:J(i.booking_status)})}),e.jsxs("div",{className:"mb-2 flex items-center text-sm text-gray-600",children:[e.jsx("span",{className:"mr-1",children:e.jsx(He,{className:"h-4 w-4"})}),e.jsxs("div",{className:"text-xs md:text-sm",children:[ye(new Date(`${i.booking_date}T${i.start_time}`),"EEEE (MMM dd) • h:mmaaa")," ","-",ye(new Date(`${i.booking_date}T${i.end_time}`),"h:mmaaa")]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-xs font-medium md:text-sm",children:S(i.sport_id,i.sub_type,i.type)}),e.jsxs("div",{className:"flex items-center text-xs text-gray-600 md:text-sm",children:[e.jsx(os,{className:"mr-1"}),e.jsx("span",{children:i.num_players})]})]})]},i.reservation_id)):!$&&e.jsxs("div",{className:"flex h-full w-full flex-col items-center justify-center rounded-lg bg-white p-6 text-center shadow-sm",children:[e.jsx("div",{className:"mb-4 text-gray-400",children:e.jsx(He,{className:"mx-auto h-12 w-12"})}),e.jsx("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Custom Requests"}),e.jsx("p",{className:"text-sm text-gray-600",children:"You don't have any custom requests at the moment."})]})}),x&&P.length>0&&e.jsxs("div",{className:`${j?"mt-4 w-full":"w-full md:w-2/3"} rounded-lg bg-white shadow-sm md:sticky md:top-20`,children:[e.jsx("div",{className:"rounded-xl bg-gray-100 p-4 text-center",children:e.jsx("p",{className:"text-sm md:text-base",children:"Custom request details"})}),e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-2 rounded-xl bg-gray-100 p-2 text-xs sm:grid-cols-2 md:grid-cols-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"STATUS"}),e.jsx("span",{className:`rounded-full px-3 py-1 text-xs font-medium ${t(x.booking_status)}`,children:J(x.booking_status)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"SPORT"}),e.jsx("p",{className:"text-xs md:text-sm",children:S(x.sport_id,x.sub_type,x.type)})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"REQUEST DATE"}),e.jsxs("p",{className:"text-xs md:text-sm",children:[ye(new Date(`${x.booking_date}T${x.start_time}`),"EEEE (MMM dd) • h:mmaaa")," ","-",ye(new Date(`${x.booking_date}T${x.end_time}`),"h:mmaaa")]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"SUBMITTED ON"}),e.jsx("p",{className:"text-xs md:text-sm",children:ye(new Date(x.reservation_created_at),"MMMM dd, yyyy, (EEEE)")})]}),E.length>0&&e.jsxs("div",{children:[e.jsxs("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:["PLAYERS (",E.length,")"]}),e.jsx("div",{className:"flex flex-col gap-2 rounded-lg border border-gray-300 bg-gray-100 p-3",children:E.map(i=>e.jsxs("div",{className:"rounded-full text-xs md:text-sm",children:[i.first_name," ",i.last_name]},i.id))})]}),x.notes&&e.jsxs("div",{children:[e.jsx("h3",{className:"mb-2 text-sm font-medium uppercase text-gray-500",children:"REQUEST"}),e.jsx("p",{className:"text-xs text-gray-700 md:text-sm",children:x.notes})]})]})]})]})}const Te=new Ce,fs=new De;function vs({coaches:h,onClose:x,selectedDate:m,selectedSport:$,sports:I,players:P,groups:M,isOpen:b,selectedType:d,selectedSubType:E,selectedTimes:g,userProfile:j,coachPricing:T}){const[L,C]=s.useState(!0),[t,J]=s.useState(null),[S,B]=s.useState(""),[i,w]=s.useState(1),[k,Z]=s.useState([]),[G,f]=s.useState(1);s.useState(!1),s.useState(null);const[O,n]=s.useState(!1),[z,H]=s.useState([]),[K,pe]=s.useState(null);Le();const{duration:ie,end_time:ae,start_time:de}=we(g),[me,ee]=s.useState(null),[ue,le]=s.useState(null),[oe,xe]=s.useState(null),{dispatch:v}=s.useContext(Se),[r,y]=s.useState(null),{user_subscription:R,user_permissions:c,club:o}=Ne(),A=localStorage.getItem("user");console.log("selectedTimes",g);const U=(a,D,X,se)=>{if(!T||T.length===0)return null;const te=T.filter(_=>_.coach_id===a);if(te.length===0)return null;const ge=parseInt(D),he=X||"",ve=se||"";let N=te.find(_=>parseInt(_.sport_id)===ge&&(_.type||"")===he&&(_.sub_type||"")===ve);if(N)return N.price;if(he){let _=te.find(Y=>parseInt(Y.sport_id)===ge&&(Y.type||"")===he&&(!Y.sub_type||Y.sub_type===""));if(_)return _.price}let l=te.find(_=>parseInt(_.sport_id)===ge&&(!_.type||_.type==="")&&(!_.sub_type||_.sub_type===""));if(l)return l.price;let u=te.find(_=>(!_.sport_id||_.sport_id===0)&&(!_.type||_.type==="")&&(!_.sub_type||_.sub_type===""));return u?u.price:null},Q=Xe(o==null?void 0:o.custom_request_threshold,$,d,E,4,I);be.useEffect(()=>{k.length>Q&&(console.log(`Clearing selected players: current ${k.length} exceeds new threshold ${Q}`),Z([]),f(1),W(v,`Player selection cleared. New maximum is ${Q} players. Please select players again.`,4e3,"warning"))},[Q]);const F=async()=>{try{const a=parseInt(A);if(!A||isNaN(a)){console.error("Invalid user_id for fetching family members:",A);return}const D=await fs.getList("user",{filter:[`guardian,eq,${a}`,"role,cs,user"]});H(D.list)}catch(a){console.error("Error fetching family members:",a)}};be.useEffect(()=>{j&&!K&&pe(j),F()},[j,K]);const ne=g.reduce((a,D)=>{const X=new Date(`2000/01/01 ${D.from}`),te=(new Date(`2000/01/01 ${D.until}`)-X)/(1e3*60*60);return a+te},0),re=()=>{if(!t)return 0;const a=U(t.id,$,d,E);return a!==null?a:t.hourly_rate},p=Ge({hourlyRate:re(),hours:ne,playerCount:k.length,feeSettings:o==null?void 0:o.fee_settings}),q=async()=>{if(!(R!=null&&R.planId)){W(v,"Please subscribe to a membership plan to book lessons with coaches",3e3,"error");return}if(!(c!=null&&c.allowCoach)){W(v,`Your current plan (${c==null?void 0:c.planName}) does not include coach lessons. Please upgrade your plan.`,3e3,"error");return}if(!$||!m||!g||!k){W(v,"Please select all required fields",3e3,"error");return}n(!0);try{const a=await Te.callRawAPI("/v3/api/custom/courtmatchup/user/reservations/payment-intent/create",{amount:p.total},"POST");if(a.error){W(v,a.message,3e3,"error"),n(!1);return}ee(a.client_secret),le(a.payment_intent);const D=ds(m).format("YYYY-MM-DD"),X={sport_id:$,type:d,sub_type:E,date:D,player_ids:k.map(te=>te.id),primary_player_id:(K==null?void 0:K.id)||(j==null?void 0:j.id),start_time:de,end_time:ae,price:p.total,coach_fee:p.coachFee,service_fee:p.serviceFee,duration:ie,coach_id:t.id,payment_intent:a.payment_intent,reservation_type:qe.lesson},se=await Te.callRawAPI("/v3/api/custom/courtmatchup/user/reservations",X,"POST");Te.setTable("activity_logs"),await Te.callRestAPI({user_id:A,activity_type:Je.lesson,action_type:Ze.CREATE,data:JSON.stringify(X),club_id:o==null?void 0:o.id,description:"Created a lesson reservation"},"POST"),se.error||(W(v,"Reservation created successfully",3e3,"success"),y(se.reservation_id),xe(se.booking_id),w(3))}catch(a){console.error(a),W(v,a.message||"Error creating reservation",3e3,"error")}finally{n(!1)}},_e=()=>{t&&w(2)},fe=o!=null&&o.lesson_description?JSON.parse(o==null?void 0:o.lesson_description):{reservation_description:"",payment_description:""};return e.jsx(Me,{onClose:x,isOpen:b,title:i===1?"Select Coach":"Reservation detail",children:e.jsxs("div",{className:"relative mx-auto h-[90vh] w-full max-w-7xl overflow-y-auto rounded-lg ",children:[e.jsx(Ke,{onBack:()=>{i===1?w(2):x()}}),i===1&&e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsxs("div",{className:"max-h-fit space-y-6 rounded-xl bg-white p-4 shadow-5",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Search by name",value:S,onChange:a=>B(a.target.value),className:"w-full rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-blue-500 focus:outline-none"}),e.jsx(Qe,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("button",{onClick:()=>C(!L),className:"absolute right-2 top-1/2 -translate-y-1/2 transform rounded-md border border-gray-300 px-2 py-1 text-sm hover:bg-gray-50",children:e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:"A-Z"}),e.jsx(ze,{className:`text-xs transition-transform ${L?"":"rotate-180"}`})]})})]}),e.jsxs("div",{className:"max-h-[calc(90vh-200px)] space-y-4 overflow-y-auto",children:[h.length>0&&h.filter(a=>`${a==null?void 0:a.first_name} ${a==null?void 0:a.last_name}`.toLowerCase().includes(S.toLowerCase())).sort((a,D)=>{const X=`${a==null?void 0:a.first_name} ${a==null?void 0:a.last_name}`.toLowerCase(),se=`${D==null?void 0:D.first_name} ${D==null?void 0:D.last_name}`.toLowerCase();return L?X.localeCompare(se):se.localeCompare(X)}).map(a=>e.jsxs("div",{className:`flex cursor-pointer items-center justify-between rounded-lg border p-3 ${(t==null?void 0:t.id)===a.id?"border-primaryBlue bg-blue-50":"border-gray-100 hover:bg-gray-50"}`,onClick:()=>J(a),children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:(a==null?void 0:a.photo)||(a==null?void 0:a.photo)||"/default-avatar.png",alt:`${a==null?void 0:a.first_name} ${a==null?void 0:a.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),e.jsx("div",{className:"flex flex-col",children:e.jsxs("span",{className:"font-medium capitalize",children:[a==null?void 0:a.first_name," ",a==null?void 0:a.last_name]})})]}),e.jsx("span",{className:"text-gray-600",children:(()=>{const D=U(a.id,$,d,E),X=D!==null?D:a.hourly_rate;return`${V(X)}/h`})()})]},a.id)),h.length===0&&e.jsx("p",{className:"text-center text-sm text-gray-500",children:"No coaches found"})]})]}),e.jsx("div",{children:t&&e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsxs("div",{className:"space-y-6 p-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("h2",{className:"text-xl font-semibold",children:"Coach Profile"})}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:(t==null?void 0:t.photo)||(t==null?void 0:t.photo)||"/default-avatar.png",alt:`${t==null?void 0:t.first_name} ${t==null?void 0:t.last_name}`,className:"h-16 w-16 rounded-lg object-cover"}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-medium capitalize",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]}),e.jsx("p",{className:"text-lg text-gray-600",children:(()=>{const a=U(t==null?void 0:t.id,$,d,E),D=a!==null?a:t==null?void 0:t.hourly_rate;return`${V(D)}/h`})()})]})]}),(t==null?void 0:t.bio)&&e.jsx("div",{className:"space-y-2",children:e.jsx("p",{className:"text-gray-600",children:t==null?void 0:t.bio})})]}),e.jsx("div",{className:"border-t p-3",children:e.jsxs("button",{onClick:_e,className:"rounded-lg bg-blue-900 px-4 py-2 text-white hover:bg-blue-800",children:["Continue with ",t==null?void 0:t.first_name]})})]})})]}),i===2&&e.jsxs("div",{className:"mx-auto grid max-w-7xl grid-cols-1 gap-4 md:grid-cols-3",children:[e.jsx(Ee,{selectedSport:$,sports:I,selectedType:d,selectedSubType:E,selectedDate:m,timeRange:we(g),playersNeeded:G,selectedCoach:t}),e.jsxs("div",{className:"h-fit rounded-lg bg-white shadow-5",children:[e.jsx("div",{className:"rounded-lg bg-gray-50 p-4 text-center",children:e.jsx("h2",{className:"text-base font-medium",children:"Reserving Details"})}),e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"divide-y",children:[e.jsxs("div",{className:"py-3",children:[e.jsxs("p",{className:"text-sm text-gray-500",children:["PLAYERS (",k.length,")"]}),e.jsx("div",{className:"mt-1",children:k.map(a=>e.jsxs("div",{className:"text-sm",children:[a.first_name," ",a.last_name]},a.id))})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"COACH"}),e.jsx("div",{className:"mt-1",children:e.jsxs("div",{className:"text-sm",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name]})})]}),e.jsxs("div",{className:"py-3",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"FEES"}),e.jsxs("div",{className:"mt-2 flex items-center justify-between",children:[e.jsx("span",{className:"flex items-center gap-1",children:"Coach fee"}),e.jsx("span",{children:V(p.coachFee)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{children:"Service fee"}),e.jsx("span",{children:V(p.serviceFee)})]})]})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{children:"Total"}),e.jsx("span",{className:"font-medium",children:V(p.total)})]}),e.jsx("div",{className:"rounded-lg bg-[#F17B2C] p-3 text-sm text-white",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.49364 2.62152L13.9236 12.1351C13.9737 12.2227 14 12.3221 14 12.4233C14 12.5245 13.9737 12.624 13.9236 12.7116C13.8736 12.7993 13.8017 12.8721 13.715 12.9227C13.6283 12.9733 13.5301 12.9999 13.43 12.9999H2.57C2.46995 12.9999 2.37165 12.9733 2.285 12.9227C2.19835 12.8721 2.12639 12.7993 2.07636 12.7116C2.02634 12.624 2 12.5245 2 12.4233C2 12.3221 2.02634 12.2227 2.07637 12.1351L7.50636 2.62152C7.5564 2.53387 7.62835 2.46109 7.715 2.41049C7.80165 2.35989 7.89995 2.33325 8 2.33325C8.10005 2.33325 8.19835 2.35989 8.285 2.41049C8.37165 2.46109 8.4436 2.53387 8.49364 2.62152ZM7.42998 10.117V11.2702H8.57002V10.117H7.42998ZM7.42998 6.08098V8.96387H8.57002V6.08098H7.42998Z",fill:"white"})}),e.jsx("span",{children:"After reserving, you will have 15 minutes to make the payment."})]})}),e.jsx(Pe,{loading:O,onClick:q,className:"w-full rounded-lg bg-[#1E2841] py-3 text-white hover:bg-[#1E2841]/90",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("span",{children:"Reserve Now"}),e.jsx("span",{className:"text-sm opacity-80",children:"and continue to payment"})]})}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:fe.reservation_description}),e.jsxs("div",{className:"space-y-2 text-center text-sm text-gray-500",children:[e.jsx("p",{children:"(You will not be charged yet)"}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})})]})]}),i===3&&e.jsxs("div",{className:"mx-auto max-w-6xl",children:[e.jsx("div",{className:"rounded-xl bg-[#F17B2C] px-4 py-3 text-white",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",children:e.jsx("path",{d:"M12 8V12M12 16H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),e.jsx("span",{className:"!text-sm",children:"Your session is reserved. You have 15 minutes to complete the payment, otherwise the reservation will be canceled."})]})}),e.jsxs("div",{className:"mt-6 grid grid-cols-1 gap-6 lg:grid-cols-2",children:[e.jsx("div",{className:"space-y-6",children:e.jsx(Ee,{selectedSport:$,sports:I,selectedType:d,selectedSubType:E,selectedDate:m,timeRange:we(g),selectedCoach:t})}),e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"rounded-xl bg-white p-6 shadow-5",children:[e.jsx("h2",{className:"mb-4 text-center text-lg font-medium",children:"Payment details"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Coach fee"}),e.jsxs("span",{children:[V(re()),"/hr × ",ne,"hr × ",k.length," players"]}),e.jsx("span",{children:V(re()*ne*k.length)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-500",children:"Service fee"}),e.jsx("span",{children:V(is(o==null?void 0:o.fee_settings,p.baseAmount))})]}),e.jsxs("div",{className:"flex items-center justify-between border-t pt-4",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:V(p.total)})]}),e.jsx("div",{children:e.jsx(Ue,{user:j,bookingId:oe,reservationId:r,clientSecret:me,paymentIntent:ue,navigateRoute:`/user/payment-success/${r}?type=lesson`})}),e.jsx("p",{className:"text-center text-sm text-gray-500",children:fe.payment_description}),e.jsxs("div",{className:"space-y-4 text-sm text-gray-500",children:[e.jsxs("p",{children:['By clicking "Pay now" you agree to our'," ",e.jsx(Oe,{to:"/terms-and-conditions",target:"_blank",className:"font-medium underline",children:"Terms and Conditions"})," ","and"," ",e.jsx(Oe,{to:"/privacy-policy",target:"_blank",className:"font-medium underline",children:"Privacy Policy"}),". All sales are final unless stated otherwise."]}),e.jsxs("p",{children:["For any issues, please contact our support team at"," ",e.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium underline",children:"<EMAIL>"})]})]})]})]})})]})]})]})})}let $e=new Ce;new De;function js({sports:h=[],players:x=[],club:m,userProfile:$}){var _e,fe,a,D,X,se,te,ge,he,ve;const[I,P]=s.useState(null),[M,b]=s.useState(null),[d,E]=s.useState(null),[g,j]=s.useState(new Date),[T,L]=s.useState([]),[C,t]=s.useState(null),[J,S]=s.useState(null),[B,i]=s.useState([]),[w,k]=s.useState(0),[Z,G]=s.useState(0),[f,O]=s.useState(!1),[n,z]=s.useState([]),[H,K]=s.useState([]),[pe,ie]=s.useState(!1),[ae,de]=s.useState([]),{state:me,dispatch:ee}=s.useContext(Se),[ue,le]=s.useState({from:null,until:null}),[oe,xe]=s.useState("main"),{club_membership:v,user_subscription:r,user_permissions:y,courts:R}=Ne(),c=s.useMemo(()=>!(r!=null&&r.planId)||!(v!=null&&v.length)?null:v.find(N=>N.plan_id===r.planId),[r,v]),o=s.useMemo(()=>{var _;const N=((_=c==null?void 0:c.advance_booking_days)==null?void 0:_.lesson)||10,l=new Date,u=new Date;return u.setDate(l.getDate()+N),u},[c]),{start_time:A,end_time:U}=we(n),Q=async()=>{try{const N=await $e.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");i(N.groups)}catch(N){console.error(N)}};s.useEffect(()=>{Q()},[]),s.useEffect(()=>{},[oe]);const F=()=>{j(new Date(g.setMonth(g.getMonth()-1)))},ne=()=>{j(new Date(g.setMonth(g.getMonth()+1)))},re=N=>{z([{from:N.from,until:N.until}])};s.useEffect(()=>{w&&(T!=null&&T.length)?G(w*(T==null?void 0:T.length)):G(w)},[w,T]);const p=async N=>{try{$e.setTable("coach_sports");const l=await $e.callRestAPI({filter:[`coach_id,in,${N.join(",")}`],join:["sports|sport_id"]},"GETALL");de(l.list||[])}catch(l){console.log(l),de([])}},q=async()=>{O(!0);const N={start_time:A,sport_id:I,end_time:U,date:ye(new Date(d),"yyyy-MM-dd")};try{const l=await $e.callRawAPI("/v3/api/custom/courtmatchup/user/coach/search-time-slots",N,"POST");if(!l.error){if(console.log("response",l),l.list.length===0){W(ee,"No coaches found for the selected time slot",4e3,"error");return}K(l.list);const u=l.list.map(_=>_.id);await p(u),ie(!0)}}catch(l){console.error("ERROR",l),W(ee,l.message,5e3)}finally{O(!1)}};return e.jsx("div",{className:"",children:e.jsxs("div",{className:"p-4",children:[e.jsx("div",{className:"",children:e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mx-auto max-w-7xl p-4",children:e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-3",children:[e.jsx(ts,{sports:h,userPermissions:y,courts:R,filterMode:"lesson",onSelectionChange:({sport:N,type:l,subType:u})=>{P(N),t(l),S(u),E(null),z([])}}),I&&(!((fe=(_e=h==null?void 0:h.find(N=>N.id===I))==null?void 0:_e.sport_types)!=null&&fe.length)||C!==null&&(J!==null||!((se=(X=(D=(a=h==null?void 0:h.find(N=>N.id===I))==null?void 0:a.sport_types)==null?void 0:D.find(N=>N.type===C))==null?void 0:X.subtype)!=null&&se.length)))?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"h-fit rounded-lg bg-white p-4 shadow-5",children:[((te=c==null?void 0:c.advance_booking_days)==null?void 0:te.lesson)!==void 0&&e.jsxs("div",{className:"mb-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["You can reserve a lesson up to"," ",(ge=c==null?void 0:c.advance_booking_days)==null?void 0:ge.lesson," ",((he=c==null?void 0:c.advance_booking_days)==null?void 0:he.lesson)===1?"day":"days"," ","in advance."]}),e.jsx(ss,{currentMonth:g,selectedDate:d,onDateSelect:N=>{var l;if(N>o){const u=((l=c==null?void 0:c.advance_booking_days)==null?void 0:l.lesson)||10;W(ee,`Your membership plan only allows booking ${u} days in advance`,3e3,"warning");return}E(N)},onPreviousMonth:F,onNextMonth:ne,daysOff:m!=null&&m.days_off?JSON.parse(m.days_off):[],allowPastDates:!1,minDate:new Date,maxDate:o,disabledDateMessage:`Your membership plan only allows booking ${((ve=c==null?void 0:c.advance_booking_days)==null?void 0:ve.lesson)||10} days in advance`})]}),d&&e.jsx(es,{isLoading:f,selectedDate:d,timeRange:n,onTimeClick:re,onNext:()=>{var N;if(!n.length){W(ee,"Please select a time slot",3e3,"error");return}if(d>o){const l=((N=c==null?void 0:c.advance_booking_days)==null?void 0:N.lesson)||10;W(ee,`Your membership plan only allows booking ${l} days in advance`,3e3,"warning");return}q()},nextButtonText:"Next: Select coach",startHour:0,endHour:24,clubTimes:m!=null&&m.times?JSON.parse(m.times):[],interval:30,isTimeSlotAvailable:()=>!0})]}):e.jsx("div",{className:"col-span-2 flex h-full items-center justify-center rounded-lg bg-white p-8 shadow-5",children:e.jsxs("div",{className:"text-center text-gray-500",children:[e.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})}),e.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"Please select a sport"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose a sport, type, and sub-type to view available time slots"})]})})]})})})}),pe&&H.length>0&&e.jsx(vs,{coaches:H,onClose:()=>ie(!1),selectedDate:d,selectedSport:I,selectedLocation:M,timeRange:ue,sports:h,players:x,groups:B,club:m,isOpen:pe,selectedTimes:n,selectedPlayers:T,selectedType:C,selectedSubType:J,userProfile:$,coachPricing:ae})]})})}let Ae=new De,ws=new Ce;function yt(){const[h]=Ye(),x=h.get("coach"),[m,$]=s.useState("coach"),[I,P]=s.useState([]),[M,b]=s.useState([]),{dispatch:d}=s.useContext(Se),[E,g]=s.useState(!0),[j,T]=s.useState([]),[L,C]=s.useState(null),t=Le(),J=[{id:"coach",label:"Find by coach"},{id:"time",label:"Find by time"},{id:"custom",label:"Custom request"}],{club:S,sports:B,user_permissions:i}=Ne(),w=localStorage.getItem("user"),k=async()=>{try{const f=await Ae.getOne("user",w,{});C(f.model)}catch(f){console.error(f)}},Z=async()=>{const f=await Ae.getList("coach",{join:["user|user_id"],filter:[`courtmatchup_coach.club_id,eq,${parseInt(S==null?void 0:S.id)}`]}),O=await Ae.getList("user",{filter:["role,cs,user",`club_id,eq,${parseInt(S==null?void 0:S.id)}`]});P(f.list),b(O.list)},G=async()=>{try{const f=await ws.callRawAPI("/v3/api/custom/courtmatchup/user/groups",{},"GET");T(f.groups)}catch(f){console.error(f)}};return s.useEffect(()=>{(async()=>S!=null&&S.id&&(g(!0),await k(),await Z(),await G(),g(!1)))()},[S==null?void 0:S.id]),be.useEffect(()=>{d({type:"SETPATH",payload:{path:"lessons"}}),x&&$("coach")},[x]),i&&!i.allowCoach?e.jsx(ls,{message:`Your current plan (${i==null?void 0:i.planName}) does not include coach lessons. Please upgrade your plan to access this feature.`}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-white px-3 py-3 sm:px-4 sm:py-4",children:[E&&e.jsx(We,{}),e.jsx("h1",{className:"mb-4 text-xl font-semibold sm:mb-6 sm:text-2xl",children:"Lessons"}),e.jsxs("div",{className:"flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0",children:[e.jsx("div",{className:"flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-0 sm:text-sm",children:J.map(f=>e.jsx("button",{onClick:()=>$(f.id),className:`whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2 ${m===f.id?"bg-white-600 font-medium":"bg-gray-100 text-gray-600"}`,children:f.label},f.id))}),m==="custom"&&e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:()=>t("/user/create-custom-request"),className:"rounded-lg bg-primaryBlue px-3 py-1.5 text-sm text-white transition-colors hover:bg-blue-600 sm:px-4 sm:py-2",children:"Create request"})})]})]}),e.jsxs("div",{className:"mx-auto max-w-7xl",children:[m==="coach"&&e.jsx(hs,{sports:B,coaches:I,players:M,groups:j,club:S,userProfile:L}),m==="time"&&e.jsx(js,{sports:B,coaches:I,players:M,groups:j,club:S,userProfile:L}),m==="custom"&&e.jsx(ys,{sports:B,coaches:I,players:M,groups:j,club:S,userProfile:L})]})]})}export{yt as default};
