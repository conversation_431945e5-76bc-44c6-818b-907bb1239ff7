import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as B,r as n}from"./vendor-851db8c1.js";import{u as ne}from"./react-hook-form-687afde5.js";import{o as ie}from"./yup-2824f222.js";import{c as ce,a as de}from"./yup-54691517.js";import{M as me,T as pe,A as ue,G as ge,h as Y,f as he,t as Z,d as P}from"./index-08a5dc5b.js";import{I as xe}from"./ImageCropModal-266718bc.js";import{u as ye,G as be,M as fe}from"./@react-google-maps/api-bec1613d.js";import{u as je,g as ve,a as Ne}from"./use-places-autocomplete-4cb4aca6.js";import{a as G,q as _e}from"./@headlessui/react-a5400090.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-image-crop-1f5038af.js";const we=["places"],Ce="AIzaSyC6zItKyKbnIcdpgwNoRIByQEvezUbdFAA";let b=new me,Pe=new pe;const gt=()=>{const u=ce({email:de().email().required()}).required(),{dispatch:E}=B.useContext(ue),[g,R]=n.useState(""),[v,S]=n.useState(!1),[i,F]=n.useState({}),[T,N]=n.useState(!0),[I,h]=n.useState(null),[f,c]=n.useState(""),[k,A]=n.useState(!1),[$,q]=n.useState(null),[d,m]=n.useState(!1),[p,_]=n.useState(null),[O,w]=n.useState(null),[M,L]=n.useState(null),[l,W]=n.useState(null),{dispatch:x}=B.useContext(ge),{isLoaded:K,loadError:X}=ye({googleMapsApiKey:Ce,libraries:we}),D=n.useCallback(t=>{L(t)},[]),{setError:V,setValue:C}=ne({resolver:ie(u)}),H=localStorage.getItem("user");async function U(){var t,s;N(!0);try{const o=await Pe.getList("profile",{filter:[`user_id,eq,${H}`],join:["user|user_id"]}),r=(t=o==null?void 0:o.list)==null?void 0:t[0];b.setTable("clubs");const y=await b.callRestAPI({filter:[`user_id,eq,${H}`]},"GET"),j=(s=y==null?void 0:y.list)==null?void 0:s[0];if(console.log("Fetched club data:",j),W(j),j!=null&&j.club_location)try{const a=JSON.parse(j.club_location);console.log("Parsed club location:",a),a.lat&&a.lng&&w({lat:a.lat,lng:a.lng})}catch(a){console.error("Error parsing club_location:",a),w(null)}else console.log("No club location data found"),w(null);if(r){const a=r.user||{},re=r.id,Q={...r,...a,profile_id:re,user_id:a.id};F(Q),C("email",a==null?void 0:a.email),C("first_name",a==null?void 0:a.first_name),C("last_name",a==null?void 0:a.last_name),C("phone",a==null?void 0:a.phone),C("bio",a==null?void 0:a.bio),R(a==null?void 0:a.photo),C("gender",r==null?void 0:r.gender),C("zip_code",r==null?void 0:r.zip_code),E({type:"UPDATE_PROFILE",payload:Q}),N(!1)}}catch(o){Z(E,o.response.data.message?o.response.data.message:o.message)}}const ee=["email","first_name","last_name","phone","bio","photo","alternative_phone","age_group","family_role","password","verify","status"],te=["gender","zip_code","date_of_birth","country","house_no"],se=["club_location"],z=async(t,s)=>{try{S(!0);const o={[t]:s},r=ee.includes(t),y=te.includes(t),j=se.includes(t);if(r){b.setTable("user");const a=await b.callRestAPI({id:i==null?void 0:i.user_id,...o},"PUT");a.error?J(a):(P(x,"Profile Updated",4e3),h(null),c(""),U())}else if(y){b.setTable("profile");const a=await b.callRestAPI({id:i==null?void 0:i.profile_id,...o},"PUT");a.error?J(a):(P(x,"Profile Updated",4e3),h(null),c(""),U())}else if(j){b.setTable("clubs");const a=await b.callRestAPI({id:l==null?void 0:l.id,...o},"PUT");a.error?J(a):(P(x,"Profile Updated",4e3),h(null),c(""),U())}else{P(x,"Unknown field type: "+t,4e3,"error"),S(!1);return}S(!1)}catch(o){S(!1),V(t,{type:"manual",message:o!=null&&o.message&&o==null?void 0:o.message}),Z(E,o!=null&&o.message&&o==null?void 0:o.message)}},J=t=>{if(t.validation){const s=Object.keys(t.validation);for(let o=0;o<s.length;o++){const r=s[o];V(r,{type:"manual",message:t.validation[r]})}}},ae=t=>{try{if(t.size>2*1024*1024){P(x,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}_(t.type);const s=new FileReader;s.onload=()=>{q(s.result),m(!0)},s.readAsDataURL(t)}catch(s){P(x,s==null?void 0:s.message,3e3,"error"),console.log(s)}},oe=async t=>{try{A(!0);const s=p==="image/png",o=new File([t],`cropped_profile.${s?"png":"jpg"}`,{type:s?"image/png":"image/jpeg"});let r=new FormData;r.append("file",o);let y=await b.uploadImage(r);z("photo",y==null?void 0:y.url)}catch(s){P(x,s==null?void 0:s.message,3e3,"error"),console.log(s)}finally{A(!1)}},le=()=>{z("photo",null),F({...i,photo:null})};return B.useEffect(()=>{x({type:"SETPATH",payload:{path:"profile"}}),U()},[x]),e.jsxs("div",{className:"p-5",children:[T||k&&e.jsx(Y,{}),e.jsx("div",{className:"",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),e.jsx(xe,{isOpen:d,onClose:()=>m(!1),image:$,onCropComplete:oe}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:g||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsx("div",{children:e.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[e.jsx("p",{className:"font-medium text-gray-700",children:"Profile Picture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:le,disabled:!g,className:"rounded-xl border border-red-600 px-3 py-1.5 text-red-600 disabled:opacity-50",children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5 text-gray-700",children:["Change Photo",e.jsx("input",{type:"file",accept:"image/*",onChange:t=>ae(t.target.files[0]),className:"hidden"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"gender",label:"Gender",type:"select",options:["male","female","other"]},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone"},{key:"zip_code",label:"Zip Code"},{key:"bio",label:"Bio",type:"textarea"},{key:"club_location",label:"Club Location",type:"location"}].map(t=>e.jsx("div",{children:I===t.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{className:"font-medium text-gray-700",children:t.label}),e.jsx("button",{onClick:()=>h(null),className:"text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),t.type==="select"?e.jsxs("select",{value:f,onChange:s=>c(s.target.value),className:"mt-1 w-full rounded-md border border-gray-300 p-2",children:[e.jsxs("option",{value:"",children:["Select ",t.label.toLowerCase()]}),t.options.map(s=>e.jsx("option",{value:s,children:s.charAt(0).toUpperCase()+s.slice(1)},s))]}):t.type==="textarea"?e.jsx("textarea",{value:f,onChange:s=>c(s.target.value),rows:4,className:"mt-1 w-full rounded-md border border-gray-300 p-2"}):t.type==="location"?e.jsx("div",{className:"mt-1",children:X?e.jsx("div",{children:"Error loading maps"}):K?e.jsx(Se,{selectedLocation:O,setSelectedLocation:w,map:M,onLoad:D,formData:{club_location:l==null?void 0:l.club_location,address:f},setFormData:s=>{const o=s({club_location:l==null?void 0:l.club_location,address:f});c(o.address)},isLoaded:K,defaultCenter:O||{lat:51.5074,lng:-.1278},onSave:z}):e.jsx(Y,{})}):e.jsx("input",{type:"text",value:f,onChange:s=>c(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),t.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:t.note}),t.type!=="location"&&e.jsx("div",{className:"mt-2",children:e.jsx(he,{loading:v,onClick:()=>z(t.key,f),className:"rounded-xl bg-primaryBlue px-4 py-2 font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"font-medium text-gray-500",children:t.label}),e.jsx("button",{onClick:()=>{if(h(t.key),t.key==="club_location")try{const s=JSON.parse((l==null?void 0:l.club_location)||"{}");c(s.address||"")}catch{c("")}else c((i==null?void 0:i[t.key])||"")},className:"text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:t.key==="club_location"?(()=>{try{return JSON.parse((l==null?void 0:l.club_location)||"{}").address||"--"}catch{return"--"}})():(i==null?void 0:i[t.key])||"--"}),t.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:t.note})]})},t.key))})]})]})})]})},Se=({selectedLocation:u,setSelectedLocation:E,map:g,onLoad:R,formData:v,setFormData:S,isLoaded:i,defaultCenter:F,onSave:T})=>{const N=B.useMemo(()=>{try{if(v!=null&&v.club_location)return JSON.parse(v.club_location).address||""}catch(d){console.error("Error parsing club_location:",d)}return""},[v]),{ready:I,value:h,suggestions:{status:f,data:c},setValue:k,clearSuggestions:A}=je({debounce:300,initOnMount:!0,cache:!1,googleMaps:i?window.google.maps:void 0,defaultValue:N});n.useEffect(()=>{N&&k(N,!1)},[N,k]),n.useEffect(()=>{g&&u&&g.panTo(u)},[g,u]);const $=async d=>{k(d,!1),A();try{const m=await ve({address:d}),{lat:p,lng:_}=await Ne(m[0]);E({lat:p,lng:_});const O=m[0].address_components;let w="",M="";O.forEach(l=>{l.types.includes("country")&&(w=l.long_name),l.types.includes("administrative_area_level_1")&&(M=l.long_name)});const L=JSON.stringify({lat:p,lng:_,address:m[0].formatted_address});S(l=>({...l,address:m[0].formatted_address,country:w,state:M,club_location:L})),T&&T("club_location",L),g==null||g.panTo({lat:p,lng:_})}catch(m){console.error("Error: ",m)}},q=d=>{k(d.target.value)};return e.jsxs("div",{className:"space-y-4",children:[e.jsx(G,{onChange:$,children:e.jsxs("div",{className:"relative",children:[e.jsx(G.Input,{className:"w-full rounded-xl border border-gray-300 p-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500",displayValue:d=>d||h,onChange:q,value:h,disabled:!I,placeholder:I?"Search location":"Loading..."}),e.jsx(_e,{as:n.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:e.jsx(G.Options,{className:"absolute z-[999] mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm",children:f==="OK"&&e.jsx(e.Fragment,{children:c.length===0&&h!==""?e.jsx("div",{className:"relative cursor-default select-none px-4 py-2 text-gray-700",children:"Nothing found."}):c.map(({place_id:d,description:m})=>e.jsx(G.Option,{className:({active:p})=>`relative cursor-default select-none py-2 pl-10 pr-4 ${p?"bg-blue-600 text-white":"text-gray-900"}`,value:m,children:({selected:p,active:_})=>e.jsxs(e.Fragment,{children:[e.jsx("span",{className:`block truncate ${p?"font-medium":"font-normal"}`,children:m}),p?e.jsx("span",{className:`absolute inset-y-0 left-0 flex items-center pl-3 ${_?"text-white":"text-blue-600"}`,children:"✓"}):null]})},d))})})})]})}),e.jsx("div",{className:"mt-3 h-[200px] w-full",children:e.jsx(be,{mapContainerClassName:"w-full h-full rounded-md",center:u||F,zoom:u?15:13,onLoad:R,children:u&&e.jsx(fe,{position:u})})})]})};export{gt as default};
