var C,c,o_,x,$,G,i_,M={},l_=[],b_=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function b(_,e){for(var t in e)_[t]=e[t];return _}function u_(_){var e=_.parentNode;e&&e.removeChild(_)}function $_(_,e,t){var o,i,r,u={};for(r in e)r=="key"?o=e[r]:r=="ref"?i=e[r]:u[r]=e[r];if(arguments.length>2&&(u.children=arguments.length>3?C.call(arguments,2):t),typeof _=="function"&&_.defaultProps!=null)for(r in _.defaultProps)u[r]===void 0&&(u[r]=_.defaultProps[r]);return E(_,u,o,i,null)}function E(_,e,t,o,i){var r={type:_,props:e,key:t,ref:o,__k:null,__:null,__b:0,__e:null,__d:void 0,__c:null,__h:null,constructor:void 0,__v:i??++o_};return i==null&&c.vnode!=null&&c.vnode(r),r}function A_(){return{current:null}}function q(_){return _.children}function H_(_,e,t,o,i){var r;for(r in t)r==="children"||r==="key"||r in e||V(_,r,null,t[r],o);for(r in e)i&&typeof e[r]!="function"||r==="children"||r==="key"||r==="value"||r==="checked"||t[r]===e[r]||V(_,r,e[r],t[r],o)}function J(_,e,t){e[0]==="-"?_.setProperty(e,t??""):_[e]=t==null?"":typeof t!="number"||b_.test(e)?t:t+"px"}function V(_,e,t,o,i){var r;_:if(e==="style")if(typeof t=="string")_.style.cssText=t;else{if(typeof o=="string"&&(_.style.cssText=o=""),o)for(e in o)t&&e in t||J(_.style,e,"");if(t)for(e in t)o&&t[e]===o[e]||J(_.style,e,t[e])}else if(e[0]==="o"&&e[1]==="n")r=e!==(e=e.replace(/Capture$/,"")),e=e.toLowerCase()in _?e.toLowerCase().slice(2):e.slice(2),_.l||(_.l={}),_.l[e+r]=t,t?o||_.addEventListener(e,r?Q:K,r):_.removeEventListener(e,r?Q:K,r);else if(e!=="dangerouslySetInnerHTML"){if(i)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(e!=="width"&&e!=="height"&&e!=="href"&&e!=="list"&&e!=="form"&&e!=="tabIndex"&&e!=="download"&&e in _)try{_[e]=t??"";break _}catch{}typeof t=="function"||(t==null||t===!1&&e.indexOf("-")==-1?_.removeAttribute(e):_.setAttribute(e,t))}}function K(_){x=!0;try{return this.l[_.type+!1](c.event?c.event(_):_)}finally{x=!1}}function Q(_){x=!0;try{return this.l[_.type+!0](c.event?c.event(_):_)}finally{x=!1}}function D(_,e){this.props=_,this.context=e}function S(_,e){if(e==null)return _.__?S(_.__,_.__.__k.indexOf(_)+1):null;for(var t;e<_.__k.length;e++)if((t=_.__k[e])!=null&&t.__e!=null)return t.__e;return typeof _.type=="function"?S(_):null}function c_(_){var e,t;if((_=_.__)!=null&&_.__c!=null){for(_.__e=_.__c.base=null,e=0;e<_.__k.length;e++)if((t=_.__k[e])!=null&&t.__e!=null){_.__e=_.__c.base=t.__e;break}return c_(_)}}function x_(_){x?setTimeout(_):i_(_)}function X(_){(!_.__d&&(_.__d=!0)&&$.push(_)&&!W.__r++||G!==c.debounceRendering)&&((G=c.debounceRendering)||x_)(W)}function W(){var _,e,t,o,i,r,u,p;for($.sort(function(a,h){return a.__v.__b-h.__v.__b});_=$.shift();)_.__d&&(e=$.length,o=void 0,i=void 0,u=(r=(t=_).__v).__e,(p=t.__P)&&(o=[],(i=b({},r)).__v=r.__v+1,j(p,r,i,t.__n,p.ownerSVGElement!==void 0,r.__h!=null?[u]:null,o,u??S(r),r.__h),h_(o,r),r.__e!=u&&c_(r)),$.length>e&&$.sort(function(a,h){return a.__v.__b-h.__v.__b}));W.__r=0}function f_(_,e,t,o,i,r,u,p,a,h){var n,d,f,l,s,H,v,m=o&&o.__k||l_,k=m.length;for(t.__k=[],n=0;n<e.length;n++)if((l=t.__k[n]=(l=e[n])==null||typeof l=="boolean"?null:typeof l=="string"||typeof l=="number"||typeof l=="bigint"?E(null,l,null,null,l):Array.isArray(l)?E(q,{children:l},null,null,null):l.__b>0?E(l.type,l.props,l.key,l.ref?l.ref:null,l.__v):l)!=null){if(l.__=t,l.__b=t.__b+1,(f=m[n])===null||f&&l.key==f.key&&l.type===f.type)m[n]=void 0;else for(d=0;d<k;d++){if((f=m[d])&&l.key==f.key&&l.type===f.type){m[d]=void 0;break}f=null}j(_,l,f=f||M,i,r,u,p,a,h),s=l.__e,(d=l.ref)&&f.ref!=d&&(v||(v=[]),f.ref&&v.push(f.ref,null,l),v.push(d,l.__c||s,l)),s!=null?(H==null&&(H=s),typeof l.type=="function"&&l.__k===f.__k?l.__d=a=s_(l,a,_):a=a_(_,l,f,m,s,a),typeof t.type=="function"&&(t.__d=a)):a&&f.__e==a&&a.parentNode!=_&&(a=S(f))}for(t.__e=H,n=k;n--;)m[n]!=null&&(typeof t.type=="function"&&m[n].__e!=null&&m[n].__e==t.__d&&(t.__d=p_(o).nextSibling),v_(m[n],m[n]));if(v)for(n=0;n<v.length;n++)d_(v[n],v[++n],v[++n])}function s_(_,e,t){for(var o,i=_.__k,r=0;i&&r<i.length;r++)(o=i[r])&&(o.__=_,e=typeof o.type=="function"?s_(o,e,t):a_(t,o,o,i,o.__e,e));return e}function N_(_,e){return e=e||[],_==null||typeof _=="boolean"||(Array.isArray(_)?_.some(function(t){N_(t,e)}):e.push(_)),e}function a_(_,e,t,o,i,r){var u,p,a;if(e.__d!==void 0)u=e.__d,e.__d=void 0;else if(t==null||i!=r||i.parentNode==null)_:if(r==null||r.parentNode!==_)_.appendChild(i),u=null;else{for(p=r,a=0;(p=p.nextSibling)&&a<o.length;a+=1)if(p==i)break _;_.insertBefore(i,r),u=r}return u!==void 0?u:i.nextSibling}function p_(_){var e,t,o;if(_.type==null||typeof _.type=="string")return _.__e;if(_.__k){for(e=_.__k.length-1;e>=0;e--)if((t=_.__k[e])&&(o=p_(t)))return o}return null}function j(_,e,t,o,i,r,u,p,a){var h,n,d,f,l,s,H,v,m,k,P,N,z,A,T,g=e.type;if(e.constructor!==void 0)return null;t.__h!=null&&(a=t.__h,p=e.__e=t.__e,e.__h=null,r=[p]),(h=c.__b)&&h(e);try{_:if(typeof g=="function"){if(v=e.props,m=(h=g.contextType)&&o[h.__c],k=h?m?m.props.value:h.__:o,t.__c?H=(n=e.__c=t.__c).__=n.__E:("prototype"in g&&g.prototype.render?e.__c=n=new g(v,k):(e.__c=n=new D(v,k),n.constructor=g,n.render=S_),m&&m.sub(n),n.props=v,n.state||(n.state={}),n.context=k,n.__n=o,d=n.__d=!0,n.__h=[],n._sb=[]),n.__s==null&&(n.__s=n.state),g.getDerivedStateFromProps!=null&&(n.__s==n.state&&(n.__s=b({},n.__s)),b(n.__s,g.getDerivedStateFromProps(v,n.__s))),f=n.props,l=n.state,n.__v=e,d)g.getDerivedStateFromProps==null&&n.componentWillMount!=null&&n.componentWillMount(),n.componentDidMount!=null&&n.__h.push(n.componentDidMount);else{if(g.getDerivedStateFromProps==null&&v!==f&&n.componentWillReceiveProps!=null&&n.componentWillReceiveProps(v,k),!n.__e&&n.shouldComponentUpdate!=null&&n.shouldComponentUpdate(v,n.__s,k)===!1||e.__v===t.__v){for(e.__v!==t.__v&&(n.props=v,n.state=n.__s,n.__d=!1),e.__e=t.__e,e.__k=t.__k,e.__k.forEach(function(F){F&&(F.__=e)}),P=0;P<n._sb.length;P++)n.__h.push(n._sb[P]);n._sb=[],n.__h.length&&u.push(n);break _}n.componentWillUpdate!=null&&n.componentWillUpdate(v,n.__s,k),n.componentDidUpdate!=null&&n.__h.push(function(){n.componentDidUpdate(f,l,s)})}if(n.context=k,n.props=v,n.__P=_,N=c.__r,z=0,"prototype"in g&&g.prototype.render){for(n.state=n.__s,n.__d=!1,N&&N(e),h=n.render(n.props,n.state,n.context),A=0;A<n._sb.length;A++)n.__h.push(n._sb[A]);n._sb=[]}else do n.__d=!1,N&&N(e),h=n.render(n.props,n.state,n.context),n.state=n.__s;while(n.__d&&++z<25);n.state=n.__s,n.getChildContext!=null&&(o=b(b({},o),n.getChildContext())),d||n.getSnapshotBeforeUpdate==null||(s=n.getSnapshotBeforeUpdate(f,l)),T=h!=null&&h.type===q&&h.key==null?h.props.children:h,f_(_,Array.isArray(T)?T:[T],e,t,o,i,r,u,p,a),n.base=e.__e,e.__h=null,n.__h.length&&u.push(n),H&&(n.__E=n.__=null),n.__e=!1}else r==null&&e.__v===t.__v?(e.__k=t.__k,e.__e=t.__e):e.__e=E_(t.__e,e,t,o,i,r,u,a);(h=c.diffed)&&h(e)}catch(F){e.__v=null,(a||r!=null)&&(e.__e=p,e.__h=!!a,r[r.indexOf(p)]=null),c.__e(F,e,t)}}function h_(_,e){c.__c&&c.__c(e,_),_.some(function(t){try{_=t.__h,t.__h=[],_.some(function(o){o.call(t)})}catch(o){c.__e(o,t.__v)}})}function E_(_,e,t,o,i,r,u,p){var a,h,n,d=t.props,f=e.props,l=e.type,s=0;if(l==="svg"&&(i=!0),r!=null){for(;s<r.length;s++)if((a=r[s])&&"setAttribute"in a==!!l&&(l?a.localName===l:a.nodeType===3)){_=a,r[s]=null;break}}if(_==null){if(l===null)return document.createTextNode(f);_=i?document.createElementNS("http://www.w3.org/2000/svg",l):document.createElement(l,f.is&&f),r=null,p=!1}if(l===null)d===f||p&&_.data===f||(_.data=f);else{if(r=r&&C.call(_.childNodes),h=(d=t.props||M).dangerouslySetInnerHTML,n=f.dangerouslySetInnerHTML,!p){if(r!=null)for(d={},s=0;s<_.attributes.length;s++)d[_.attributes[s].name]=_.attributes[s].value;(n||h)&&(n&&(h&&n.__html==h.__html||n.__html===_.innerHTML)||(_.innerHTML=n&&n.__html||""))}if(H_(_,f,d,i,p),n)e.__k=[];else if(s=e.props.children,f_(_,Array.isArray(s)?s:[s],e,t,o,i&&l!=="foreignObject",r,u,r?r[0]:t.__k&&S(t,0),p),r!=null)for(s=r.length;s--;)r[s]!=null&&u_(r[s]);p||("value"in f&&(s=f.value)!==void 0&&(s!==_.value||l==="progress"&&!s||l==="option"&&s!==d.value)&&V(_,"value",s,d.value,!1),"checked"in f&&(s=f.checked)!==void 0&&s!==_.checked&&V(_,"checked",s,d.checked,!1))}return _}function d_(_,e,t){try{typeof _=="function"?_(e):_.current=e}catch(o){c.__e(o,t)}}function v_(_,e,t){var o,i;if(c.unmount&&c.unmount(_),(o=_.ref)&&(o.current&&o.current!==_.__e||d_(o,null,e)),(o=_.__c)!=null){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(r){c.__e(r,e)}o.base=o.__P=null,_.__c=void 0}if(o=_.__k)for(i=0;i<o.length;i++)o[i]&&v_(o[i],e,t||typeof _.type!="function");t||_.__e==null||u_(_.__e),_.__=_.__e=_.__d=void 0}function S_(_,e,t){return this.constructor(_,t)}function T_(_,e,t){var o,i,r;c.__&&c.__(_,e),i=(o=typeof t=="function")?null:t&&t.__k||e.__k,r=[],j(e,_=(!o&&t||e).__k=$_(q,null,[_]),i||M,M,e.ownerSVGElement!==void 0,!o&&t?[t]:i?null:e.firstChild?C.call(e.childNodes):null,r,!o&&t?t:i?i.__e:e.firstChild,o),h_(r,_)}function F_(_,e,t){var o,i,r,u=b({},_.props);for(r in e)r=="key"?o=e[r]:r=="ref"?i=e[r]:u[r]=e[r];return arguments.length>2&&(u.children=arguments.length>3?C.call(arguments,2):t),E(_.type,u,o||_.key,i||_.ref,null)}C=l_.slice,c={__e:function(_,e,t,o){for(var i,r,u;e=e.__;)if((i=e.__c)&&!i.__)try{if((r=i.constructor)&&r.getDerivedStateFromError!=null&&(i.setState(r.getDerivedStateFromError(_)),u=i.__d),i.componentDidCatch!=null&&(i.componentDidCatch(_,o||{}),u=i.__d),u)return i.__E=i}catch(p){_=p}throw _}},o_=0,x=!1,D.prototype.setState=function(_,e){var t;t=this.__s!=null&&this.__s!==this.state?this.__s:this.__s=b({},this.state),typeof _=="function"&&(_=_(b({},t),this.props)),_&&b(t,_),_!=null&&this.__v&&(e&&this._sb.push(e),X(this))},D.prototype.forceUpdate=function(_){this.__v&&(this.__e=!0,_&&this.__h.push(_),X(this))},D.prototype.render=q,$=[],i_=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,W.__r=0;var B,y,I,Y,w=0,y_=[],U=[],Z=c.__b,__=c.__r,e_=c.diffed,t_=c.__c,n_=c.unmount;function R(_,e){c.__h&&c.__h(y,_,w||e),w=0;var t=y.__H||(y.__H={__:[],__h:[]});return _>=t.__.length&&t.__.push({__V:U}),t.__[_]}function D_(_){return w=1,w_(k_,_)}function w_(_,e,t){var o=R(B++,2);if(o.t=_,!o.__c&&(o.__=[t?t(e):k_(void 0,e),function(r){var u=o.__N?o.__N[0]:o.__[0],p=o.t(u,r);u!==p&&(o.__N=[p,o.__[1]],o.__c.setState({}))}],o.__c=y,!y.u)){y.u=!0;var i=y.shouldComponentUpdate;y.shouldComponentUpdate=function(r,u,p){if(!o.__c.__H)return!0;var a=o.__c.__H.__.filter(function(n){return n.__c});if(a.every(function(n){return!n.__N}))return!i||i.call(this,r,u,p);var h=!1;return a.forEach(function(n){if(n.__N){var d=n.__[0];n.__=n.__N,n.__N=void 0,d!==n.__[0]&&(h=!0)}}),!(!h&&o.__c.props===r)&&(!i||i.call(this,r,u,p))}}return o.__N||o.__}function U_(_,e){var t=R(B++,3);!c.__s&&g_(t.__H,e)&&(t.__=_,t.i=e,y.__H.__h.push(t))}function L_(_){return w=5,m_(function(){return{current:_}},[])}function m_(_,e){var t=R(B++,7);return g_(t.__H,e)?(t.__V=_(),t.i=e,t.__h=_,t.__V):t.__}function M_(_,e){return w=8,m_(function(){return _},e)}function C_(){for(var _;_=y_.shift();)if(_.__P&&_.__H)try{_.__H.__h.forEach(L),_.__H.__h.forEach(O),_.__H.__h=[]}catch(e){_.__H.__h=[],c.__e(e,_.__v)}}c.__b=function(_){y=null,Z&&Z(_)},c.__r=function(_){__&&__(_),B=0;var e=(y=_.__c).__H;e&&(I===y?(e.__h=[],y.__h=[],e.__.forEach(function(t){t.__N&&(t.__=t.__N),t.__V=U,t.__N=t.i=void 0})):(e.__h.forEach(L),e.__h.forEach(O),e.__h=[])),I=y},c.diffed=function(_){e_&&e_(_);var e=_.__c;e&&e.__H&&(e.__H.__h.length&&(y_.push(e)!==1&&Y===c.requestAnimationFrame||((Y=c.requestAnimationFrame)||P_)(C_)),e.__H.__.forEach(function(t){t.i&&(t.__H=t.i),t.__V!==U&&(t.__=t.__V),t.i=void 0,t.__V=U})),I=y=null},c.__c=function(_,e){e.some(function(t){try{t.__h.forEach(L),t.__h=t.__h.filter(function(o){return!o.__||O(o)})}catch(o){e.some(function(i){i.__h&&(i.__h=[])}),e=[],c.__e(o,t.__v)}}),t_&&t_(_,e)},c.unmount=function(_){n_&&n_(_);var e,t=_.__c;t&&t.__H&&(t.__H.__.forEach(function(o){try{L(o)}catch(i){e=i}}),t.__H=void 0,e&&c.__e(e,t.__v))};var r_=typeof requestAnimationFrame=="function";function P_(_){var e,t=function(){clearTimeout(o),r_&&cancelAnimationFrame(e),setTimeout(_)},o=setTimeout(t,100);r_&&(e=requestAnimationFrame(t))}function L(_){var e=y,t=_.__c;typeof t=="function"&&(_.__c=void 0,t()),y=e}function O(_){var e=y;_.__c=_.__(),y=e}function g_(_,e){return!_||_.length!==e.length||e.some(function(t,o){return t!==_[o]})}function k_(_,e){return typeof e=="function"?e(_):e}export{T_ as D,F_ as F,M_ as T,q as _,m_ as a,L_ as b,A_ as d,U_ as h,N_ as j,D_ as p,D as x,$_ as y};
