import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r,b as L,k as ce,f as de}from"./vendor-851db8c1.js";import{M as K,h as re,A as me,G as le,Z as ee,_ as te,f as se,t as ae,d as k,i as pe,j as ue,u as xe,F as he,n as fe,o as ge}from"./index-08a5dc5b.js";import{B as ye}from"./BackButton-11ba52b2.js";import{S as be}from"./StripeConnectionStatus-33f354ad.js";import{u as je}from"./react-hook-form-687afde5.js";import{o as we}from"./yup-2824f222.js";import{c as Ne,a as ve}from"./yup-54691517.js";import"./index-02625b16.js";import{I as _e}from"./ImageCropModal-266718bc.js";import{L as Ce}from"./index.esm-3a36c7d6.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-image-crop-1f5038af.js";const Se=new K;function Pe({profile:f}){const[x,d]=r.useState(!1),[g,p]=r.useState((f==null?void 0:f.not_paid_through_platform)||0),y=L.useRef(null),u=n=>{console.log("Stripe connection status changed:",n)},i=async()=>{d(!0);try{const n=g===1?0:1;await Se.callRawAPI("/v3/api/custom/courtmatchup/staff/profile-edit",{not_paid_through_platform:n},"POST"),p(n)}catch(n){console.error("Error updating payment preference:",n)}finally{d(!1)}};return e.jsxs("div",{className:"mx-auto flex w-full flex-col bg-white px-4 pb-7",children:[x&&e.jsx(re,{}),e.jsx("div",{className:"mx-auto flex w-full max-w-2xl flex-col justify-center",children:g===1?e.jsx("div",{className:"flex flex-col gap-4",children:e.jsxs("div",{className:"rounded-lg border border-blue-200 bg-blue-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-blue-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-lg font-medium text-gray-900",children:"Not paid through Court Matchup"})]}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"You have chosen not to receive payments through the Court Matchup platform. Your club will arrange payments with you directly through other means."}),e.jsx("button",{onClick:i,className:"mt-4 rounded-xl border border-blue-300 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700 hover:bg-blue-100",disabled:x,children:"Change Payment Preference"})]})}):e.jsxs(e.Fragment,{children:[e.jsx(be,{ref:y,onConnectionStatusChange:u,successMessage:"You can now receive payments from your club managers.",noConnectionMessage:"Connect your Stripe account to receive payments from your club managers. This is required for processing payments on the platform."}),e.jsx("button",{onClick:i,className:"mt-4 w-full rounded-xl border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50",disabled:x,children:"I don't want to be paid through Court Matchup"}),e.jsx("div",{className:"flex w-full flex-col self-center",children:e.jsx("p",{className:"mt-6 text-xs leading-4 text-neutral-400",children:"Note: Multiple accounts can be set up if you want to change accounts later on a particular month or year."})})]})})]})}let F=new K;const Te=()=>{var X;const f=Ne({email:ve().email().required()}).required(),{dispatch:x}=L.useContext(me),[d,g]=r.useState("");L.useState({});const[p,y]=r.useState("");r.useState(!1);const[u,i]=r.useState(!1),[n,A]=r.useState("Profile"),[c,w]=r.useState({}),[H,B]=r.useState(!0),[N,O]=r.useState(!1),[$,I]=r.useState(null),[v,_]=r.useState(""),[Q,z]=r.useState(!1),[W,G]=r.useState(null),[V,C]=r.useState(!1),[E,t]=r.useState(null),{dispatch:l}=L.useContext(le),m=["email","first_name","last_name","phone","photo","password","verify","status"],h=["gender","address","city","state","zip_code","ntrp","date_of_birth","country","house_no"],{register:b,handleSubmit:Y,setError:R,setValue:S,getValues:J,formState:{errors:D}}=je({resolver:we(f)}),P=()=>{O(!N)};async function j(){B(!0);try{const s=await F.getProfile();if(!s.error){const a={...s,user_id:s.id,profile_id:s.profile_id};w(a),S("email",s==null?void 0:s.email),S("first_name",s==null?void 0:s.first_name),S("last_name",s==null?void 0:s.last_name),S("phone",s==null?void 0:s.phone),g(s==null?void 0:s.email),y(s==null?void 0:s.photo),x({type:"UPDATE_PROFILE",payload:a}),B(!1)}}catch(s){ae(x,s.response.data.message?s.response.data.message:s.message)}}const q=async(s,a)=>{try{i(!0);const o={[s]:a},T=m.includes(s),M=h.includes(s);if(T){F.setTable("user");const U=await F.callRestAPI({id:c==null?void 0:c.user_id,...o},"PUT");U.error?Z(U):(k(l,"Profile Updated",4e3),I(null),_(""),j())}else if(M){F.setTable("profile");const U=await F.callRestAPI({id:c==null?void 0:c.profile_id,...o},"PUT");U.error?Z(U):(k(l,"Profile Updated",4e3),I(null),_(""),j())}else{k(l,"Unknown field type: "+s,4e3,"error"),i(!1);return}i(!1)}catch(o){i(!1),R(s,{type:"manual",message:o!=null&&o.message&&o==null?void 0:o.message}),ae(x,o!=null&&o.message&&o==null?void 0:o.message)}},Z=s=>{if(s.validation){const a=Object.keys(s.validation);for(let o=0;o<a.length;o++){const T=a[o];R(T,{type:"manual",message:s.validation[T]})}}},oe=s=>{try{if(s.size>2*1024*1024){k(l,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}t(s.type);const a=new FileReader;a.onload=()=>{G(a.result),C(!0)},a.readAsDataURL(s)}catch(a){k(l,(a==null?void 0:a.message)||"Failed to upload photo",3e3,"error"),console.log(a)}},ne=async s=>{try{z(!0);const a=E==="image/png",o=new File([s],`cropped_profile.${a?"png":"jpg"}`,{type:a?"image/png":"image/jpeg"});let T=new FormData;T.append("file",o);let M=await F.uploadImage(T);M.error?k(l,M.message||"Failed to upload photo",3e3,"error"):q("photo",M.url)}catch(a){k(l,(a==null?void 0:a.message)||"Failed to upload photo",3e3,"error"),console.log(a)}finally{z(!1)}},ie=()=>{q("photo",null),w({...c,photo:null})};return L.useEffect(()=>{l({type:"SETPATH",payload:{path:"profile"}}),j()},[]),e.jsx("div",{className:"w-full",children:e.jsxs("main",{className:"p-8",children:[H||Q&&e.jsx(re,{}),e.jsx(_e,{isOpen:V,onClose:()=>C(!1),image:W,onCropComplete:ne}),n==="Profile"&&e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:p||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsx("div",{children:e.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[e.jsx("p",{className:"font-medium text-gray-700",children:"Profile Picture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:ie,disabled:!p,className:"rounded-xl border border-red-600 px-3 py-1.5 text-red-600 disabled:opacity-50",children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5 text-gray-700",children:["Change Photo",e.jsx("input",{type:"file",accept:"image/*",onChange:s=>{const a=s.target.files[0];a&&oe(a)},className:"hidden"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone Number"},{key:"password",label:"Password",type:"password"}].map(s=>e.jsx("div",{children:$===s.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{className:"font-medium text-gray-700",children:s.label}),e.jsx("button",{onClick:()=>I(null),className:"text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),s.type==="password"?e.jsxs("div",{className:"relative mt-1",children:[e.jsx("input",{type:N?"text":"password",value:v,onChange:a=>_(a.target.value),className:"w-full rounded-xl border border-gray-300 p-2 pr-10",placeholder:"Enter new password"}),e.jsx("button",{type:"button",className:"absolute right-3 top-1/2 -translate-y-1/2",onClick:P,children:N?e.jsx(ee,{}):e.jsx(te,{})})]}):e.jsx("input",{type:"text",value:v,onChange:a=>_(a.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),s.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:s.note}),e.jsx("div",{className:"mt-2",children:e.jsx(se,{loading:u,onClick:()=>q(s.key,v),className:"rounded-xl bg-primaryBlue px-4 py-2 font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"font-medium text-gray-500",children:s.label}),e.jsx("button",{onClick:()=>{I(s.key),_((c==null?void 0:c[s.key])||"")},className:"text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:s.key==="password"?"********":(c==null?void 0:c[s.key])||"--"}),s.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:s.note})]})},s.key))})]})]}),n==="Security"&&e.jsx("div",{className:"rounded bg-white",children:e.jsx("form",{onSubmit:Y(q),className:"max-w-lg",children:e.jsxs("div",{className:"",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Password"}),e.jsxs("div",{className:"relative w-full md:w-3/4 lg:w-2/3",children:[e.jsx("input",{...b("password"),name:"password",className:"focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",id:"password",placeholder:"********",type:N?"text":"password"}),e.jsx("div",{className:"absolute inset-y-0 right-0 flex cursor-pointer items-center pr-3",onClick:P,children:N?e.jsx(ee,{}):e.jsx(te,{})})]}),e.jsx("p",{className:"text-xs italic text-red-500",children:(X=D.password)==null?void 0:X.message})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsx(se,{className:"focus:shadow-outline rounded bg-indigo-600 px-4 py-2 font-bold text-white hover:bg-indigo-600 focus:outline-none disabled:cursor-not-allowed",type:"submit",loading:u,disabled:u,children:"Update"})})]})})})]})})};let ke=new K;const Ie=()=>{const[f,x]=r.useState({}),[d,g]=r.useState(""),[p,y]=r.useState(""),[u,i]=r.useState(""),[n,A]=r.useState("date"),[c,w]=r.useState("desc"),[H,B]=r.useState([]),[N,O]=r.useState(!1),[$,I]=r.useState(null),[v,_]=r.useState("all"),Q=t=>{x(l=>({...l,[t]:!l[t]}))},z=async()=>{O(!0);try{const t=await ke.callRawAPI("/v3/api/custom/courtmatchup/staff/reservations/billing/staff-invoices",{},"GET");I(t),B(t.invoices||[])}catch(t){console.log(t)}finally{O(!1)}},G=[...H.filter(t=>{var b,Y,R,S,J;const l=((b=t.user_first_name)==null?void 0:b.toLowerCase().includes(d.toLowerCase()))||((Y=t.user_last_name)==null?void 0:Y.toLowerCase().includes(d.toLowerCase()))||((R=t.receipt_id)==null?void 0:R.toLowerCase().includes(d.toLowerCase()))||((S=t.status)==null?void 0:S.toLowerCase().includes(d.toLowerCase())),m=v==="all"||((J=t.invoice_type)==null?void 0:J.toLowerCase())===v.toLowerCase(),h=(()=>{if(!p&&!u)return!0;if(!t.date)return!1;const D=new Date(t.date),P=p?new Date(p):null,j=u?new Date(u):null;return P&&j?D>=P&&D<=j:P?D>=P:j?D<=j:!0})();return l&&m&&h})].sort((t,l)=>{let m=0;if(n==="date"){const h=new Date(t.date||t.create_at),b=new Date(l.date||l.create_at);m=h.getTime()-b.getTime()}else if(n==="amount")m=(t.amount||0)-(l.amount||0);else if(n==="status")m=(t.status||"").localeCompare(l.status||"");else if(n==="customer"){const h=`${t.user_first_name||""} ${t.user_last_name||""}`.trim(),b=`${l.user_first_name||""} ${l.user_last_name||""}`.trim();m=h.localeCompare(b)}return c==="desc"?-m:m}),V=()=>{n==="date"?(A("amount"),w("desc")):n==="amount"?(A("status"),w("asc")):n==="status"?(A("customer"),w("asc")):(A("date"),w(c==="desc"?"asc":"desc"))},C=(t,l="usd")=>new Intl.NumberFormat("en-US",{style:"currency",currency:l.toUpperCase()}).format(t),E=t=>t?new Date(t).toLocaleDateString("en-US",{month:"2-digit",day:"2-digit",year:"2-digit"}):"N/A";return r.useEffect(()=>{z()},[]),N?e.jsx("div",{className:"flex w-full items-center justify-center py-8",children:e.jsx("div",{className:"text-lg",children:"Loading invoices..."})}):e.jsxs("div",{className:"w-full",children:[e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"My invoices"}),$&&e.jsxs("p",{className:"mt-1 text-sm text-gray-600",children:["Total: ",$.total_invoices||0," invoices • Total Earnings: ",C($.total_earnings||0)]})]}),e.jsx("div",{className:"relative inline-block",children:e.jsxs("select",{className:"appearance-none rounded-lg border border-gray-200 bg-white px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500",value:v,onChange:t=>_(t.target.value),children:[e.jsx("option",{value:"all",children:"Type: All"}),e.jsx("option",{value:"Staff",children:"Staff"}),e.jsx("option",{value:"Subscription",children:"Subscription"}),e.jsx("option",{value:"Payment",children:"Payment"})]})})]}),e.jsxs("div",{className:"mb-6 flex flex-col gap-3 sm:flex-row",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(pe,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("input",{type:"text",value:d,onChange:t=>g(t.target.value),placeholder:"Search invoices...",className:"w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex w-full gap-2 sm:w-auto",children:[e.jsx("input",{type:"date",value:p,onChange:t=>y(t.target.value),placeholder:"Start date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),e.jsx("input",{type:"date",value:u,onChange:t=>i(t.target.value),placeholder:"End date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),(p||u)&&e.jsx("button",{onClick:()=>{y(""),i("")},className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 hover:bg-gray-50 hover:text-gray-700",title:"Clear dates",children:"✕"})]}),e.jsxs("button",{onClick:V,className:"flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:["BY ",n," ",c==="desc"?"↓":"↑",e.jsx(Ce,{className:"transform"})]})]}),e.jsx("div",{className:"space-y-4",children:G.length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"No invoices found."}):G.map(t=>{var l;return e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm",children:[e.jsxs("button",{onClick:()=>Q(t.id),className:"flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ue,{className:`transform transition-transform ${f[t.id]?"":"-rotate-90"}`}),e.jsx("span",{className:"text-sm sm:text-base",children:t.invoice_type||t.type}),t.status&&e.jsx("span",{className:`rounded-full px-2 py-1 text-xs ${t.status==="completed"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:t.status})]}),e.jsxs("div",{className:"flex items-center justify-between gap-4 pl-6 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600",children:E(t.date)}),e.jsx("span",{className:"font-medium",children:C(t.amount,t.currency)})]})]}),f[t.id]&&e.jsxs("div",{className:"mt-4 space-y-3 border-t border-gray-200 p-4",children:[t.receipt_id&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Receipt ID"}),e.jsx("span",{children:t.receipt_id})]}),t.user_first_name&&t.user_last_name&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Customer"}),e.jsxs("span",{children:[t.user_first_name," ",t.user_last_name]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Created on"}),e.jsx("span",{children:E(t.create_at)})]}),t.total_amount&&t.total_amount!==t.amount&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Total amount"}),e.jsx("span",{children:C(t.total_amount,t.currency)})]}),t.valid_until&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{children:E(t.valid_until)})]}),t.club_name&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Club"}),e.jsx("span",{children:t.club_name})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Payment method"}),e.jsx("span",{children:t.payment_method})]}),e.jsx("span",{className:"capitalize",children:(l=eventTypeOptions.find(m=>m.value===t.reservation_type))==null?void 0:l.label}),e.jsx("button",{className:"mt-2 rounded-lg border border-gray-200 px-4 py-2 text-sm hover:bg-gray-50",onClick:()=>{const m=`
                        <div style="padding: 20px; font-family: Arial, sans-serif;">
                          <h2>Invoice Receipt</h2>
                          <p><strong>Receipt ID:</strong> ${t.receipt_id||"N/A"}</p>
                          <p><strong>Amount:</strong> ${C(t.amount,t.currency)}</p>
                          <p><strong>Date:</strong> ${E(t.date)}</p>
                          <p><strong>Status:</strong> ${t.status}</p>
                          <p><strong>Customer:</strong> ${t.user_first_name} ${t.user_last_name}</p>
                          <p><strong>Payment Method:</strong> ${t.payment_method}</p>
                          ${t.club_name?`<p><strong>Club:</strong> ${t.club_name}</p>`:""}
                        </div>
                      `,h=window.open("","_blank");h.document.write(m),h.document.close(),h.print()},children:"Print receipt"})]})]},t.id)})})]})},bt=()=>{const{dispatch:f}=L.useContext(le),[x]=ce(),[d,g]=r.useState("profile"),p=de(),{staff_profile:y}=xe();r.useEffect(()=>{const i=x.get("tab");i&&g({"payment-methods":"payment-methods",profile:"profile",membership:"membership",billing:"billing",invoices:"invoices"}[i]||"profile")},[x.get("tab")]);const u=[{label:"Profile details",value:"profile",icon:he},{label:"Bank Accounts",value:"payment-methods",icon:fe},{label:"Invoices",value:"invoices",icon:ge}];return r.useEffect(()=>{f({type:"SETPATH",payload:{path:"profile"}})},[]),e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"mx-auto  max-w-6xl px-4",children:[e.jsx(ye,{onBack:()=>p("/staff/dashboard")}),e.jsxs("div",{className:"flex flex-col gap-8 md:flex-row",children:[e.jsx("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-sm md:max-w-xs",children:e.jsx("nav",{className:"no-scrollbar flex w-full flex-row overflow-x-auto p-4 md:flex-col",children:u.map(i=>{const n=i.icon;return e.jsxs("button",{onClick:()=>{g(i.value),p(`/staff/profile?tab=${i.value}`)},className:`mr-2 flex min-w-fit items-center whitespace-nowrap rounded-lg px-4 py-3 transition-colors md:mb-2 md:mr-0 ${d===i.value?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50"}`,children:[e.jsx(n,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{className:"text-sm",children:i.label})]},i.value)})})}),e.jsxs("div",{className:" w-full rounded-xl bg-white p-6 shadow-sm",children:[d==="profile"&&e.jsx(Te,{}),d==="payment-methods"&&e.jsx(Pe,{profile:y}),d==="invoices"&&e.jsx(Ie,{})]})]})]})})};export{bt as default};
