import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{W as o}from"./index-08a5dc5b.js";const u=({price:x,isCurrentPlan:l,features:m,onSelect:c,popular:a,plan_name:h,isActive:n,advance_booking_days:r,billing_interval:d,billing_interval_count:p})=>{const j=(t,i=1)=>{if(!t)return"month";const s=i||1;return{day:s===1?"day":`${s} days`,week:s===1?"week":`${s} weeks`,month:s===1?"month":`${s} months`,year:s===1?"year":`${s} years`}[t]||"month"};return e.jsxs("div",{className:`h-fit rounded-xl border border-gray-200 bg-white p-5 shadow-5 ${n?"border-primaryBlue":""}`,children:[e.jsxs("div",{className:" mb-3 flex items-center justify-between border-b border-gray-100 pb-4",children:[e.jsx("h3",{className:"text-lg font-semibold",children:h}),n&&e.jsx("span",{className:"rounded-lg bg-primaryBlue px-3 py-1 text-sm text-white",children:"Active"})]}),e.jsx("div",{className:"mb-4 text-2xl font-bold",children:x===0?e.jsx("span",{className:"text-green-600",children:"Free"}):e.jsxs(e.Fragment,{children:[o(x),e.jsxs("span",{className:"text-sm font-normal text-gray-500",children:["/",j(d,p)]})]})}),e.jsxs("div",{className:"mb-4 rounded-lg bg-gray-50 p-3",children:[e.jsx("h6",{className:"mb-2 font-medium",children:"Advance Booking Days:"}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[e.jsxs("div",{children:["Court: ",(r==null?void 0:r.court)||10," days"]}),e.jsxs("div",{children:["Lesson: ",(r==null?void 0:r.lesson)||10," days"]}),e.jsxs("div",{children:["Clinic: ",(r==null?void 0:r.clinic)||10," days"]}),e.jsxs("div",{children:["Buddy: ",(r==null?void 0:r.buddy)||10," days"]})]})]}),e.jsx("div",{className:"mb-4 space-y-3",children:m==null?void 0:m.map((t,i)=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-4 w-4 flex-shrink-0 items-center justify-center",children:e.jsx("svg",{className:"h-4 w-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsx("span",{className:"text-sm text-gray-700",children:t.text})]},i))}),e.jsx("button",{onClick:c,className:`w-full rounded-lg px-4 py-2 text-sm font-medium transition-colors ${l?"cursor-not-allowed bg-gray-100 text-gray-500":"bg-primaryBlue text-white hover:bg-blue-700"}`,disabled:l,children:l?"Current Plan":"Select Plan"})]})},y=u;export{y as M};
