import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{M as J,G as Y,h as D,f as F,d as M,u as ce,aY as pe,A as xe,N as he,t as se}from"./index-08a5dc5b.js";import{r as n,b as fe,f as ge}from"./vendor-851db8c1.js";import{A as ye}from"./AuthLayout-3236f682.js";import{u as ee}from"./react-hook-form-687afde5.js";import"./LoadingOverlay-87926629.js";import{b as re}from"./index.esm-9c6194ba.js";import{M as ae}from"./react-tooltip-7a26650a.js";import{T as be}from"./TimeSlotGrid-640f0461.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const je=new J;function ve({onNext:d,setValue:s,isSubmitting:y,defaultValues:l}){var N;const[h,f]=n.useState(!1),{register:w,handleSubmit:x,formState:{errors:u},setValue:c,watch:o}=ee({defaultValues:{hourly_rate:"",bio:"",photo:null}}),{dispatch:b}=n.useContext(Y);n.useEffect(()=>{l&&(c("photo",l.photo),c("hourly_rate",l.hourly_rate),c("bio",l.bio))},[l]);const H=async i=>{try{let v=new FormData;v.append("file",i);const a=await je.uploadImage(v);if(!(a!=null&&a.url))throw new Error("Upload failed");return a.url}catch(v){return console.error("Upload error:",v),M(b,"Failed to upload file. Please try again.",3e3,"error"),null}},I=async i=>{const v=i.target.files[0];if(console.log("uploading image"),f(!0),v.size>5*1024*1024){M(b,"File size should be less than 5MB",3e3,"error");return}try{const a=await H(v);c("photo",a)}catch(a){console.error("Image upload error:",a),M(b,"Failed to upload image",3e3,"error")}finally{f(!1)}},E=async i=>{try{if(!i.bio){M(b,"Please fill in all required fields",3e3,"error");return}let v=null;const a={hourly_rate:i.hourly_rate,bio:i.bio,photo:i.photo};console.log("finalData",a),s("hourly_rate",i.hourly_rate),s("bio",i.bio),s("photo",i.photo),d&&await d(a)}catch(v){console.error("Submit error:",v),M(b,"Failed to submit form. Please try again.",3e3,"error")}};return e.jsxs("div",{className:"mx-auto w-full max-w-xl flex-1 items-start justify-center",children:[h&&e.jsx(D,{}),e.jsx("h1",{className:"mb-8 text-center text-2xl font-semibold",children:"Set up your profile"}),e.jsxs("form",{onSubmit:x(E),className:"space-y-6",children:[e.jsxs("div",{className:"flex items-start justify-start gap-4",children:[e.jsx("div",{className:"relative mb-2 h-16 w-16",children:e.jsx("img",{src:o("photo")||"/default-avatar.png",alt:"Profile",className:"h-full w-full rounded-full object-cover"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("p",{className:"mb-1 text-sm font-medium",children:"Upload Image"}),e.jsx("p",{className:"mb-2 text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"}),e.jsxs("label",{className:"mt-2 cursor-pointer rounded-lg border border-gray-300 bg-white px-4 py-1.5 text-sm hover:bg-gray-50",children:[o("photo")?"Change":"Upload",e.jsx("input",{type:"file",className:"hidden",accept:"image/png,image/jpeg",onChange:I})]}),u.photo&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:u.photo.message})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium",children:"Bio"}),e.jsx("textarea",{rows:5,className:`w-full resize-none rounded-lg border ${u.bio?"border-red-500":"border-gray-300"} px-4 py-2 focus:border-transparent focus:outline-none focus:ring-2 focus:ring-green-500`,...w("bio",{required:"Bio is required",minLength:{value:50,message:"Bio should be at least 50 characters"},maxLength:{value:500,message:"Bio should not exceed 500 characters"}})}),u.bio&&e.jsx("p",{className:"mt-1 text-xs text-red-500",children:u.bio.message}),e.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:[((N=o("bio"))==null?void 0:N.length)||0,"/500 characters"]})]}),e.jsx(F,{type:"submit",loading:y,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Continue"})]})]})}const W=new J;function we({onNext:d,onBack:s,setValue:y,defaultValues:l,isSubmitting:h,clubSports:f,refetchCoachProfile:w}){var $,U;const[x,u]=n.useState({sport_id:"",type:"",sub_type:"",price:""}),[c,o]=n.useState({}),[b,H]=n.useState(!1),[I,E]=n.useState(!1),{dispatch:N}=n.useContext(Y),i=(f==null?void 0:f.filter(t=>t.status===1))||[],v=t=>{var B,L,C;const m={};t.sport_id||(m.sport_id="Sport is required");const k=i.find(T=>T.id===parseInt(t.sport_id)),A=(B=k==null?void 0:k.sport_types)==null?void 0:B.some(T=>T.type&&T.type.trim()!=="");A&&!t.type&&(m.type="Type is required");let O=!1;if(A&&t.type&&t.type!=="All"){const T=(L=k==null?void 0:k.sport_types)==null?void 0:L.find(P=>P.type===t.type);O=(C=T==null?void 0:T.subtype)==null?void 0:C.some(P=>P&&P.trim()!==""),O&&!t.sub_type&&(m.sub_type="Sub-type is required")}return t.price||(m.price="Price is required"),t.price&&Number(t.price)<=0&&(m.price="Price must be greater than 0"),t.type==="All"&&(delete m.type,t.sub_type==="All"&&delete m.sub_type),m},a=t=>{const{name:m,value:k}=t.target;u(A=>({...A,[m]:k})),o(A=>({...A,[m]:""}))},j=async()=>{var O,B,L;const t={...x},m=i.find(C=>C.id===parseInt(x.sport_id));if(!((O=m==null?void 0:m.sport_types)==null?void 0:O.some(C=>C.type&&C.type.trim()!=="")))t.type="",t.sub_type="";else if(t.type&&t.type!=="All"){const C=(B=m==null?void 0:m.sport_types)==null?void 0:B.find(P=>P.type===t.type);((L=C==null?void 0:C.subtype)==null?void 0:L.some(P=>P&&P.trim()!==""))||(t.sub_type="")}const A=v(t);if(Object.keys(A).length>0){o(A);return}E(!0);try{W.setTable("coach_sports"),await W.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",{sport_ids:[t]},"POST"),u({sport_id:"",type:"",sub_type:"",price:""}),await w(),M(N,"Sport added successfully",3e3,"success")}catch(C){console.log(C),M(N,C.message,3e3,"error")}finally{E(!1)}},S=async t=>{H(!0);try{W.setTable("coach_sports"),await W.callRestAPI({id:t},"DELETE"),await w(),M(N,"Sport deleted successfully",3e3,"success")}catch(m){console.log(m),M(N,m.message,3e3,"error")}finally{H(!1)}},_=async()=>{if(!(l!=null&&l.sports)||(l==null?void 0:l.sports.length)===0){M(N,"Please add at least one sport",3e3,"error");return}d&&await d()},p=i.find(t=>t.id===parseInt(x.sport_id));return e.jsxs("div",{className:"mx-auto w-full max-w-xl flex-1 items-start justify-center",children:[b&&e.jsx(D,{}),e.jsx("h1",{className:"mb-8 text-center text-2xl font-semibold",children:"What sports are you coaching?"}),e.jsxs("div",{className:"space-y-6",children:[(l==null?void 0:l.sports.length)>0&&e.jsxs("div",{className:"mt-6 rounded-xl border border-gray-200 p-4 shadow-sm",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold",children:"Added Sports"}),e.jsx("ul",{className:"divide-y divide-gray-200",children:l==null?void 0:l.sports.map((t,m)=>{var k;return e.jsxs("li",{className:"flex items-center justify-between py-3 transition-colors hover:bg-gray-50",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-4",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sport"}),e.jsx("span",{className:"font-medium",children:(k=i.find(A=>A.id===parseInt(t.sport_id)))==null?void 0:k.name})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Type"}),e.jsx("span",{className:"font-medium",children:t.type})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Sub-type"}),e.jsx("span",{className:"font-medium",children:t.sub_type})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Price"}),e.jsxs("span",{className:"font-medium",children:["$",t.price]})]})]}),e.jsx("button",{onClick:()=>S(t.id),className:"ml-4 text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49737 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49737 4.97796Z",fill:"#868C98"})})})]},m)})})]}),e.jsxs("div",{className:"rounded-xl border border-gray-200 p-4 shadow-sm",children:[e.jsx("h2",{className:"mb-4 text-lg font-semibold",children:"Add New Sport"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("select",{name:"sport_id",value:x.sport_id,onChange:a,className:`w-full rounded-lg border ${c.sport_id?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sport"}),i.map(t=>e.jsx("option",{value:t.id,children:t.name},t.id))]}),c.sport_id&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:c.sport_id})]}),p&&e.jsxs(e.Fragment,{children:[p.sport_types.some(t=>t.type&&t.type.trim()!=="")?e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Type"}),e.jsx(re,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-type-tooltip",size:16}),e.jsx(ae,{id:"sport-type-tooltip",className:"z-50 max-w-xs rounded-xl bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"type",value:x.type,onChange:a,className:`w-full rounded-lg border ${c.type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Type"}),p.sport_types.length>=2&&e.jsx("option",{value:"All",children:"All"}),p.sport_types.map((t,m)=>t.type&&t.type.trim()!==""&&e.jsx("option",{value:t.type,children:t.type},m))]}),c.type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:c.type})]}):null,x.type&&p.sport_types.some(t=>t.type===x.type&&t.subtype&&t.subtype.length>0)&&e.jsxs("div",{children:[e.jsxs("div",{className:"mb-2 flex items-center gap-2",children:[e.jsx("label",{className:"text-sm text-gray-700",children:"Sub-type"}),e.jsx(re,{className:"cursor-help text-gray-400 hover:text-gray-600","data-tooltip-id":"sport-subtype-tooltip",size:16}),e.jsx(ae,{id:"sport-subtype-tooltip",className:"z-50 max-w-xs rounded-lg bg-white p-2 text-sm text-black",style:{backgroundColor:"#fff",border:"1px solid #e4e4e7",color:"#000",borderRadius:"12px",padding:"12px",maxWidth:"300px"},children:"Unless you intend to charge different rates for different sport types or subtypes, select 'All' for both. If you play a sport across multiple types/subtypes, ensure all are added individually to appear in relevant searches."})]}),e.jsxs("select",{name:"sub_type",value:x.sub_type,onChange:a,className:`w-full rounded-lg border ${c.sub_type?"border-red-500":"border-gray-300"} p-2`,children:[e.jsx("option",{value:"",children:"Select Sub-type"}),x.type==="All"?e.jsx("option",{value:"All",children:"All"}):e.jsxs(e.Fragment,{children:[(($=p.sport_types.find(t=>t.type===x.type))==null?void 0:$.subtype.length)>=2&&e.jsx("option",{value:"All",children:"All"}),(U=p.sport_types.find(t=>t.type===x.type))==null?void 0:U.subtype.filter(t=>t&&t.trim()!=="").map((t,m)=>e.jsx("option",{value:t,children:t},m))]})]}),c.sub_type&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:c.sub_type})]}),e.jsxs("div",{children:[e.jsx("input",{type:"number",name:"price",value:x.price,onChange:a,placeholder:"Enter price",className:`w-full rounded-lg border ${c.price?"border-red-500":"border-gray-300"} p-2`}),c.price&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:c.price})]}),e.jsx("button",{type:"button",onClick:j,disabled:I,className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80 disabled:opacity-50",children:I?"Adding Sport...":"Add Sport"})]})]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(F,{type:"button",onClick:s,className:"w-full rounded-xl border border-gray-300 bg-white py-3 text-gray-700 hover:bg-gray-50",children:"Back"}),e.jsx(F,{type:"button",onClick:_,loading:h,className:"w-full rounded-xl bg-primaryGreen py-3 text-white disabled:opacity-50",children:"Continue"})]})]})]})}const Ne=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Q=(d,s)=>{if(!s||s.length===0)return!0;const[y,l]=d.split(":"),h=parseInt(y)*60+parseInt(l);return s.some(f=>{const[w,x]=f.from.split(":"),[u,c]=f.until.split(":"),o=parseInt(w)*60+parseInt(x);let b=parseInt(u)*60+parseInt(c);return b===0&&(u==="00"||u==="0")&&(b=24*60),b<=o?h>=o||h<b:h>=o&&h<b})},X=(d,s)=>!s||s.length===0?!1:s.includes(d),le=(d,s)=>{if(!s||s.length===0)return[];const y=d.toLowerCase();return s.reduce((l,h)=>{const f=h.days.find(w=>w.day===y);return f&&l.push({name:h.name,timeslots:f.timeslots}),l},[])},ie=(d,s)=>{if(!s||s.length===0)return!1;const y=d.includes(":00",5)?d:`${d}:00`;return s.some(l=>l.timeslots.includes(y))},oe=(d,s)=>{if(!s||s.length===0)return null;const y=d.includes(":00",5)?d:`${d}:00`,l=s.find(h=>h.timeslots.includes(y));return l?l.name:null};function Se({onNext:d,isSubmitting:s,selectedTimeSlot:y,setSelectedTimeSlot:l,originalAvailability:h,setOriginalAvailability:f,setValue:w,defaultValues:x}){n.useState({});const{club:u}=ce(),{dispatch:c}=n.useContext(Y),[o,b]=n.useState({times:[],daysOff:[],exceptions:[]});ee();const H=(a,j)=>{const S=y==null?void 0:y.find(p=>p.day===j.toLowerCase());if(!S)return!1;const _=a.replace(":00","");return S.timeslots.some(p=>p===a||p.replace(":00","")===_)},I=(a,j)=>{if(X(j,o.daysOff)){M(c,`${j} is a club day off`,3e3,"error");return}if(!Q(a,o.times)){M(c,"This time is outside club hours",3e3,"error");return}const S=le(j,o.exceptions);if(ie(a,S)){const _=oe(a,S);M(c,`This time is marked as "${_||"Exception"}"`,3e3,"warning")}l(_=>_.map(p=>{if(p.day===j.toLowerCase()){const $=a.replace(":00","");if(!p.timeslots.some(t=>t===a||t===$))return{...p,timeslots:[...p.timeslots,a].sort()}}return p}))},E=(a,j)=>{l(S=>S.map(_=>_.day===j.toLowerCase()?{..._,timeslots:_.timeslots.filter(p=>p!==a&&p!==a.replace(":00",""))}:_))},N=()=>y.filter(a=>a.timeslots.length>0),i=async()=>{try{const a=N();w("availability",a),await d()}catch(a){console.error("Error saving working hours:",a)}},v=async()=>{w("availability",[]),await d()};return n.useEffect(()=>{if(u){const a=u.times?JSON.parse(u.times):[],j=u.days_off?JSON.parse(u.days_off):[],S=u.exceptions?JSON.parse(u.exceptions):[];b({times:a,daysOff:j,exceptions:S})}},[u]),e.jsxs("div",{className:"mx-auto w-full max-w-7xl px-4",children:[e.jsx("h1",{className:"mb-8 text-center text-2xl font-semibold",children:"Set your working hours"}),e.jsxs("div",{children:[e.jsx(be,{days:Ne,isSelected:H,handleTimeSelect:I,handleDeleteTime:E,renderTimeSlotContent:(a,j)=>{if(X(j,o.daysOff))return e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});if(!Q(a.value,o.times))return e.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-red-50 text-xs font-medium text-red-500",children:"Club Closed"});const S=le(j,o.exceptions);if(ie(a.value,S)){const _=oe(a.value,S);return e.jsxs("div",{className:"absolute inset-0 flex items-center justify-center bg-orange-50 text-xs font-medium text-orange-500",children:[e.jsx(pe,{className:"mr-1 hidden sm:inline"}),e.jsx("span",{className:"truncate",children:_||"Exception"})]})}return null},disableTimeSlot:(a,j)=>!!(X(j,o.daysOff)||!Q(a.value,o.times))}),e.jsxs("div",{className:"mt-8 flex justify-center gap-4",children:[e.jsx(F,{type:"button",onClick:v,className:"rounded-lg border border-gray-300 px-6 py-2 hover:bg-gray-50",children:"Skip"}),e.jsx(F,{type:"submit",onClick:i,loading:s,disabled:N().length===0,className:"rounded-lg bg-primaryGreen px-6 py-2 text-white hover:bg-primaryGreen/80 disabled:opacity-50",children:"Continue"})]})]})]})}const ne=new J;function _e({onNext:d,stripeConnectionData:s,isSubmitting:y,setStripeConnectionData:l}){const[h]=fe.useState(y||!1),[f,w]=n.useState(!1),[x,u]=n.useState(!1),[c,o]=n.useState(!1),b=localStorage.getItem("role"),H=async()=>{try{const i=await ne.callRawAPI(`/v3/api/custom/courtmatchup/${b}/stripe/account/verify`,{},"POST");return l&&l(i),i}catch(i){return console.error("Error checking Stripe connection:",i),!1}},I=async()=>{w(!0);try{const i=await ne.callRawAPI(`/v3/api/custom/courtmatchup/${b}/stripe/onboarding`,{},"POST");i&&i.url&&window.open(i.url,"_blank")}catch(i){console.error("Error connecting to Stripe:",i)}w(!1)};n.useEffect(()=>{if(f===!1){const i=setTimeout(()=>{H()},2e3);return()=>clearTimeout(i)}},[f]);const E=async()=>{u(!0),await d(),u(!1)},N=async()=>{o(!0);try{await d({skip_payment:!0})}finally{o(!1)}};return e.jsx("div",{className:"mx-auto w-fit",children:e.jsx("div",{className:"flex flex-col bg-white pb-7",children:e.jsxs("section",{className:"flex w-[432px] max-w-full flex-col justify-center",children:[e.jsx("div",{className:"flex w-full max-w-[432px] flex-col self-center max-md:max-w-full",children:e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3",children:[e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_26852)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_26852)"}),e.jsxs("g",{filter:"url(#filter0_d_397_26852)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M47.3125 34.4999H47V42.7054C47.6877 42.8975 48.2531 43.4195 48.4868 44.1207L48.9035 45.3707C49.3756 46.7872 48.3213 48.2499 46.8282 48.2499H27.1718C25.6787 48.2499 24.6244 46.7872 25.0965 45.3707L25.5132 44.1207C25.7469 43.4195 26.3123 42.8975 27 42.7054V34.4999H26.6875C25.4794 34.4999 24.5 33.5206 24.5 32.3124V31.7352C24.5 30.9099 24.9645 30.1549 25.7011 29.7828L36.0136 24.5729C36.6339 24.2596 37.3661 24.2596 37.9864 24.5729L48.2989 29.7828C49.0355 30.1549 49.5 30.9099 49.5 31.7352V32.3124C49.5 33.5206 48.5206 34.4999 47.3125 34.4999ZM42 34.4999H45.125V42.6249H42V34.4999ZM32 42.6249H28.875V34.4999H32V42.6249ZM33.875 42.6249V34.4999H40.125V42.6249H33.875Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_26852",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_26852"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_26852",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]}),e.jsx("p",{className:"w-full text-center text-2xl font-medium leading-none text-gray-950 max-md:max-w-full",children:"Connect stripe"})]})}),e.jsxs("div",{className:"mt-10 flex w-full flex-col gap-4 self-center max-md:max-w-full",children:[((s==null?void 0:s.complete)||(s==null?void 0:s.details_submitted))&&e.jsxs("div",{className:"flex flex-col gap-4 rounded-lg border border-green-200 bg-green-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-lg font-medium text-gray-900",children:s!=null&&s.complete?"Stripe account connected":"Stripe account details submitted"})]}),e.jsxs("div",{className:"mt-2 grid grid-cols-1 gap-3",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-xs text-gray-500",children:"Account ID"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:s.account_id})]}),e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("div",{className:"mt-2 text-sm text-gray-600",children:s!=null&&s.complete?"You can now receive payments from your club members.":"Your Stripe account details have been submitted and are pending approval. You can continue with the setup process."}),e.jsx(F,{onClick:E,className:"mt-4 w-full rounded-xl bg-primaryGreen px-4 py-3 text-sm font-medium text-white",loading:h||x,children:h?"Processing...":"Continue"})]}),!(s!=null&&s.complete||s!=null&&s.details_submitted)&&e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"rounded-lg border border-yellow-200 bg-yellow-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"No Stripe account connected"})]}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Connect your Stripe account to receive payments from your clients. This is required for processing payments on the platform."}),s&&e.jsxs("div",{className:"mt-3 grid grid-cols-1 gap-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${s.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("button",{onClick:I,className:"w-full rounded-xl bg-primaryBlue px-4 py-3 text-sm font-medium text-white",disabled:f||c,children:f?"Connecting...":"Connect Stripe Account"}),e.jsxs("div",{className:"mt-4 text-center",children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"- OR -"}),e.jsx("button",{onClick:N,className:"w-full rounded-xl border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50",disabled:f||c,children:c?"Processing...":"Will not be paid through Court Matchup"})]})]})]})]})})})}const q=new J,Ce=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"];function mt(){const d=ge(),{state:s,dispatch:y}=n.useContext(xe),[l,h]=n.useState(0),[f,w]=n.useState(!1),{dispatch:x,state:u}=n.useContext(Y),{triggerRefetch:c}=ce(),[o,b]=n.useState(null),[H,I]=n.useState(null),[E,N]=n.useState(!1),[i,v]=n.useState(null),[a,j]=n.useState(null),[S,_]=n.useState([]),[p,$]=n.useState(!1),[U,t]=n.useState(null),[m,k]=n.useState(!0),A=localStorage.getItem("role"),O=r=>{var Z,g;return!r||!r.bio?0:!((Z=r.sports)!=null&&Z.length)||!r.sports?1:(g=r.availability)!=null&&g.length?!r.account_details||JSON.stringify(r.account_details).length?3:4:2},{register:B,setValue:L,formState:{errors:C},watch:T,getValues:P}=ee({defaultValues:{hourly_rate:"",bio:"",photo:null,sports:[],availability:[],account_details:[]}}),de=async()=>{k(!0);try{const r=await q.callRawAPI(`/v3/api/custom/courtmatchup/${A}/stripe/account/verify`,{},"POST");t(r)}catch(r){return console.error("Error checking Stripe connection:",r),!1}finally{k(!1)}},K=async()=>{$(!0);try{const r=await q.callRawAPI("/v3/api/custom/courtmatchup/coach/profile",{},"GET");if(await de(),(r==null?void 0:r.completed)==1){d("/coach/dashboard");return}const Z=await q.callRawAPI(`/v3/api/custom/courtmatchup/user/club/${r.club_id}`,{},"GET");I(r),b(Z.model),_(Z.sports),L("hourly_rate",(r==null?void 0:r.hourly_rate)||""),L("bio",(r==null?void 0:r.bio)||""),L("photo",(r==null?void 0:r.photo)||"");const g=JSON.parse(r.availability),R=Ce.map(G=>({day:G.toLowerCase(),timeslots:[]}));return L("availability",R),L("account_details",(r==null?void 0:r.account_details)||[]),L("sports",(r==null?void 0:r.sports)||[]),v(R),g&&Array.isArray(g)&&g.length>0&&g.forEach(G=>{const te=R.findIndex(ue=>ue.day===G.day.toLowerCase());te!==-1&&(R[te].timeslots=G.timeslots)}),v(R),j(g||[]),r}catch(r){se(y,r.code),M(x,r.message,3e3,"error")}finally{$(!1)}},V=async r=>{const Z=P();N(!0);try{const g={};switch(l){case 0:g.bio=r.bio,r.photo&&(g.photo=r.photo);break;case 1:break;case 2:g.availability=Z.availability,g.default_availability=Z.availability;break;case 3:r&&r.skip_payment?g.not_paid_through_platform=1:g.not_paid_through_platform=0,g.completed=1;break}await q.callRawAPI("/v3/api/custom/courtmatchup/coach/profile-edit",g,"POST");const R=await K();I(R),l===3?(c(),w(!0)):h(G=>G+1)}catch(g){console.error(g),M(x,g.message,3e3,"error"),se(y,g.code)}finally{N(!1)}},z=()=>{h(Math.max(l-1,0))};n.useEffect(()=>{(async()=>{const r=await K();r&&h(O(r))})()},[]),n.useEffect(()=>{he({title:o==null?void 0:o.name,path:"/coach/profile-setup",clubName:o==null?void 0:o.name,favicon:o==null?void 0:o.club_logo,description:"Coach Profile Setup"})},[o]);const me=()=>{const r=T();switch(l){case 0:return e.jsx(ve,{onNext:V,register:B,setValue:L,errors:C,defaultValues:r,isSubmitting:E});case 1:return e.jsx(we,{onNext:V,onBack:z,register:B,setValue:L,defaultValues:r,isSubmitting:E,clubSports:S,refetchCoachProfile:K});case 2:return e.jsx(Se,{onNext:V,onBack:z,selectedTimeSlot:i,originalAvailability:a,setSelectedTimeSlot:v,setOriginalAvailability:j,setValue:L,defaultValues:r,isSubmitting:E});case 3:return e.jsx(_e,{onNext:V,onBack:z,isSubmitting:E,stripeConnectionData:U,setStripeConnectionData:t});default:return null}};return console.log(l),e.jsxs(ye,{children:[p&&e.jsx(D,{}),e.jsxs("div",{className:"flex flex-col bg-white pb-7",children:[e.jsx("div",{className:"flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5",children:e.jsx("div",{className:"mb-10",children:l!==0&&e.jsxs("button",{className:"mt-5 flex items-center gap-2 text-[#525866]",onClick:z,children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.33333 4.79199L3.125 10.0003L8.33333 15.2087M3.75 10.0003H16.875",stroke:"#525866","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),e.jsx("span",{children:"Back"})]})})}),e.jsx("div",{className:"",children:me()}),f&&e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsx("div",{className:"w-full max-w-xl rounded-2xl bg-white ",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex items-start gap-4 p-5",children:[e.jsx("div",{className:"",children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:"Sign up complete!"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Congratulations! You have successfully signed up for Court Matchup. You can now access your coach portal"})]})]}),e.jsx("div",{className:"flex w-full justify-end border-t border-gray-200 p-5",children:e.jsx(F,{onClick:()=>{c(),d("/coach/dashboard")},className:"w-fit rounded-xl bg-primaryGreen px-5 py-3 text-white",children:"Continue to Coach portal!"})})]})})})]})]})}export{mt as default};
