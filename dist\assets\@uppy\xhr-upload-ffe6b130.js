import{P as C,N as f,B,i as T,f as D,a as L,R as H,b as q}from"./aws-s3-c5961f7a.js";import{E as X}from"./aws-s3-multipart-9d71a8f2.js";const E=()=>{};function k(s,t){t===void 0&&(t={});const{body:e=null,headers:a={},method:o="GET",onBeforeRequest:r=E,onUploadProgress:l=E,shouldRetry:i=()=>!0,onAfterRequest:u=E,onTimeout:w=E,responseType:d,retries:c=3,signal:F=null,timeout:_=3e4,withCredentials:j=!1}=t,x=p=>.3*2**(p-1)*1e3,v=new C(_,w);function R(p){return p===void 0&&(p=0),new Promise(async(O,b)=>{const n=new XMLHttpRequest;n.open(o,s,!0),n.withCredentials=j,d&&(n.responseType=d),F==null||F.addEventListener("abort",()=>{n.abort(),b(new DOMException("Aborted","AbortError"))}),n.onload=async()=>{await u(n,p),n.status>=200&&n.status<300?(v.done(),O(n)):i(n)&&p<c?setTimeout(()=>{R(p+1).then(O,b)},x(p)):(v.done(),b(new f(n.statusText,n)))},n.onerror=()=>{i(n)&&p<c?setTimeout(()=>{R(p+1).then(O,b)},x(p)):(v.done(),b(new f(n.statusText,n)))},n.upload.onprogress=U=>{v.progress(),l(U)},a&&Object.keys(a).forEach(U=>{n.setRequestHeader(U,a[U])}),await r(n,p),n.send(e)})}return R()}const W={strings:{uploadStalled:"Upload has not made any progress for %{seconds} seconds. You may want to retry it."}};function h(s,t){if(!Object.prototype.hasOwnProperty.call(s,t))throw new TypeError("attempted to use private field on non-instance");return s}var z=0;function y(s){return"__private_"+z+++"_"+s}const I={version:"3.6.8"};function Q(s,t){let e=t;return e||(e=new Error("Upload error")),typeof e=="string"&&(e=new Error(e)),e instanceof Error||(e=Object.assign(new Error("Upload error"),{data:e})),q(s)?(e=new f(e,s),e):(e.request=s,e)}function N(s){return s.data.slice(0,s.data.size,s.meta.type)}const $={formData:!0,fieldName:"file",method:"post",allowedMetaFields:null,responseUrlFieldName:"url",bundle:!1,headers:{},timeout:30*1e3,limit:5,withCredentials:!1,responseType:"",getResponseData(s){let t={};try{t=JSON.parse(s)}catch{}return t},getResponseError(s,t){let e=new Error("Upload error");return q(t)&&(e=new f(e,t)),e},validateStatus(s){return s>=200&&s<300}};var m=y("getFetcher"),M=y("uploadLocalFile"),P=y("uploadBundle"),A=y("getCompanionClientArgs"),S=y("uploadFiles"),g=y("handleUpload");class J extends B{constructor(t,e){if(super(t,{...$,fieldName:e.bundle?"files[]":"file",...e}),Object.defineProperty(this,S,{value:Y}),Object.defineProperty(this,A,{value:V}),Object.defineProperty(this,P,{value:K}),Object.defineProperty(this,M,{value:G}),Object.defineProperty(this,m,{writable:!0,value:void 0}),Object.defineProperty(this,g,{writable:!0,value:async a=>{if(a.length===0){this.uppy.log("[XHRUpload] No files to upload!");return}this.opts.limit===0&&!this.opts[T]&&this.uppy.log("[XHRUpload] When uploading multiple files at once, consider setting the `limit` option (to `10` for example), to limit the number of concurrent uploads, which helps prevent memory and network issues: https://uppy.io/docs/xhr-upload/#limit-0","warning"),this.uppy.log("[XHRUpload] Uploading...");const o=this.uppy.getFilesByIds(a),r=D(o),l=L(r);if(this.uppy.emit("upload-start",l),this.opts.bundle){if(r.some(u=>u.isRemote))throw new Error("Can’t upload remote files when the `bundle: true` option is set");if(typeof this.opts.headers=="function")throw new TypeError("`headers` may not be a function when the `bundle: true` option is set");await h(this,P)[P](r)}else await h(this,S)[S](r)}}),this.type="uploader",this.id=this.opts.id||"XHRUpload",this.defaultLocale=W,this.i18nInit(),T in this.opts?this.requests=this.opts[T]:this.requests=new H(this.opts.limit),this.opts.bundle&&!this.opts.formData)throw new Error("`opts.formData` must be true when `opts.bundle` is enabled.");if(this.opts.bundle&&typeof this.opts.headers=="function")throw new Error("`opts.headers` can not be a function when the `bundle: true` option is set.");if((e==null?void 0:e.allowedMetaFields)===void 0&&"metaFields"in this.opts)throw new Error("The `metaFields` option has been renamed to `allowedMetaFields`.");this.uploaderEvents=Object.create(null),h(this,m)[m]=a=>async(o,r)=>{try{var l;const i=await k(o,{...r,method:r==null||(l=r.method)==null?void 0:l.toUpperCase(),onTimeout:d=>{const c=Math.ceil(d/1e3),F=new Error(this.i18n("uploadStalled",{seconds:c}));this.uppy.emit("upload-stalled",F,a)},onUploadProgress:d=>{if(d.lengthComputable)for(const c of a)this.uppy.emit("upload-progress",c,{uploader:this,bytesUploaded:d.loaded/d.total*c.size,bytesTotal:c.size})}});if(!this.opts.validateStatus(i.status,i.responseText,i))throw new f(i.statusText,i);const u=this.opts.getResponseData(i.responseText,i),w=u==null?void 0:u[this.opts.responseUrlFieldName];for(const d of a)this.uppy.emit("upload-success",d,{status:i.status,body:u,uploadURL:w});return i}catch(i){if(i.name==="AbortError")return;if(i instanceof f){const u=i.request,w=Q(u,this.opts.getResponseError(u.responseText,u));for(const d of a)this.uppy.emit("upload-error",d,w)}throw i}}}getOptions(t){const e=this.uppy.getState().xhrUpload,{headers:a}=this.opts,o={...this.opts,...e||{},...t.xhrUpload||{},headers:{}};return typeof a=="function"?o.headers=a(t):Object.assign(o.headers,this.opts.headers),e&&Object.assign(o.headers,e.headers),t.xhrUpload&&Object.assign(o.headers,t.xhrUpload.headers),o}addMetadata(t,e,a){(Array.isArray(a.allowedMetaFields)?a.allowedMetaFields:Object.keys(e)).forEach(r=>{const l=e[r];Array.isArray(l)?l.forEach(i=>t.append(r,i)):t.append(r,l)})}createFormDataUpload(t,e){const a=new FormData;this.addMetadata(a,t.meta,e);const o=N(t);return t.name?a.append(e.fieldName,o,t.meta.name):a.append(e.fieldName,o),a}createBundledUpload(t,e){const a=new FormData,{meta:o}=this.uppy.getState();return this.addMetadata(a,o,e),t.forEach(r=>{const l=this.getOptions(r),i=N(r);r.name?a.append(l.fieldName,i,r.name):a.append(l.fieldName,i)}),a}install(){if(this.opts.bundle){const{capabilities:t}=this.uppy.getState();this.uppy.setState({capabilities:{...t,individualCancellation:!1}})}this.uppy.addUploader(h(this,g)[g])}uninstall(){if(this.opts.bundle){const{capabilities:t}=this.uppy.getState();this.uppy.setState({capabilities:{...t,individualCancellation:!0}})}this.uppy.removeUploader(h(this,g)[g])}}async function G(s){const t=new X(this.uppy),e=new AbortController,a=this.requests.wrapPromiseFunction(async()=>{const o=this.getOptions(s),r=h(this,m)[m]([s]),l=o.formData?this.createFormDataUpload(s,o):s.data;return r(o.endpoint,{...o,body:l,signal:e.signal})});t.onFileRemove(s.id,()=>e.abort()),t.onCancelAll(s.id,o=>{let{reason:r}=o;r==="user"&&e.abort()});try{await a().abortOn(e.signal)}catch(o){if(o.message!=="Cancelled")throw o}finally{t.remove()}}async function K(s){const t=new AbortController,e=this.requests.wrapPromiseFunction(async()=>{var o;const r=(o=this.uppy.getState().xhrUpload)!=null?o:{},l=h(this,m)[m](s),i=this.createBundledUpload(s,{...this.opts,...r});return l(this.opts.endpoint,{...this.opts,body:i,signal:t.signal})});function a(){t.abort()}this.uppy.once("cancel-all",a);try{await e().abortOn(t.signal)}catch(o){if(o.message!=="Cancelled")throw o}finally{this.uppy.off("cancel-all",a)}}function V(s){var t;const e=this.getOptions(s),a=Array.isArray(e.allowedMetaFields)?e.allowedMetaFields:Object.keys(s.meta);return{...(t=s.remote)==null?void 0:t.body,protocol:"multipart",endpoint:e.endpoint,size:s.data.size,fieldname:e.fieldName,metadata:Object.fromEntries(a.map(o=>[o,s.meta[o]])),httpMethod:e.method,useFormData:e.formData,headers:e.headers}}async function Y(s){await Promise.allSettled(s.map(t=>{if(t.isRemote){const e=()=>this.requests,a=new AbortController,o=l=>{l.id===t.id&&a.abort()};this.uppy.on("file-removed",o);const r=this.uppy.getRequestClientForFile(t).uploadRemoteFile(t,h(this,A)[A](t),{signal:a.signal,getQueue:e});return this.requests.wrapSyncFunction(()=>{this.uppy.off("file-removed",o)},{priority:-1})(),r}return h(this,M)[M](t)}))}J.VERSION=I.version;export{J as X};
