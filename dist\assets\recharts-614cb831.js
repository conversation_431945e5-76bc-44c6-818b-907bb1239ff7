import{L as Jt,Q as X,b as Ya,M as dt,_ as df,i as vf,N as hf,y as yf,q as mf,k as gf}from"./@craftjs/core-d3c11b68.js";import{g as xe,r as B,b as P}from"./vendor-851db8c1.js";import{t as cl}from"./@uppy/aws-s3-c5961f7a.js";import{b as bf,O as Zr,e as Of,k as Se,S as xf,s as ll,c as wf,d as Af,f as Pf,g as Sf,h as jf,l as Ef,m as jo,r as Za,n as Li,o as $f,p as _f,q as Tf,u as If,v as kf,w as Cf,x as Mf,y as zi,z as Df,A as Nf,B as hr,C as Bf,D as Rf,E as Lf,F as zf,G as Wf,H as Ff,I as Kf,J as Gf,K as qf,L as Vf,M as Xf,N as Hf,P as Uf,Q as Yf,R as Zf,T as Jf,i as Qf,U as sl,V as Je,W as ep,X as tp,Y as rp,Z as np,$ as ip,a0 as Hn,a1 as ap,a2 as op,a3 as up,a4 as cp,a5 as lp,a6 as rn,a7 as sp,a8 as fl,a9 as fp,aa as pp,ab as dp,ac as vp,ad as hp,ae as yp,af as mp,ag as gp,ah as bp,ai as Op,aj as xp,ak as wp,al as Ap,am as Ja,an as pl,ao as Pp,ap as Sp,aq as jp}from"./@nivo/heatmap-ba1ecfff.js";import{_ as Ep,t as $p,a as _p}from"./react-google-maps-db82edcf.js";import{P as H}from"./@fortawesome/react-fontawesome-13437837.js";function dl(t){var e,r,n="";if(typeof t=="string"||typeof t=="number")n+=t;else if(typeof t=="object")if(Array.isArray(t)){var i=t.length;for(e=0;e<i;e++)t[e]&&(r=dl(t[e]))&&(n&&(n+=" "),n+=r)}else for(r in t)t[r]&&(n&&(n+=" "),n+=r);return n}function U(){for(var t,e,r=0,n="",i=arguments.length;r<i;r++)(t=arguments[r])&&(e=dl(t))&&(n&&(n+=" "),n+=e);return n}function Tp(t){return t==null}var Ip=Tp;const Y=xe(Ip);var vl={exports:{}},J={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qa=Symbol.for("react.element"),eo=Symbol.for("react.portal"),Un=Symbol.for("react.fragment"),Yn=Symbol.for("react.strict_mode"),Zn=Symbol.for("react.profiler"),Jn=Symbol.for("react.provider"),Qn=Symbol.for("react.context"),kp=Symbol.for("react.server_context"),ei=Symbol.for("react.forward_ref"),ti=Symbol.for("react.suspense"),ri=Symbol.for("react.suspense_list"),ni=Symbol.for("react.memo"),ii=Symbol.for("react.lazy"),Cp=Symbol.for("react.offscreen"),hl;hl=Symbol.for("react.module.reference");function Ie(t){if(typeof t=="object"&&t!==null){var e=t.$$typeof;switch(e){case Qa:switch(t=t.type,t){case Un:case Zn:case Yn:case ti:case ri:return t;default:switch(t=t&&t.$$typeof,t){case kp:case Qn:case ei:case ii:case ni:case Jn:return t;default:return e}}case eo:return e}}}J.ContextConsumer=Qn;J.ContextProvider=Jn;J.Element=Qa;J.ForwardRef=ei;J.Fragment=Un;J.Lazy=ii;J.Memo=ni;J.Portal=eo;J.Profiler=Zn;J.StrictMode=Yn;J.Suspense=ti;J.SuspenseList=ri;J.isAsyncMode=function(){return!1};J.isConcurrentMode=function(){return!1};J.isContextConsumer=function(t){return Ie(t)===Qn};J.isContextProvider=function(t){return Ie(t)===Jn};J.isElement=function(t){return typeof t=="object"&&t!==null&&t.$$typeof===Qa};J.isForwardRef=function(t){return Ie(t)===ei};J.isFragment=function(t){return Ie(t)===Un};J.isLazy=function(t){return Ie(t)===ii};J.isMemo=function(t){return Ie(t)===ni};J.isPortal=function(t){return Ie(t)===eo};J.isProfiler=function(t){return Ie(t)===Zn};J.isStrictMode=function(t){return Ie(t)===Yn};J.isSuspense=function(t){return Ie(t)===ti};J.isSuspenseList=function(t){return Ie(t)===ri};J.isValidElementType=function(t){return typeof t=="string"||typeof t=="function"||t===Un||t===Zn||t===Yn||t===ti||t===ri||t===Cp||typeof t=="object"&&t!==null&&(t.$$typeof===ii||t.$$typeof===ni||t.$$typeof===Jn||t.$$typeof===Qn||t.$$typeof===ei||t.$$typeof===hl||t.getModuleId!==void 0)};J.typeOf=Ie;vl.exports=J;var Mp=vl.exports,Dp=bf;function Np(t){return Dp(t)&&t!=+t}var Bp=Np;const Jr=xe(Bp);var be=function(e){return e===0?0:e>0?1:-1},st=function(e){return Zr(e)&&e.indexOf("%")===e.length-1},N=function(e){return Of(e)&&!Jr(e)},se=function(e){return N(e)||Zr(e)},Rp=0,Qr=function(e){var r=++Rp;return"".concat(e||"").concat(r)},Oe=function(e,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(!N(e)&&!Zr(e))return n;var a;if(st(e)){var o=e.indexOf("%");a=r*parseFloat(e.slice(0,o))/100}else a=+e;return Jr(a)&&(a=n),i&&a>r&&(a=r),a},Ue=function(e){if(!e)return null;var r=Object.keys(e);return r&&r.length?e[r[0]]:null},Lp=function(e){if(!Array.isArray(e))return!1;for(var r=e.length,n={},i=0;i<r;i++)if(!n[e[i]])n[e[i]]=!0;else return!0;return!1},Xe=function(e,r){return N(e)&&N(r)?function(n){return e+n*(r-e)}:function(){return r}};function Wi(t,e,r){return!t||!t.length?null:t.find(function(n){return n&&(typeof e=="function"?e(n):Se(n,e))===r})}function _t(t,e){for(var r in t)if({}.hasOwnProperty.call(t,r)&&(!{}.hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if({}.hasOwnProperty.call(e,n)&&!{}.hasOwnProperty.call(t,n))return!1;return!0}function Fi(t){"@babel/helpers - typeof";return Fi=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fi(t)}var zp=["viewBox","children"],Wp=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],Eo=["points","pathLength"],ji={svg:zp,polygon:Eo,polyline:Eo},to=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],vn=function(e,r){if(!e||typeof e=="function"||typeof e=="boolean")return null;var n=e;if(B.isValidElement(e)&&(n=e.props),!Jt(n))return null;var i={};return Object.keys(n).forEach(function(a){to.includes(a)&&(i[a]=r||function(o){return n[a](n,o)})}),i},Fp=function(e,r,n){return function(i){return e(r,n,i),null}},vt=function(e,r,n){if(!Jt(e)||Fi(e)!=="object")return null;var i=null;return Object.keys(e).forEach(function(a){var o=e[a];to.includes(a)&&typeof o=="function"&&(i||(i={}),i[a]=Fp(o,r,n))}),i},Kp=["children"],Gp=["children"];function $o(t,e){if(t==null)return{};var r=qp(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function qp(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}var _o={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart"},Ke=function(e){return typeof e=="string"?e:e?e.displayName||e.name||"Component":""},To=null,Ei=null,ro=function t(e){if(e===To&&Array.isArray(Ei))return Ei;var r=[];return B.Children.forEach(e,function(n){Y(n)||(Mp.isFragment(n)?r=r.concat(t(n.props.children)):r.push(n))}),Ei=r,To=e,r};function _e(t,e){var r=[],n=[];return Array.isArray(e)?n=e.map(function(i){return Ke(i)}):n=[Ke(e)],ro(t).forEach(function(i){var a=Se(i,"type.displayName")||Se(i,"type.name");n.indexOf(a)!==-1&&r.push(i)}),r}function Ae(t,e){var r=_e(t,e);return r&&r[0]}var Io=function(e){if(!e||!e.props)return!1;var r=e.props,n=r.width,i=r.height;return!(!N(n)||n<=0||!N(i)||i<=0)},Vp=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],Xp=function(e){return e&&e.type&&Zr(e.type)&&Vp.indexOf(e.type)>=0},Hp=function(e,r,n,i){var a,o=(a=ji==null?void 0:ji[i])!==null&&a!==void 0?a:[];return!X(e)&&(i&&o.includes(r)||Wp.includes(r))||n&&to.includes(r)},G=function(e,r,n){if(!e||typeof e=="function"||typeof e=="boolean")return null;var i=e;if(B.isValidElement(e)&&(i=e.props),!Jt(i))return null;var a={};return Object.keys(i).forEach(function(o){var u;Hp((u=i)===null||u===void 0?void 0:u[o],o,r,n)&&(a[o]=i[o])}),a},Ki=function t(e,r){if(e===r)return!0;var n=B.Children.count(e);if(n!==B.Children.count(r))return!1;if(n===0)return!0;if(n===1)return ko(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=e[i],o=r[i];if(Array.isArray(a)||Array.isArray(o)){if(!t(a,o))return!1}else if(!ko(a,o))return!1}return!0},ko=function(e,r){if(Y(e)&&Y(r))return!0;if(!Y(e)&&!Y(r)){var n=e.props||{},i=n.children,a=$o(n,Kp),o=r.props||{},u=o.children,c=$o(o,Gp);return i&&u?_t(a,c)&&Ki(i,u):!i&&!u?_t(a,c):!1}return!1},Co=function(e,r){var n=[],i={};return ro(e).forEach(function(a,o){if(Xp(a))n.push(a);else if(a){var u=Ke(a.type),c=r[u]||{},l=c.handler,s=c.once;if(l&&(!s||!i[u])){var f=l(a,u,o);n.push(f),i[u]=!0}}}),n},Up=function(e){var r=e&&e.type;return r&&_o[r]?_o[r]:null},Yp=function(e,r){return ro(r).indexOf(e)},Zp=["children","width","height","viewBox","className","style","title","desc"];function Gi(){return Gi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Gi.apply(this,arguments)}function Jp(t,e){if(t==null)return{};var r=Qp(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Qp(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function qi(t){var e=t.children,r=t.width,n=t.height,i=t.viewBox,a=t.className,o=t.style,u=t.title,c=t.desc,l=Jp(t,Zp),s=i||{width:r,height:n,x:0,y:0},f=U("recharts-surface",a);return P.createElement("svg",Gi({},G(l,!0,"svg"),{className:f,width:r,height:n,style:o,viewBox:"".concat(s.x," ").concat(s.y," ").concat(s.width," ").concat(s.height)}),P.createElement("title",null,u),P.createElement("desc",null,c),e)}var ed=["children","className"];function Vi(){return Vi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Vi.apply(this,arguments)}function td(t,e){if(t==null)return{};var r=rd(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function rd(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}var Z=P.forwardRef(function(t,e){var r=t.children,n=t.className,i=td(t,ed),a=U("recharts-layer",n);return P.createElement("g",Vi({className:a},G(i,!0),{ref:e}),r)}),De=function(e,r){for(var n=arguments.length,i=new Array(n>2?n-2:0),a=2;a<n;a++)i[a-2]=arguments[a]},nd=Ep,id=nd("toUpperCase"),ad=id;const ai=xe(ad);function xr(t){"@babel/helpers - typeof";return xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xr(t)}var od=["type","size","sizeType"];function Xi(){return Xi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Xi.apply(this,arguments)}function Mo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Do(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Mo(Object(r),!0).forEach(function(n){ud(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Mo(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function ud(t,e,r){return e=cd(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function cd(t){var e=ld(t,"string");return xr(e)=="symbol"?e:e+""}function ld(t,e){if(xr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function sd(t,e){if(t==null)return{};var r=fd(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function fd(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}var yl={symbolCircle:ll,symbolCross:wf,symbolDiamond:Af,symbolSquare:Pf,symbolStar:Sf,symbolTriangle:jf,symbolWye:Ef},pd=Math.PI/180,dd=function(e){var r="symbol".concat(ai(e));return yl[r]||ll},vd=function(e,r,n){if(r==="area")return e;switch(n){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":{var i=18*pd;return 1.25*e*e*(Math.tan(i)-Math.tan(i*2)*Math.pow(Math.tan(i),2))}case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},hd=function(e,r){yl["symbol".concat(ai(e))]=r},no=function(e){var r=e.type,n=r===void 0?"circle":r,i=e.size,a=i===void 0?64:i,o=e.sizeType,u=o===void 0?"area":o,c=sd(e,od),l=Do(Do({},c),{},{type:n,size:a,sizeType:u}),s=function(){var v=dd(n),b=xf().type(v).size(vd(a,u,n));return b()},f=l.className,p=l.cx,d=l.cy,m=G(l,!0);return p===+p&&d===+d&&a===+a?P.createElement("path",Xi({},m,{className:U("recharts-symbols",f),transform:"translate(".concat(p,", ").concat(d,")"),d:s()})):null};no.registerSymbol=hd;function kt(t){"@babel/helpers - typeof";return kt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},kt(t)}function Hi(){return Hi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Hi.apply(this,arguments)}function No(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function yd(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?No(Object(r),!0).forEach(function(n){wr(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):No(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function md(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Bo(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,gl(n.key),n)}}function gd(t,e,r){return e&&Bo(t.prototype,e),r&&Bo(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function bd(t,e,r){return e=hn(e),Od(t,ml()?Reflect.construct(e,r||[],hn(t).constructor):e.apply(t,r))}function Od(t,e){if(e&&(kt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return xd(t)}function xd(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ml(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ml=function(){return!!t})()}function hn(t){return hn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},hn(t)}function wd(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ui(t,e)}function Ui(t,e){return Ui=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ui(t,e)}function wr(t,e,r){return e=gl(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function gl(t){var e=Ad(t,"string");return kt(e)=="symbol"?e:e+""}function Ad(t,e){if(kt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(kt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Ee=32,io=function(t){function e(){return md(this,e),bd(this,e,arguments)}return wd(e,t),gd(e,[{key:"renderIcon",value:function(n){var i=this.props.inactiveColor,a=Ee/2,o=Ee/6,u=Ee/3,c=n.inactive?i:n.color;if(n.type==="plainline")return P.createElement("line",{strokeWidth:4,fill:"none",stroke:c,strokeDasharray:n.payload.strokeDasharray,x1:0,y1:a,x2:Ee,y2:a,className:"recharts-legend-icon"});if(n.type==="line")return P.createElement("path",{strokeWidth:4,fill:"none",stroke:c,d:"M0,".concat(a,"h").concat(u,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(2*u,",").concat(a,`
            H`).concat(Ee,"M").concat(2*u,",").concat(a,`
            A`).concat(o,",").concat(o,",0,1,1,").concat(u,",").concat(a),className:"recharts-legend-icon"});if(n.type==="rect")return P.createElement("path",{stroke:"none",fill:c,d:"M0,".concat(Ee/8,"h").concat(Ee,"v").concat(Ee*3/4,"h").concat(-Ee,"z"),className:"recharts-legend-icon"});if(P.isValidElement(n.legendIcon)){var l=yd({},n);return delete l.legendIcon,P.cloneElement(n.legendIcon,l)}return P.createElement(no,{fill:c,cx:a,cy:a,size:Ee,sizeType:"diameter",type:n.type})}},{key:"renderItems",value:function(){var n=this,i=this.props,a=i.payload,o=i.iconSize,u=i.layout,c=i.formatter,l=i.inactiveColor,s={x:0,y:0,width:Ee,height:Ee},f={display:u==="horizontal"?"inline-block":"block",marginRight:10},p={display:"inline-block",verticalAlign:"middle",marginRight:4};return a.map(function(d,m){var y=d.formatter||c,v=U(wr(wr({"recharts-legend-item":!0},"legend-item-".concat(m),!0),"inactive",d.inactive));if(d.type==="none")return null;var b=X(d.value)?null:d.value;De(!X(d.value),`The name property is also required when using a function for the dataKey of a chart's cartesian components. Ex: <Bar name="Name of my Data"/>`);var O=d.inactive?l:d.color;return P.createElement("li",Hi({className:v,style:f,key:"legend-item-".concat(m)},vt(n.props,d,m)),P.createElement(qi,{width:o,height:o,viewBox:s,style:p},n.renderIcon(d)),P.createElement("span",{className:"recharts-legend-item-text",style:{color:O}},y?y(b,d,m):b))})}},{key:"render",value:function(){var n=this.props,i=n.payload,a=n.layout,o=n.align;if(!i||!i.length)return null;var u={padding:0,margin:0,textAlign:a==="horizontal"?o:"left"};return P.createElement("ul",{className:"recharts-default-legend",style:u},this.renderItems())}}])}(B.PureComponent);wr(io,"displayName","Legend");wr(io,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});function bl(t,e,r){return e===!0?jo(t,r):X(e)?jo(t,e):t}function Ct(t){"@babel/helpers - typeof";return Ct=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ct(t)}var Pd=["ref"];function Ro(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Le(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ro(Object(r),!0).forEach(function(n){oi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ro(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Sd(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Lo(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,xl(n.key),n)}}function jd(t,e,r){return e&&Lo(t.prototype,e),r&&Lo(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Ed(t,e,r){return e=yn(e),$d(t,Ol()?Reflect.construct(e,r||[],yn(t).constructor):e.apply(t,r))}function $d(t,e){if(e&&(Ct(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return _d(t)}function _d(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ol(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ol=function(){return!!t})()}function yn(t){return yn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},yn(t)}function Td(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Yi(t,e)}function Yi(t,e){return Yi=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Yi(t,e)}function oi(t,e,r){return e=xl(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function xl(t){var e=Id(t,"string");return Ct(e)=="symbol"?e:e+""}function Id(t,e){if(Ct(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Ct(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function kd(t,e){if(t==null)return{};var r=Cd(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Cd(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Md(t){return t.value}function Dd(t,e){if(P.isValidElement(t))return P.cloneElement(t,e);if(typeof t=="function")return P.createElement(t,e);e.ref;var r=kd(e,Pd);return P.createElement(io,r)}var zo=1,Tt=function(t){function e(){var r;Sd(this,e);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=Ed(this,e,[].concat(i)),oi(r,"lastBoundingBox",{width:-1,height:-1}),r}return Td(e,t),jd(e,[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();return n.height=this.wrapperNode.offsetHeight,n.width=this.wrapperNode.offsetWidth,n}return null}},{key:"updateBBox",value:function(){var n=this.props.onBBoxUpdate,i=this.getBBox();i?(Math.abs(i.width-this.lastBoundingBox.width)>zo||Math.abs(i.height-this.lastBoundingBox.height)>zo)&&(this.lastBoundingBox.width=i.width,this.lastBoundingBox.height=i.height,n&&n(i)):(this.lastBoundingBox.width!==-1||this.lastBoundingBox.height!==-1)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,n&&n(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?Le({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(n){var i=this.props,a=i.layout,o=i.align,u=i.verticalAlign,c=i.margin,l=i.chartWidth,s=i.chartHeight,f,p;if(!n||(n.left===void 0||n.left===null)&&(n.right===void 0||n.right===null))if(o==="center"&&a==="vertical"){var d=this.getBBoxSnapshot();f={left:((l||0)-d.width)/2}}else f=o==="right"?{right:c&&c.right||0}:{left:c&&c.left||0};if(!n||(n.top===void 0||n.top===null)&&(n.bottom===void 0||n.bottom===null))if(u==="middle"){var m=this.getBBoxSnapshot();p={top:((s||0)-m.height)/2}}else p=u==="bottom"?{bottom:c&&c.bottom||0}:{top:c&&c.top||0};return Le(Le({},f),p)}},{key:"render",value:function(){var n=this,i=this.props,a=i.content,o=i.width,u=i.height,c=i.wrapperStyle,l=i.payloadUniqBy,s=i.payload,f=Le(Le({position:"absolute",width:o||"auto",height:u||"auto"},this.getDefaultPosition(c)),c);return P.createElement("div",{className:"recharts-legend-wrapper",style:f,ref:function(d){n.wrapperNode=d}},Dd(a,Le(Le({},this.props),{},{payload:bl(s,l,Md)})))}}],[{key:"getWithHeight",value:function(n,i){var a=Le(Le({},this.defaultProps),n.props),o=a.layout;return o==="vertical"&&N(n.props.height)?{height:n.props.height}:o==="horizontal"?{width:n.props.width||i}:null}}])}(B.PureComponent);oi(Tt,"displayName","Legend");oi(Tt,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});function Ar(t){"@babel/helpers - typeof";return Ar=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ar(t)}function Zi(){return Zi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Zi.apply(this,arguments)}function Nd(t,e){return zd(t)||Ld(t,e)||Rd(t,e)||Bd()}function Bd(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Rd(t,e){if(t){if(typeof t=="string")return Wo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wo(t,e)}}function Wo(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ld(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function zd(t){if(Array.isArray(t))return t}function Fo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function $i(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Fo(Object(r),!0).forEach(function(n){Wd(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Fo(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Wd(t,e,r){return e=Fd(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Fd(t){var e=Kd(t,"string");return Ar(e)=="symbol"?e:e+""}function Kd(t,e){if(Ar(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Ar(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Gd(t){return Array.isArray(t)&&se(t[0])&&se(t[1])?t.join(" ~ "):t}var qd=function(e){var r=e.separator,n=r===void 0?" : ":r,i=e.contentStyle,a=i===void 0?{}:i,o=e.itemStyle,u=o===void 0?{}:o,c=e.labelStyle,l=c===void 0?{}:c,s=e.payload,f=e.formatter,p=e.itemSorter,d=e.wrapperClassName,m=e.labelClassName,y=e.label,v=e.labelFormatter,b=e.accessibilityLayer,O=b===void 0?!1:b,x=function(){if(s&&s.length){var $={padding:0,margin:0},C=(p?Za(s,p):s).map(function(k,I){if(k.type==="none")return null;var M=$i({display:"block",paddingTop:4,paddingBottom:4,color:k.color||"#000"},u),D=k.formatter||f||Gd,R=k.value,W=k.name,F=R,q=W;if(D&&F!=null&&q!=null){var L=D(R,W,k,I,s);if(Array.isArray(L)){var V=Nd(L,2);F=V[0],q=V[1]}else F=L}return P.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(I),style:M},se(q)?P.createElement("span",{className:"recharts-tooltip-item-name"},q):null,se(q)?P.createElement("span",{className:"recharts-tooltip-item-separator"},n):null,P.createElement("span",{className:"recharts-tooltip-item-value"},F),P.createElement("span",{className:"recharts-tooltip-item-unit"},k.unit||""))});return P.createElement("ul",{className:"recharts-tooltip-item-list",style:$},C)}return null},w=$i({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},a),h=$i({margin:0},l),g=!Y(y),A=g?y:"",S=U("recharts-default-tooltip",d),j=U("recharts-tooltip-label",m);g&&v&&s!==void 0&&s!==null&&(A=v(y,s));var T=O?{role:"status","aria-live":"assertive"}:{};return P.createElement("div",Zi({className:S,style:w},T),P.createElement("p",{className:j,style:h},P.isValidElement(A)?A:"".concat(A)),x())};function Pr(t){"@babel/helpers - typeof";return Pr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pr(t)}function nn(t,e,r){return e=Vd(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Vd(t){var e=Xd(t,"string");return Pr(e)=="symbol"?e:e+""}function Xd(t,e){if(Pr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Pr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var lr="recharts-tooltip-wrapper",Hd={visibility:"hidden"};function Ud(t){var e=t.coordinate,r=t.translateX,n=t.translateY;return U(lr,nn(nn(nn(nn({},"".concat(lr,"-right"),N(r)&&e&&N(e.x)&&r>=e.x),"".concat(lr,"-left"),N(r)&&e&&N(e.x)&&r<e.x),"".concat(lr,"-bottom"),N(n)&&e&&N(e.y)&&n>=e.y),"".concat(lr,"-top"),N(n)&&e&&N(e.y)&&n<e.y))}function Ko(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,i=t.offsetTopLeft,a=t.position,o=t.reverseDirection,u=t.tooltipDimension,c=t.viewBox,l=t.viewBoxDimension;if(a&&N(a[n]))return a[n];var s=r[n]-u-i,f=r[n]+i;if(e[n])return o[n]?s:f;if(o[n]){var p=s,d=c[n];return p<d?Math.max(f,c[n]):Math.max(s,c[n])}var m=f+u,y=c[n]+l;return m>y?Math.max(s,c[n]):Math.max(f,c[n])}function Yd(t){var e=t.translateX,r=t.translateY,n=t.useTranslate3d;return{transform:n?"translate3d(".concat(e,"px, ").concat(r,"px, 0)"):"translate(".concat(e,"px, ").concat(r,"px)")}}function Zd(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.offsetTopLeft,i=t.position,a=t.reverseDirection,o=t.tooltipBox,u=t.useTranslate3d,c=t.viewBox,l,s,f;return o.height>0&&o.width>0&&r?(s=Ko({allowEscapeViewBox:e,coordinate:r,key:"x",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.width,viewBox:c,viewBoxDimension:c.width}),f=Ko({allowEscapeViewBox:e,coordinate:r,key:"y",offsetTopLeft:n,position:i,reverseDirection:a,tooltipDimension:o.height,viewBox:c,viewBoxDimension:c.height}),l=Yd({translateX:s,translateY:f,useTranslate3d:u})):l=Hd,{cssProperties:l,cssClasses:Ud({translateX:s,translateY:f,coordinate:r})}}function Mt(t){"@babel/helpers - typeof";return Mt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Mt(t)}function Go(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function qo(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Go(Object(r),!0).forEach(function(n){Qi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Go(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Jd(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Vo(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Al(n.key),n)}}function Qd(t,e,r){return e&&Vo(t.prototype,e),r&&Vo(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function ev(t,e,r){return e=mn(e),tv(t,wl()?Reflect.construct(e,r||[],mn(t).constructor):e.apply(t,r))}function tv(t,e){if(e&&(Mt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return rv(t)}function rv(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function wl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(wl=function(){return!!t})()}function mn(t){return mn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},mn(t)}function nv(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ji(t,e)}function Ji(t,e){return Ji=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ji(t,e)}function Qi(t,e,r){return e=Al(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Al(t){var e=iv(t,"string");return Mt(e)=="symbol"?e:e+""}function iv(t,e){if(Mt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Mt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Xo=1,av=function(t){function e(){var r;Jd(this,e);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=ev(this,e,[].concat(i)),Qi(r,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),Qi(r,"handleKeyDown",function(o){if(o.key==="Escape"){var u,c,l,s;r.setState({dismissed:!0,dismissedAtCoordinate:{x:(u=(c=r.props.coordinate)===null||c===void 0?void 0:c.x)!==null&&u!==void 0?u:0,y:(l=(s=r.props.coordinate)===null||s===void 0?void 0:s.y)!==null&&l!==void 0?l:0}})}}),r}return nv(e,t),Qd(e,[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var n=this.wrapperNode.getBoundingClientRect();(Math.abs(n.width-this.state.lastBoundingBox.width)>Xo||Math.abs(n.height-this.state.lastBoundingBox.height)>Xo)&&this.setState({lastBoundingBox:{width:n.width,height:n.height}})}else(this.state.lastBoundingBox.width!==-1||this.state.lastBoundingBox.height!==-1)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var n,i;this.props.active&&this.updateBBox(),this.state.dismissed&&(((n=this.props.coordinate)===null||n===void 0?void 0:n.x)!==this.state.dismissedAtCoordinate.x||((i=this.props.coordinate)===null||i===void 0?void 0:i.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,l=i.children,s=i.coordinate,f=i.hasPayload,p=i.isAnimationActive,d=i.offset,m=i.position,y=i.reverseDirection,v=i.useTranslate3d,b=i.viewBox,O=i.wrapperStyle,x=Zd({allowEscapeViewBox:o,coordinate:s,offsetTopLeft:d,position:m,reverseDirection:y,tooltipBox:this.state.lastBoundingBox,useTranslate3d:v,viewBox:b}),w=x.cssClasses,h=x.cssProperties,g=qo(qo({transition:p&&a?"transform ".concat(u,"ms ").concat(c):void 0},h),{},{pointerEvents:"none",visibility:!this.state.dismissed&&a&&f?"visible":"hidden",position:"absolute",top:0,left:0},O);return P.createElement("div",{tabIndex:-1,className:w,style:g,ref:function(S){n.wrapperNode=S}},l)}}])}(B.PureComponent),ov=function(){return!(typeof window<"u"&&window.document&&window.document.createElement&&window.setTimeout)},Ge={isSsr:ov(),get:function(e){return Ge[e]},set:function(e,r){if(typeof e=="string")Ge[e]=r;else{var n=Object.keys(e);n&&n.length&&n.forEach(function(i){Ge[i]=e[i]})}}};function Dt(t){"@babel/helpers - typeof";return Dt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Dt(t)}function Ho(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Uo(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ho(Object(r),!0).forEach(function(n){ao(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ho(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function uv(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Yo(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Sl(n.key),n)}}function cv(t,e,r){return e&&Yo(t.prototype,e),r&&Yo(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function lv(t,e,r){return e=gn(e),sv(t,Pl()?Reflect.construct(e,r||[],gn(t).constructor):e.apply(t,r))}function sv(t,e){if(e&&(Dt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fv(t)}function fv(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Pl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Pl=function(){return!!t})()}function gn(t){return gn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},gn(t)}function pv(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ea(t,e)}function ea(t,e){return ea=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ea(t,e)}function ao(t,e,r){return e=Sl(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Sl(t){var e=dv(t,"string");return Dt(e)=="symbol"?e:e+""}function dv(t,e){if(Dt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Dt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function vv(t){return t.dataKey}function hv(t,e){return P.isValidElement(t)?P.cloneElement(t,e):typeof t=="function"?P.createElement(t,e):P.createElement(qd,e)}var ze=function(t){function e(){return uv(this,e),lv(this,e,arguments)}return pv(e,t),cv(e,[{key:"render",value:function(){var n=this,i=this.props,a=i.active,o=i.allowEscapeViewBox,u=i.animationDuration,c=i.animationEasing,l=i.content,s=i.coordinate,f=i.filterNull,p=i.isAnimationActive,d=i.offset,m=i.payload,y=i.payloadUniqBy,v=i.position,b=i.reverseDirection,O=i.useTranslate3d,x=i.viewBox,w=i.wrapperStyle,h=m??[];f&&h.length&&(h=bl(m.filter(function(A){return A.value!=null&&(A.hide!==!0||n.props.includeHidden)}),y,vv));var g=h.length>0;return P.createElement(av,{allowEscapeViewBox:o,animationDuration:u,animationEasing:c,isAnimationActive:p,active:a,coordinate:s,hasPayload:g,offset:d,position:v,reverseDirection:b,useTranslate3d:O,viewBox:x,wrapperStyle:w},hv(l,Uo(Uo({},this.props),{},{payload:h})))}}])}(B.PureComponent);ao(ze,"displayName","Tooltip");ao(ze,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!Ge.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});function Sr(t){"@babel/helpers - typeof";return Sr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Sr(t)}function Zo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function an(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Zo(Object(r),!0).forEach(function(n){yv(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Zo(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function yv(t,e,r){return e=mv(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function mv(t){var e=gv(t,"string");return Sr(e)=="symbol"?e:e+""}function gv(t,e){if(Sr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Sr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function bv(t,e){return Av(t)||wv(t,e)||xv(t,e)||Ov()}function Ov(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xv(t,e){if(t){if(typeof t=="string")return Jo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Jo(t,e)}}function Jo(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function wv(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function Av(t){if(Array.isArray(t))return t}var fP=B.forwardRef(function(t,e){var r=t.aspect,n=t.initialDimension,i=n===void 0?{width:-1,height:-1}:n,a=t.width,o=a===void 0?"100%":a,u=t.height,c=u===void 0?"100%":u,l=t.minWidth,s=l===void 0?0:l,f=t.minHeight,p=t.maxHeight,d=t.children,m=t.debounce,y=m===void 0?0:m,v=t.id,b=t.className,O=t.onResize,x=t.style,w=x===void 0?{}:x,h=B.useRef(null),g=B.useRef();g.current=O,B.useImperativeHandle(e,function(){return Object.defineProperty(h.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),h.current},configurable:!0})});var A=B.useState({containerWidth:i.width,containerHeight:i.height}),S=bv(A,2),j=S[0],T=S[1],E=B.useCallback(function(C,k){T(function(I){var M=Math.round(C),D=Math.round(k);return I.containerWidth===M&&I.containerHeight===D?I:{containerWidth:M,containerHeight:D}})},[]);B.useEffect(function(){var C=function(W){var F,q=W[0].contentRect,L=q.width,V=q.height;E(L,V),(F=g.current)===null||F===void 0||F.call(g,L,V)};y>0&&(C=cl(C,y,{trailing:!0,leading:!1}));var k=new ResizeObserver(C),I=h.current.getBoundingClientRect(),M=I.width,D=I.height;return E(M,D),k.observe(h.current),function(){k.disconnect()}},[E,y]);var $=B.useMemo(function(){var C=j.containerWidth,k=j.containerHeight;if(C<0||k<0)return null;De(st(o)||st(c),`The width(%s) and height(%s) are both fixed numbers,
       maybe you don't need to use a ResponsiveContainer.`,o,c),De(!r||r>0,"The aspect(%s) must be greater than zero.",r);var I=st(o)?C:o,M=st(c)?k:c;r&&r>0&&(I?M=I/r:M&&(I=M*r),p&&M>p&&(M=p)),De(I>0||M>0,`The width(%s) and height(%s) of chart should be greater than 0,
       please check the style of container, or the props width(%s) and height(%s),
       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the
       height and width.`,I,M,o,c,s,f,r);var D=!Array.isArray(d)&&Ke(d.type).endsWith("Chart");return P.Children.map(d,function(R){return P.isValidElement(R)?B.cloneElement(R,an({width:I,height:M},D?{style:an({height:"100%",width:"100%",maxHeight:M,maxWidth:I},R.props.style)}:{})):R})},[r,d,c,p,f,s,j,o]);return P.createElement("div",{id:v?"".concat(v):void 0,className:U("recharts-responsive-container",b),style:an(an({},w),{},{width:o,height:c,minWidth:s,minHeight:f,maxHeight:p}),ref:h},$)}),oo=function(e){return null};oo.displayName="Cell";function jr(t){"@babel/helpers - typeof";return jr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jr(t)}function Qo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ta(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Qo(Object(r),!0).forEach(function(n){Pv(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Qo(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Pv(t,e,r){return e=Sv(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Sv(t){var e=jv(t,"string");return jr(e)=="symbol"?e:e+""}function jv(t,e){if(jr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(jr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var wt={widthCache:{},cacheCount:0},Ev=2e3,$v={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},eu="recharts_measurement_span";function _v(t){var e=ta({},t);return Object.keys(e).forEach(function(r){e[r]||delete e[r]}),e}var yr=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(e==null||Ge.isSsr)return{width:0,height:0};var n=_v(r),i=JSON.stringify({text:e,copyStyle:n});if(wt.widthCache[i])return wt.widthCache[i];try{var a=document.getElementById(eu);a||(a=document.createElement("span"),a.setAttribute("id",eu),a.setAttribute("aria-hidden","true"),document.body.appendChild(a));var o=ta(ta({},$v),n);Object.assign(a.style,o),a.textContent="".concat(e);var u=a.getBoundingClientRect(),c={width:u.width,height:u.height};return wt.widthCache[i]=c,++wt.cacheCount>Ev&&(wt.cacheCount=0,wt.widthCache={}),c}catch{return{width:0,height:0}}},Tv=function(e){return{top:e.top+window.scrollY-document.documentElement.clientTop,left:e.left+window.scrollX-document.documentElement.clientLeft}};function Er(t){"@babel/helpers - typeof";return Er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Er(t)}function bn(t,e){return Mv(t)||Cv(t,e)||kv(t,e)||Iv()}function Iv(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function kv(t,e){if(t){if(typeof t=="string")return tu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tu(t,e)}}function tu(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Cv(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function Mv(t){if(Array.isArray(t))return t}function Dv(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ru(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Bv(n.key),n)}}function Nv(t,e,r){return e&&ru(t.prototype,e),r&&ru(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Bv(t){var e=Rv(t,"string");return Er(e)=="symbol"?e:e+""}function Rv(t,e){if(Er(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var nu=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,iu=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,Lv=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,zv=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,jl={cm:96/2.54,mm:96/25.4,pt:96/72,pc:96/6,in:96,Q:96/(2.54*40),px:1},Wv=Object.keys(jl),Pt="NaN";function Fv(t,e){return t*jl[e]}var on=function(){function t(e,r){Dv(this,t),this.num=e,this.unit=r,this.num=e,this.unit=r,Number.isNaN(e)&&(this.unit=""),r!==""&&!Lv.test(r)&&(this.num=NaN,this.unit=""),Wv.includes(r)&&(this.num=Fv(e,r),this.unit="px")}return Nv(t,[{key:"add",value:function(r){return this.unit!==r.unit?new t(NaN,""):new t(this.num+r.num,this.unit)}},{key:"subtract",value:function(r){return this.unit!==r.unit?new t(NaN,""):new t(this.num-r.num,this.unit)}},{key:"multiply",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new t(NaN,""):new t(this.num*r.num,this.unit||r.unit)}},{key:"divide",value:function(r){return this.unit!==""&&r.unit!==""&&this.unit!==r.unit?new t(NaN,""):new t(this.num/r.num,this.unit||r.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],[{key:"parse",value:function(r){var n,i=(n=zv.exec(r))!==null&&n!==void 0?n:[],a=bn(i,3),o=a[1],u=a[2];return new t(parseFloat(o),u??"")}}])}();function El(t){if(t.includes(Pt))return Pt;for(var e=t;e.includes("*")||e.includes("/");){var r,n=(r=nu.exec(e))!==null&&r!==void 0?r:[],i=bn(n,4),a=i[1],o=i[2],u=i[3],c=on.parse(a??""),l=on.parse(u??""),s=o==="*"?c.multiply(l):c.divide(l);if(s.isNaN())return Pt;e=e.replace(nu,s.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var f,p=(f=iu.exec(e))!==null&&f!==void 0?f:[],d=bn(p,4),m=d[1],y=d[2],v=d[3],b=on.parse(m??""),O=on.parse(v??""),x=y==="+"?b.add(O):b.subtract(O);if(x.isNaN())return Pt;e=e.replace(iu,x.toString())}return e}var au=/\(([^()]*)\)/;function Kv(t){for(var e=t;e.includes("(");){var r=au.exec(e),n=bn(r,2),i=n[1];e=e.replace(au,El(i))}return e}function Gv(t){var e=t.replace(/\s+/g,"");return e=Kv(e),e=El(e),e}function qv(t){try{return Gv(t)}catch{return Pt}}function _i(t){var e=qv(t.slice(5,-1));return e===Pt?"":e}var Vv=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],Xv=["dx","dy","angle","className","breakAll"];function ra(){return ra=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ra.apply(this,arguments)}function ou(t,e){if(t==null)return{};var r=Hv(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Hv(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function uu(t,e){return Jv(t)||Zv(t,e)||Yv(t,e)||Uv()}function Uv(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Yv(t,e){if(t){if(typeof t=="string")return cu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return cu(t,e)}}function cu(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Zv(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function Jv(t){if(Array.isArray(t))return t}var $l=/[ \f\n\r\t\v\u2028\u2029]+/,_l=function(e){var r=e.children,n=e.breakAll,i=e.style;try{var a=[];Y(r)||(n?a=r.toString().split(""):a=r.toString().split($l));var o=a.map(function(c){return{word:c,width:yr(c,i).width}}),u=n?0:yr(" ",i).width;return{wordsWithComputedWidth:o,spaceWidth:u}}catch{return null}},Qv=function(e,r,n,i,a){var o=e.maxLines,u=e.children,c=e.style,l=e.breakAll,s=N(o),f=u,p=function(){var I=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return I.reduce(function(M,D){var R=D.word,W=D.width,F=M[M.length-1];if(F&&(i==null||a||F.width+W+n<Number(i)))F.words.push(R),F.width+=W+n;else{var q={words:[R],width:W};M.push(q)}return M},[])},d=p(r),m=function(I){return I.reduce(function(M,D){return M.width>D.width?M:D})};if(!s)return d;for(var y="…",v=function(I){var M=f.slice(0,I),D=_l({breakAll:l,style:c,children:M+y}).wordsWithComputedWidth,R=p(D),W=R.length>o||m(R).width>Number(i);return[W,R]},b=0,O=f.length-1,x=0,w;b<=O&&x<=f.length-1;){var h=Math.floor((b+O)/2),g=h-1,A=v(g),S=uu(A,2),j=S[0],T=S[1],E=v(h),$=uu(E,1),C=$[0];if(!j&&!C&&(b=h+1),j&&C&&(O=h-1),!j&&C){w=T;break}x++}return w||d},lu=function(e){var r=Y(e)?[]:e.toString().split($l);return[{words:r}]},eh=function(e){var r=e.width,n=e.scaleToFit,i=e.children,a=e.style,o=e.breakAll,u=e.maxLines;if((r||n)&&!Ge.isSsr){var c,l,s=_l({breakAll:o,children:i,style:a});if(s){var f=s.wordsWithComputedWidth,p=s.spaceWidth;c=f,l=p}else return lu(i);return Qv({breakAll:o,children:i,maxLines:u,style:a},c,l,r,n)}return lu(i)},su="#808080",ht=function(e){var r=e.x,n=r===void 0?0:r,i=e.y,a=i===void 0?0:i,o=e.lineHeight,u=o===void 0?"1em":o,c=e.capHeight,l=c===void 0?"0.71em":c,s=e.scaleToFit,f=s===void 0?!1:s,p=e.textAnchor,d=p===void 0?"start":p,m=e.verticalAnchor,y=m===void 0?"end":m,v=e.fill,b=v===void 0?su:v,O=ou(e,Vv),x=B.useMemo(function(){return eh({breakAll:O.breakAll,children:O.children,maxLines:O.maxLines,scaleToFit:f,style:O.style,width:O.width})},[O.breakAll,O.children,O.maxLines,f,O.style,O.width]),w=O.dx,h=O.dy,g=O.angle,A=O.className,S=O.breakAll,j=ou(O,Xv);if(!se(n)||!se(a))return null;var T=n+(N(w)?w:0),E=a+(N(h)?h:0),$;switch(y){case"start":$=_i("calc(".concat(l,")"));break;case"middle":$=_i("calc(".concat((x.length-1)/2," * -").concat(u," + (").concat(l," / 2))"));break;default:$=_i("calc(".concat(x.length-1," * -").concat(u,")"));break}var C=[];if(f){var k=x[0].width,I=O.width;C.push("scale(".concat((N(I)?I/k:1)/k,")"))}return g&&C.push("rotate(".concat(g,", ").concat(T,", ").concat(E,")")),C.length&&(j.transform=C.join(" ")),P.createElement("text",ra({},G(j,!0),{x:T,y:E,className:U("recharts-text",A),textAnchor:d,fill:b.includes("url")?su:b}),x.map(function(M,D){var R=M.words.join(S?"":" ");return P.createElement("tspan",{x:T,dy:D===0?$:u,key:"".concat(R,"-").concat(D)},R)}))};const fu=Object.freeze(Object.defineProperty({__proto__:null,scaleBand:Li,scaleDiverging:$f,scaleDivergingLog:_f,scaleDivergingPow:Tf,scaleDivergingSqrt:If,scaleDivergingSymlog:kf,scaleIdentity:Cf,scaleImplicit:Mf,scaleLinear:zi,scaleLog:Df,scaleOrdinal:Nf,scalePoint:hr,scalePow:Bf,scaleQuantile:Rf,scaleQuantize:Lf,scaleRadial:zf,scaleSequential:Wf,scaleSequentialLog:Ff,scaleSequentialPow:Kf,scaleSequentialQuantile:Gf,scaleSequentialSqrt:qf,scaleSequentialSymlog:Vf,scaleSqrt:Xf,scaleSymlog:Hf,scaleThreshold:Uf,scaleTime:Yf,scaleUtc:Zf,tickFormat:Jf},Symbol.toStringTag,{value:"Module"}));var th=Qf;function rh(t,e,r){for(var n=-1,i=t.length;++n<i;){var a=t[n],o=e(a);if(o!=null&&(u===void 0?o===o&&!th(o):r(o,u)))var u=o,c=a}return c}var ui=rh;function nh(t,e){return t>e}var Tl=nh,ih=ui,ah=Tl,oh=sl;function uh(t){return t&&t.length?ih(t,oh,ah):void 0}var ch=uh;const ci=xe(ch);function lh(t,e){return t<e}var Il=lh,sh=ui,fh=Il,ph=sl;function dh(t){return t&&t.length?sh(t,ph,fh):void 0}var vh=dh;const li=xe(vh);var hh=ep,yh=Je,mh=tp,gh=Ya;function bh(t,e){var r=gh(t)?hh:mh;return r(t,yh(e))}var Oh=bh,xh=rp,wh=Oh;function Ah(t,e){return xh(wh(t,e),1)}var Ph=Ah;const Sh=xe(Ph);var Qt=1e9,jh={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},co,ie=!0,Te="[DecimalError] ",pt=Te+"Invalid argument: ",uo=Te+"Exponent out of range: ",er=Math.floor,ct=Math.pow,Eh=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,Pe,pe=1e7,ne=7,kl=9007199254740991,On=er(kl/ne),z={};z.absoluteValue=z.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t};z.comparedTo=z.cmp=function(t){var e,r,n,i,a=this;if(t=new a.constructor(t),a.s!==t.s)return a.s||-t.s;if(a.e!==t.e)return a.e>t.e^a.s<0?1:-1;for(n=a.d.length,i=t.d.length,e=0,r=n<i?n:i;e<r;++e)if(a.d[e]!==t.d[e])return a.d[e]>t.d[e]^a.s<0?1:-1;return n===i?0:n>i^a.s<0?1:-1};z.decimalPlaces=z.dp=function(){var t=this,e=t.d.length-1,r=(e-t.e)*ne;if(e=t.d[e],e)for(;e%10==0;e/=10)r--;return r<0?0:r};z.dividedBy=z.div=function(t){return qe(this,new this.constructor(t))};z.dividedToIntegerBy=z.idiv=function(t){var e=this,r=e.constructor;return ee(qe(e,new r(t),0,1),r.precision)};z.equals=z.eq=function(t){return!this.cmp(t)};z.exponent=function(){return ce(this)};z.greaterThan=z.gt=function(t){return this.cmp(t)>0};z.greaterThanOrEqualTo=z.gte=function(t){return this.cmp(t)>=0};z.isInteger=z.isint=function(){return this.e>this.d.length-2};z.isNegative=z.isneg=function(){return this.s<0};z.isPositive=z.ispos=function(){return this.s>0};z.isZero=function(){return this.s===0};z.lessThan=z.lt=function(t){return this.cmp(t)<0};z.lessThanOrEqualTo=z.lte=function(t){return this.cmp(t)<1};z.logarithm=z.log=function(t){var e,r=this,n=r.constructor,i=n.precision,a=i+5;if(t===void 0)t=new n(10);else if(t=new n(t),t.s<1||t.eq(Pe))throw Error(Te+"NaN");if(r.s<1)throw Error(Te+(r.s?"NaN":"-Infinity"));return r.eq(Pe)?new n(0):(ie=!1,e=qe($r(r,a),$r(t,a),a),ie=!0,ee(e,i))};z.minus=z.sub=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?Dl(e,t):Cl(e,(t.s=-t.s,t))};z.modulo=z.mod=function(t){var e,r=this,n=r.constructor,i=n.precision;if(t=new n(t),!t.s)throw Error(Te+"NaN");return r.s?(ie=!1,e=qe(r,t,0,1).times(t),ie=!0,r.minus(e)):ee(new n(r),i)};z.naturalExponential=z.exp=function(){return Ml(this)};z.naturalLogarithm=z.ln=function(){return $r(this)};z.negated=z.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t};z.plus=z.add=function(t){var e=this;return t=new e.constructor(t),e.s==t.s?Cl(e,t):Dl(e,(t.s=-t.s,t))};z.precision=z.sd=function(t){var e,r,n,i=this;if(t!==void 0&&t!==!!t&&t!==1&&t!==0)throw Error(pt+t);if(e=ce(i)+1,n=i.d.length-1,r=n*ne+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return t&&e>r?e:r};z.squareRoot=z.sqrt=function(){var t,e,r,n,i,a,o,u=this,c=u.constructor;if(u.s<1){if(!u.s)return new c(0);throw Error(Te+"NaN")}for(t=ce(u),ie=!1,i=Math.sqrt(+u),i==0||i==1/0?(e=Ne(u.d),(e.length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=er((t+1)/2)-(t<0||t%2),i==1/0?e="5e"+t:(e=i.toExponential(),e=e.slice(0,e.indexOf("e")+1)+t),n=new c(e)):n=new c(i.toString()),r=c.precision,i=o=r+3;;)if(a=n,n=a.plus(qe(u,a,o+2)).times(.5),Ne(a.d).slice(0,o)===(e=Ne(n.d)).slice(0,o)){if(e=e.slice(o-3,o+1),i==o&&e=="4999"){if(ee(a,r+1,0),a.times(a).eq(u)){n=a;break}}else if(e!="9999")break;o+=4}return ie=!0,ee(n,r)};z.times=z.mul=function(t){var e,r,n,i,a,o,u,c,l,s=this,f=s.constructor,p=s.d,d=(t=new f(t)).d;if(!s.s||!t.s)return new f(0);for(t.s*=s.s,r=s.e+t.e,c=p.length,l=d.length,c<l&&(a=p,p=d,d=a,o=c,c=l,l=o),a=[],o=c+l,n=o;n--;)a.push(0);for(n=l;--n>=0;){for(e=0,i=c+n;i>n;)u=a[i]+d[n]*p[i-n-1]+e,a[i--]=u%pe|0,e=u/pe|0;a[i]=(a[i]+e)%pe|0}for(;!a[--o];)a.pop();return e?++r:a.shift(),t.d=a,t.e=r,ie?ee(t,f.precision):t};z.toDecimalPlaces=z.todp=function(t,e){var r=this,n=r.constructor;return r=new n(r),t===void 0?r:(Re(t,0,Qt),e===void 0?e=n.rounding:Re(e,0,8),ee(r,t+ce(r)+1,e))};z.toExponential=function(t,e){var r,n=this,i=n.constructor;return t===void 0?r=yt(n,!0):(Re(t,0,Qt),e===void 0?e=i.rounding:Re(e,0,8),n=ee(new i(n),t+1,e),r=yt(n,!0,t+1)),r};z.toFixed=function(t,e){var r,n,i=this,a=i.constructor;return t===void 0?yt(i):(Re(t,0,Qt),e===void 0?e=a.rounding:Re(e,0,8),n=ee(new a(i),t+ce(i)+1,e),r=yt(n.abs(),!1,t+ce(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};z.toInteger=z.toint=function(){var t=this,e=t.constructor;return ee(new e(t),ce(t)+1,e.rounding)};z.toNumber=function(){return+this};z.toPower=z.pow=function(t){var e,r,n,i,a,o,u=this,c=u.constructor,l=12,s=+(t=new c(t));if(!t.s)return new c(Pe);if(u=new c(u),!u.s){if(t.s<1)throw Error(Te+"Infinity");return u}if(u.eq(Pe))return u;if(n=c.precision,t.eq(Pe))return ee(u,n);if(e=t.e,r=t.d.length-1,o=e>=r,a=u.s,o){if((r=s<0?-s:s)<=kl){for(i=new c(Pe),e=Math.ceil(n/ne+4),ie=!1;r%2&&(i=i.times(u),du(i.d,e)),r=er(r/2),r!==0;)u=u.times(u),du(u.d,e);return ie=!0,t.s<0?new c(Pe).div(i):ee(i,n)}}else if(a<0)throw Error(Te+"NaN");return a=a<0&&t.d[Math.max(e,r)]&1?-1:1,u.s=1,ie=!1,i=t.times($r(u,n+l)),ie=!0,i=Ml(i),i.s=a,i};z.toPrecision=function(t,e){var r,n,i=this,a=i.constructor;return t===void 0?(r=ce(i),n=yt(i,r<=a.toExpNeg||r>=a.toExpPos)):(Re(t,1,Qt),e===void 0?e=a.rounding:Re(e,0,8),i=ee(new a(i),t,e),r=ce(i),n=yt(i,t<=r||r<=a.toExpNeg,t)),n};z.toSignificantDigits=z.tosd=function(t,e){var r=this,n=r.constructor;return t===void 0?(t=n.precision,e=n.rounding):(Re(t,1,Qt),e===void 0?e=n.rounding:Re(e,0,8)),ee(new n(r),t,e)};z.toString=z.valueOf=z.val=z.toJSON=z[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=this,e=ce(t),r=t.constructor;return yt(t,e<=r.toExpNeg||e>=r.toExpPos)};function Cl(t,e){var r,n,i,a,o,u,c,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),ie?ee(e,f):e;if(c=t.d,l=e.d,o=t.e,i=e.e,c=c.slice(),a=o-i,a){for(a<0?(n=c,a=-a,u=l.length):(n=l,i=o,u=c.length),o=Math.ceil(f/ne),u=o>u?o+1:u+1,a>u&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for(u=c.length,a=l.length,u-a<0&&(a=u,n=l,l=c,c=n),r=0;a;)r=(c[--a]=c[a]+l[a]+r)/pe|0,c[a]%=pe;for(r&&(c.unshift(r),++i),u=c.length;c[--u]==0;)c.pop();return e.d=c,e.e=i,ie?ee(e,f):e}function Re(t,e,r){if(t!==~~t||t<e||t>r)throw Error(pt+t)}function Ne(t){var e,r,n,i=t.length-1,a="",o=t[0];if(i>0){for(a+=o,e=1;e<i;e++)n=t[e]+"",r=ne-n.length,r&&(a+=He(r)),a+=n;o=t[e],n=o+"",r=ne-n.length,r&&(a+=He(r))}else if(o===0)return"0";for(;o%10===0;)o/=10;return a+o}var qe=function(){function t(n,i){var a,o=0,u=n.length;for(n=n.slice();u--;)a=n[u]*i+o,n[u]=a%pe|0,o=a/pe|0;return o&&n.unshift(o),n}function e(n,i,a,o){var u,c;if(a!=o)c=a>o?1:-1;else for(u=c=0;u<a;u++)if(n[u]!=i[u]){c=n[u]>i[u]?1:-1;break}return c}function r(n,i,a){for(var o=0;a--;)n[a]-=o,o=n[a]<i[a]?1:0,n[a]=o*pe+n[a]-i[a];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,a,o){var u,c,l,s,f,p,d,m,y,v,b,O,x,w,h,g,A,S,j=n.constructor,T=n.s==i.s?1:-1,E=n.d,$=i.d;if(!n.s)return new j(n);if(!i.s)throw Error(Te+"Division by zero");for(c=n.e-i.e,A=$.length,h=E.length,d=new j(T),m=d.d=[],l=0;$[l]==(E[l]||0);)++l;if($[l]>(E[l]||0)&&--c,a==null?O=a=j.precision:o?O=a+(ce(n)-ce(i))+1:O=a,O<0)return new j(0);if(O=O/ne+2|0,l=0,A==1)for(s=0,$=$[0],O++;(l<h||s)&&O--;l++)x=s*pe+(E[l]||0),m[l]=x/$|0,s=x%$|0;else{for(s=pe/($[0]+1)|0,s>1&&($=t($,s),E=t(E,s),A=$.length,h=E.length),w=A,y=E.slice(0,A),v=y.length;v<A;)y[v++]=0;S=$.slice(),S.unshift(0),g=$[0],$[1]>=pe/2&&++g;do s=0,u=e($,y,A,v),u<0?(b=y[0],A!=v&&(b=b*pe+(y[1]||0)),s=b/g|0,s>1?(s>=pe&&(s=pe-1),f=t($,s),p=f.length,v=y.length,u=e(f,y,p,v),u==1&&(s--,r(f,A<p?S:$,p))):(s==0&&(u=s=1),f=$.slice()),p=f.length,p<v&&f.unshift(0),r(y,f,v),u==-1&&(v=y.length,u=e($,y,A,v),u<1&&(s++,r(y,A<v?S:$,v))),v=y.length):u===0&&(s++,y=[0]),m[l++]=s,u&&y[0]?y[v++]=E[w]||0:(y=[E[w]],v=1);while((w++<h||y[0]!==void 0)&&O--)}return m[0]||m.shift(),d.e=c,ee(d,o?a+ce(d)+1:a)}}();function Ml(t,e){var r,n,i,a,o,u,c=0,l=0,s=t.constructor,f=s.precision;if(ce(t)>16)throw Error(uo+ce(t));if(!t.s)return new s(Pe);for(e==null?(ie=!1,u=f):u=e,o=new s(.03125);t.abs().gte(.1);)t=t.times(o),l+=5;for(n=Math.log(ct(2,l))/Math.LN10*2+5|0,u+=n,r=i=a=new s(Pe),s.precision=u;;){if(i=ee(i.times(t),u),r=r.times(++c),o=a.plus(qe(i,r,u)),Ne(o.d).slice(0,u)===Ne(a.d).slice(0,u)){for(;l--;)a=ee(a.times(a),u);return s.precision=f,e==null?(ie=!0,ee(a,f)):a}a=o}}function ce(t){for(var e=t.e*ne,r=t.d[0];r>=10;r/=10)e++;return e}function Ti(t,e,r){if(e>t.LN10.sd())throw ie=!0,r&&(t.precision=r),Error(Te+"LN10 precision limit exceeded");return ee(new t(t.LN10),e)}function He(t){for(var e="";t--;)e+="0";return e}function $r(t,e){var r,n,i,a,o,u,c,l,s,f=1,p=10,d=t,m=d.d,y=d.constructor,v=y.precision;if(d.s<1)throw Error(Te+(d.s?"NaN":"-Infinity"));if(d.eq(Pe))return new y(0);if(e==null?(ie=!1,l=v):l=e,d.eq(10))return e==null&&(ie=!0),Ti(y,l);if(l+=p,y.precision=l,r=Ne(m),n=r.charAt(0),a=ce(d),Math.abs(a)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)d=d.times(t),r=Ne(d.d),n=r.charAt(0),f++;a=ce(d),n>1?(d=new y("0."+r),a++):d=new y(n+"."+r.slice(1))}else return c=Ti(y,l+2,v).times(a+""),d=$r(new y(n+"."+r.slice(1)),l-p).plus(c),y.precision=v,e==null?(ie=!0,ee(d,v)):d;for(u=o=d=qe(d.minus(Pe),d.plus(Pe),l),s=ee(d.times(d),l),i=3;;){if(o=ee(o.times(s),l),c=u.plus(qe(o,new y(i),l)),Ne(c.d).slice(0,l)===Ne(u.d).slice(0,l))return u=u.times(2),a!==0&&(u=u.plus(Ti(y,l+2,v).times(a+""))),u=qe(u,new y(f),l),y.precision=v,e==null?(ie=!0,ee(u,v)):u;u=c,i+=2}}function pu(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;e.charCodeAt(n)===48;)++n;for(i=e.length;e.charCodeAt(i-1)===48;)--i;if(e=e.slice(n,i),e){if(i-=n,r=r-n-1,t.e=er(r/ne),t.d=[],n=(r+1)%ne,r<0&&(n+=ne),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=ne;n<i;)t.d.push(+e.slice(n,n+=ne));e=e.slice(n),n=ne-e.length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),ie&&(t.e>On||t.e<-On))throw Error(uo+r)}else t.s=0,t.e=0,t.d=[0];return t}function ee(t,e,r){var n,i,a,o,u,c,l,s,f=t.d;for(o=1,a=f[0];a>=10;a/=10)o++;if(n=e-o,n<0)n+=ne,i=e,l=f[s=0];else{if(s=Math.ceil((n+1)/ne),a=f.length,s>=a)return t;for(l=a=f[s],o=1;a>=10;a/=10)o++;n%=ne,i=n-ne+o}if(r!==void 0&&(a=ct(10,o-i-1),u=l/a%10|0,c=e<0||f[s+1]!==void 0||l%a,c=r<4?(u||c)&&(r==0||r==(t.s<0?3:2)):u>5||u==5&&(r==4||c||r==6&&(n>0?i>0?l/ct(10,o-i):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return c?(a=ce(t),f.length=1,e=e-a-1,f[0]=ct(10,(ne-e%ne)%ne),t.e=er(-e/ne)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(n==0?(f.length=s,a=1,s--):(f.length=s+1,a=ct(10,ne-n),f[s]=i>0?(l/ct(10,o-i)%ct(10,i)|0)*a:0),c)for(;;)if(s==0){(f[0]+=a)==pe&&(f[0]=1,++t.e);break}else{if(f[s]+=a,f[s]!=pe)break;f[s--]=0,a=1}for(n=f.length;f[--n]===0;)f.pop();if(ie&&(t.e>On||t.e<-On))throw Error(uo+ce(t));return t}function Dl(t,e){var r,n,i,a,o,u,c,l,s,f,p=t.constructor,d=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),ie?ee(e,d):e;if(c=t.d,f=e.d,n=e.e,l=t.e,c=c.slice(),o=l-n,o){for(s=o<0,s?(r=c,o=-o,u=f.length):(r=f,n=l,u=c.length),i=Math.max(Math.ceil(d/ne),u)+2,o>i&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for(i=c.length,u=f.length,s=i<u,s&&(u=i),i=0;i<u;i++)if(c[i]!=f[i]){s=c[i]<f[i];break}o=0}for(s&&(r=c,c=f,f=r,e.s=-e.s),u=c.length,i=f.length-u;i>0;--i)c[u++]=0;for(i=f.length;i>o;){if(c[--i]<f[i]){for(a=i;a&&c[--a]===0;)c[a]=pe-1;--c[a],c[i]+=pe}c[i]-=f[i]}for(;c[--u]===0;)c.pop();for(;c[0]===0;c.shift())--n;return c[0]?(e.d=c,e.e=n,ie?ee(e,d):e):new p(0)}function yt(t,e,r){var n,i=ce(t),a=Ne(t.d),o=a.length;return e?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+He(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+He(-i-1)+a,r&&(n=r-o)>0&&(a+=He(n))):i>=o?(a+=He(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+He(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=He(n))),t.s<0?"-"+a:a}function du(t,e){if(t.length>e)return t.length=e,!0}function Nl(t){var e,r,n;function i(a){var o=this;if(!(o instanceof i))return new i(a);if(o.constructor=i,a instanceof i){o.s=a.s,o.e=a.e,o.d=(a=a.d)?a.slice():a;return}if(typeof a=="number"){if(a*0!==0)throw Error(pt+a);if(a>0)o.s=1;else if(a<0)a=-a,o.s=-1;else{o.s=0,o.e=0,o.d=[0];return}if(a===~~a&&a<1e7){o.e=0,o.d=[a];return}return pu(o,a.toString())}else if(typeof a!="string")throw Error(pt+a);if(a.charCodeAt(0)===45?(a=a.slice(1),o.s=-1):o.s=1,Eh.test(a))pu(o,a);else throw Error(pt+a)}if(i.prototype=z,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Nl,i.config=i.set=$h,t===void 0&&(t={}),t)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],e=0;e<n.length;)t.hasOwnProperty(r=n[e++])||(t[r]=this[r]);return i.config(t),i}function $h(t){if(!t||typeof t!="object")throw Error(Te+"Object expected");var e,r,n,i=["precision",1,Qt,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if((n=t[r=i[e]])!==void 0)if(er(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(pt+r+": "+n);if((n=t[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(pt+r+": "+n);return this}var co=Nl(jh);Pe=new co(1);const Q=co;function _h(t){return Ch(t)||kh(t)||Ih(t)||Th()}function Th(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ih(t,e){if(t){if(typeof t=="string")return na(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return na(t,e)}}function kh(t){if(typeof Symbol<"u"&&Symbol.iterator in Object(t))return Array.from(t)}function Ch(t){if(Array.isArray(t))return na(t)}function na(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Mh=function(e){return e},Bl={"@@functional/placeholder":!0},Rl=function(e){return e===Bl},vu=function(e){return function r(){return arguments.length===0||arguments.length===1&&Rl(arguments.length<=0?void 0:arguments[0])?r:e.apply(void 0,arguments)}},Dh=function t(e,r){return e===1?r:vu(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];var o=i.filter(function(u){return u!==Bl}).length;return o>=e?r.apply(void 0,i):t(e-o,vu(function(){for(var u=arguments.length,c=new Array(u),l=0;l<u;l++)c[l]=arguments[l];var s=i.map(function(f){return Rl(f)?c.shift():f});return r.apply(void 0,_h(s).concat(c))}))})},si=function(e){return Dh(e.length,e)},ia=function(e,r){for(var n=[],i=e;i<r;++i)n[i-e]=i;return n},Nh=si(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(r){return e[r]}).map(t)}),Bh=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];if(!r.length)return Mh;var i=r.reverse(),a=i[0],o=i.slice(1);return function(){return o.reduce(function(u,c){return c(u)},a.apply(void 0,arguments))}},aa=function(e){return Array.isArray(e)?e.reverse():e.split("").reverse.join("")},Ll=function(e){var r=null,n=null;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];return r&&a.every(function(u,c){return u===r[c]})||(r=a,n=e.apply(void 0,a)),n}};function Rh(t){var e;return t===0?e=1:e=Math.floor(new Q(t).abs().log(10).toNumber())+1,e}function Lh(t,e,r){for(var n=new Q(t),i=0,a=[];n.lt(e)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}var zh=si(function(t,e,r){var n=+t,i=+e;return n+r*(i-n)}),Wh=si(function(t,e,r){var n=e-+t;return n=n||1/0,(r-t)/n}),Fh=si(function(t,e,r){var n=e-+t;return n=n||1/0,Math.max(0,Math.min(1,(r-t)/n))});const fi={rangeStep:Lh,getDigitCount:Rh,interpolateNumber:zh,uninterpolateNumber:Wh,uninterpolateTruncation:Fh};function oa(t){return qh(t)||Gh(t)||zl(t)||Kh()}function Kh(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Gh(t){if(typeof Symbol<"u"&&Symbol.iterator in Object(t))return Array.from(t)}function qh(t){if(Array.isArray(t))return ua(t)}function _r(t,e){return Hh(t)||Xh(t,e)||zl(t,e)||Vh()}function Vh(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function zl(t,e){if(t){if(typeof t=="string")return ua(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ua(t,e)}}function ua(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Xh(t,e){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(t)))){var r=[],n=!0,i=!1,a=void 0;try{for(var o=t[Symbol.iterator](),u;!(n=(u=o.next()).done)&&(r.push(u.value),!(e&&r.length===e));n=!0);}catch(c){i=!0,a=c}finally{try{!n&&o.return!=null&&o.return()}finally{if(i)throw a}}return r}}function Hh(t){if(Array.isArray(t))return t}function Wl(t){var e=_r(t,2),r=e[0],n=e[1],i=r,a=n;return r>n&&(i=n,a=r),[i,a]}function Fl(t,e,r){if(t.lte(0))return new Q(0);var n=fi.getDigitCount(t.toNumber()),i=new Q(10).pow(n),a=t.div(i),o=n!==1?.05:.1,u=new Q(Math.ceil(a.div(o).toNumber())).add(r).mul(o),c=u.mul(i);return e?c:new Q(Math.ceil(c))}function Uh(t,e,r){var n=1,i=new Q(t);if(!i.isint()&&r){var a=Math.abs(t);a<1?(n=new Q(10).pow(fi.getDigitCount(t)-1),i=new Q(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new Q(Math.floor(t)))}else t===0?i=new Q(Math.floor((e-1)/2)):r||(i=new Q(Math.floor(t)));var o=Math.floor((e-1)/2),u=Bh(Nh(function(c){return i.add(new Q(c-o).mul(n)).toNumber()}),ia);return u(0,e)}function Kl(t,e,r,n){var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:0;if(!Number.isFinite((e-t)/(r-1)))return{step:new Q(0),tickMin:new Q(0),tickMax:new Q(0)};var a=Fl(new Q(e).sub(t).div(r-1),n,i),o;t<=0&&e>=0?o=new Q(0):(o=new Q(t).add(e).div(2),o=o.sub(new Q(o).mod(a)));var u=Math.ceil(o.sub(t).div(a).toNumber()),c=Math.ceil(new Q(e).sub(o).div(a).toNumber()),l=u+c+1;return l>r?Kl(t,e,r,n,i+1):(l<r&&(c=e>0?c+(r-l):c,u=e>0?u:u+(r-l)),{step:a,tickMin:o.sub(new Q(u).mul(a)),tickMax:o.add(new Q(c).mul(a))})}function Yh(t){var e=_r(t,2),r=e[0],n=e[1],i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:6,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Math.max(i,2),u=Wl([r,n]),c=_r(u,2),l=c[0],s=c[1];if(l===-1/0||s===1/0){var f=s===1/0?[l].concat(oa(ia(0,i-1).map(function(){return 1/0}))):[].concat(oa(ia(0,i-1).map(function(){return-1/0})),[s]);return r>n?aa(f):f}if(l===s)return Uh(l,i,a);var p=Kl(l,s,o,a),d=p.step,m=p.tickMin,y=p.tickMax,v=fi.rangeStep(m,y.add(new Q(.1).mul(d)),d);return r>n?aa(v):v}function Zh(t,e){var r=_r(t,2),n=r[0],i=r[1],a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0,o=Wl([n,i]),u=_r(o,2),c=u[0],l=u[1];if(c===-1/0||l===1/0)return[n,i];if(c===l)return[c];var s=Math.max(e,2),f=Fl(new Q(l).sub(c).div(s-1),a,0),p=[].concat(oa(fi.rangeStep(new Q(c),new Q(l).sub(new Q(.99).mul(f)),f)),[l]);return n>i?aa(p):p}var Jh=Ll(Yh),Qh=Ll(Zh),ey=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function Nt(t){"@babel/helpers - typeof";return Nt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Nt(t)}function xn(){return xn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},xn.apply(this,arguments)}function ty(t,e){return ay(t)||iy(t,e)||ny(t,e)||ry()}function ry(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ny(t,e){if(t){if(typeof t=="string")return hu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hu(t,e)}}function hu(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function iy(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function ay(t){if(Array.isArray(t))return t}function oy(t,e){if(t==null)return{};var r=uy(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function uy(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function cy(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function yu(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Vl(n.key),n)}}function ly(t,e,r){return e&&yu(t.prototype,e),r&&yu(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function sy(t,e,r){return e=wn(e),fy(t,Gl()?Reflect.construct(e,r||[],wn(t).constructor):e.apply(t,r))}function fy(t,e){if(e&&(Nt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return py(t)}function py(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Gl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Gl=function(){return!!t})()}function wn(t){return wn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},wn(t)}function dy(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ca(t,e)}function ca(t,e){return ca=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},ca(t,e)}function ql(t,e,r){return e=Vl(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Vl(t){var e=vy(t,"string");return Nt(e)=="symbol"?e:e+""}function vy(t,e){if(Nt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Nt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var pi=function(t){function e(){return cy(this,e),sy(this,e,arguments)}return dy(e,t),ly(e,[{key:"render",value:function(){var n=this.props,i=n.offset,a=n.layout,o=n.width,u=n.dataKey,c=n.data,l=n.dataPointFormatter,s=n.xAxis,f=n.yAxis,p=oy(n,ey),d=G(p,!1);this.props.direction==="x"&&s.type!=="number"&&dt(!1);var m=c.map(function(y){var v=l(y,u),b=v.x,O=v.y,x=v.value,w=v.errorVal;if(!w)return null;var h=[],g,A;if(Array.isArray(w)){var S=ty(w,2);g=S[0],A=S[1]}else g=A=w;if(a==="vertical"){var j=s.scale,T=O+i,E=T+o,$=T-o,C=j(x-g),k=j(x+A);h.push({x1:k,y1:E,x2:k,y2:$}),h.push({x1:C,y1:T,x2:k,y2:T}),h.push({x1:C,y1:E,x2:C,y2:$})}else if(a==="horizontal"){var I=f.scale,M=b+i,D=M-o,R=M+o,W=I(x-g),F=I(x+A);h.push({x1:D,y1:F,x2:R,y2:F}),h.push({x1:M,y1:W,x2:M,y2:F}),h.push({x1:D,y1:W,x2:R,y2:W})}return P.createElement(Z,xn({className:"recharts-errorBar",key:"bar-".concat(h.map(function(q){return"".concat(q.x1,"-").concat(q.x2,"-").concat(q.y1,"-").concat(q.y2)}))},d),h.map(function(q){return P.createElement("line",xn({},q,{key:"line-".concat(q.x1,"-").concat(q.x2,"-").concat(q.y1,"-").concat(q.y2)}))}))});return P.createElement(Z,{className:"recharts-errorBars"},m)}}])}(P.Component);ql(pi,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"});ql(pi,"displayName","ErrorBar");function Tr(t){"@babel/helpers - typeof";return Tr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tr(t)}function mu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function at(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?mu(Object(r),!0).forEach(function(n){hy(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):mu(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function hy(t,e,r){return e=yy(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yy(t){var e=my(t,"string");return Tr(e)=="symbol"?e:e+""}function my(t,e){if(Tr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Tr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Xl=function(e){var r=e.children,n=e.formattedGraphicalItems,i=e.legendWidth,a=e.legendContent,o=Ae(r,Tt);if(!o)return null;var u=Tt.defaultProps,c=u!==void 0?at(at({},u),o.props):{},l;return o.props&&o.props.payload?l=o.props&&o.props.payload:a==="children"?l=(n||[]).reduce(function(s,f){var p=f.item,d=f.props,m=d.sectors||d.data||[];return s.concat(m.map(function(y){return{type:o.props.iconType||p.props.legendType,value:y.name,color:y.fill,payload:y}}))},[]):l=(n||[]).map(function(s){var f=s.item,p=f.type.defaultProps,d=p!==void 0?at(at({},p),f.props):{},m=d.dataKey,y=d.name,v=d.legendType,b=d.hide;return{inactive:b,dataKey:m,type:c.iconType||v||"square",color:lo(f),value:y||m,payload:d}}),at(at(at({},c),Tt.getWithHeight(o,i)),{},{payload:l,item:o})};function Ir(t){"@babel/helpers - typeof";return Ir=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ir(t)}function gu(t){return xy(t)||Oy(t)||by(t)||gy()}function gy(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function by(t,e){if(t){if(typeof t=="string")return la(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return la(t,e)}}function Oy(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function xy(t){if(Array.isArray(t))return la(t)}function la(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function bu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function oe(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?bu(Object(r),!0).forEach(function(n){It(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):bu(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function It(t,e,r){return e=wy(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function wy(t){var e=Ay(t,"string");return Ir(e)=="symbol"?e:e+""}function Ay(t,e){if(Ir(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Ir(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function me(t,e,r){return Y(t)||Y(e)?r:se(e)?Se(t,e,r):X(e)?e(t):r}function mr(t,e,r,n){var i=Sh(t,function(u){return me(u,e)});if(r==="number"){var a=i.filter(function(u){return N(u)||parseFloat(u)});return a.length?[li(a),ci(a)]:[1/0,-1/0]}var o=n?i.filter(function(u){return!Y(u)}):i;return o.map(function(u){return se(u)||u instanceof Date?u:""})}var Py=function(e){var r,n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=-1,u=(r=n==null?void 0:n.length)!==null&&r!==void 0?r:0;if(u<=1)return 0;if(a&&a.axisType==="angleAxis"&&Math.abs(Math.abs(a.range[1]-a.range[0])-360)<=1e-6)for(var c=a.range,l=0;l<u;l++){var s=l>0?i[l-1].coordinate:i[u-1].coordinate,f=i[l].coordinate,p=l>=u-1?i[0].coordinate:i[l+1].coordinate,d=void 0;if(be(f-s)!==be(p-f)){var m=[];if(be(p-f)===be(c[1]-c[0])){d=p;var y=f+c[1]-c[0];m[0]=Math.min(y,(y+s)/2),m[1]=Math.max(y,(y+s)/2)}else{d=s;var v=p+c[1]-c[0];m[0]=Math.min(f,(v+f)/2),m[1]=Math.max(f,(v+f)/2)}var b=[Math.min(f,(d+f)/2),Math.max(f,(d+f)/2)];if(e>b[0]&&e<=b[1]||e>=m[0]&&e<=m[1]){o=i[l].index;break}}else{var O=Math.min(s,p),x=Math.max(s,p);if(e>(O+f)/2&&e<=(x+f)/2){o=i[l].index;break}}}else for(var w=0;w<u;w++)if(w===0&&e<=(n[w].coordinate+n[w+1].coordinate)/2||w>0&&w<u-1&&e>(n[w].coordinate+n[w-1].coordinate)/2&&e<=(n[w].coordinate+n[w+1].coordinate)/2||w===u-1&&e>(n[w].coordinate+n[w-1].coordinate)/2){o=n[w].index;break}return o},lo=function(e){var r,n=e,i=n.type.displayName,a=(r=e.type)!==null&&r!==void 0&&r.defaultProps?oe(oe({},e.type.defaultProps),e.props):e.props,o=a.stroke,u=a.fill,c;switch(i){case"Line":c=o;break;case"Area":case"Radar":c=o&&o!=="none"?o:u;break;default:c=u;break}return c},Sy=function(e){var r=e.barSize,n=e.totalSize,i=e.stackGroups,a=i===void 0?{}:i;if(!a)return{};for(var o={},u=Object.keys(a),c=0,l=u.length;c<l;c++)for(var s=a[u[c]].stackGroups,f=Object.keys(s),p=0,d=f.length;p<d;p++){var m=s[f[p]],y=m.items,v=m.cateAxisId,b=y.filter(function(A){return Ke(A.type).indexOf("Bar")>=0});if(b&&b.length){var O=b[0].type.defaultProps,x=O!==void 0?oe(oe({},O),b[0].props):b[0].props,w=x.barSize,h=x[v];o[h]||(o[h]=[]);var g=Y(w)?r:w;o[h].push({item:b[0],stackList:b.slice(1),barSize:Y(g)?void 0:Oe(g,n,0)})}}return o},jy=function(e){var r=e.barGap,n=e.barCategoryGap,i=e.bandSize,a=e.sizeList,o=a===void 0?[]:a,u=e.maxBarSize,c=o.length;if(c<1)return null;var l=Oe(r,i,0,!0),s,f=[];if(o[0].barSize===+o[0].barSize){var p=!1,d=i/c,m=o.reduce(function(w,h){return w+h.barSize||0},0);m+=(c-1)*l,m>=i&&(m-=(c-1)*l,l=0),m>=i&&d>0&&(p=!0,d*=.9,m=c*d);var y=(i-m)/2>>0,v={offset:y-l,size:0};s=o.reduce(function(w,h){var g={item:h.item,position:{offset:v.offset+v.size+l,size:p?d:h.barSize}},A=[].concat(gu(w),[g]);return v=A[A.length-1].position,h.stackList&&h.stackList.length&&h.stackList.forEach(function(S){A.push({item:S,position:v})}),A},f)}else{var b=Oe(n,i,0,!0);i-2*b-(c-1)*l<=0&&(l=0);var O=(i-2*b-(c-1)*l)/c;O>1&&(O>>=0);var x=u===+u?Math.min(O,u):O;s=o.reduce(function(w,h,g){var A=[].concat(gu(w),[{item:h.item,position:{offset:b+(O+l)*g+(O-x)/2,size:x}}]);return h.stackList&&h.stackList.length&&h.stackList.forEach(function(S){A.push({item:S,position:A[A.length-1].position})}),A},f)}return s},Ey=function(e,r,n,i){var a=n.children,o=n.width,u=n.margin,c=o-(u.left||0)-(u.right||0),l=Xl({children:a,legendWidth:c});if(l){var s=i||{},f=s.width,p=s.height,d=l.align,m=l.verticalAlign,y=l.layout;if((y==="vertical"||y==="horizontal"&&m==="middle")&&d!=="center"&&N(e[d]))return oe(oe({},e),{},It({},d,e[d]+(f||0)));if((y==="horizontal"||y==="vertical"&&d==="center")&&m!=="middle"&&N(e[m]))return oe(oe({},e),{},It({},m,e[m]+(p||0)))}return e},$y=function(e,r,n){return Y(r)?!0:e==="horizontal"?r==="yAxis":e==="vertical"||n==="x"?r==="xAxis":n==="y"?r==="yAxis":!0},Hl=function(e,r,n,i,a){var o=r.props.children,u=_e(o,pi).filter(function(l){return $y(i,a,l.props.direction)});if(u&&u.length){var c=u.map(function(l){return l.props.dataKey});return e.reduce(function(l,s){var f=me(s,n);if(Y(f))return l;var p=Array.isArray(f)?[li(f),ci(f)]:[f,f],d=c.reduce(function(m,y){var v=me(s,y,0),b=p[0]-Math.abs(Array.isArray(v)?v[0]:v),O=p[1]+Math.abs(Array.isArray(v)?v[1]:v);return[Math.min(b,m[0]),Math.max(O,m[1])]},[1/0,-1/0]);return[Math.min(d[0],l[0]),Math.max(d[1],l[1])]},[1/0,-1/0])}return null},_y=function(e,r,n,i,a){var o=r.map(function(u){return Hl(e,u,n,a,i)}).filter(function(u){return!Y(u)});return o&&o.length?o.reduce(function(u,c){return[Math.min(u[0],c[0]),Math.max(u[1],c[1])]},[1/0,-1/0]):null},Ul=function(e,r,n,i,a){var o=r.map(function(c){var l=c.props.dataKey;return n==="number"&&l&&Hl(e,c,l,i)||mr(e,l,n,a)});if(n==="number")return o.reduce(function(c,l){return[Math.min(c[0],l[0]),Math.max(c[1],l[1])]},[1/0,-1/0]);var u={};return o.reduce(function(c,l){for(var s=0,f=l.length;s<f;s++)u[l[s]]||(u[l[s]]=!0,c.push(l[s]));return c},[])},Yl=function(e,r){return e==="horizontal"&&r==="xAxis"||e==="vertical"&&r==="yAxis"||e==="centric"&&r==="angleAxis"||e==="radial"&&r==="radiusAxis"},Zl=function(e,r,n,i){if(i)return e.map(function(c){return c.coordinate});var a,o,u=e.map(function(c){return c.coordinate===r&&(a=!0),c.coordinate===n&&(o=!0),c.coordinate});return a||u.push(r),o||u.push(n),u},Fe=function(e,r,n){if(!e)return null;var i=e.scale,a=e.duplicateDomain,o=e.type,u=e.range,c=e.realScaleType==="scaleBand"?i.bandwidth()/2:2,l=(r||n)&&o==="category"&&i.bandwidth?i.bandwidth()/c:0;if(l=e.axisType==="angleAxis"&&(u==null?void 0:u.length)>=2?be(u[0]-u[1])*2*l:l,r&&(e.ticks||e.niceTicks)){var s=(e.ticks||e.niceTicks).map(function(f){var p=a?a.indexOf(f):f;return{coordinate:i(p)+l,value:f,offset:l}});return s.filter(function(f){return!Jr(f.coordinate)})}return e.isCategorical&&e.categoricalDomain?e.categoricalDomain.map(function(f,p){return{coordinate:i(f)+l,value:f,index:p,offset:l}}):i.ticks&&!n?i.ticks(e.tickCount).map(function(f){return{coordinate:i(f)+l,value:f,offset:l}}):i.domain().map(function(f,p){return{coordinate:i(f)+l,value:a?a[f]:f,index:p,offset:l}})},Ii=new WeakMap,un=function(e,r){if(typeof r!="function")return e;Ii.has(e)||Ii.set(e,new WeakMap);var n=Ii.get(e);if(n.has(r))return n.get(r);var i=function(){e.apply(void 0,arguments),r.apply(void 0,arguments)};return n.set(r,i),i},Jl=function(e,r,n){var i=e.scale,a=e.type,o=e.layout,u=e.axisType;if(i==="auto")return o==="radial"&&u==="radiusAxis"?{scale:Li(),realScaleType:"band"}:o==="radial"&&u==="angleAxis"?{scale:zi(),realScaleType:"linear"}:a==="category"&&r&&(r.indexOf("LineChart")>=0||r.indexOf("AreaChart")>=0||r.indexOf("ComposedChart")>=0&&!n)?{scale:hr(),realScaleType:"point"}:a==="category"?{scale:Li(),realScaleType:"band"}:{scale:zi(),realScaleType:"linear"};if(Zr(i)){var c="scale".concat(ai(i));return{scale:(fu[c]||hr)(),realScaleType:fu[c]?c:"point"}}return X(i)?{scale:i}:{scale:hr(),realScaleType:"point"}},Ou=1e-4,Ql=function(e){var r=e.domain();if(!(!r||r.length<=2)){var n=r.length,i=e.range(),a=Math.min(i[0],i[1])-Ou,o=Math.max(i[0],i[1])+Ou,u=e(r[0]),c=e(r[n-1]);(u<a||u>o||c<a||c>o)&&e.domain([r[0],r[n-1]])}},Ty=function(e,r){if(!e)return null;for(var n=0,i=e.length;n<i;n++)if(e[n].item===r)return e[n].position;return null},Iy=function(e,r){if(!r||r.length!==2||!N(r[0])||!N(r[1]))return e;var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]),a=[e[0],e[1]];return(!N(e[0])||e[0]<n)&&(a[0]=n),(!N(e[1])||e[1]>i)&&(a[1]=i),a[0]>i&&(a[0]=i),a[1]<n&&(a[1]=n),a},ky=function(e){var r=e.length;if(!(r<=0))for(var n=0,i=e[0].length;n<i;++n)for(var a=0,o=0,u=0;u<r;++u){var c=Jr(e[u][n][1])?e[u][n][0]:e[u][n][1];c>=0?(e[u][n][0]=a,e[u][n][1]=a+c,a=e[u][n][1]):(e[u][n][0]=o,e[u][n][1]=o+c,o=e[u][n][1])}},Cy=function(e){var r=e.length;if(!(r<=0))for(var n=0,i=e[0].length;n<i;++n)for(var a=0,o=0;o<r;++o){var u=Jr(e[o][n][1])?e[o][n][0]:e[o][n][1];u>=0?(e[o][n][0]=a,e[o][n][1]=a+u,a=e[o][n][1]):(e[o][n][0]=0,e[o][n][1]=0)}},My={sign:ky,expand:ap,none:op,silhouette:up,wiggle:cp,positive:Cy},Dy=function(e,r,n){var i=r.map(function(u){return u.props.dataKey}),a=My[n],o=np().keys(i).value(function(u,c){return+me(u,c,0)}).order(ip).offset(a);return o(e)},Ny=function(e,r,n,i,a,o){if(!e)return null;var u=o?r.reverse():r,c={},l=u.reduce(function(f,p){var d,m=(d=p.type)!==null&&d!==void 0&&d.defaultProps?oe(oe({},p.type.defaultProps),p.props):p.props,y=m.stackId,v=m.hide;if(v)return f;var b=m[n],O=f[b]||{hasStack:!1,stackGroups:{}};if(se(y)){var x=O.stackGroups[y]||{numericAxisId:n,cateAxisId:i,items:[]};x.items.push(p),O.hasStack=!0,O.stackGroups[y]=x}else O.stackGroups[Qr("_stackId_")]={numericAxisId:n,cateAxisId:i,items:[p]};return oe(oe({},f),{},It({},b,O))},c),s={};return Object.keys(l).reduce(function(f,p){var d=l[p];if(d.hasStack){var m={};d.stackGroups=Object.keys(d.stackGroups).reduce(function(y,v){var b=d.stackGroups[v];return oe(oe({},y),{},It({},v,{numericAxisId:n,cateAxisId:i,items:b.items,stackedData:Dy(e,b.items,a)}))},m)}return oe(oe({},f),{},It({},p,d))},s)},es=function(e,r){var n=r.realScaleType,i=r.type,a=r.tickCount,o=r.originalDomain,u=r.allowDecimals,c=n||r.scale;if(c!=="auto"&&c!=="linear")return null;if(a&&i==="number"&&o&&(o[0]==="auto"||o[1]==="auto")){var l=e.domain();if(!l.length)return null;var s=Jh(l,a,u);return e.domain([li(s),ci(s)]),{niceTicks:s}}if(a&&i==="number"){var f=e.domain(),p=Qh(f,a,u);return{niceTicks:p}}return null},xu=function(e){var r=e.axis,n=e.ticks,i=e.offset,a=e.bandSize,o=e.entry,u=e.index;if(r.type==="category")return n[u]?n[u].coordinate+i:null;var c=me(o,r.dataKey,r.domain[u]);return Y(c)?null:r.scale(c)-a/2+i},By=function(e){var r=e.numericAxis,n=r.scale.domain();if(r.type==="number"){var i=Math.min(n[0],n[1]),a=Math.max(n[0],n[1]);return i<=0&&a>=0?0:a<0?a:i}return n[0]},Ry=function(e,r){var n,i=(n=e.type)!==null&&n!==void 0&&n.defaultProps?oe(oe({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(se(a)){var o=r[a];if(o){var u=o.items.indexOf(e);return u>=0?o.stackedData[u]:null}}return null},Ly=function(e){return e.reduce(function(r,n){return[li(n.concat([r[0]]).filter(N)),ci(n.concat([r[1]]).filter(N))]},[1/0,-1/0])},ts=function(e,r,n){return Object.keys(e).reduce(function(i,a){var o=e[a],u=o.stackedData,c=u.reduce(function(l,s){var f=Ly(s.slice(r,n+1));return[Math.min(l[0],f[0]),Math.max(l[1],f[1])]},[1/0,-1/0]);return[Math.min(c[0],i[0]),Math.max(c[1],i[1])]},[1/0,-1/0]).map(function(i){return i===1/0||i===-1/0?0:i})},wu=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,Au=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,sa=function(e,r,n){if(X(e))return e(r,n);if(!Array.isArray(e))return r;var i=[];if(N(e[0]))i[0]=n?e[0]:Math.min(e[0],r[0]);else if(wu.test(e[0])){var a=+wu.exec(e[0])[1];i[0]=r[0]-a}else X(e[0])?i[0]=e[0](r[0]):i[0]=r[0];if(N(e[1]))i[1]=n?e[1]:Math.max(e[1],r[1]);else if(Au.test(e[1])){var o=+Au.exec(e[1])[1];i[1]=r[1]+o}else X(e[1])?i[1]=e[1](r[1]):i[1]=r[1];return i},An=function(e,r,n){if(e&&e.scale&&e.scale.bandwidth){var i=e.scale.bandwidth();if(!n||i>0)return i}if(e&&r&&r.length>=2){for(var a=Za(r,function(f){return f.coordinate}),o=1/0,u=1,c=a.length;u<c;u++){var l=a[u],s=a[u-1];o=Math.min((l.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return n?void 0:0},Pu=function(e,r,n){return!e||!e.length||Hn(e,Se(n,"type.defaultProps.domain"))?r:e},rs=function(e,r){var n=e.type.defaultProps?oe(oe({},e.type.defaultProps),e.props):e.props,i=n.dataKey,a=n.name,o=n.unit,u=n.formatter,c=n.tooltipType,l=n.chartType,s=n.hide;return oe(oe({},G(e,!1)),{},{dataKey:i,unit:o,formatter:u,name:a||i,color:lo(e),value:me(r,i),type:c,payload:r,chartType:l,hide:s})};function kr(t){"@babel/helpers - typeof";return kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},kr(t)}function Su(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function We(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Su(Object(r),!0).forEach(function(n){ns(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Su(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function ns(t,e,r){return e=zy(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function zy(t){var e=Wy(t,"string");return kr(e)=="symbol"?e:e+""}function Wy(t,e){if(kr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Fy(t,e){return Vy(t)||qy(t,e)||Gy(t,e)||Ky()}function Ky(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Gy(t,e){if(t){if(typeof t=="string")return ju(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ju(t,e)}}function ju(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function qy(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function Vy(t){if(Array.isArray(t))return t}var Pn=Math.PI/180,Xy=function(e){return e*180/Math.PI},re=function(e,r,n,i){return{x:e+Math.cos(-Pn*i)*n,y:r+Math.sin(-Pn*i)*n}},is=function(e,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(e-(n.left||0)-(n.right||0)),Math.abs(r-(n.top||0)-(n.bottom||0)))/2},Hy=function(e,r,n,i,a){var o=e.width,u=e.height,c=e.startAngle,l=e.endAngle,s=Oe(e.cx,o,o/2),f=Oe(e.cy,u,u/2),p=is(o,u,n),d=Oe(e.innerRadius,p,0),m=Oe(e.outerRadius,p,p*.8),y=Object.keys(r);return y.reduce(function(v,b){var O=r[b],x=O.domain,w=O.reversed,h;if(Y(O.range))i==="angleAxis"?h=[c,l]:i==="radiusAxis"&&(h=[d,m]),w&&(h=[h[1],h[0]]);else{h=O.range;var g=h,A=Fy(g,2);c=A[0],l=A[1]}var S=Jl(O,a),j=S.realScaleType,T=S.scale;T.domain(x).range(h),Ql(T);var E=es(T,We(We({},O),{},{realScaleType:j})),$=We(We(We({},O),E),{},{range:h,radius:m,realScaleType:j,scale:T,cx:s,cy:f,innerRadius:d,outerRadius:m,startAngle:c,endAngle:l});return We(We({},v),{},ns({},b,$))},{})},Uy=function(e,r){var n=e.x,i=e.y,a=r.x,o=r.y;return Math.sqrt(Math.pow(n-a,2)+Math.pow(i-o,2))},Yy=function(e,r){var n=e.x,i=e.y,a=r.cx,o=r.cy,u=Uy({x:n,y:i},{x:a,y:o});if(u<=0)return{radius:u};var c=(n-a)/u,l=Math.acos(c);return i>o&&(l=2*Math.PI-l),{radius:u,angle:Xy(l),angleInRadian:l}},Zy=function(e){var r=e.startAngle,n=e.endAngle,i=Math.floor(r/360),a=Math.floor(n/360),o=Math.min(i,a);return{startAngle:r-o*360,endAngle:n-o*360}},Jy=function(e,r){var n=r.startAngle,i=r.endAngle,a=Math.floor(n/360),o=Math.floor(i/360),u=Math.min(a,o);return e+u*360},Eu=function(e,r){var n=e.x,i=e.y,a=Yy({x:n,y:i},r),o=a.radius,u=a.angle,c=r.innerRadius,l=r.outerRadius;if(o<c||o>l)return!1;if(o===0)return!0;var s=Zy(r),f=s.startAngle,p=s.endAngle,d=u,m;if(f<=p){for(;d>p;)d-=360;for(;d<f;)d+=360;m=d>=f&&d<=p}else{for(;d>f;)d-=360;for(;d<p;)d+=360;m=d>=p&&d<=f}return m?We(We({},r),{},{radius:o,angle:Jy(d,r)}):null},as=function(e){return!B.isValidElement(e)&&!X(e)&&typeof e!="boolean"?e.className:""};function Cr(t){"@babel/helpers - typeof";return Cr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Cr(t)}var Qy=["offset"];function em(t){return im(t)||nm(t)||rm(t)||tm()}function tm(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function rm(t,e){if(t){if(typeof t=="string")return fa(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fa(t,e)}}function nm(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function im(t){if(Array.isArray(t))return fa(t)}function fa(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function am(t,e){if(t==null)return{};var r=om(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function om(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function $u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function le(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?$u(Object(r),!0).forEach(function(n){um(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):$u(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function um(t,e,r){return e=cm(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function cm(t){var e=lm(t,"string");return Cr(e)=="symbol"?e:e+""}function lm(t,e){if(Cr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Cr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Mr(){return Mr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Mr.apply(this,arguments)}var sm=function(e){var r=e.value,n=e.formatter,i=Y(e.children)?r:e.children;return X(n)?n(i):i},fm=function(e,r){var n=be(r-e),i=Math.min(Math.abs(r-e),360);return n*i},pm=function(e,r,n){var i=e.position,a=e.viewBox,o=e.offset,u=e.className,c=a,l=c.cx,s=c.cy,f=c.innerRadius,p=c.outerRadius,d=c.startAngle,m=c.endAngle,y=c.clockWise,v=(f+p)/2,b=fm(d,m),O=b>=0?1:-1,x,w;i==="insideStart"?(x=d+O*o,w=y):i==="insideEnd"?(x=m-O*o,w=!y):i==="end"&&(x=m+O*o,w=y),w=b<=0?w:!w;var h=re(l,s,v,x),g=re(l,s,v,x+(w?1:-1)*359),A="M".concat(h.x,",").concat(h.y,`
    A`).concat(v,",").concat(v,",0,1,").concat(w?0:1,`,
    `).concat(g.x,",").concat(g.y),S=Y(e.id)?Qr("recharts-radial-line-"):e.id;return P.createElement("text",Mr({},n,{dominantBaseline:"central",className:U("recharts-radial-bar-label",u)}),P.createElement("defs",null,P.createElement("path",{id:S,d:A})),P.createElement("textPath",{xlinkHref:"#".concat(S)},r))},dm=function(e){var r=e.viewBox,n=e.offset,i=e.position,a=r,o=a.cx,u=a.cy,c=a.innerRadius,l=a.outerRadius,s=a.startAngle,f=a.endAngle,p=(s+f)/2;if(i==="outside"){var d=re(o,u,l+n,p),m=d.x,y=d.y;return{x:m,y,textAnchor:m>=o?"start":"end",verticalAnchor:"middle"}}if(i==="center")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"middle"};if(i==="centerTop")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"start"};if(i==="centerBottom")return{x:o,y:u,textAnchor:"middle",verticalAnchor:"end"};var v=(c+l)/2,b=re(o,u,v,p),O=b.x,x=b.y;return{x:O,y:x,textAnchor:"middle",verticalAnchor:"middle"}},vm=function(e){var r=e.viewBox,n=e.parentViewBox,i=e.offset,a=e.position,o=r,u=o.x,c=o.y,l=o.width,s=o.height,f=s>=0?1:-1,p=f*i,d=f>0?"end":"start",m=f>0?"start":"end",y=l>=0?1:-1,v=y*i,b=y>0?"end":"start",O=y>0?"start":"end";if(a==="top"){var x={x:u+l/2,y:c-f*i,textAnchor:"middle",verticalAnchor:d};return le(le({},x),n?{height:Math.max(c-n.y,0),width:l}:{})}if(a==="bottom"){var w={x:u+l/2,y:c+s+p,textAnchor:"middle",verticalAnchor:m};return le(le({},w),n?{height:Math.max(n.y+n.height-(c+s),0),width:l}:{})}if(a==="left"){var h={x:u-v,y:c+s/2,textAnchor:b,verticalAnchor:"middle"};return le(le({},h),n?{width:Math.max(h.x-n.x,0),height:s}:{})}if(a==="right"){var g={x:u+l+v,y:c+s/2,textAnchor:O,verticalAnchor:"middle"};return le(le({},g),n?{width:Math.max(n.x+n.width-g.x,0),height:s}:{})}var A=n?{width:l,height:s}:{};return a==="insideLeft"?le({x:u+v,y:c+s/2,textAnchor:O,verticalAnchor:"middle"},A):a==="insideRight"?le({x:u+l-v,y:c+s/2,textAnchor:b,verticalAnchor:"middle"},A):a==="insideTop"?le({x:u+l/2,y:c+p,textAnchor:"middle",verticalAnchor:m},A):a==="insideBottom"?le({x:u+l/2,y:c+s-p,textAnchor:"middle",verticalAnchor:d},A):a==="insideTopLeft"?le({x:u+v,y:c+p,textAnchor:O,verticalAnchor:m},A):a==="insideTopRight"?le({x:u+l-v,y:c+p,textAnchor:b,verticalAnchor:m},A):a==="insideBottomLeft"?le({x:u+v,y:c+s-p,textAnchor:O,verticalAnchor:d},A):a==="insideBottomRight"?le({x:u+l-v,y:c+s-p,textAnchor:b,verticalAnchor:d},A):Jt(a)&&(N(a.x)||st(a.x))&&(N(a.y)||st(a.y))?le({x:u+Oe(a.x,l),y:c+Oe(a.y,s),textAnchor:"end",verticalAnchor:"end"},A):le({x:u+l/2,y:c+s/2,textAnchor:"middle",verticalAnchor:"middle"},A)},hm=function(e){return"cx"in e&&N(e.cx)};function de(t){var e=t.offset,r=e===void 0?5:e,n=am(t,Qy),i=le({offset:r},n),a=i.viewBox,o=i.position,u=i.value,c=i.children,l=i.content,s=i.className,f=s===void 0?"":s,p=i.textBreakAll;if(!a||Y(u)&&Y(c)&&!B.isValidElement(l)&&!X(l))return null;if(B.isValidElement(l))return B.cloneElement(l,i);var d;if(X(l)){if(d=B.createElement(l,i),B.isValidElement(d))return d}else d=sm(i);var m=hm(a),y=G(i,!0);if(m&&(o==="insideStart"||o==="insideEnd"||o==="end"))return pm(i,d,y);var v=m?dm(i):vm(i);return P.createElement(ht,Mr({className:U("recharts-label",f)},y,v,{breakAll:p}),d)}de.displayName="Label";var os=function(e){var r=e.cx,n=e.cy,i=e.angle,a=e.startAngle,o=e.endAngle,u=e.r,c=e.radius,l=e.innerRadius,s=e.outerRadius,f=e.x,p=e.y,d=e.top,m=e.left,y=e.width,v=e.height,b=e.clockWise,O=e.labelViewBox;if(O)return O;if(N(y)&&N(v)){if(N(f)&&N(p))return{x:f,y:p,width:y,height:v};if(N(d)&&N(m))return{x:d,y:m,width:y,height:v}}return N(f)&&N(p)?{x:f,y:p,width:0,height:0}:N(r)&&N(n)?{cx:r,cy:n,startAngle:a||i||0,endAngle:o||i||0,innerRadius:l||0,outerRadius:s||c||u||0,clockWise:b}:e.viewBox?e.viewBox:{}},ym=function(e,r){return e?e===!0?P.createElement(de,{key:"label-implicit",viewBox:r}):se(e)?P.createElement(de,{key:"label-implicit",viewBox:r,value:e}):B.isValidElement(e)?e.type===de?B.cloneElement(e,{key:"label-implicit",viewBox:r}):P.createElement(de,{key:"label-implicit",content:e,viewBox:r}):X(e)?P.createElement(de,{key:"label-implicit",content:e,viewBox:r}):Jt(e)?P.createElement(de,Mr({viewBox:r},e,{key:"label-implicit"})):null:null},mm=function(e,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!e||!e.children&&n&&!e.label)return null;var i=e.children,a=os(e),o=_e(i,de).map(function(c,l){return B.cloneElement(c,{viewBox:r||a,key:"label-".concat(l)})});if(!n)return o;var u=ym(e.label,r||a);return[u].concat(em(o))};de.parseViewBox=os;de.renderCallByParent=mm;function Dr(t){"@babel/helpers - typeof";return Dr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Dr(t)}var gm=["valueAccessor"],bm=["data","dataKey","clockWise","id","textBreakAll"];function Om(t){return Pm(t)||Am(t)||wm(t)||xm()}function xm(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function wm(t,e){if(t){if(typeof t=="string")return pa(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pa(t,e)}}function Am(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Pm(t){if(Array.isArray(t))return pa(t)}function pa(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Sn(){return Sn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Sn.apply(this,arguments)}function _u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Tu(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?_u(Object(r),!0).forEach(function(n){Sm(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_u(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Sm(t,e,r){return e=jm(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function jm(t){var e=Em(t,"string");return Dr(e)=="symbol"?e:e+""}function Em(t,e){if(Dr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Dr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Iu(t,e){if(t==null)return{};var r=$m(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function $m(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}var _m=function(e){return Array.isArray(e.value)?lp(e.value):e.value};function Ze(t){var e=t.valueAccessor,r=e===void 0?_m:e,n=Iu(t,gm),i=n.data,a=n.dataKey,o=n.clockWise,u=n.id,c=n.textBreakAll,l=Iu(n,bm);return!i||!i.length?null:P.createElement(Z,{className:"recharts-label-list"},i.map(function(s,f){var p=Y(a)?r(s,f):me(s&&s.payload,a),d=Y(u)?{}:{id:"".concat(u,"-").concat(f)};return P.createElement(de,Sn({},G(s,!0),l,d,{parentViewBox:s.parentViewBox,value:p,textBreakAll:c,viewBox:de.parseViewBox(Y(o)?s:Tu(Tu({},s),{},{clockWise:o})),key:"label-".concat(f),index:f}))}))}Ze.displayName="LabelList";function Tm(t,e){return t?t===!0?P.createElement(Ze,{key:"labelList-implicit",data:e}):P.isValidElement(t)||X(t)?P.createElement(Ze,{key:"labelList-implicit",data:e,content:t}):Jt(t)?P.createElement(Ze,Sn({data:e},t,{key:"labelList-implicit"})):null:null}function Im(t,e){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!0;if(!t||!t.children&&r&&!t.label)return null;var n=t.children,i=_e(n,Ze).map(function(o,u){return B.cloneElement(o,{data:e,key:"labelList-".concat(u)})});if(!r)return i;var a=Tm(t.label,e);return[a].concat(Om(i))}Ze.renderCallByParent=Im;function Nr(t){"@babel/helpers - typeof";return Nr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Nr(t)}function da(){return da=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},da.apply(this,arguments)}function ku(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Cu(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?ku(Object(r),!0).forEach(function(n){km(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ku(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function km(t,e,r){return e=Cm(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Cm(t){var e=Mm(t,"string");return Nr(e)=="symbol"?e:e+""}function Mm(t,e){if(Nr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Nr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Dm=function(e,r){var n=be(r-e),i=Math.min(Math.abs(r-e),359.999);return n*i},cn=function(e){var r=e.cx,n=e.cy,i=e.radius,a=e.angle,o=e.sign,u=e.isExternal,c=e.cornerRadius,l=e.cornerIsExternal,s=c*(u?1:-1)+i,f=Math.asin(c/s)/Pn,p=l?a:a+o*f,d=re(r,n,s,p),m=re(r,n,i,p),y=l?a-o*f:a,v=re(r,n,s*Math.cos(f*Pn),y);return{center:d,circleTangency:m,lineTangency:v,theta:f}},us=function(e){var r=e.cx,n=e.cy,i=e.innerRadius,a=e.outerRadius,o=e.startAngle,u=e.endAngle,c=Dm(o,u),l=o+c,s=re(r,n,a,o),f=re(r,n,a,l),p="M ".concat(s.x,",").concat(s.y,`
    A `).concat(a,",").concat(a,`,0,
    `).concat(+(Math.abs(c)>180),",").concat(+(o>l),`,
    `).concat(f.x,",").concat(f.y,`
  `);if(i>0){var d=re(r,n,i,o),m=re(r,n,i,l);p+="L ".concat(m.x,",").concat(m.y,`
            A `).concat(i,",").concat(i,`,0,
            `).concat(+(Math.abs(c)>180),",").concat(+(o<=l),`,
            `).concat(d.x,",").concat(d.y," Z")}else p+="L ".concat(r,",").concat(n," Z");return p},Nm=function(e){var r=e.cx,n=e.cy,i=e.innerRadius,a=e.outerRadius,o=e.cornerRadius,u=e.forceCornerRadius,c=e.cornerIsExternal,l=e.startAngle,s=e.endAngle,f=be(s-l),p=cn({cx:r,cy:n,radius:a,angle:l,sign:f,cornerRadius:o,cornerIsExternal:c}),d=p.circleTangency,m=p.lineTangency,y=p.theta,v=cn({cx:r,cy:n,radius:a,angle:s,sign:-f,cornerRadius:o,cornerIsExternal:c}),b=v.circleTangency,O=v.lineTangency,x=v.theta,w=c?Math.abs(l-s):Math.abs(l-s)-y-x;if(w<0)return u?"M ".concat(m.x,",").concat(m.y,`
        a`).concat(o,",").concat(o,",0,0,1,").concat(o*2,`,0
        a`).concat(o,",").concat(o,",0,0,1,").concat(-o*2,`,0
      `):us({cx:r,cy:n,innerRadius:i,outerRadius:a,startAngle:l,endAngle:s});var h="M ".concat(m.x,",").concat(m.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(d.x,",").concat(d.y,`
    A`).concat(a,",").concat(a,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(b.x,",").concat(b.y,`
    A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(O.x,",").concat(O.y,`
  `);if(i>0){var g=cn({cx:r,cy:n,radius:i,angle:l,sign:f,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),A=g.circleTangency,S=g.lineTangency,j=g.theta,T=cn({cx:r,cy:n,radius:i,angle:s,sign:-f,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),E=T.circleTangency,$=T.lineTangency,C=T.theta,k=c?Math.abs(l-s):Math.abs(l-s)-j-C;if(k<0&&o===0)return"".concat(h,"L").concat(r,",").concat(n,"Z");h+="L".concat($.x,",").concat($.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(E.x,",").concat(E.y,`
      A`).concat(i,",").concat(i,",0,").concat(+(k>180),",").concat(+(f>0),",").concat(A.x,",").concat(A.y,`
      A`).concat(o,",").concat(o,",0,0,").concat(+(f<0),",").concat(S.x,",").concat(S.y,"Z")}else h+="L".concat(r,",").concat(n,"Z");return h},Bm={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},cs=function(e){var r=Cu(Cu({},Bm),e),n=r.cx,i=r.cy,a=r.innerRadius,o=r.outerRadius,u=r.cornerRadius,c=r.forceCornerRadius,l=r.cornerIsExternal,s=r.startAngle,f=r.endAngle,p=r.className;if(o<a||s===f)return null;var d=U("recharts-sector",p),m=o-a,y=Oe(u,m,0,!0),v;return y>0&&Math.abs(s-f)<360?v=Nm({cx:n,cy:i,innerRadius:a,outerRadius:o,cornerRadius:Math.min(y,m/2),forceCornerRadius:c,cornerIsExternal:l,startAngle:s,endAngle:f}):v=us({cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:s,endAngle:f}),P.createElement("path",da({},G(r,!0),{className:d,d:v,role:"img"}))};function Br(t){"@babel/helpers - typeof";return Br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Br(t)}function va(){return va=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},va.apply(this,arguments)}function Mu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Du(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Mu(Object(r),!0).forEach(function(n){Rm(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Mu(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Rm(t,e,r){return e=Lm(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Lm(t){var e=zm(t,"string");return Br(e)=="symbol"?e:e+""}function zm(t,e){if(Br(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Nu={curveBasisClosed:fp,curveBasisOpen:pp,curveBasis:dp,curveBumpX:vp,curveBumpY:hp,curveLinearClosed:yp,curveLinear:fl,curveMonotoneX:mp,curveMonotoneY:gp,curveNatural:bp,curveStep:Op,curveStepAfter:xp,curveStepBefore:wp},ln=function(e){return e.x===+e.x&&e.y===+e.y},sr=function(e){return e.x},fr=function(e){return e.y},Wm=function(e,r){if(X(e))return e;var n="curve".concat(ai(e));return(n==="curveMonotone"||n==="curveBump")&&r?Nu["".concat(n).concat(r==="vertical"?"Y":"X")]:Nu[n]||fl},Fm=function(e){var r=e.type,n=r===void 0?"linear":r,i=e.points,a=i===void 0?[]:i,o=e.baseLine,u=e.layout,c=e.connectNulls,l=c===void 0?!1:c,s=Wm(n,u),f=l?a.filter(function(y){return ln(y)}):a,p;if(Array.isArray(o)){var d=l?o.filter(function(y){return ln(y)}):o,m=f.map(function(y,v){return Du(Du({},y),{},{base:d[v]})});return u==="vertical"?p=rn().y(fr).x1(sr).x0(function(y){return y.base.x}):p=rn().x(sr).y1(fr).y0(function(y){return y.base.y}),p.defined(ln).curve(s),p(m)}return u==="vertical"&&N(o)?p=rn().y(fr).x1(sr).x0(o):N(o)?p=rn().x(sr).y1(fr).y0(o):p=sp().x(sr).y(fr),p.defined(ln).curve(s),p(f)},ha=function(e){var r=e.className,n=e.points,i=e.path,a=e.pathRef;if((!n||!n.length)&&!i)return null;var o=n&&n.length?Fm(e):i;return P.createElement("path",va({},G(e,!1),vn(e),{className:U("recharts-curve",r),d:o,ref:a}))},Km=Object.getOwnPropertyNames,Gm=Object.getOwnPropertySymbols,qm=Object.prototype.hasOwnProperty;function Bu(t,e){return function(n,i,a){return t(n,i,a)&&e(n,i,a)}}function sn(t){return function(r,n,i){if(!r||!n||typeof r!="object"||typeof n!="object")return t(r,n,i);var a=i.cache,o=a.get(r),u=a.get(n);if(o&&u)return o===n&&u===r;a.set(r,n),a.set(n,r);var c=t(r,n,i);return a.delete(r),a.delete(n),c}}function Ru(t){return Km(t).concat(Gm(t))}var ls=Object.hasOwn||function(t,e){return qm.call(t,e)};function tr(t,e){return t||e?t===e:t===e||t!==t&&e!==e}var ss="_owner",Lu=Object.getOwnPropertyDescriptor,zu=Object.keys;function Vm(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function Xm(t,e){return tr(t.getTime(),e.getTime())}function Wu(t,e,r){if(t.size!==e.size)return!1;for(var n={},i=t.entries(),a=0,o,u;(o=i.next())&&!o.done;){for(var c=e.entries(),l=!1,s=0;(u=c.next())&&!u.done;){var f=o.value,p=f[0],d=f[1],m=u.value,y=m[0],v=m[1];!l&&!n[s]&&(l=r.equals(p,y,a,s,t,e,r)&&r.equals(d,v,p,y,t,e,r))&&(n[s]=!0),s++}if(!l)return!1;a++}return!0}function Hm(t,e,r){var n=zu(t),i=n.length;if(zu(e).length!==i)return!1;for(var a;i-- >0;)if(a=n[i],a===ss&&(t.$$typeof||e.$$typeof)&&t.$$typeof!==e.$$typeof||!ls(e,a)||!r.equals(t[a],e[a],a,a,t,e,r))return!1;return!0}function pr(t,e,r){var n=Ru(t),i=n.length;if(Ru(e).length!==i)return!1;for(var a,o,u;i-- >0;)if(a=n[i],a===ss&&(t.$$typeof||e.$$typeof)&&t.$$typeof!==e.$$typeof||!ls(e,a)||!r.equals(t[a],e[a],a,a,t,e,r)||(o=Lu(t,a),u=Lu(e,a),(o||u)&&(!o||!u||o.configurable!==u.configurable||o.enumerable!==u.enumerable||o.writable!==u.writable)))return!1;return!0}function Um(t,e){return tr(t.valueOf(),e.valueOf())}function Ym(t,e){return t.source===e.source&&t.flags===e.flags}function Fu(t,e,r){if(t.size!==e.size)return!1;for(var n={},i=t.values(),a,o;(a=i.next())&&!a.done;){for(var u=e.values(),c=!1,l=0;(o=u.next())&&!o.done;)!c&&!n[l]&&(c=r.equals(a.value,o.value,a.value,o.value,t,e,r))&&(n[l]=!0),l++;if(!c)return!1}return!0}function Zm(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}var Jm="[object Arguments]",Qm="[object Boolean]",eg="[object Date]",tg="[object Map]",rg="[object Number]",ng="[object Object]",ig="[object RegExp]",ag="[object Set]",og="[object String]",ug=Array.isArray,Ku=typeof ArrayBuffer=="function"&&ArrayBuffer.isView?ArrayBuffer.isView:null,Gu=Object.assign,cg=Object.prototype.toString.call.bind(Object.prototype.toString);function lg(t){var e=t.areArraysEqual,r=t.areDatesEqual,n=t.areMapsEqual,i=t.areObjectsEqual,a=t.arePrimitiveWrappersEqual,o=t.areRegExpsEqual,u=t.areSetsEqual,c=t.areTypedArraysEqual;return function(s,f,p){if(s===f)return!0;if(s==null||f==null||typeof s!="object"||typeof f!="object")return s!==s&&f!==f;var d=s.constructor;if(d!==f.constructor)return!1;if(d===Object)return i(s,f,p);if(ug(s))return e(s,f,p);if(Ku!=null&&Ku(s))return c(s,f,p);if(d===Date)return r(s,f,p);if(d===RegExp)return o(s,f,p);if(d===Map)return n(s,f,p);if(d===Set)return u(s,f,p);var m=cg(s);return m===eg?r(s,f,p):m===ig?o(s,f,p):m===tg?n(s,f,p):m===ag?u(s,f,p):m===ng?typeof s.then!="function"&&typeof f.then!="function"&&i(s,f,p):m===Jm?i(s,f,p):m===Qm||m===rg||m===og?a(s,f,p):!1}}function sg(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,i={areArraysEqual:n?pr:Vm,areDatesEqual:Xm,areMapsEqual:n?Bu(Wu,pr):Wu,areObjectsEqual:n?pr:Hm,arePrimitiveWrappersEqual:Um,areRegExpsEqual:Ym,areSetsEqual:n?Bu(Fu,pr):Fu,areTypedArraysEqual:n?pr:Zm};if(r&&(i=Gu({},i,r(i))),e){var a=sn(i.areArraysEqual),o=sn(i.areMapsEqual),u=sn(i.areObjectsEqual),c=sn(i.areSetsEqual);i=Gu({},i,{areArraysEqual:a,areMapsEqual:o,areObjectsEqual:u,areSetsEqual:c})}return i}function fg(t){return function(e,r,n,i,a,o,u){return t(e,r,u)}}function pg(t){var e=t.circular,r=t.comparator,n=t.createState,i=t.equals,a=t.strict;if(n)return function(c,l){var s=n(),f=s.cache,p=f===void 0?e?new WeakMap:void 0:f,d=s.meta;return r(c,l,{cache:p,equals:i,meta:d,strict:a})};if(e)return function(c,l){return r(c,l,{cache:new WeakMap,equals:i,meta:void 0,strict:a})};var o={cache:void 0,equals:i,meta:void 0,strict:a};return function(c,l){return r(c,l,o)}}var dg=Qe();Qe({strict:!0});Qe({circular:!0});Qe({circular:!0,strict:!0});Qe({createInternalComparator:function(){return tr}});Qe({strict:!0,createInternalComparator:function(){return tr}});Qe({circular:!0,createInternalComparator:function(){return tr}});Qe({circular:!0,createInternalComparator:function(){return tr},strict:!0});function Qe(t){t===void 0&&(t={});var e=t.circular,r=e===void 0?!1:e,n=t.createInternalComparator,i=t.createState,a=t.strict,o=a===void 0?!1:a,u=sg(t),c=lg(u),l=n?n(c):fg(c);return pg({circular:r,comparator:c,createState:i,equals:l,strict:o})}function vg(t){typeof requestAnimationFrame<"u"&&requestAnimationFrame(t)}function qu(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,r=-1,n=function i(a){r<0&&(r=a),a-r>e?(t(a),r=-1):vg(i)};requestAnimationFrame(n)}function ya(t){"@babel/helpers - typeof";return ya=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ya(t)}function hg(t){return bg(t)||gg(t)||mg(t)||yg()}function yg(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function mg(t,e){if(t){if(typeof t=="string")return Vu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Vu(t,e)}}function Vu(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function gg(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function bg(t){if(Array.isArray(t))return t}function Og(){var t={},e=function(){return null},r=!1,n=function i(a){if(!r){if(Array.isArray(a)){if(!a.length)return;var o=a,u=hg(o),c=u[0],l=u.slice(1);if(typeof c=="number"){qu(i.bind(null,l),c);return}i(c),qu(i.bind(null,l));return}ya(a)==="object"&&(t=a,e(t)),typeof a=="function"&&a()}};return{stop:function(){r=!0},start:function(a){r=!1,n(a)},subscribe:function(a){return e=a,function(){e=function(){return null}}}}}function Rr(t){"@babel/helpers - typeof";return Rr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Rr(t)}function Xu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Hu(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Xu(Object(r),!0).forEach(function(n){fs(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Xu(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function fs(t,e,r){return e=xg(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function xg(t){var e=wg(t,"string");return Rr(e)==="symbol"?e:String(e)}function wg(t,e){if(Rr(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Rr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Ag=function(e,r){return[Object.keys(e),Object.keys(r)].reduce(function(n,i){return n.filter(function(a){return i.includes(a)})})},Pg=function(e){return e},Sg=function(e){return e.replace(/([A-Z])/g,function(r){return"-".concat(r.toLowerCase())})},gr=function(e,r){return Object.keys(r).reduce(function(n,i){return Hu(Hu({},n),{},fs({},i,e(i,r[i])))},{})},Uu=function(e,r,n){return e.map(function(i){return"".concat(Sg(i)," ").concat(r,"ms ").concat(n)}).join(",")};function jg(t,e){return _g(t)||$g(t,e)||ps(t,e)||Eg()}function Eg(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function $g(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function _g(t){if(Array.isArray(t))return t}function Tg(t){return Cg(t)||kg(t)||ps(t)||Ig()}function Ig(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function ps(t,e){if(t){if(typeof t=="string")return ma(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ma(t,e)}}function kg(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Cg(t){if(Array.isArray(t))return ma(t)}function ma(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var jn=1e-4,ds=function(e,r){return[0,3*e,3*r-6*e,3*e-3*r+1]},vs=function(e,r){return e.map(function(n,i){return n*Math.pow(r,i)}).reduce(function(n,i){return n+i})},Yu=function(e,r){return function(n){var i=ds(e,r);return vs(i,n)}},Mg=function(e,r){return function(n){var i=ds(e,r),a=[].concat(Tg(i.map(function(o,u){return o*u}).slice(1)),[0]);return vs(a,n)}},Zu=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];var i=r[0],a=r[1],o=r[2],u=r[3];if(r.length===1)switch(r[0]){case"linear":i=0,a=0,o=1,u=1;break;case"ease":i=.25,a=.1,o=.25,u=1;break;case"ease-in":i=.42,a=0,o=1,u=1;break;case"ease-out":i=.42,a=0,o=.58,u=1;break;case"ease-in-out":i=0,a=0,o=.58,u=1;break;default:{var c=r[0].split("(");if(c[0]==="cubic-bezier"&&c[1].split(")")[0].split(",").length===4){var l=c[1].split(")")[0].split(",").map(function(v){return parseFloat(v)}),s=jg(l,4);i=s[0],a=s[1],o=s[2],u=s[3]}}}var f=Yu(i,o),p=Yu(a,u),d=Mg(i,o),m=function(b){return b>1?1:b<0?0:b},y=function(b){for(var O=b>1?1:b,x=O,w=0;w<8;++w){var h=f(x)-O,g=d(x);if(Math.abs(h-O)<jn||g<jn)return p(x);x=m(x-h/g)}return p(x)};return y.isStepper=!1,y},Dg=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=e.stiff,n=r===void 0?100:r,i=e.damping,a=i===void 0?8:i,o=e.dt,u=o===void 0?17:o,c=function(s,f,p){var d=-(s-f)*n,m=p*a,y=p+(d-m)*u/1e3,v=p*u/1e3+s;return Math.abs(v-f)<jn&&Math.abs(y)<jn?[f,0]:[v,y]};return c.isStepper=!0,c.dt=u,c},Ng=function(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];var i=r[0];if(typeof i=="string")switch(i){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return Zu(i);case"spring":return Dg();default:if(i.split("(")[0]==="cubic-bezier")return Zu(i)}return typeof i=="function"?i:null};function Lr(t){"@babel/helpers - typeof";return Lr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Lr(t)}function Ju(t){return Lg(t)||Rg(t)||hs(t)||Bg()}function Bg(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Rg(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Lg(t){if(Array.isArray(t))return ba(t)}function Qu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ve(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Qu(Object(r),!0).forEach(function(n){ga(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Qu(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function ga(t,e,r){return e=zg(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function zg(t){var e=Wg(t,"string");return Lr(e)==="symbol"?e:String(e)}function Wg(t,e){if(Lr(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Lr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Fg(t,e){return qg(t)||Gg(t,e)||hs(t,e)||Kg()}function Kg(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function hs(t,e){if(t){if(typeof t=="string")return ba(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ba(t,e)}}function ba(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Gg(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function qg(t){if(Array.isArray(t))return t}var En=function(e,r,n){return e+(r-e)*n},Oa=function(e){var r=e.from,n=e.to;return r!==n},Vg=function t(e,r,n){var i=gr(function(a,o){if(Oa(o)){var u=e(o.from,o.to,o.velocity),c=Fg(u,2),l=c[0],s=c[1];return ve(ve({},o),{},{from:l,velocity:s})}return o},r);return n<1?gr(function(a,o){return Oa(o)?ve(ve({},o),{},{velocity:En(o.velocity,i[a].velocity,n),from:En(o.from,i[a].from,n)}):o},r):t(e,i,n-1)};const Xg=function(t,e,r,n,i){var a=Ag(t,e),o=a.reduce(function(v,b){return ve(ve({},v),{},ga({},b,[t[b],e[b]]))},{}),u=a.reduce(function(v,b){return ve(ve({},v),{},ga({},b,{from:t[b],velocity:0,to:e[b]}))},{}),c=-1,l,s,f=function(){return null},p=function(){return gr(function(b,O){return O.from},u)},d=function(){return!Object.values(u).filter(Oa).length},m=function(b){l||(l=b);var O=b-l,x=O/r.dt;u=Vg(r,u,x),i(ve(ve(ve({},t),e),p())),l=b,d()||(c=requestAnimationFrame(f))},y=function(b){s||(s=b);var O=(b-s)/n,x=gr(function(h,g){return En.apply(void 0,Ju(g).concat([r(O)]))},o);if(i(ve(ve(ve({},t),e),x)),O<1)c=requestAnimationFrame(f);else{var w=gr(function(h,g){return En.apply(void 0,Ju(g).concat([r(1)]))},o);i(ve(ve(ve({},t),e),w))}};return f=r.isStepper?m:y,function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(c)}}};function Bt(t){"@babel/helpers - typeof";return Bt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Bt(t)}var Hg=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function Ug(t,e){if(t==null)return{};var r=Yg(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Yg(t,e){if(t==null)return{};var r={},n=Object.keys(t),i,a;for(a=0;a<n.length;a++)i=n[a],!(e.indexOf(i)>=0)&&(r[i]=t[i]);return r}function ki(t){return eb(t)||Qg(t)||Jg(t)||Zg()}function Zg(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Jg(t,e){if(t){if(typeof t=="string")return xa(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return xa(t,e)}}function Qg(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function eb(t){if(Array.isArray(t))return xa(t)}function xa(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ec(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Ce(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?ec(Object(r),!0).forEach(function(n){vr(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ec(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function vr(t,e,r){return e=ys(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tb(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function tc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ys(n.key),n)}}function rb(t,e,r){return e&&tc(t.prototype,e),r&&tc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function ys(t){var e=nb(t,"string");return Bt(e)==="symbol"?e:String(e)}function nb(t,e){if(Bt(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Bt(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ib(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&wa(t,e)}function wa(t,e){return wa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},wa(t,e)}function ab(t){var e=ob();return function(){var n=$n(t),i;if(e){var a=$n(this).constructor;i=Reflect.construct(n,arguments,a)}else i=n.apply(this,arguments);return Aa(this,i)}}function Aa(t,e){if(e&&(Bt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Pa(t)}function Pa(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ob(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function $n(t){return $n=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},$n(t)}var Ve=function(t){ib(r,t);var e=ab(r);function r(n,i){var a;tb(this,r),a=e.call(this,n,i);var o=a.props,u=o.isActive,c=o.attributeName,l=o.from,s=o.to,f=o.steps,p=o.children,d=o.duration;if(a.handleStyleChange=a.handleStyleChange.bind(Pa(a)),a.changeStyle=a.changeStyle.bind(Pa(a)),!u||d<=0)return a.state={style:{}},typeof p=="function"&&(a.state={style:s}),Aa(a);if(f&&f.length)a.state={style:f[0].style};else if(l){if(typeof p=="function")return a.state={style:l},Aa(a);a.state={style:c?vr({},c,l):l}}else a.state={style:{}};return a}return rb(r,[{key:"componentDidMount",value:function(){var i=this.props,a=i.isActive,o=i.canBegin;this.mounted=!0,!(!a||!o)&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(i){var a=this.props,o=a.isActive,u=a.canBegin,c=a.attributeName,l=a.shouldReAnimate,s=a.to,f=a.from,p=this.state.style;if(u){if(!o){var d={style:c?vr({},c,s):s};this.state&&p&&(c&&p[c]!==s||!c&&p!==s)&&this.setState(d);return}if(!(dg(i.to,s)&&i.canBegin&&i.isActive)){var m=!i.canBegin||!i.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var y=m||l?f:i.to;if(this.state&&p){var v={style:c?vr({},c,y):y};(c&&p[c]!==y||!c&&p!==y)&&this.setState(v)}this.runAnimation(Ce(Ce({},this.props),{},{from:y,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var i=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),i&&i()}},{key:"handleStyleChange",value:function(i){this.changeStyle(i)}},{key:"changeStyle",value:function(i){this.mounted&&this.setState({style:i})}},{key:"runJSAnimation",value:function(i){var a=this,o=i.from,u=i.to,c=i.duration,l=i.easing,s=i.begin,f=i.onAnimationEnd,p=i.onAnimationStart,d=Xg(o,u,Ng(l),c,this.changeStyle),m=function(){a.stopJSAnimation=d()};this.manager.start([p,s,m,c,f])}},{key:"runStepAnimation",value:function(i){var a=this,o=i.steps,u=i.begin,c=i.onAnimationStart,l=o[0],s=l.style,f=l.duration,p=f===void 0?0:f,d=function(y,v,b){if(b===0)return y;var O=v.duration,x=v.easing,w=x===void 0?"ease":x,h=v.style,g=v.properties,A=v.onAnimationEnd,S=b>0?o[b-1]:v,j=g||Object.keys(h);if(typeof w=="function"||w==="spring")return[].concat(ki(y),[a.runJSAnimation.bind(a,{from:S.style,to:h,duration:O,easing:w}),O]);var T=Uu(j,O,w),E=Ce(Ce(Ce({},S.style),h),{},{transition:T});return[].concat(ki(y),[E,O,A]).filter(Pg)};return this.manager.start([c].concat(ki(o.reduce(d,[s,Math.max(p,u)])),[i.onAnimationEnd]))}},{key:"runAnimation",value:function(i){this.manager||(this.manager=Og());var a=i.begin,o=i.duration,u=i.attributeName,c=i.to,l=i.easing,s=i.onAnimationStart,f=i.onAnimationEnd,p=i.steps,d=i.children,m=this.manager;if(this.unSubscribe=m.subscribe(this.handleStyleChange),typeof l=="function"||typeof d=="function"||l==="spring"){this.runJSAnimation(i);return}if(p.length>1){this.runStepAnimation(i);return}var y=u?vr({},u,c):c,v=Uu(Object.keys(y),o,l);m.start([s,a,Ce(Ce({},y),{},{transition:v}),o,f])}},{key:"render",value:function(){var i=this.props,a=i.children;i.begin;var o=i.duration;i.attributeName,i.easing;var u=i.isActive;i.steps,i.from,i.to,i.canBegin,i.onAnimationEnd,i.shouldReAnimate,i.onAnimationReStart;var c=Ug(i,Hg),l=B.Children.count(a),s=this.state.style;if(typeof a=="function")return a(s);if(!u||l===0||o<=0)return a;var f=function(d){var m=d.props,y=m.style,v=y===void 0?{}:y,b=m.className,O=B.cloneElement(d,Ce(Ce({},c),{},{style:Ce(Ce({},v),s),className:b}));return O};return l===1?f(B.Children.only(a)):P.createElement("div",null,B.Children.map(a,function(p){return f(p)}))}}]),r}(B.PureComponent);Ve.displayName="Animate";Ve.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}};Ve.propTypes={from:H.oneOfType([H.object,H.string]),to:H.oneOfType([H.object,H.string]),attributeName:H.string,duration:H.number,begin:H.number,easing:H.oneOfType([H.string,H.func]),steps:H.arrayOf(H.shape({duration:H.number.isRequired,style:H.object.isRequired,easing:H.oneOfType([H.oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),H.func]),properties:H.arrayOf("string"),onAnimationEnd:H.func})),children:H.oneOfType([H.node,H.func]),isActive:H.bool,canBegin:H.bool,onAnimationEnd:H.func,shouldReAnimate:H.bool,onAnimationStart:H.func,onAnimationReStart:H.func};H.object,H.object,H.object,H.element;H.object,H.object,H.object,H.oneOfType([H.array,H.element]),H.any;function zr(t){"@babel/helpers - typeof";return zr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zr(t)}function _n(){return _n=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},_n.apply(this,arguments)}function ub(t,e){return fb(t)||sb(t,e)||lb(t,e)||cb()}function cb(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function lb(t,e){if(t){if(typeof t=="string")return rc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rc(t,e)}}function rc(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function sb(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function fb(t){if(Array.isArray(t))return t}function nc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ic(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?nc(Object(r),!0).forEach(function(n){pb(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function pb(t,e,r){return e=db(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function db(t){var e=vb(t,"string");return zr(e)=="symbol"?e:e+""}function vb(t,e){if(zr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(zr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var ac=function(e,r,n,i,a){var o=Math.min(Math.abs(n)/2,Math.abs(i)/2),u=i>=0?1:-1,c=n>=0?1:-1,l=i>=0&&n>=0||i<0&&n<0?1:0,s;if(o>0&&a instanceof Array){for(var f=[0,0,0,0],p=0,d=4;p<d;p++)f[p]=a[p]>o?o:a[p];s="M".concat(e,",").concat(r+u*f[0]),f[0]>0&&(s+="A ".concat(f[0],",").concat(f[0],",0,0,").concat(l,",").concat(e+c*f[0],",").concat(r)),s+="L ".concat(e+n-c*f[1],",").concat(r),f[1]>0&&(s+="A ".concat(f[1],",").concat(f[1],",0,0,").concat(l,`,
        `).concat(e+n,",").concat(r+u*f[1])),s+="L ".concat(e+n,",").concat(r+i-u*f[2]),f[2]>0&&(s+="A ".concat(f[2],",").concat(f[2],",0,0,").concat(l,`,
        `).concat(e+n-c*f[2],",").concat(r+i)),s+="L ".concat(e+c*f[3],",").concat(r+i),f[3]>0&&(s+="A ".concat(f[3],",").concat(f[3],",0,0,").concat(l,`,
        `).concat(e,",").concat(r+i-u*f[3])),s+="Z"}else if(o>0&&a===+a&&a>0){var m=Math.min(o,a);s="M ".concat(e,",").concat(r+u*m,`
            A `).concat(m,",").concat(m,",0,0,").concat(l,",").concat(e+c*m,",").concat(r,`
            L `).concat(e+n-c*m,",").concat(r,`
            A `).concat(m,",").concat(m,",0,0,").concat(l,",").concat(e+n,",").concat(r+u*m,`
            L `).concat(e+n,",").concat(r+i-u*m,`
            A `).concat(m,",").concat(m,",0,0,").concat(l,",").concat(e+n-c*m,",").concat(r+i,`
            L `).concat(e+c*m,",").concat(r+i,`
            A `).concat(m,",").concat(m,",0,0,").concat(l,",").concat(e,",").concat(r+i-u*m," Z")}else s="M ".concat(e,",").concat(r," h ").concat(n," v ").concat(i," h ").concat(-n," Z");return s},hb=function(e,r){if(!e||!r)return!1;var n=e.x,i=e.y,a=r.x,o=r.y,u=r.width,c=r.height;if(Math.abs(u)>0&&Math.abs(c)>0){var l=Math.min(a,a+u),s=Math.max(a,a+u),f=Math.min(o,o+c),p=Math.max(o,o+c);return n>=l&&n<=s&&i>=f&&i<=p}return!1},yb={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},so=function(e){var r=ic(ic({},yb),e),n=B.useRef(),i=B.useState(-1),a=ub(i,2),o=a[0],u=a[1];B.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var w=n.current.getTotalLength();w&&u(w)}catch{}},[]);var c=r.x,l=r.y,s=r.width,f=r.height,p=r.radius,d=r.className,m=r.animationEasing,y=r.animationDuration,v=r.animationBegin,b=r.isAnimationActive,O=r.isUpdateAnimationActive;if(c!==+c||l!==+l||s!==+s||f!==+f||s===0||f===0)return null;var x=U("recharts-rectangle",d);return O?P.createElement(Ve,{canBegin:o>0,from:{width:s,height:f,x:c,y:l},to:{width:s,height:f,x:c,y:l},duration:y,animationEasing:m,isActive:O},function(w){var h=w.width,g=w.height,A=w.x,S=w.y;return P.createElement(Ve,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:y,isActive:b,easing:m},P.createElement("path",_n({},G(r,!0),{className:x,d:ac(A,S,h,g,p),ref:n})))}):P.createElement("path",_n({},G(r,!0),{className:x,d:ac(c,l,s,f,p)}))},mb=["points","className","baseLinePoints","connectNulls"];function St(){return St=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},St.apply(this,arguments)}function gb(t,e){if(t==null)return{};var r=bb(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function bb(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function oc(t){return Ab(t)||wb(t)||xb(t)||Ob()}function Ob(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function xb(t,e){if(t){if(typeof t=="string")return Sa(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Sa(t,e)}}function wb(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Ab(t){if(Array.isArray(t))return Sa(t)}function Sa(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var uc=function(e){return e&&e.x===+e.x&&e.y===+e.y},Pb=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=[[]];return e.forEach(function(n){uc(n)?r[r.length-1].push(n):r[r.length-1].length>0&&r.push([])}),uc(e[0])&&r[r.length-1].push(e[0]),r[r.length-1].length<=0&&(r=r.slice(0,-1)),r},br=function(e,r){var n=Pb(e);r&&(n=[n.reduce(function(a,o){return[].concat(oc(a),oc(o))},[])]);var i=n.map(function(a){return a.reduce(function(o,u,c){return"".concat(o).concat(c===0?"M":"L").concat(u.x,",").concat(u.y)},"")}).join("");return n.length===1?"".concat(i,"Z"):i},Sb=function(e,r,n){var i=br(e,n);return"".concat(i.slice(-1)==="Z"?i.slice(0,-1):i,"L").concat(br(r.reverse(),n).slice(1))},jb=function(e){var r=e.points,n=e.className,i=e.baseLinePoints,a=e.connectNulls,o=gb(e,mb);if(!r||!r.length)return null;var u=U("recharts-polygon",n);if(i&&i.length){var c=o.stroke&&o.stroke!=="none",l=Sb(r,i,a);return P.createElement("g",{className:u},P.createElement("path",St({},G(o,!0),{fill:l.slice(-1)==="Z"?o.fill:"none",stroke:"none",d:l})),c?P.createElement("path",St({},G(o,!0),{fill:"none",d:br(r,a)})):null,c?P.createElement("path",St({},G(o,!0),{fill:"none",d:br(i,a)})):null)}var s=br(r,a);return P.createElement("path",St({},G(o,!0),{fill:s.slice(-1)==="Z"?o.fill:"none",className:u,d:s}))};function ja(){return ja=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ja.apply(this,arguments)}var fo=function(e){var r=e.cx,n=e.cy,i=e.r,a=e.className,o=U("recharts-dot",a);return r===+r&&n===+n&&i===+i?P.createElement("circle",ja({},G(e,!1),vn(e),{className:o,cx:r,cy:n,r:i})):null};function Wr(t){"@babel/helpers - typeof";return Wr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Wr(t)}var Eb=["x","y","top","left","width","height","className"];function Ea(){return Ea=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ea.apply(this,arguments)}function cc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function $b(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?cc(Object(r),!0).forEach(function(n){_b(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):cc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function _b(t,e,r){return e=Tb(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Tb(t){var e=Ib(t,"string");return Wr(e)=="symbol"?e:e+""}function Ib(t,e){if(Wr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Wr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function kb(t,e){if(t==null)return{};var r=Cb(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Cb(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}var Mb=function(e,r,n,i,a,o){return"M".concat(e,",").concat(a,"v").concat(i,"M").concat(o,",").concat(r,"h").concat(n)},Db=function(e){var r=e.x,n=r===void 0?0:r,i=e.y,a=i===void 0?0:i,o=e.top,u=o===void 0?0:o,c=e.left,l=c===void 0?0:c,s=e.width,f=s===void 0?0:s,p=e.height,d=p===void 0?0:p,m=e.className,y=kb(e,Eb),v=$b({x:n,y:a,top:u,left:l,width:f,height:d},y);return!N(n)||!N(a)||!N(f)||!N(d)||!N(u)||!N(l)?null:P.createElement("path",Ea({},G(v,!0),{className:U("recharts-cross",m),d:Mb(n,a,f,d,u,l)}))},Nb=ui,Bb=Tl,Rb=Je;function Lb(t,e){return t&&t.length?Nb(t,Rb(e),Bb):void 0}var zb=Lb;const Wb=xe(zb);var Fb=ui,Kb=Je,Gb=Il;function qb(t,e){return t&&t.length?Fb(t,Kb(e),Gb):void 0}var Vb=qb;const Xb=xe(Vb);var Hb=["cx","cy","angle","ticks","axisLine"],Ub=["ticks","tick","angle","tickFormatter","stroke"];function Rt(t){"@babel/helpers - typeof";return Rt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Rt(t)}function Or(){return Or=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Or.apply(this,arguments)}function lc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ot(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?lc(Object(r),!0).forEach(function(n){di(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function sc(t,e){if(t==null)return{};var r=Yb(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Yb(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Zb(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function fc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,gs(n.key),n)}}function Jb(t,e,r){return e&&fc(t.prototype,e),r&&fc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Qb(t,e,r){return e=Tn(e),eO(t,ms()?Reflect.construct(e,r||[],Tn(t).constructor):e.apply(t,r))}function eO(t,e){if(e&&(Rt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return tO(t)}function tO(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ms(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ms=function(){return!!t})()}function Tn(t){return Tn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Tn(t)}function rO(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&$a(t,e)}function $a(t,e){return $a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},$a(t,e)}function di(t,e,r){return e=gs(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function gs(t){var e=nO(t,"string");return Rt(e)=="symbol"?e:e+""}function nO(t,e){if(Rt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Rt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var vi=function(t){function e(){return Zb(this,e),Qb(this,e,arguments)}return rO(e,t),Jb(e,[{key:"getTickValueCoord",value:function(n){var i=n.coordinate,a=this.props,o=a.angle,u=a.cx,c=a.cy;return re(u,c,i,o)}},{key:"getTickTextAnchor",value:function(){var n=this.props.orientation,i;switch(n){case"left":i="end";break;case"right":i="start";break;default:i="middle";break}return i}},{key:"getViewBox",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=Wb(u,function(s){return s.coordinate||0}),l=Xb(u,function(s){return s.coordinate||0});return{cx:i,cy:a,startAngle:o,endAngle:o,innerRadius:l.coordinate||0,outerRadius:c.coordinate||0}}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.angle,u=n.ticks,c=n.axisLine,l=sc(n,Hb),s=u.reduce(function(m,y){return[Math.min(m[0],y.coordinate),Math.max(m[1],y.coordinate)]},[1/0,-1/0]),f=re(i,a,s[0],o),p=re(i,a,s[1],o),d=ot(ot(ot({},G(l,!1)),{},{fill:"none"},G(c,!1)),{},{x1:f.x,y1:f.y,x2:p.x,y2:p.y});return P.createElement("line",Or({className:"recharts-polar-radius-axis-line"},d))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.angle,c=i.tickFormatter,l=i.stroke,s=sc(i,Ub),f=this.getTickTextAnchor(),p=G(s,!1),d=G(o,!1),m=a.map(function(y,v){var b=n.getTickValueCoord(y),O=ot(ot(ot(ot({textAnchor:f,transform:"rotate(".concat(90-u,", ").concat(b.x,", ").concat(b.y,")")},p),{},{stroke:"none",fill:l},d),{},{index:v},b),{},{payload:y});return P.createElement(Z,Or({className:U("recharts-polar-radius-axis-tick",as(o)),key:"tick-".concat(y.coordinate)},vt(n.props,y,v)),e.renderTickItem(o,O,c?c(y.value,v):y.value))});return P.createElement(Z,{className:"recharts-polar-radius-axis-ticks"},m)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.axisLine,o=n.tick;return!i||!i.length?null:P.createElement(Z,{className:U("recharts-polar-radius-axis",this.props.className)},a&&this.renderAxisLine(),o&&this.renderTicks(),de.renderCallByParent(this.props,this.getViewBox()))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return P.isValidElement(n)?o=P.cloneElement(n,i):X(n)?o=n(i):o=P.createElement(ht,Or({},i,{className:"recharts-polar-radius-axis-tick-value"}),a),o}}])}(B.PureComponent);di(vi,"displayName","PolarRadiusAxis");di(vi,"axisType","radiusAxis");di(vi,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});function Lt(t){"@babel/helpers - typeof";return Lt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Lt(t)}function lt(){return lt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},lt.apply(this,arguments)}function pc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ut(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?pc(Object(r),!0).forEach(function(n){hi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function iO(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function dc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Os(n.key),n)}}function aO(t,e,r){return e&&dc(t.prototype,e),r&&dc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function oO(t,e,r){return e=In(e),uO(t,bs()?Reflect.construct(e,r||[],In(t).constructor):e.apply(t,r))}function uO(t,e){if(e&&(Lt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return cO(t)}function cO(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function bs(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(bs=function(){return!!t})()}function In(t){return In=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},In(t)}function lO(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_a(t,e)}function _a(t,e){return _a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},_a(t,e)}function hi(t,e,r){return e=Os(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Os(t){var e=sO(t,"string");return Lt(e)=="symbol"?e:e+""}function sO(t,e){if(Lt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Lt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var fO=Math.PI/180,vc=1e-5,yi=function(t){function e(){return iO(this,e),oO(this,e,arguments)}return lO(e,t),aO(e,[{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.cx,o=i.cy,u=i.radius,c=i.orientation,l=i.tickSize,s=l||8,f=re(a,o,u,n.coordinate),p=re(a,o,u+(c==="inner"?-1:1)*s,n.coordinate);return{x1:f.x,y1:f.y,x2:p.x,y2:p.y}}},{key:"getTickTextAnchor",value:function(n){var i=this.props.orientation,a=Math.cos(-n.coordinate*fO),o;return a>vc?o=i==="outer"?"start":"end":a<-vc?o=i==="outer"?"end":"start":o="middle",o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.cx,a=n.cy,o=n.radius,u=n.axisLine,c=n.axisLineType,l=ut(ut({},G(this.props,!1)),{},{fill:"none"},G(u,!1));if(c==="circle")return P.createElement(fo,lt({className:"recharts-polar-angle-axis-line"},l,{cx:i,cy:a,r:o}));var s=this.props.ticks,f=s.map(function(p){return re(i,a,o,p.coordinate)});return P.createElement(jb,lt({className:"recharts-polar-angle-axis-line"},l,{points:f}))}},{key:"renderTicks",value:function(){var n=this,i=this.props,a=i.ticks,o=i.tick,u=i.tickLine,c=i.tickFormatter,l=i.stroke,s=G(this.props,!1),f=G(o,!1),p=ut(ut({},s),{},{fill:"none"},G(u,!1)),d=a.map(function(m,y){var v=n.getTickLineCoord(m),b=n.getTickTextAnchor(m),O=ut(ut(ut({textAnchor:b},s),{},{stroke:"none",fill:l},f),{},{index:y,payload:m,x:v.x2,y:v.y2});return P.createElement(Z,lt({className:U("recharts-polar-angle-axis-tick",as(o)),key:"tick-".concat(m.coordinate)},vt(n.props,m,y)),u&&P.createElement("line",lt({className:"recharts-polar-angle-axis-tick-line"},p,v)),o&&e.renderTickItem(o,O,c?c(m.value,y):m.value))});return P.createElement(Z,{className:"recharts-polar-angle-axis-ticks"},d)}},{key:"render",value:function(){var n=this.props,i=n.ticks,a=n.radius,o=n.axisLine;return a<=0||!i||!i.length?null:P.createElement(Z,{className:U("recharts-polar-angle-axis",this.props.className)},o&&this.renderAxisLine(),this.renderTicks())}}],[{key:"renderTickItem",value:function(n,i,a){var o;return P.isValidElement(n)?o=P.cloneElement(n,i):X(n)?o=n(i):o=P.createElement(ht,lt({},i,{className:"recharts-polar-angle-axis-tick-value"}),a),o}}])}(B.PureComponent);hi(yi,"displayName","PolarAngleAxis");hi(yi,"axisType","angleAxis");hi(yi,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var pO=df,dO=vf,vO="[object Boolean]";function hO(t){return t===!0||t===!1||dO(t)&&pO(t)==vO}var yO=hO;const mO=xe(yO);function Fr(t){"@babel/helpers - typeof";return Fr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fr(t)}function kn(){return kn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},kn.apply(this,arguments)}function gO(t,e){return wO(t)||xO(t,e)||OO(t,e)||bO()}function bO(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function OO(t,e){if(t){if(typeof t=="string")return hc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hc(t,e)}}function hc(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function xO(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function wO(t){if(Array.isArray(t))return t}function yc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function mc(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?yc(Object(r),!0).forEach(function(n){AO(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function AO(t,e,r){return e=PO(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function PO(t){var e=SO(t,"string");return Fr(e)=="symbol"?e:e+""}function SO(t,e){if(Fr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Fr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var gc=function(e,r,n,i,a){var o=n-i,u;return u="M ".concat(e,",").concat(r),u+="L ".concat(e+n,",").concat(r),u+="L ".concat(e+n-o/2,",").concat(r+a),u+="L ".concat(e+n-o/2-i,",").concat(r+a),u+="L ".concat(e,",").concat(r," Z"),u},jO={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},EO=function(e){var r=mc(mc({},jO),e),n=B.useRef(),i=B.useState(-1),a=gO(i,2),o=a[0],u=a[1];B.useEffect(function(){if(n.current&&n.current.getTotalLength)try{var x=n.current.getTotalLength();x&&u(x)}catch{}},[]);var c=r.x,l=r.y,s=r.upperWidth,f=r.lowerWidth,p=r.height,d=r.className,m=r.animationEasing,y=r.animationDuration,v=r.animationBegin,b=r.isUpdateAnimationActive;if(c!==+c||l!==+l||s!==+s||f!==+f||p!==+p||s===0&&f===0||p===0)return null;var O=U("recharts-trapezoid",d);return b?P.createElement(Ve,{canBegin:o>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:l},to:{upperWidth:s,lowerWidth:f,height:p,x:c,y:l},duration:y,animationEasing:m,isActive:b},function(x){var w=x.upperWidth,h=x.lowerWidth,g=x.height,A=x.x,S=x.y;return P.createElement(Ve,{canBegin:o>0,from:"0px ".concat(o===-1?1:o,"px"),to:"".concat(o,"px 0px"),attributeName:"strokeDasharray",begin:v,duration:y,easing:m},P.createElement("path",kn({},G(r,!0),{className:O,d:gc(A,S,w,h,g),ref:n})))}):P.createElement("g",null,P.createElement("path",kn({},G(r,!0),{className:O,d:gc(c,l,s,f,p)})))},$O=["option","shapeType","propTransformer","activeClassName","isActive"];function Kr(t){"@babel/helpers - typeof";return Kr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kr(t)}function _O(t,e){if(t==null)return{};var r=TO(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function TO(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function bc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Cn(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?bc(Object(r),!0).forEach(function(n){IO(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):bc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function IO(t,e,r){return e=kO(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function kO(t){var e=CO(t,"string");return Kr(e)=="symbol"?e:e+""}function CO(t,e){if(Kr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Kr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function MO(t,e){return Cn(Cn({},e),t)}function DO(t,e){return t==="symbols"}function Oc(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return P.createElement(so,r);case"trapezoid":return P.createElement(EO,r);case"sector":return P.createElement(cs,r);case"symbols":if(DO(e))return P.createElement(no,r);break;default:return null}}function NO(t){return B.isValidElement(t)?t.props:t}function xs(t){var e=t.option,r=t.shapeType,n=t.propTransformer,i=n===void 0?MO:n,a=t.activeClassName,o=a===void 0?"recharts-active-shape":a,u=t.isActive,c=_O(t,$O),l;if(B.isValidElement(e))l=B.cloneElement(e,Cn(Cn({},c),NO(e)));else if(X(e))l=e(c);else if(Ap(e)&&!mO(e)){var s=i(e,c);l=P.createElement(Oc,{shapeType:r,elementProps:s})}else{var f=c;l=P.createElement(Oc,{shapeType:r,elementProps:f})}return u?P.createElement(Z,{className:o},l):l}function mi(t,e){return e!=null&&"trapezoids"in t.props}function gi(t,e){return e!=null&&"sectors"in t.props}function Gr(t,e){return e!=null&&"points"in t.props}function BO(t,e){var r,n,i=t.x===(e==null||(r=e.labelViewBox)===null||r===void 0?void 0:r.x)||t.x===e.x,a=t.y===(e==null||(n=e.labelViewBox)===null||n===void 0?void 0:n.y)||t.y===e.y;return i&&a}function RO(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function LO(t,e){var r=t.x===e.x,n=t.y===e.y,i=t.z===e.z;return r&&n&&i}function zO(t,e){var r;return mi(t,e)?r=BO:gi(t,e)?r=RO:Gr(t,e)&&(r=LO),r}function WO(t,e){var r;return mi(t,e)?r="trapezoids":gi(t,e)?r="sectors":Gr(t,e)&&(r="points"),r}function FO(t,e){if(mi(t,e)){var r;return(r=e.tooltipPayload)===null||r===void 0||(r=r[0])===null||r===void 0||(r=r.payload)===null||r===void 0?void 0:r.payload}if(gi(t,e)){var n;return(n=e.tooltipPayload)===null||n===void 0||(n=n[0])===null||n===void 0||(n=n.payload)===null||n===void 0?void 0:n.payload}return Gr(t,e)?e.payload:{}}function KO(t){var e=t.activeTooltipItem,r=t.graphicalItem,n=t.itemData,i=WO(r,e),a=FO(r,e),o=n.filter(function(c,l){var s=Hn(a,c),f=r.props[i].filter(function(m){var y=zO(r,e);return y(m,e)}),p=r.props[i].indexOf(f[f.length-1]),d=l===p;return s&&d}),u=n.indexOf(o[o.length-1]);return u}var dn;function zt(t){"@babel/helpers - typeof";return zt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zt(t)}function jt(){return jt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},jt.apply(this,arguments)}function xc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function te(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?xc(Object(r),!0).forEach(function(n){$e(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function GO(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function wc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,As(n.key),n)}}function qO(t,e,r){return e&&wc(t.prototype,e),r&&wc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function VO(t,e,r){return e=Mn(e),XO(t,ws()?Reflect.construct(e,r||[],Mn(t).constructor):e.apply(t,r))}function XO(t,e){if(e&&(zt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return HO(t)}function HO(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ws(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ws=function(){return!!t})()}function Mn(t){return Mn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Mn(t)}function UO(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ta(t,e)}function Ta(t,e){return Ta=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ta(t,e)}function $e(t,e,r){return e=As(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function As(t){var e=YO(t,"string");return zt(e)=="symbol"?e:e+""}function YO(t,e){if(zt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(zt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var et=function(t){function e(r){var n;return GO(this,e),n=VO(this,e,[r]),$e(n,"pieRef",null),$e(n,"sectorRefs",[]),$e(n,"id",Qr("recharts-pie-")),$e(n,"handleAnimationEnd",function(){var i=n.props.onAnimationEnd;n.setState({isAnimationFinished:!0}),X(i)&&i()}),$e(n,"handleAnimationStart",function(){var i=n.props.onAnimationStart;n.setState({isAnimationFinished:!1}),X(i)&&i()}),n.state={isAnimationFinished:!r.isAnimationActive,prevIsAnimationActive:r.isAnimationActive,prevAnimationId:r.animationId,sectorToFocus:0},n}return UO(e,t),qO(e,[{key:"isActiveIndex",value:function(n){var i=this.props.activeIndex;return Array.isArray(i)?i.indexOf(n)!==-1:n===i}},{key:"hasActiveIndex",value:function(){var n=this.props.activeIndex;return Array.isArray(n)?n.length!==0:n||n===0}},{key:"renderLabels",value:function(n){var i=this.props.isAnimationActive;if(i&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.label,u=a.labelLine,c=a.dataKey,l=a.valueKey,s=G(this.props,!1),f=G(o,!1),p=G(u,!1),d=o&&o.offsetRadius||20,m=n.map(function(y,v){var b=(y.startAngle+y.endAngle)/2,O=re(y.cx,y.cy,y.outerRadius+d,b),x=te(te(te(te({},s),y),{},{stroke:"none"},f),{},{index:v,textAnchor:e.getTextAnchor(O.x,y.cx)},O),w=te(te(te(te({},s),y),{},{fill:"none",stroke:y.fill},p),{},{index:v,points:[re(y.cx,y.cy,y.outerRadius,b),O]}),h=c;return Y(c)&&Y(l)?h="value":Y(c)&&(h=l),P.createElement(Z,{key:"label-".concat(y.startAngle,"-").concat(y.endAngle,"-").concat(y.midAngle,"-").concat(v)},u&&e.renderLabelLineItem(u,w,"line"),e.renderLabelItem(o,x,me(y,h)))});return P.createElement(Z,{className:"recharts-pie-labels"},m)}},{key:"renderSectorsStatically",value:function(n){var i=this,a=this.props,o=a.activeShape,u=a.blendStroke,c=a.inactiveShape;return n.map(function(l,s){if((l==null?void 0:l.startAngle)===0&&(l==null?void 0:l.endAngle)===0&&n.length!==1)return null;var f=i.isActiveIndex(s),p=c&&i.hasActiveIndex()?c:null,d=f?o:p,m=te(te({},l),{},{stroke:u?l.fill:l.stroke,tabIndex:-1});return P.createElement(Z,jt({ref:function(v){v&&!i.sectorRefs.includes(v)&&i.sectorRefs.push(v)},tabIndex:-1,className:"recharts-pie-sector"},vt(i.props,l,s),{key:"sector-".concat(l==null?void 0:l.startAngle,"-").concat(l==null?void 0:l.endAngle,"-").concat(l.midAngle,"-").concat(s)}),P.createElement(xs,jt({option:d,isActive:f,shapeType:"sector"},m)))})}},{key:"renderSectorsWithAnimation",value:function(){var n=this,i=this.props,a=i.sectors,o=i.isAnimationActive,u=i.animationBegin,c=i.animationDuration,l=i.animationEasing,s=i.animationId,f=this.state,p=f.prevSectors,d=f.prevIsAnimationActive;return P.createElement(Ve,{begin:u,duration:c,isActive:o,easing:l,from:{t:0},to:{t:1},key:"pie-".concat(s,"-").concat(d),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(m){var y=m.t,v=[],b=a&&a[0],O=b.startAngle;return a.forEach(function(x,w){var h=p&&p[w],g=w>0?Se(x,"paddingAngle",0):0;if(h){var A=Xe(h.endAngle-h.startAngle,x.endAngle-x.startAngle),S=te(te({},x),{},{startAngle:O+g,endAngle:O+A(y)+g});v.push(S),O=S.endAngle}else{var j=x.endAngle,T=x.startAngle,E=Xe(0,j-T),$=E(y),C=te(te({},x),{},{startAngle:O+g,endAngle:O+$+g});v.push(C),O=C.endAngle}}),P.createElement(Z,null,n.renderSectorsStatically(v))})}},{key:"attachKeyboardHandlers",value:function(n){var i=this;n.onkeydown=function(a){if(!a.altKey)switch(a.key){case"ArrowLeft":{var o=++i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[o].focus(),i.setState({sectorToFocus:o});break}case"ArrowRight":{var u=--i.state.sectorToFocus<0?i.sectorRefs.length-1:i.state.sectorToFocus%i.sectorRefs.length;i.sectorRefs[u].focus(),i.setState({sectorToFocus:u});break}case"Escape":{i.sectorRefs[i.state.sectorToFocus].blur(),i.setState({sectorToFocus:0});break}}}}},{key:"renderSectors",value:function(){var n=this.props,i=n.sectors,a=n.isAnimationActive,o=this.state.prevSectors;return a&&i&&i.length&&(!o||!Hn(o,i))?this.renderSectorsWithAnimation():this.renderSectorsStatically(i)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var n=this,i=this.props,a=i.hide,o=i.sectors,u=i.className,c=i.label,l=i.cx,s=i.cy,f=i.innerRadius,p=i.outerRadius,d=i.isAnimationActive,m=this.state.isAnimationFinished;if(a||!o||!o.length||!N(l)||!N(s)||!N(f)||!N(p))return null;var y=U("recharts-pie",u);return P.createElement(Z,{tabIndex:this.props.rootTabIndex,className:y,ref:function(b){n.pieRef=b}},this.renderSectors(),c&&this.renderLabels(o),de.renderCallByParent(this.props,null,!1),(!d||m)&&Ze.renderCallByParent(this.props,o,!1))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return i.prevIsAnimationActive!==n.isAnimationActive?{prevIsAnimationActive:n.isAnimationActive,prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:[],isAnimationFinished:!0}:n.isAnimationActive&&n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curSectors:n.sectors,prevSectors:i.curSectors,isAnimationFinished:!0}:n.sectors!==i.curSectors?{curSectors:n.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(n,i){return n>i?"start":n<i?"end":"middle"}},{key:"renderLabelLineItem",value:function(n,i,a){if(P.isValidElement(n))return P.cloneElement(n,i);if(X(n))return n(i);var o=U("recharts-pie-label-line",typeof n!="boolean"?n.className:"");return P.createElement(ha,jt({},i,{key:a,type:"linear",className:o}))}},{key:"renderLabelItem",value:function(n,i,a){if(P.isValidElement(n))return P.cloneElement(n,i);var o=a;if(X(n)&&(o=n(i),P.isValidElement(o)))return o;var u=U("recharts-pie-label-text",typeof n!="boolean"&&!X(n)?n.className:"");return P.createElement(ht,jt({},i,{alignmentBaseline:"middle",className:u}),o)}}])}(B.PureComponent);dn=et;$e(et,"displayName","Pie");$e(et,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!Ge.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0});$e(et,"parseDeltaAngle",function(t,e){var r=be(e-t),n=Math.min(Math.abs(e-t),360);return r*n});$e(et,"getRealPieData",function(t){var e=t.data,r=t.children,n=G(t,!1),i=_e(r,oo);return e&&e.length?e.map(function(a,o){return te(te(te({payload:a},n),a),i&&i[o]&&i[o].props)}):i&&i.length?i.map(function(a){return te(te({},n),a.props)}):[]});$e(et,"parseCoordinateOfPie",function(t,e){var r=e.top,n=e.left,i=e.width,a=e.height,o=is(i,a),u=n+Oe(t.cx,i,i/2),c=r+Oe(t.cy,a,a/2),l=Oe(t.innerRadius,o,0),s=Oe(t.outerRadius,o,o*.8),f=t.maxRadius||Math.sqrt(i*i+a*a)/2;return{cx:u,cy:c,innerRadius:l,outerRadius:s,maxRadius:f}});$e(et,"getComposedData",function(t){var e=t.item,r=t.offset,n=e.type.defaultProps!==void 0?te(te({},e.type.defaultProps),e.props):e.props,i=dn.getRealPieData(n);if(!i||!i.length)return null;var a=n.cornerRadius,o=n.startAngle,u=n.endAngle,c=n.paddingAngle,l=n.dataKey,s=n.nameKey,f=n.valueKey,p=n.tooltipType,d=Math.abs(n.minAngle),m=dn.parseCoordinateOfPie(n,r),y=dn.parseDeltaAngle(o,u),v=Math.abs(y),b=l;Y(l)&&Y(f)?(De(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),b="value"):Y(l)&&(De(!1,`Use "dataKey" to specify the value of pie,
      the props "valueKey" will be deprecated in 1.1.0`),b=f);var O=i.filter(function(S){return me(S,b,0)!==0}).length,x=(v>=360?O:O-1)*c,w=v-O*d-x,h=i.reduce(function(S,j){var T=me(j,b,0);return S+(N(T)?T:0)},0),g;if(h>0){var A;g=i.map(function(S,j){var T=me(S,b,0),E=me(S,s,j),$=(N(T)?T:0)/h,C;j?C=A.endAngle+be(y)*c*(T!==0?1:0):C=o;var k=C+be(y)*((T!==0?d:0)+$*w),I=(C+k)/2,M=(m.innerRadius+m.outerRadius)/2,D=[{name:E,value:T,payload:S,dataKey:b,type:p}],R=re(m.cx,m.cy,M,I);return A=te(te(te({percent:$,cornerRadius:a,name:E,tooltipPayload:D,midAngle:I,middleRadius:M,tooltipPosition:R},S),m),{},{value:me(S,b),startAngle:C,endAngle:k,payload:S,paddingAngle:be(y)*c}),A})}return te(te({},m),{},{sectors:g,data:i})});var ZO=Math.ceil,JO=Math.max;function QO(t,e,r,n){for(var i=-1,a=JO(ZO((e-t)/(r||1)),0),o=Array(a);a--;)o[n?a:++i]=t,t+=r;return o}var ex=QO,tx=ex,rx=Ja,Ci=$p;function nx(t){return function(e,r,n){return n&&typeof n!="number"&&rx(e,r,n)&&(r=n=void 0),e=Ci(e),r===void 0?(r=e,e=0):r=Ci(r),n=n===void 0?e<r?1:-1:Ci(n),tx(e,r,n,t)}}var ix=nx,ax=ix,ox=ax(),ux=ox;const Dn=xe(ux);function qr(t){"@babel/helpers - typeof";return qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qr(t)}function Ac(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Pc(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ac(Object(r),!0).forEach(function(n){Ps(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ac(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Ps(t,e,r){return e=cx(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function cx(t){var e=lx(t,"string");return qr(e)=="symbol"?e:e+""}function lx(t,e){if(qr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var sx=["Webkit","Moz","O","ms"],fx=function(e,r){if(!e)return null;var n=e.replace(/(\w)/,function(a){return a.toUpperCase()}),i=sx.reduce(function(a,o){return Pc(Pc({},a),{},Ps({},o+n,r))},{});return i[e]=r,i};function Wt(t){"@babel/helpers - typeof";return Wt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Wt(t)}function Nn(){return Nn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Nn.apply(this,arguments)}function Sc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Mi(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Sc(Object(r),!0).forEach(function(n){we(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Sc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function px(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function jc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,js(n.key),n)}}function dx(t,e,r){return e&&jc(t.prototype,e),r&&jc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function vx(t,e,r){return e=Bn(e),hx(t,Ss()?Reflect.construct(e,r||[],Bn(t).constructor):e.apply(t,r))}function hx(t,e){if(e&&(Wt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return yx(t)}function yx(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ss(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ss=function(){return!!t})()}function Bn(t){return Bn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Bn(t)}function mx(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ia(t,e)}function Ia(t,e){return Ia=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ia(t,e)}function we(t,e,r){return e=js(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function js(t){var e=gx(t,"string");return Wt(e)=="symbol"?e:e+""}function gx(t,e){if(Wt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Wt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var bx=function(e){var r=e.data,n=e.startIndex,i=e.endIndex,a=e.x,o=e.width,u=e.travellerWidth;if(!r||!r.length)return{};var c=r.length,l=hr().domain(Dn(0,c)).range([a,a+o-u]),s=l.domain().map(function(f){return l(f)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:l(n),endX:l(i),scale:l,scaleValues:s}},Ec=function(e){return e.changedTouches&&!!e.changedTouches.length},Ft=function(t){function e(r){var n;return px(this,e),n=vx(this,e,[r]),we(n,"handleDrag",function(i){n.leaveTimer&&(clearTimeout(n.leaveTimer),n.leaveTimer=null),n.state.isTravellerMoving?n.handleTravellerMove(i):n.state.isSlideMoving&&n.handleSlideDrag(i)}),we(n,"handleTouchMove",function(i){i.changedTouches!=null&&i.changedTouches.length>0&&n.handleDrag(i.changedTouches[0])}),we(n,"handleDragEnd",function(){n.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var i=n.props,a=i.endIndex,o=i.onDragEnd,u=i.startIndex;o==null||o({endIndex:a,startIndex:u})}),n.detachDragEndListener()}),we(n,"handleLeaveWrapper",function(){(n.state.isTravellerMoving||n.state.isSlideMoving)&&(n.leaveTimer=window.setTimeout(n.handleDragEnd,n.props.leaveTimeOut))}),we(n,"handleEnterSlideOrTraveller",function(){n.setState({isTextActive:!0})}),we(n,"handleLeaveSlideOrTraveller",function(){n.setState({isTextActive:!1})}),we(n,"handleSlideDragStart",function(i){var a=Ec(i)?i.changedTouches[0]:i;n.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:a.pageX}),n.attachDragEndListener()}),n.travellerDragStartHandlers={startX:n.handleTravellerDragStart.bind(n,"startX"),endX:n.handleTravellerDragStart.bind(n,"endX")},n.state={},n}return mx(e,t),dx(e,[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(n){var i=n.startX,a=n.endX,o=this.state.scaleValues,u=this.props,c=u.gap,l=u.data,s=l.length-1,f=Math.min(i,a),p=Math.max(i,a),d=e.getIndexInRange(o,f),m=e.getIndexInRange(o,p);return{startIndex:d-d%c,endIndex:m===s?s:m-m%c}}},{key:"getTextOfTick",value:function(n){var i=this.props,a=i.data,o=i.tickFormatter,u=i.dataKey,c=me(a[n],u,n);return X(o)?o(c,n):c}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(n){var i=this.state,a=i.slideMoveStartX,o=i.startX,u=i.endX,c=this.props,l=c.x,s=c.width,f=c.travellerWidth,p=c.startIndex,d=c.endIndex,m=c.onChange,y=n.pageX-a;y>0?y=Math.min(y,l+s-f-u,l+s-f-o):y<0&&(y=Math.max(y,l-o,l-u));var v=this.getIndex({startX:o+y,endX:u+y});(v.startIndex!==p||v.endIndex!==d)&&m&&m(v),this.setState({startX:o+y,endX:u+y,slideMoveStartX:n.pageX})}},{key:"handleTravellerDragStart",value:function(n,i){var a=Ec(i)?i.changedTouches[0]:i;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:n,brushMoveStartX:a.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(n){var i=this.state,a=i.brushMoveStartX,o=i.movingTravellerId,u=i.endX,c=i.startX,l=this.state[o],s=this.props,f=s.x,p=s.width,d=s.travellerWidth,m=s.onChange,y=s.gap,v=s.data,b={startX:this.state.startX,endX:this.state.endX},O=n.pageX-a;O>0?O=Math.min(O,f+p-d-l):O<0&&(O=Math.max(O,f-l)),b[o]=l+O;var x=this.getIndex(b),w=x.startIndex,h=x.endIndex,g=function(){var S=v.length-1;return o==="startX"&&(u>c?w%y===0:h%y===0)||u<c&&h===S||o==="endX"&&(u>c?h%y===0:w%y===0)||u>c&&h===S};this.setState(we(we({},o,l+O),"brushMoveStartX",n.pageX),function(){m&&g()&&m(x)})}},{key:"handleTravellerMoveKeyboard",value:function(n,i){var a=this,o=this.state,u=o.scaleValues,c=o.startX,l=o.endX,s=this.state[i],f=u.indexOf(s);if(f!==-1){var p=f+n;if(!(p===-1||p>=u.length)){var d=u[p];i==="startX"&&d>=l||i==="endX"&&d<=c||this.setState(we({},i,d),function(){a.props.onChange(a.getIndex({startX:a.state.startX,endX:a.state.endX}))})}}}},{key:"renderBackground",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.fill,l=n.stroke;return P.createElement("rect",{stroke:l,fill:c,x:i,y:a,width:o,height:u})}},{key:"renderPanorama",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.data,l=n.children,s=n.padding,f=B.Children.only(l);return f?P.cloneElement(f,{x:i,y:a,width:o,height:u,margin:s,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(n,i){var a,o,u=this,c=this.props,l=c.y,s=c.travellerWidth,f=c.height,p=c.traveller,d=c.ariaLabel,m=c.data,y=c.startIndex,v=c.endIndex,b=Math.max(n,this.props.x),O=Mi(Mi({},G(this.props,!1)),{},{x:b,y:l,width:s,height:f}),x=d||"Min value: ".concat((a=m[y])===null||a===void 0?void 0:a.name,", Max value: ").concat((o=m[v])===null||o===void 0?void 0:o.name);return P.createElement(Z,{tabIndex:0,role:"slider","aria-label":x,"aria-valuenow":n,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[i],onTouchStart:this.travellerDragStartHandlers[i],onKeyDown:function(h){["ArrowLeft","ArrowRight"].includes(h.key)&&(h.preventDefault(),h.stopPropagation(),u.handleTravellerMoveKeyboard(h.key==="ArrowRight"?1:-1,i))},onFocus:function(){u.setState({isTravellerFocused:!0})},onBlur:function(){u.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},e.renderTraveller(p,O))}},{key:"renderSlide",value:function(n,i){var a=this.props,o=a.y,u=a.height,c=a.stroke,l=a.travellerWidth,s=Math.min(n,i)+l,f=Math.max(Math.abs(i-n)-l,0);return P.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:c,fillOpacity:.2,x:s,y:o,width:f,height:u})}},{key:"renderText",value:function(){var n=this.props,i=n.startIndex,a=n.endIndex,o=n.y,u=n.height,c=n.travellerWidth,l=n.stroke,s=this.state,f=s.startX,p=s.endX,d=5,m={pointerEvents:"none",fill:l};return P.createElement(Z,{className:"recharts-brush-texts"},P.createElement(ht,Nn({textAnchor:"end",verticalAnchor:"middle",x:Math.min(f,p)-d,y:o+u/2},m),this.getTextOfTick(i)),P.createElement(ht,Nn({textAnchor:"start",verticalAnchor:"middle",x:Math.max(f,p)+c+d,y:o+u/2},m),this.getTextOfTick(a)))}},{key:"render",value:function(){var n=this.props,i=n.data,a=n.className,o=n.children,u=n.x,c=n.y,l=n.width,s=n.height,f=n.alwaysShowText,p=this.state,d=p.startX,m=p.endX,y=p.isTextActive,v=p.isSlideMoving,b=p.isTravellerMoving,O=p.isTravellerFocused;if(!i||!i.length||!N(u)||!N(c)||!N(l)||!N(s)||l<=0||s<=0)return null;var x=U("recharts-brush",a),w=P.Children.count(o)===1,h=fx("userSelect","none");return P.createElement(Z,{className:x,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:h},this.renderBackground(),w&&this.renderPanorama(),this.renderSlide(d,m),this.renderTravellerLayer(d,"startX"),this.renderTravellerLayer(m,"endX"),(y||v||b||O||f)&&this.renderText())}}],[{key:"renderDefaultTraveller",value:function(n){var i=n.x,a=n.y,o=n.width,u=n.height,c=n.stroke,l=Math.floor(a+u/2)-1;return P.createElement(P.Fragment,null,P.createElement("rect",{x:i,y:a,width:o,height:u,fill:c,stroke:"none"}),P.createElement("line",{x1:i+1,y1:l,x2:i+o-1,y2:l,fill:"none",stroke:"#fff"}),P.createElement("line",{x1:i+1,y1:l+2,x2:i+o-1,y2:l+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(n,i){var a;return P.isValidElement(n)?a=P.cloneElement(n,i):X(n)?a=n(i):a=e.renderDefaultTraveller(i),a}},{key:"getDerivedStateFromProps",value:function(n,i){var a=n.data,o=n.width,u=n.x,c=n.travellerWidth,l=n.updateId,s=n.startIndex,f=n.endIndex;if(a!==i.prevData||l!==i.prevUpdateId)return Mi({prevData:a,prevTravellerWidth:c,prevUpdateId:l,prevX:u,prevWidth:o},a&&a.length?bx({data:a,width:o,x:u,travellerWidth:c,startIndex:s,endIndex:f}):{scale:null,scaleValues:null});if(i.scale&&(o!==i.prevWidth||u!==i.prevX||c!==i.prevTravellerWidth)){i.scale.range([u,u+o-c]);var p=i.scale.domain().map(function(d){return i.scale(d)});return{prevData:a,prevTravellerWidth:c,prevUpdateId:l,prevX:u,prevWidth:o,startX:i.scale(n.startIndex),endX:i.scale(n.endIndex),scaleValues:p}}return null}},{key:"getIndexInRange",value:function(n,i){for(var a=n.length,o=0,u=a-1;u-o>1;){var c=Math.floor((o+u)/2);n[c]>i?u=c:o=c}return i>=n[u]?u:o}}])}(B.PureComponent);we(Ft,"displayName","Brush");we(Ft,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var Ox=pl;function xx(t,e){var r;return Ox(t,function(n,i,a){return r=e(n,i,a),!r}),!!r}var wx=xx,Ax=hf,Px=Je,Sx=wx,jx=Ya,Ex=Ja;function $x(t,e,r){var n=jx(t)?Ax:Sx;return r&&Ex(t,e,r)&&(e=void 0),n(t,Px(e))}var _x=$x;const Tx=xe(_x);var Be=function(e,r){var n=e.alwaysShow,i=e.ifOverflow;return n&&(i="extendDomain"),i===r},Ix=yf,kx=Pp,Cx=Je;function Mx(t,e){var r={};return e=Cx(e),kx(t,function(n,i,a){Ix(r,i,e(n,i,a))}),r}var Dx=Mx;const Nx=xe(Dx);function Bx(t,e){for(var r=-1,n=t==null?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}var Rx=Bx,Lx=pl;function zx(t,e){var r=!0;return Lx(t,function(n,i,a){return r=!!e(n,i,a),r}),r}var Wx=zx,Fx=Rx,Kx=Wx,Gx=Je,qx=Ya,Vx=Ja;function Xx(t,e,r){var n=qx(t)?Fx:Kx;return r&&Vx(t,e,r)&&(e=void 0),n(t,Gx(e))}var Hx=Xx;const Es=xe(Hx);var Ux=["x","y"];function Vr(t){"@babel/helpers - typeof";return Vr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Vr(t)}function ka(){return ka=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ka.apply(this,arguments)}function $c(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function dr(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?$c(Object(r),!0).forEach(function(n){Yx(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):$c(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Yx(t,e,r){return e=Zx(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Zx(t){var e=Jx(t,"string");return Vr(e)=="symbol"?e:e+""}function Jx(t,e){if(Vr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Vr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Qx(t,e){if(t==null)return{};var r=e0(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function e0(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function t0(t,e){var r=t.x,n=t.y,i=Qx(t,Ux),a="".concat(r),o=parseInt(a,10),u="".concat(n),c=parseInt(u,10),l="".concat(e.height||i.height),s=parseInt(l,10),f="".concat(e.width||i.width),p=parseInt(f,10);return dr(dr(dr(dr(dr({},e),i),o?{x:o}:{}),c?{y:c}:{}),{},{height:s,width:p,name:e.name,radius:e.radius})}function _c(t){return P.createElement(xs,ka({shapeType:"rectangle",propTransformer:t0,activeClassName:"recharts-active-bar"},t))}var r0=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return function(n,i){if(typeof e=="number")return e;var a=typeof n=="number";return a?e(n,i):(a||dt(!1),r)}},n0=["value","background"],$s;function Kt(t){"@babel/helpers - typeof";return Kt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kt(t)}function i0(t,e){if(t==null)return{};var r=a0(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function a0(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function Rn(){return Rn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Rn.apply(this,arguments)}function Tc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ue(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Tc(Object(r),!0).forEach(function(n){Ye(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Tc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function o0(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ic(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Ts(n.key),n)}}function u0(t,e,r){return e&&Ic(t.prototype,e),r&&Ic(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function c0(t,e,r){return e=Ln(e),l0(t,_s()?Reflect.construct(e,r||[],Ln(t).constructor):e.apply(t,r))}function l0(t,e){if(e&&(Kt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return s0(t)}function s0(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function _s(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(_s=function(){return!!t})()}function Ln(t){return Ln=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Ln(t)}function f0(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ca(t,e)}function Ca(t,e){return Ca=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ca(t,e)}function Ye(t,e,r){return e=Ts(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ts(t){var e=p0(t,"string");return Kt(e)=="symbol"?e:e+""}function p0(t,e){if(Kt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Kt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var rr=function(t){function e(){var r;o0(this,e);for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return r=c0(this,e,[].concat(i)),Ye(r,"state",{isAnimationFinished:!1}),Ye(r,"id",Qr("recharts-bar-")),Ye(r,"handleAnimationEnd",function(){var o=r.props.onAnimationEnd;r.setState({isAnimationFinished:!0}),o&&o()}),Ye(r,"handleAnimationStart",function(){var o=r.props.onAnimationStart;r.setState({isAnimationFinished:!1}),o&&o()}),r}return f0(e,t),u0(e,[{key:"renderRectanglesStatically",value:function(n){var i=this,a=this.props,o=a.shape,u=a.dataKey,c=a.activeIndex,l=a.activeBar,s=G(this.props,!1);return n&&n.map(function(f,p){var d=p===c,m=d?l:o,y=ue(ue(ue({},s),f),{},{isActive:d,option:m,index:p,dataKey:u,onAnimationStart:i.handleAnimationStart,onAnimationEnd:i.handleAnimationEnd});return P.createElement(Z,Rn({className:"recharts-bar-rectangle"},vt(i.props,f,p),{key:"rectangle-".concat(f==null?void 0:f.x,"-").concat(f==null?void 0:f.y,"-").concat(f==null?void 0:f.value)}),P.createElement(_c,y))})}},{key:"renderRectanglesWithAnimation",value:function(){var n=this,i=this.props,a=i.data,o=i.layout,u=i.isAnimationActive,c=i.animationBegin,l=i.animationDuration,s=i.animationEasing,f=i.animationId,p=this.state.prevData;return P.createElement(Ve,{begin:c,duration:l,isActive:u,easing:s,from:{t:0},to:{t:1},key:"bar-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(d){var m=d.t,y=a.map(function(v,b){var O=p&&p[b];if(O){var x=Xe(O.x,v.x),w=Xe(O.y,v.y),h=Xe(O.width,v.width),g=Xe(O.height,v.height);return ue(ue({},v),{},{x:x(m),y:w(m),width:h(m),height:g(m)})}if(o==="horizontal"){var A=Xe(0,v.height),S=A(m);return ue(ue({},v),{},{y:v.y+v.height-S,height:S})}var j=Xe(0,v.width),T=j(m);return ue(ue({},v),{},{width:T})});return P.createElement(Z,null,n.renderRectanglesStatically(y))})}},{key:"renderRectangles",value:function(){var n=this.props,i=n.data,a=n.isAnimationActive,o=this.state.prevData;return a&&i&&i.length&&(!o||!Hn(o,i))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(i)}},{key:"renderBackground",value:function(){var n=this,i=this.props,a=i.data,o=i.dataKey,u=i.activeIndex,c=G(this.props.background,!1);return a.map(function(l,s){l.value;var f=l.background,p=i0(l,n0);if(!f)return null;var d=ue(ue(ue(ue(ue({},p),{},{fill:"#eee"},f),c),vt(n.props,l,s)),{},{onAnimationStart:n.handleAnimationStart,onAnimationEnd:n.handleAnimationEnd,dataKey:o,index:s,className:"recharts-bar-background-rectangle"});return P.createElement(_c,Rn({key:"background-bar-".concat(s),option:n.props.background,isActive:s===u},d))})}},{key:"renderErrorBar",value:function(n,i){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var a=this.props,o=a.data,u=a.xAxis,c=a.yAxis,l=a.layout,s=a.children,f=_e(s,pi);if(!f)return null;var p=l==="vertical"?o[0].height/2:o[0].width/2,d=function(v,b){var O=Array.isArray(v.value)?v.value[1]:v.value;return{x:v.x,y:v.y,value:O,errorVal:me(v,b)}},m={clipPath:n?"url(#clipPath-".concat(i,")"):null};return P.createElement(Z,m,f.map(function(y){return P.cloneElement(y,{key:"error-bar-".concat(i,"-").concat(y.props.dataKey),data:o,xAxis:u,yAxis:c,layout:l,offset:p,dataPointFormatter:d})}))}},{key:"render",value:function(){var n=this.props,i=n.hide,a=n.data,o=n.className,u=n.xAxis,c=n.yAxis,l=n.left,s=n.top,f=n.width,p=n.height,d=n.isAnimationActive,m=n.background,y=n.id;if(i||!a||!a.length)return null;var v=this.state.isAnimationFinished,b=U("recharts-bar",o),O=u&&u.allowDataOverflow,x=c&&c.allowDataOverflow,w=O||x,h=Y(y)?this.id:y;return P.createElement(Z,{className:b},O||x?P.createElement("defs",null,P.createElement("clipPath",{id:"clipPath-".concat(h)},P.createElement("rect",{x:O?l:l-f/2,y:x?s:s-p/2,width:O?f:f*2,height:x?p:p*2}))):null,P.createElement(Z,{className:"recharts-bar-rectangles",clipPath:w?"url(#clipPath-".concat(h,")"):null},m?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(w,h),(!d||v)&&Ze.renderCallByParent(this.props,a))}}],[{key:"getDerivedStateFromProps",value:function(n,i){return n.animationId!==i.prevAnimationId?{prevAnimationId:n.animationId,curData:n.data,prevData:i.curData}:n.data!==i.curData?{curData:n.data}:null}}])}(B.PureComponent);$s=rr;Ye(rr,"displayName","Bar");Ye(rr,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!Ge.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"});Ye(rr,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,a=t.xAxis,o=t.yAxis,u=t.xAxisTicks,c=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,d=Ty(n,r);if(!d)return null;var m=e.layout,y=r.type.defaultProps,v=y!==void 0?ue(ue({},y),r.props):r.props,b=v.dataKey,O=v.children,x=v.minPointSize,w=m==="horizontal"?o:a,h=l?w.scale.domain():null,g=By({numericAxis:w}),A=_e(O,oo),S=f.map(function(j,T){var E,$,C,k,I,M;l?E=Iy(l[s+T],h):(E=me(j,b),Array.isArray(E)||(E=[g,E]));var D=r0(x,$s.defaultProps.minPointSize)(E[1],T);if(m==="horizontal"){var R,W=[o.scale(E[0]),o.scale(E[1])],F=W[0],q=W[1];$=xu({axis:a,ticks:u,bandSize:i,offset:d.offset,entry:j,index:T}),C=(R=q??F)!==null&&R!==void 0?R:void 0,k=d.size;var L=F-q;if(I=Number.isNaN(L)?0:L,M={x:$,y:o.y,width:k,height:o.height},Math.abs(D)>0&&Math.abs(I)<Math.abs(D)){var V=be(I||D)*(Math.abs(D)-Math.abs(I));C-=V,I+=V}}else{var ae=[a.scale(E[0]),a.scale(E[1])],fe=ae[0],ke=ae[1];if($=fe,C=xu({axis:o,ticks:c,bandSize:i,offset:d.offset,entry:j,index:T}),k=ke-fe,I=d.size,M={x:a.x,y:C,width:a.width,height:I},Math.abs(D)>0&&Math.abs(k)<Math.abs(D)){var ir=be(k||D)*(Math.abs(D)-Math.abs(k));k+=ir}}return ue(ue(ue({},j),{},{x:$,y:C,width:k,height:I,value:l?E:E[1],payload:j,background:M},A&&A[T]&&A[T].props),{},{tooltipPayload:[rs(r,j)],tooltipPosition:{x:$+k/2,y:C+I/2}})});return ue({data:S,layout:m},p)});function Xr(t){"@babel/helpers - typeof";return Xr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xr(t)}function d0(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function kc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Is(n.key),n)}}function v0(t,e,r){return e&&kc(t.prototype,e),r&&kc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Cc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Me(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Cc(Object(r),!0).forEach(function(n){bi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Cc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function bi(t,e,r){return e=Is(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Is(t){var e=h0(t,"string");return Xr(e)=="symbol"?e:e+""}function h0(t,e){if(Xr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Xr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var y0=function(e,r,n,i,a){var o=e.width,u=e.height,c=e.layout,l=e.children,s=Object.keys(r),f={left:n.left,leftMirror:n.left,right:o-n.right,rightMirror:o-n.right,top:n.top,topMirror:n.top,bottom:u-n.bottom,bottomMirror:u-n.bottom},p=!!Ae(l,rr);return s.reduce(function(d,m){var y=r[m],v=y.orientation,b=y.domain,O=y.padding,x=O===void 0?{}:O,w=y.mirror,h=y.reversed,g="".concat(v).concat(w?"Mirror":""),A,S,j,T,E;if(y.type==="number"&&(y.padding==="gap"||y.padding==="no-gap")){var $=b[1]-b[0],C=1/0,k=y.categoricalDomain.sort();if(k.forEach(function(ae,fe){fe>0&&(C=Math.min((ae||0)-(k[fe-1]||0),C))}),Number.isFinite(C)){var I=C/$,M=y.layout==="vertical"?n.height:n.width;if(y.padding==="gap"&&(A=I*M/2),y.padding==="no-gap"){var D=Oe(e.barCategoryGap,I*M),R=I*M/2;A=R-D-(R-D)/M*D}}}i==="xAxis"?S=[n.left+(x.left||0)+(A||0),n.left+n.width-(x.right||0)-(A||0)]:i==="yAxis"?S=c==="horizontal"?[n.top+n.height-(x.bottom||0),n.top+(x.top||0)]:[n.top+(x.top||0)+(A||0),n.top+n.height-(x.bottom||0)-(A||0)]:S=y.range,h&&(S=[S[1],S[0]]);var W=Jl(y,a,p),F=W.scale,q=W.realScaleType;F.domain(b).range(S),Ql(F);var L=es(F,Me(Me({},y),{},{realScaleType:q}));i==="xAxis"?(E=v==="top"&&!w||v==="bottom"&&w,j=n.left,T=f[g]-E*y.height):i==="yAxis"&&(E=v==="left"&&!w||v==="right"&&w,j=f[g]-E*y.width,T=n.top);var V=Me(Me(Me({},y),L),{},{realScaleType:q,x:j,y:T,scale:F,width:i==="xAxis"?n.width:y.width,height:i==="yAxis"?n.height:y.height});return V.bandSize=An(V,L),!y.hide&&i==="xAxis"?f[g]+=(E?-1:1)*V.height:y.hide||(f[g]+=(E?-1:1)*V.width),Me(Me({},d),{},bi({},m,V))},{})},ks=function(e,r){var n=e.x,i=e.y,a=r.x,o=r.y;return{x:Math.min(n,a),y:Math.min(i,o),width:Math.abs(a-n),height:Math.abs(o-i)}},m0=function(e){var r=e.x1,n=e.y1,i=e.x2,a=e.y2;return ks({x:r,y:n},{x:i,y:a})},Cs=function(){function t(e){d0(this,t),this.scale=e}return v0(t,[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(r){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=n.bandAware,a=n.position;if(r!==void 0){if(a)switch(a){case"start":return this.scale(r);case"middle":{var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+o}case"end":{var u=this.bandwidth?this.bandwidth():0;return this.scale(r)+u}default:return this.scale(r)}if(i){var c=this.bandwidth?this.bandwidth()/2:0;return this.scale(r)+c}return this.scale(r)}}},{key:"isInRange",value:function(r){var n=this.range(),i=n[0],a=n[n.length-1];return i<=a?r>=i&&r<=a:r>=a&&r<=i}}],[{key:"create",value:function(r){return new t(r)}}])}();bi(Cs,"EPS",1e-4);var po=function(e){var r=Object.keys(e).reduce(function(n,i){return Me(Me({},n),{},bi({},i,Cs.create(e[i])))},{});return Me(Me({},r),{},{apply:function(i){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},o=a.bandAware,u=a.position;return Nx(i,function(c,l){return r[l].apply(c,{bandAware:o,position:u})})},isInRange:function(i){return Es(i,function(a,o){return r[o].isInRange(a)})}})};function g0(t){return(t%180+180)%180}var b0=function(e){var r=e.width,n=e.height,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,a=g0(i),o=a*Math.PI/180,u=Math.atan(n/r),c=o>u&&o<Math.PI-u?n/Math.sin(o):r/Math.cos(o);return Math.abs(c)},O0=Je,x0=mf,w0=gf;function A0(t){return function(e,r,n){var i=Object(e);if(!x0(e)){var a=O0(r);e=w0(e),r=function(u){return a(i[u],u,i)}}var o=t(e,r,n);return o>-1?i[a?e[o]:o]:void 0}}var P0=A0,S0=Sp,j0=Je,E0=_p,$0=Math.max;function _0(t,e,r){var n=t==null?0:t.length;if(!n)return-1;var i=r==null?0:E0(r);return i<0&&(i=$0(n+i,0)),S0(t,j0(e),i)}var T0=_0,I0=P0,k0=T0,C0=I0(k0),M0=C0;const D0=xe(M0);var N0=jp(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),vo=B.createContext(void 0),ho=B.createContext(void 0),Ms=B.createContext(void 0),Ds=B.createContext({}),Ns=B.createContext(void 0),Bs=B.createContext(0),Rs=B.createContext(0),Mc=function(e){var r=e.state,n=r.xAxisMap,i=r.yAxisMap,a=r.offset,o=e.clipPathId,u=e.children,c=e.width,l=e.height,s=N0(a);return P.createElement(vo.Provider,{value:n},P.createElement(ho.Provider,{value:i},P.createElement(Ds.Provider,{value:a},P.createElement(Ms.Provider,{value:s},P.createElement(Ns.Provider,{value:o},P.createElement(Bs.Provider,{value:l},P.createElement(Rs.Provider,{value:c},u)))))))},B0=function(){return B.useContext(Ns)},Ls=function(e){var r=B.useContext(vo);r==null&&dt(!1);var n=r[e];return n==null&&dt(!1),n},R0=function(){var e=B.useContext(vo);return Ue(e)},L0=function(){var e=B.useContext(ho),r=D0(e,function(n){return Es(n.domain,Number.isFinite)});return r||Ue(e)},zs=function(e){var r=B.useContext(ho);r==null&&dt(!1);var n=r[e];return n==null&&dt(!1),n},z0=function(){var e=B.useContext(Ms);return e},W0=function(){return B.useContext(Ds)},yo=function(){return B.useContext(Rs)},mo=function(){return B.useContext(Bs)};function Gt(t){"@babel/helpers - typeof";return Gt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Gt(t)}function F0(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Dc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Fs(n.key),n)}}function K0(t,e,r){return e&&Dc(t.prototype,e),r&&Dc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function G0(t,e,r){return e=zn(e),q0(t,Ws()?Reflect.construct(e,r||[],zn(t).constructor):e.apply(t,r))}function q0(t,e){if(e&&(Gt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return V0(t)}function V0(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ws(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ws=function(){return!!t})()}function zn(t){return zn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},zn(t)}function X0(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ma(t,e)}function Ma(t,e){return Ma=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ma(t,e)}function Nc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Bc(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Nc(Object(r),!0).forEach(function(n){go(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Nc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function go(t,e,r){return e=Fs(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Fs(t){var e=H0(t,"string");return Gt(e)=="symbol"?e:e+""}function H0(t,e){if(Gt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Gt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function U0(t,e){return Q0(t)||J0(t,e)||Z0(t,e)||Y0()}function Y0(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Z0(t,e){if(t){if(typeof t=="string")return Rc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Rc(t,e)}}function Rc(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function J0(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function Q0(t){if(Array.isArray(t))return t}function Da(){return Da=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Da.apply(this,arguments)}var ew=function(e,r){var n;return P.isValidElement(e)?n=P.cloneElement(e,r):X(e)?n=e(r):n=P.createElement("line",Da({},r,{className:"recharts-reference-line-line"})),n},tw=function(e,r,n,i,a,o,u,c,l){var s=a.x,f=a.y,p=a.width,d=a.height;if(n){var m=l.y,y=e.y.apply(m,{position:o});if(Be(l,"discard")&&!e.y.isInRange(y))return null;var v=[{x:s+p,y},{x:s,y}];return c==="left"?v.reverse():v}if(r){var b=l.x,O=e.x.apply(b,{position:o});if(Be(l,"discard")&&!e.x.isInRange(O))return null;var x=[{x:O,y:f+d},{x:O,y:f}];return u==="top"?x.reverse():x}if(i){var w=l.segment,h=w.map(function(g){return e.apply(g,{position:o})});return Be(l,"discard")&&Tx(h,function(g){return!e.isInRange(g)})?null:h}return null};function rw(t){var e=t.x,r=t.y,n=t.segment,i=t.xAxisId,a=t.yAxisId,o=t.shape,u=t.className,c=t.alwaysShow,l=B0(),s=Ls(i),f=zs(a),p=z0();if(!l||!p)return null;De(c===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var d=po({x:s.scale,y:f.scale}),m=se(e),y=se(r),v=n&&n.length===2,b=tw(d,m,y,v,p,t.position,s.orientation,f.orientation,t);if(!b)return null;var O=U0(b,2),x=O[0],w=x.x,h=x.y,g=O[1],A=g.x,S=g.y,j=Be(t,"hidden")?"url(#".concat(l,")"):void 0,T=Bc(Bc({clipPath:j},G(t,!0)),{},{x1:w,y1:h,x2:A,y2:S});return P.createElement(Z,{className:U("recharts-reference-line",u)},ew(o,T),de.renderCallByParent(t,m0({x1:w,y1:h,x2:A,y2:S})))}var bo=function(t){function e(){return F0(this,e),G0(this,e,arguments)}return X0(e,t),K0(e,[{key:"render",value:function(){return P.createElement(rw,this.props)}}])}(P.Component);go(bo,"displayName","ReferenceLine");go(bo,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function Na(){return Na=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Na.apply(this,arguments)}function qt(t){"@babel/helpers - typeof";return qt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qt(t)}function Lc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function zc(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Lc(Object(r),!0).forEach(function(n){Oi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Lc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function nw(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Wc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Gs(n.key),n)}}function iw(t,e,r){return e&&Wc(t.prototype,e),r&&Wc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function aw(t,e,r){return e=Wn(e),ow(t,Ks()?Reflect.construct(e,r||[],Wn(t).constructor):e.apply(t,r))}function ow(t,e){if(e&&(qt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return uw(t)}function uw(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ks(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ks=function(){return!!t})()}function Wn(t){return Wn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Wn(t)}function cw(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ba(t,e)}function Ba(t,e){return Ba=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ba(t,e)}function Oi(t,e,r){return e=Gs(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Gs(t){var e=lw(t,"string");return qt(e)=="symbol"?e:e+""}function lw(t,e){if(qt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(qt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var sw=function(e){var r=e.x,n=e.y,i=e.xAxis,a=e.yAxis,o=po({x:i.scale,y:a.scale}),u=o.apply({x:r,y:n},{bandAware:!0});return Be(e,"discard")&&!o.isInRange(u)?null:u},xi=function(t){function e(){return nw(this,e),aw(this,e,arguments)}return cw(e,t),iw(e,[{key:"render",value:function(){var n=this.props,i=n.x,a=n.y,o=n.r,u=n.alwaysShow,c=n.clipPathId,l=se(i),s=se(a);if(De(u===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!l||!s)return null;var f=sw(this.props);if(!f)return null;var p=f.x,d=f.y,m=this.props,y=m.shape,v=m.className,b=Be(this.props,"hidden")?"url(#".concat(c,")"):void 0,O=zc(zc({clipPath:b},G(this.props,!0)),{},{cx:p,cy:d});return P.createElement(Z,{className:U("recharts-reference-dot",v)},e.renderDot(y,O),de.renderCallByParent(this.props,{x:p-o,y:d-o,width:2*o,height:2*o}))}}])}(P.Component);Oi(xi,"displayName","ReferenceDot");Oi(xi,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});Oi(xi,"renderDot",function(t,e){var r;return P.isValidElement(t)?r=P.cloneElement(t,e):X(t)?r=t(e):r=P.createElement(fo,Na({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"})),r});function Ra(){return Ra=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ra.apply(this,arguments)}function Vt(t){"@babel/helpers - typeof";return Vt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Vt(t)}function Fc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Kc(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Fc(Object(r),!0).forEach(function(n){wi(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Fc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function fw(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Gc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Vs(n.key),n)}}function pw(t,e,r){return e&&Gc(t.prototype,e),r&&Gc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function dw(t,e,r){return e=Fn(e),vw(t,qs()?Reflect.construct(e,r||[],Fn(t).constructor):e.apply(t,r))}function vw(t,e){if(e&&(Vt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return hw(t)}function hw(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function qs(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(qs=function(){return!!t})()}function Fn(t){return Fn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Fn(t)}function yw(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&La(t,e)}function La(t,e){return La=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},La(t,e)}function wi(t,e,r){return e=Vs(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Vs(t){var e=mw(t,"string");return Vt(e)=="symbol"?e:e+""}function mw(t,e){if(Vt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Vt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var gw=function(e,r,n,i,a){var o=a.x1,u=a.x2,c=a.y1,l=a.y2,s=a.xAxis,f=a.yAxis;if(!s||!f)return null;var p=po({x:s.scale,y:f.scale}),d={x:e?p.x.apply(o,{position:"start"}):p.x.rangeMin,y:n?p.y.apply(c,{position:"start"}):p.y.rangeMin},m={x:r?p.x.apply(u,{position:"end"}):p.x.rangeMax,y:i?p.y.apply(l,{position:"end"}):p.y.rangeMax};return Be(a,"discard")&&(!p.isInRange(d)||!p.isInRange(m))?null:ks(d,m)},Ai=function(t){function e(){return fw(this,e),dw(this,e,arguments)}return yw(e,t),pw(e,[{key:"render",value:function(){var n=this.props,i=n.x1,a=n.x2,o=n.y1,u=n.y2,c=n.className,l=n.alwaysShow,s=n.clipPathId;De(l===void 0,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var f=se(i),p=se(a),d=se(o),m=se(u),y=this.props.shape;if(!f&&!p&&!d&&!m&&!y)return null;var v=gw(f,p,d,m,this.props);if(!v&&!y)return null;var b=Be(this.props,"hidden")?"url(#".concat(s,")"):void 0;return P.createElement(Z,{className:U("recharts-reference-area",c)},e.renderRect(y,Kc(Kc({clipPath:b},G(this.props,!0)),v)),de.renderCallByParent(this.props,v))}}])}(P.Component);wi(Ai,"displayName","ReferenceArea");wi(Ai,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});wi(Ai,"renderRect",function(t,e){var r;return P.isValidElement(t)?r=P.cloneElement(t,e):X(t)?r=t(e):r=P.createElement(so,Ra({},e,{className:"recharts-reference-area-rect"})),r});function Xs(t,e,r){if(e<1)return[];if(e===1&&r===void 0)return t;for(var n=[],i=0;i<t.length;i+=e)if(r===void 0||r(t[i])===!0)n.push(t[i]);else return;return n}function bw(t,e,r){var n={width:t.width+e.width,height:t.height+e.height};return b0(n,r)}function Ow(t,e,r){var n=r==="width",i=t.x,a=t.y,o=t.width,u=t.height;return e===1?{start:n?i:a,end:n?i+o:a+u}:{start:n?i+o:a+u,end:n?i:a}}function Kn(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var a=r();return t*(e-t*a/2-n)>=0&&t*(e+t*a/2-i)<=0}function xw(t,e){return Xs(t,e+1)}function ww(t,e,r,n,i){for(var a=(n||[]).slice(),o=e.start,u=e.end,c=0,l=1,s=o,f=function(){var m=n==null?void 0:n[c];if(m===void 0)return{v:Xs(n,l)};var y=c,v,b=function(){return v===void 0&&(v=r(m,y)),v},O=m.coordinate,x=c===0||Kn(t,O,b,s,u);x||(c=0,s=o,l+=1),x&&(s=O+t*(b()/2+i),c+=l)},p;l<=a.length;)if(p=f(),p)return p.v;return[]}function Hr(t){"@babel/helpers - typeof";return Hr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Hr(t)}function qc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function he(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?qc(Object(r),!0).forEach(function(n){Aw(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):qc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Aw(t,e,r){return e=Pw(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Pw(t){var e=Sw(t,"string");return Hr(e)=="symbol"?e:e+""}function Sw(t,e){if(Hr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Hr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function jw(t,e,r,n,i){for(var a=(n||[]).slice(),o=a.length,u=e.start,c=e.end,l=function(p){var d=a[p],m,y=function(){return m===void 0&&(m=r(d,p)),m};if(p===o-1){var v=t*(d.coordinate+t*y()/2-c);a[p]=d=he(he({},d),{},{tickCoord:v>0?d.coordinate-v*t:d.coordinate})}else a[p]=d=he(he({},d),{},{tickCoord:d.coordinate});var b=Kn(t,d.tickCoord,y,u,c);b&&(c=d.tickCoord-t*(y()/2+i),a[p]=he(he({},d),{},{isShow:!0}))},s=o-1;s>=0;s--)l(s);return a}function Ew(t,e,r,n,i,a){var o=(n||[]).slice(),u=o.length,c=e.start,l=e.end;if(a){var s=n[u-1],f=r(s,u-1),p=t*(s.coordinate+t*f/2-l);o[u-1]=s=he(he({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate});var d=Kn(t,s.tickCoord,function(){return f},c,l);d&&(l=s.tickCoord-t*(f/2+i),o[u-1]=he(he({},s),{},{isShow:!0}))}for(var m=a?u-1:u,y=function(O){var x=o[O],w,h=function(){return w===void 0&&(w=r(x,O)),w};if(O===0){var g=t*(x.coordinate-t*h()/2-c);o[O]=x=he(he({},x),{},{tickCoord:g<0?x.coordinate-g*t:x.coordinate})}else o[O]=x=he(he({},x),{},{tickCoord:x.coordinate});var A=Kn(t,x.tickCoord,h,c,l);A&&(c=x.tickCoord+t*(h()/2+i),o[O]=he(he({},x),{},{isShow:!0}))},v=0;v<m;v++)y(v);return o}function Oo(t,e,r){var n=t.tick,i=t.ticks,a=t.viewBox,o=t.minTickGap,u=t.orientation,c=t.interval,l=t.tickFormatter,s=t.unit,f=t.angle;if(!i||!i.length||!n)return[];if(N(c)||Ge.isSsr)return xw(i,typeof c=="number"&&N(c)?c:0);var p=[],d=u==="top"||u==="bottom"?"width":"height",m=s&&d==="width"?yr(s,{fontSize:e,letterSpacing:r}):{width:0,height:0},y=function(x,w){var h=X(l)?l(x.value,w):x.value;return d==="width"?bw(yr(h,{fontSize:e,letterSpacing:r}),m,f):yr(h,{fontSize:e,letterSpacing:r})[d]},v=i.length>=2?be(i[1].coordinate-i[0].coordinate):1,b=Ow(a,v,d);return c==="equidistantPreserveStart"?ww(v,b,y,i,o):(c==="preserveStart"||c==="preserveStartEnd"?p=Ew(v,b,y,i,o,c==="preserveStartEnd"):p=jw(v,b,y,i,o),p.filter(function(O){return O.isShow}))}var $w=["viewBox"],_w=["viewBox"],Tw=["ticks"];function Xt(t){"@babel/helpers - typeof";return Xt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xt(t)}function Et(){return Et=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Et.apply(this,arguments)}function Vc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ge(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Vc(Object(r),!0).forEach(function(n){xo(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Vc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Di(t,e){if(t==null)return{};var r=Iw(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Iw(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function kw(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Xc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Us(n.key),n)}}function Cw(t,e,r){return e&&Xc(t.prototype,e),r&&Xc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Mw(t,e,r){return e=Gn(e),Dw(t,Hs()?Reflect.construct(e,r||[],Gn(t).constructor):e.apply(t,r))}function Dw(t,e){if(e&&(Xt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Nw(t)}function Nw(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Hs(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Hs=function(){return!!t})()}function Gn(t){return Gn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Gn(t)}function Bw(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&za(t,e)}function za(t,e){return za=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},za(t,e)}function xo(t,e,r){return e=Us(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Us(t){var e=Rw(t,"string");return Xt(e)=="symbol"?e:e+""}function Rw(t,e){if(Xt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Xt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var nr=function(t){function e(r){var n;return kw(this,e),n=Mw(this,e,[r]),n.state={fontSize:"",letterSpacing:""},n}return Bw(e,t),Cw(e,[{key:"shouldComponentUpdate",value:function(n,i){var a=n.viewBox,o=Di(n,$w),u=this.props,c=u.viewBox,l=Di(u,_w);return!_t(a,c)||!_t(o,l)||!_t(i,this.state)}},{key:"componentDidMount",value:function(){var n=this.layerReference;if(n){var i=n.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];i&&this.setState({fontSize:window.getComputedStyle(i).fontSize,letterSpacing:window.getComputedStyle(i).letterSpacing})}}},{key:"getTickLineCoord",value:function(n){var i=this.props,a=i.x,o=i.y,u=i.width,c=i.height,l=i.orientation,s=i.tickSize,f=i.mirror,p=i.tickMargin,d,m,y,v,b,O,x=f?-1:1,w=n.tickSize||s,h=N(n.tickCoord)?n.tickCoord:n.coordinate;switch(l){case"top":d=m=n.coordinate,v=o+ +!f*c,y=v-x*w,O=y-x*p,b=h;break;case"left":y=v=n.coordinate,m=a+ +!f*u,d=m-x*w,b=d-x*p,O=h;break;case"right":y=v=n.coordinate,m=a+ +f*u,d=m+x*w,b=d+x*p,O=h;break;default:d=m=n.coordinate,v=o+ +f*c,y=v+x*w,O=y+x*p,b=h;break}return{line:{x1:d,y1:y,x2:m,y2:v},tick:{x:b,y:O}}}},{key:"getTickTextAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o;switch(i){case"left":o=a?"start":"end";break;case"right":o=a?"end":"start";break;default:o="middle";break}return o}},{key:"getTickVerticalAnchor",value:function(){var n=this.props,i=n.orientation,a=n.mirror,o="end";switch(i){case"left":case"right":o="middle";break;case"top":o=a?"start":"end";break;default:o=a?"end":"start";break}return o}},{key:"renderAxisLine",value:function(){var n=this.props,i=n.x,a=n.y,o=n.width,u=n.height,c=n.orientation,l=n.mirror,s=n.axisLine,f=ge(ge(ge({},G(this.props,!1)),G(s,!1)),{},{fill:"none"});if(c==="top"||c==="bottom"){var p=+(c==="top"&&!l||c==="bottom"&&l);f=ge(ge({},f),{},{x1:i,y1:a+p*u,x2:i+o,y2:a+p*u})}else{var d=+(c==="left"&&!l||c==="right"&&l);f=ge(ge({},f),{},{x1:i+d*o,y1:a,x2:i+d*o,y2:a+u})}return P.createElement("line",Et({},f,{className:U("recharts-cartesian-axis-line",Se(s,"className"))}))}},{key:"renderTicks",value:function(n,i,a){var o=this,u=this.props,c=u.tickLine,l=u.stroke,s=u.tick,f=u.tickFormatter,p=u.unit,d=Oo(ge(ge({},this.props),{},{ticks:n}),i,a),m=this.getTickTextAnchor(),y=this.getTickVerticalAnchor(),v=G(this.props,!1),b=G(s,!1),O=ge(ge({},v),{},{fill:"none"},G(c,!1)),x=d.map(function(w,h){var g=o.getTickLineCoord(w),A=g.line,S=g.tick,j=ge(ge(ge(ge({textAnchor:m,verticalAnchor:y},v),{},{stroke:"none",fill:l},b),S),{},{index:h,payload:w,visibleTicksCount:d.length,tickFormatter:f});return P.createElement(Z,Et({className:"recharts-cartesian-axis-tick",key:"tick-".concat(w.value,"-").concat(w.coordinate,"-").concat(w.tickCoord)},vt(o.props,w,h)),c&&P.createElement("line",Et({},O,A,{className:U("recharts-cartesian-axis-tick-line",Se(c,"className"))})),s&&e.renderTickItem(s,j,"".concat(X(f)?f(w.value,h):w.value).concat(p||"")))});return P.createElement("g",{className:"recharts-cartesian-axis-ticks"},x)}},{key:"render",value:function(){var n=this,i=this.props,a=i.axisLine,o=i.width,u=i.height,c=i.ticksGenerator,l=i.className,s=i.hide;if(s)return null;var f=this.props,p=f.ticks,d=Di(f,Tw),m=p;return X(c)&&(m=p&&p.length>0?c(this.props):c(d)),o<=0||u<=0||!m||!m.length?null:P.createElement(Z,{className:U("recharts-cartesian-axis",l),ref:function(v){n.layerReference=v}},a&&this.renderAxisLine(),this.renderTicks(m,this.state.fontSize,this.state.letterSpacing),de.renderCallByParent(this.props))}}],[{key:"renderTickItem",value:function(n,i,a){var o;return P.isValidElement(n)?o=P.cloneElement(n,i):X(n)?o=n(i):o=P.createElement(ht,Et({},i,{className:"recharts-cartesian-axis-tick-value"}),a),o}}])}(B.Component);xo(nr,"displayName","CartesianAxis");xo(nr,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var Lw=["x1","y1","x2","y2","key"],zw=["offset"];function mt(t){"@babel/helpers - typeof";return mt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},mt(t)}function Hc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function ye(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Hc(Object(r),!0).forEach(function(n){Ww(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Hc(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Ww(t,e,r){return e=Fw(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Fw(t){var e=Kw(t,"string");return mt(e)=="symbol"?e:e+""}function Kw(t,e){if(mt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(mt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ft(){return ft=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ft.apply(this,arguments)}function Uc(t,e){if(t==null)return{};var r=Gw(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Gw(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}var qw=function(e){var r=e.fill;if(!r||r==="none")return null;var n=e.fillOpacity,i=e.x,a=e.y,o=e.width,u=e.height,c=e.ry;return P.createElement("rect",{x:i,y:a,ry:c,width:o,height:u,stroke:"none",fill:r,fillOpacity:n,className:"recharts-cartesian-grid-bg"})};function Ys(t,e){var r;if(P.isValidElement(t))r=P.cloneElement(t,e);else if(X(t))r=t(e);else{var n=e.x1,i=e.y1,a=e.x2,o=e.y2,u=e.key,c=Uc(e,Lw),l=G(c,!1);l.offset;var s=Uc(l,zw);r=P.createElement("line",ft({},s,{x1:n,y1:i,x2:a,y2:o,fill:"none",key:u}))}return r}function Vw(t){var e=t.x,r=t.width,n=t.horizontal,i=n===void 0?!0:n,a=t.horizontalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var l=ye(ye({},t),{},{x1:e,y1:u,x2:e+r,y2:u,key:"line-".concat(c),index:c});return Ys(i,l)});return P.createElement("g",{className:"recharts-cartesian-grid-horizontal"},o)}function Xw(t){var e=t.y,r=t.height,n=t.vertical,i=n===void 0?!0:n,a=t.verticalPoints;if(!i||!a||!a.length)return null;var o=a.map(function(u,c){var l=ye(ye({},t),{},{x1:u,y1:e,x2:u,y2:e+r,key:"line-".concat(c),index:c});return Ys(i,l)});return P.createElement("g",{className:"recharts-cartesian-grid-vertical"},o)}function Hw(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,i=t.y,a=t.width,o=t.height,u=t.horizontalPoints,c=t.horizontal,l=c===void 0?!0:c;if(!l||!e||!e.length)return null;var s=u.map(function(p){return Math.round(p+i-i)}).sort(function(p,d){return p-d});i!==s[0]&&s.unshift(0);var f=s.map(function(p,d){var m=!s[d+1],y=m?i+o-p:s[d+1]-p;if(y<=0)return null;var v=d%e.length;return P.createElement("rect",{key:"react-".concat(d),y:p,x:n,height:y,width:a,stroke:"none",fill:e[v],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return P.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function Uw(t){var e=t.vertical,r=e===void 0?!0:e,n=t.verticalFill,i=t.fillOpacity,a=t.x,o=t.y,u=t.width,c=t.height,l=t.verticalPoints;if(!r||!n||!n.length)return null;var s=l.map(function(p){return Math.round(p+a-a)}).sort(function(p,d){return p-d});a!==s[0]&&s.unshift(0);var f=s.map(function(p,d){var m=!s[d+1],y=m?a+u-p:s[d+1]-p;if(y<=0)return null;var v=d%n.length;return P.createElement("rect",{key:"react-".concat(d),x:p,y:o,width:y,height:c,stroke:"none",fill:n[v],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return P.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var Yw=function(e,r){var n=e.xAxis,i=e.width,a=e.height,o=e.offset;return Zl(Oo(ye(ye(ye({},nr.defaultProps),n),{},{ticks:Fe(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.left,o.left+o.width,r)},Zw=function(e,r){var n=e.yAxis,i=e.width,a=e.height,o=e.offset;return Zl(Oo(ye(ye(ye({},nr.defaultProps),n),{},{ticks:Fe(n,!0),viewBox:{x:0,y:0,width:i,height:a}})),o.top,o.top+o.height,r)},At={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function Jw(t){var e,r,n,i,a,o,u=yo(),c=mo(),l=W0(),s=ye(ye({},t),{},{stroke:(e=t.stroke)!==null&&e!==void 0?e:At.stroke,fill:(r=t.fill)!==null&&r!==void 0?r:At.fill,horizontal:(n=t.horizontal)!==null&&n!==void 0?n:At.horizontal,horizontalFill:(i=t.horizontalFill)!==null&&i!==void 0?i:At.horizontalFill,vertical:(a=t.vertical)!==null&&a!==void 0?a:At.vertical,verticalFill:(o=t.verticalFill)!==null&&o!==void 0?o:At.verticalFill,x:N(t.x)?t.x:l.left,y:N(t.y)?t.y:l.top,width:N(t.width)?t.width:l.width,height:N(t.height)?t.height:l.height}),f=s.x,p=s.y,d=s.width,m=s.height,y=s.syncWithTicks,v=s.horizontalValues,b=s.verticalValues,O=R0(),x=L0();if(!N(d)||d<=0||!N(m)||m<=0||!N(f)||f!==+f||!N(p)||p!==+p)return null;var w=s.verticalCoordinatesGenerator||Yw,h=s.horizontalCoordinatesGenerator||Zw,g=s.horizontalPoints,A=s.verticalPoints;if((!g||!g.length)&&X(h)){var S=v&&v.length,j=h({yAxis:x?ye(ye({},x),{},{ticks:S?v:x.ticks}):void 0,width:u,height:c,offset:l},S?!0:y);De(Array.isArray(j),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(mt(j),"]")),Array.isArray(j)&&(g=j)}if((!A||!A.length)&&X(w)){var T=b&&b.length,E=w({xAxis:O?ye(ye({},O),{},{ticks:T?b:O.ticks}):void 0,width:u,height:c,offset:l},T?!0:y);De(Array.isArray(E),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(mt(E),"]")),Array.isArray(E)&&(A=E)}return P.createElement("g",{className:"recharts-cartesian-grid"},P.createElement(qw,{fill:s.fill,fillOpacity:s.fillOpacity,x:s.x,y:s.y,width:s.width,height:s.height,ry:s.ry}),P.createElement(Vw,ft({},s,{offset:l,horizontalPoints:g,xAxis:O,yAxis:x})),P.createElement(Xw,ft({},s,{offset:l,verticalPoints:A,xAxis:O,yAxis:x})),P.createElement(Hw,ft({},s,{horizontalPoints:g})),P.createElement(Uw,ft({},s,{verticalPoints:A})))}Jw.displayName="CartesianGrid";function Ht(t){"@babel/helpers - typeof";return Ht=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ht(t)}function Qw(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Yc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Qs(n.key),n)}}function eA(t,e,r){return e&&Yc(t.prototype,e),r&&Yc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function tA(t,e,r){return e=qn(e),rA(t,Zs()?Reflect.construct(e,r||[],qn(t).constructor):e.apply(t,r))}function rA(t,e){if(e&&(Ht(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return nA(t)}function nA(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Zs(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Zs=function(){return!!t})()}function qn(t){return qn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},qn(t)}function iA(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Wa(t,e)}function Wa(t,e){return Wa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Wa(t,e)}function Js(t,e,r){return e=Qs(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Qs(t){var e=aA(t,"string");return Ht(e)=="symbol"?e:e+""}function aA(t,e){if(Ht(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Ht(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Fa(){return Fa=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Fa.apply(this,arguments)}function oA(t){var e=t.xAxisId,r=yo(),n=mo(),i=Ls(e);return i==null?null:P.createElement(nr,Fa({},i,{className:U("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(o){return Fe(o,!0)}}))}var wo=function(t){function e(){return Qw(this,e),tA(this,e,arguments)}return iA(e,t),eA(e,[{key:"render",value:function(){return P.createElement(oA,this.props)}}])}(P.Component);Js(wo,"displayName","XAxis");Js(wo,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function Ut(t){"@babel/helpers - typeof";return Ut=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ut(t)}function uA(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Zc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,rf(n.key),n)}}function cA(t,e,r){return e&&Zc(t.prototype,e),r&&Zc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function lA(t,e,r){return e=Vn(e),sA(t,ef()?Reflect.construct(e,r||[],Vn(t).constructor):e.apply(t,r))}function sA(t,e){if(e&&(Ut(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return fA(t)}function fA(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ef(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(ef=function(){return!!t})()}function Vn(t){return Vn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Vn(t)}function pA(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Ka(t,e)}function Ka(t,e){return Ka=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Ka(t,e)}function tf(t,e,r){return e=rf(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rf(t){var e=dA(t,"string");return Ut(e)=="symbol"?e:e+""}function dA(t,e){if(Ut(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Ut(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ga(){return Ga=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Ga.apply(this,arguments)}var vA=function(e){var r=e.yAxisId,n=yo(),i=mo(),a=zs(r);return a==null?null:P.createElement(nr,Ga({},a,{className:U("recharts-".concat(a.axisType," ").concat(a.axisType),a.className),viewBox:{x:0,y:0,width:n,height:i},ticksGenerator:function(u){return Fe(u,!0)}}))},Ao=function(t){function e(){return uA(this,e),lA(this,e,arguments)}return pA(e,t),cA(e,[{key:"render",value:function(){return P.createElement(vA,this.props)}}])}(P.Component);tf(Ao,"displayName","YAxis");tf(Ao,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});function Jc(t){return gA(t)||mA(t)||yA(t)||hA()}function hA(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function yA(t,e){if(t){if(typeof t=="string")return qa(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return qa(t,e)}}function mA(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function gA(t){if(Array.isArray(t))return qa(t)}function qa(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Va=function(e,r,n,i,a){var o=_e(e,bo),u=_e(e,xi),c=[].concat(Jc(o),Jc(u)),l=_e(e,Ai),s="".concat(i,"Id"),f=i[0],p=r;if(c.length&&(p=c.reduce(function(y,v){if(v.props[s]===n&&Be(v.props,"extendDomain")&&N(v.props[f])){var b=v.props[f];return[Math.min(y[0],b),Math.max(y[1],b)]}return y},p)),l.length){var d="".concat(f,"1"),m="".concat(f,"2");p=l.reduce(function(y,v){if(v.props[s]===n&&Be(v.props,"extendDomain")&&N(v.props[d])&&N(v.props[m])){var b=v.props[d],O=v.props[m];return[Math.min(y[0],b,O),Math.max(y[1],b,O)]}return y},p)}return a&&a.length&&(p=a.reduce(function(y,v){return N(v)?[Math.min(y[0],v),Math.max(y[1],v)]:y},p)),p},nf={exports:{}};(function(t){var e=Object.prototype.hasOwnProperty,r="~";function n(){}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1));function i(c,l,s){this.fn=c,this.context=l,this.once=s||!1}function a(c,l,s,f,p){if(typeof s!="function")throw new TypeError("The listener must be a function");var d=new i(s,f||c,p),m=r?r+l:l;return c._events[m]?c._events[m].fn?c._events[m]=[c._events[m],d]:c._events[m].push(d):(c._events[m]=d,c._eventsCount++),c}function o(c,l){--c._eventsCount===0?c._events=new n:delete c._events[l]}function u(){this._events=new n,this._eventsCount=0}u.prototype.eventNames=function(){var l=[],s,f;if(this._eventsCount===0)return l;for(f in s=this._events)e.call(s,f)&&l.push(r?f.slice(1):f);return Object.getOwnPropertySymbols?l.concat(Object.getOwnPropertySymbols(s)):l},u.prototype.listeners=function(l){var s=r?r+l:l,f=this._events[s];if(!f)return[];if(f.fn)return[f.fn];for(var p=0,d=f.length,m=new Array(d);p<d;p++)m[p]=f[p].fn;return m},u.prototype.listenerCount=function(l){var s=r?r+l:l,f=this._events[s];return f?f.fn?1:f.length:0},u.prototype.emit=function(l,s,f,p,d,m){var y=r?r+l:l;if(!this._events[y])return!1;var v=this._events[y],b=arguments.length,O,x;if(v.fn){switch(v.once&&this.removeListener(l,v.fn,void 0,!0),b){case 1:return v.fn.call(v.context),!0;case 2:return v.fn.call(v.context,s),!0;case 3:return v.fn.call(v.context,s,f),!0;case 4:return v.fn.call(v.context,s,f,p),!0;case 5:return v.fn.call(v.context,s,f,p,d),!0;case 6:return v.fn.call(v.context,s,f,p,d,m),!0}for(x=1,O=new Array(b-1);x<b;x++)O[x-1]=arguments[x];v.fn.apply(v.context,O)}else{var w=v.length,h;for(x=0;x<w;x++)switch(v[x].once&&this.removeListener(l,v[x].fn,void 0,!0),b){case 1:v[x].fn.call(v[x].context);break;case 2:v[x].fn.call(v[x].context,s);break;case 3:v[x].fn.call(v[x].context,s,f);break;case 4:v[x].fn.call(v[x].context,s,f,p);break;default:if(!O)for(h=1,O=new Array(b-1);h<b;h++)O[h-1]=arguments[h];v[x].fn.apply(v[x].context,O)}}return!0},u.prototype.on=function(l,s,f){return a(this,l,s,f,!1)},u.prototype.once=function(l,s,f){return a(this,l,s,f,!0)},u.prototype.removeListener=function(l,s,f,p){var d=r?r+l:l;if(!this._events[d])return this;if(!s)return o(this,d),this;var m=this._events[d];if(m.fn)m.fn===s&&(!p||m.once)&&(!f||m.context===f)&&o(this,d);else{for(var y=0,v=[],b=m.length;y<b;y++)(m[y].fn!==s||p&&!m[y].once||f&&m[y].context!==f)&&v.push(m[y]);v.length?this._events[d]=v.length===1?v[0]:v:o(this,d)}return this},u.prototype.removeAllListeners=function(l){var s;return l?(s=r?r+l:l,this._events[s]&&o(this,s)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,t.exports=u})(nf);var bA=nf.exports;const OA=xe(bA);var Ni=new OA,Bi="recharts.syncMouseEvents";function Ur(t){"@babel/helpers - typeof";return Ur=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ur(t)}function xA(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Qc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,af(n.key),n)}}function wA(t,e,r){return e&&Qc(t.prototype,e),r&&Qc(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Ri(t,e,r){return e=af(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function af(t){var e=AA(t,"string");return Ur(e)=="symbol"?e:e+""}function AA(t,e){if(Ur(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Ur(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var PA=function(){function t(){xA(this,t),Ri(this,"activeIndex",0),Ri(this,"coordinateList",[]),Ri(this,"layout","horizontal")}return wA(t,[{key:"setDetails",value:function(r){var n,i=r.coordinateList,a=i===void 0?null:i,o=r.container,u=o===void 0?null:o,c=r.layout,l=c===void 0?null:c,s=r.offset,f=s===void 0?null:s,p=r.mouseHandlerCallback,d=p===void 0?null:p;this.coordinateList=(n=a??this.coordinateList)!==null&&n!==void 0?n:[],this.container=u??this.container,this.layout=l??this.layout,this.offset=f??this.offset,this.mouseHandlerCallback=d??this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(r){if(this.coordinateList.length!==0)switch(r.key){case"ArrowRight":{if(this.layout!=="horizontal")return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break}case"ArrowLeft":{if(this.layout!=="horizontal")return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse();break}}}},{key:"setIndex",value:function(r){this.activeIndex=r}},{key:"spoofMouse",value:function(){var r,n;if(this.layout==="horizontal"&&this.coordinateList.length!==0){var i=this.container.getBoundingClientRect(),a=i.x,o=i.y,u=i.height,c=this.coordinateList[this.activeIndex].coordinate,l=((r=window)===null||r===void 0?void 0:r.scrollX)||0,s=((n=window)===null||n===void 0?void 0:n.scrollY)||0,f=a+c+l,p=o+this.offset.top+u/2+s;this.mouseHandlerCallback({pageX:f,pageY:p})}}}])}();function SA(t,e,r){if(r==="number"&&e===!0&&Array.isArray(t)){var n=t==null?void 0:t[0],i=t==null?void 0:t[1];if(n&&i&&N(n)&&N(i))return!0}return!1}function jA(t,e,r,n){var i=n/2;return{stroke:"none",fill:"#ccc",x:t==="horizontal"?e.x-i:r.left+.5,y:t==="horizontal"?r.top+.5:e.y-i,width:t==="horizontal"?n:r.width-1,height:t==="horizontal"?r.height-1:n}}function of(t){var e=t.cx,r=t.cy,n=t.radius,i=t.startAngle,a=t.endAngle,o=re(e,r,n,i),u=re(e,r,n,a);return{points:[o,u],cx:e,cy:r,radius:n,startAngle:i,endAngle:a}}function EA(t,e,r){var n,i,a,o;if(t==="horizontal")n=e.x,a=n,i=r.top,o=r.top+r.height;else if(t==="vertical")i=e.y,o=i,n=r.left,a=r.left+r.width;else if(e.cx!=null&&e.cy!=null)if(t==="centric"){var u=e.cx,c=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=re(u,c,l,f),d=re(u,c,s,f);n=p.x,i=p.y,a=d.x,o=d.y}else return of(e);return[{x:n,y:i},{x:a,y:o}]}function Yr(t){"@babel/helpers - typeof";return Yr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Yr(t)}function el(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function fn(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?el(Object(r),!0).forEach(function(n){$A(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):el(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function $A(t,e,r){return e=_A(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _A(t){var e=TA(t,"string");return Yr(e)=="symbol"?e:e+""}function TA(t,e){if(Yr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Yr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function IA(t){var e,r,n=t.element,i=t.tooltipEventType,a=t.isActive,o=t.activeCoordinate,u=t.activePayload,c=t.offset,l=t.activeTooltipIndex,s=t.tooltipAxisBandSize,f=t.layout,p=t.chartName,d=(e=n.props.cursor)!==null&&e!==void 0?e:(r=n.type.defaultProps)===null||r===void 0?void 0:r.cursor;if(!n||!d||!a||!o||p!=="ScatterChart"&&i!=="axis")return null;var m,y=ha;if(p==="ScatterChart")m=o,y=Db;else if(p==="BarChart")m=jA(f,o,c,s),y=so;else if(f==="radial"){var v=of(o),b=v.cx,O=v.cy,x=v.radius,w=v.startAngle,h=v.endAngle;m={cx:b,cy:O,startAngle:w,endAngle:h,innerRadius:x,outerRadius:x},y=cs}else m={points:EA(f,o,c)},y=ha;var g=fn(fn(fn(fn({stroke:"#ccc",pointerEvents:"none"},c),m),G(d,!1)),{},{payload:u,payloadIndex:l,className:U("recharts-tooltip-cursor",d.className)});return B.isValidElement(d)?B.cloneElement(d,g):B.createElement(y,g)}var kA=["item"],CA=["children","className","width","height","style","compact","title","desc"];function Yt(t){"@babel/helpers - typeof";return Yt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Yt(t)}function $t(){return $t=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},$t.apply(this,arguments)}function tl(t,e){return NA(t)||DA(t,e)||cf(t,e)||MA()}function MA(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function DA(t,e){var r=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(r!=null){var n,i,a,o,u=[],c=!0,l=!1;try{if(a=(r=r.call(t)).next,e===0){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(s){l=!0,i=s}finally{try{if(!c&&r.return!=null&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return u}}function NA(t){if(Array.isArray(t))return t}function rl(t,e){if(t==null)return{};var r=BA(t,e),n,i;if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)n=a[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function BA(t,e){if(t==null)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}function RA(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function nl(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lf(n.key),n)}}function LA(t,e,r){return e&&nl(t.prototype,e),r&&nl(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function zA(t,e,r){return e=Xn(e),WA(t,uf()?Reflect.construct(e,r||[],Xn(t).constructor):e.apply(t,r))}function WA(t,e){if(e&&(Yt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return FA(t)}function FA(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function uf(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(uf=function(){return!!t})()}function Xn(t){return Xn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},Xn(t)}function KA(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Xa(t,e)}function Xa(t,e){return Xa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Xa(t,e)}function Zt(t){return VA(t)||qA(t)||cf(t)||GA()}function GA(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function cf(t,e){if(t){if(typeof t=="string")return Ha(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor&&(r=t.constructor.name),r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ha(t,e)}}function qA(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function VA(t){if(Array.isArray(t))return Ha(t)}function Ha(t,e){(e==null||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function il(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function _(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?il(Object(r),!0).forEach(function(n){K(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):il(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function K(t,e,r){return e=lf(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lf(t){var e=XA(t,"string");return Yt(e)=="symbol"?e:e+""}function XA(t,e){if(Yt(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Yt(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var HA={xAxis:["bottom","top"],yAxis:["left","right"]},UA={width:"100%",height:"100%"},sf={x:0,y:0};function pn(t){return t}var YA=function(e,r){return r==="horizontal"?e.x:r==="vertical"?e.y:r==="centric"?e.angle:e.radius},ZA=function(e,r,n,i){var a=r.find(function(s){return s&&s.index===n});if(a){if(e==="horizontal")return{x:a.coordinate,y:i.y};if(e==="vertical")return{x:i.x,y:a.coordinate};if(e==="centric"){var o=a.coordinate,u=i.radius;return _(_(_({},i),re(i.cx,i.cy,u,o)),{},{angle:o,radius:u})}var c=a.coordinate,l=i.angle;return _(_(_({},i),re(i.cx,i.cy,c,l)),{},{angle:l,radius:c})}return sf},Pi=function(e,r){var n=r.graphicalItems,i=r.dataStartIndex,a=r.dataEndIndex,o=(n??[]).reduce(function(u,c){var l=c.props.data;return l&&l.length?[].concat(Zt(u),Zt(l)):u},[]);return o.length>0?o:e&&e.length&&N(i)&&N(a)?e.slice(i,a+1):[]};function ff(t){return t==="number"?[0,"auto"]:void 0}var Ua=function(e,r,n,i){var a=e.graphicalItems,o=e.tooltipAxis,u=Pi(r,e);return n<0||!a||!a.length||n>=u.length?null:a.reduce(function(c,l){var s,f=(s=l.props.data)!==null&&s!==void 0?s:r;f&&e.dataStartIndex+e.dataEndIndex!==0&&e.dataEndIndex-e.dataStartIndex>=n&&(f=f.slice(e.dataStartIndex,e.dataEndIndex+1));var p;if(o.dataKey&&!o.allowDuplicatedCategory){var d=f===void 0?u:f;p=Wi(d,o.dataKey,i)}else p=f&&f[n]||u[n];return p?[].concat(Zt(c),[rs(l,p)]):c},[])},al=function(e,r,n,i){var a=i||{x:e.chartX,y:e.chartY},o=YA(a,n),u=e.orderedTooltipTicks,c=e.tooltipAxis,l=e.tooltipTicks,s=Py(o,u,l,c);if(s>=0&&l){var f=l[s]&&l[s].value,p=Ua(e,r,s,f),d=ZA(n,u,s,a);return{activeTooltipIndex:s,activeLabel:f,activePayload:p,activeCoordinate:d}}return null},JA=function(e,r){var n=r.axes,i=r.graphicalItems,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,l=r.dataEndIndex,s=e.layout,f=e.children,p=e.stackOffset,d=Yl(s,a);return n.reduce(function(m,y){var v,b=y.type.defaultProps!==void 0?_(_({},y.type.defaultProps),y.props):y.props,O=b.type,x=b.dataKey,w=b.allowDataOverflow,h=b.allowDuplicatedCategory,g=b.scale,A=b.ticks,S=b.includeHidden,j=b[o];if(m[j])return m;var T=Pi(e.data,{graphicalItems:i.filter(function(L){var V,ae=o in L.props?L.props[o]:(V=L.type.defaultProps)===null||V===void 0?void 0:V[o];return ae===j}),dataStartIndex:c,dataEndIndex:l}),E=T.length,$,C,k;SA(b.domain,w,O)&&($=sa(b.domain,null,w),d&&(O==="number"||g!=="auto")&&(k=mr(T,x,"category")));var I=ff(O);if(!$||$.length===0){var M,D=(M=b.domain)!==null&&M!==void 0?M:I;if(x){if($=mr(T,x,O),O==="category"&&d){var R=Lp($);h&&R?(C=$,$=Dn(0,E)):h||($=Pu(D,$,y).reduce(function(L,V){return L.indexOf(V)>=0?L:[].concat(Zt(L),[V])},[]))}else if(O==="category")h?$=$.filter(function(L){return L!==""&&!Y(L)}):$=Pu(D,$,y).reduce(function(L,V){return L.indexOf(V)>=0||V===""||Y(V)?L:[].concat(Zt(L),[V])},[]);else if(O==="number"){var W=_y(T,i.filter(function(L){var V,ae,fe=o in L.props?L.props[o]:(V=L.type.defaultProps)===null||V===void 0?void 0:V[o],ke="hide"in L.props?L.props.hide:(ae=L.type.defaultProps)===null||ae===void 0?void 0:ae.hide;return fe===j&&(S||!ke)}),x,a,s);W&&($=W)}d&&(O==="number"||g!=="auto")&&(k=mr(T,x,"category"))}else d?$=Dn(0,E):u&&u[j]&&u[j].hasStack&&O==="number"?$=p==="expand"?[0,1]:ts(u[j].stackGroups,c,l):$=Ul(T,i.filter(function(L){var V=o in L.props?L.props[o]:L.type.defaultProps[o],ae="hide"in L.props?L.props.hide:L.type.defaultProps.hide;return V===j&&(S||!ae)}),O,s,!0);if(O==="number")$=Va(f,$,j,a,A),D&&($=sa(D,$,w));else if(O==="category"&&D){var F=D,q=$.every(function(L){return F.indexOf(L)>=0});q&&($=F)}}return _(_({},m),{},K({},j,_(_({},b),{},{axisType:a,domain:$,categoricalDomain:k,duplicateDomain:C,originalDomain:(v=b.domain)!==null&&v!==void 0?v:I,isCategorical:d,layout:s})))},{})},QA=function(e,r){var n=r.graphicalItems,i=r.Axis,a=r.axisType,o=r.axisIdKey,u=r.stackGroups,c=r.dataStartIndex,l=r.dataEndIndex,s=e.layout,f=e.children,p=Pi(e.data,{graphicalItems:n,dataStartIndex:c,dataEndIndex:l}),d=p.length,m=Yl(s,a),y=-1;return n.reduce(function(v,b){var O=b.type.defaultProps!==void 0?_(_({},b.type.defaultProps),b.props):b.props,x=O[o],w=ff("number");if(!v[x]){y++;var h;return m?h=Dn(0,d):u&&u[x]&&u[x].hasStack?(h=ts(u[x].stackGroups,c,l),h=Va(f,h,x,a)):(h=sa(w,Ul(p,n.filter(function(g){var A,S,j=o in g.props?g.props[o]:(A=g.type.defaultProps)===null||A===void 0?void 0:A[o],T="hide"in g.props?g.props.hide:(S=g.type.defaultProps)===null||S===void 0?void 0:S.hide;return j===x&&!T}),"number",s),i.defaultProps.allowDataOverflow),h=Va(f,h,x,a)),_(_({},v),{},K({},x,_(_({axisType:a},i.defaultProps),{},{hide:!0,orientation:Se(HA,"".concat(a,".").concat(y%2),null),domain:h,originalDomain:w,isCategorical:m,layout:s})))}return v},{})},eP=function(e,r){var n=r.axisType,i=n===void 0?"xAxis":n,a=r.AxisComp,o=r.graphicalItems,u=r.stackGroups,c=r.dataStartIndex,l=r.dataEndIndex,s=e.children,f="".concat(i,"Id"),p=_e(s,a),d={};return p&&p.length?d=JA(e,{axes:p,graphicalItems:o,axisType:i,axisIdKey:f,stackGroups:u,dataStartIndex:c,dataEndIndex:l}):o&&o.length&&(d=QA(e,{Axis:a,graphicalItems:o,axisType:i,axisIdKey:f,stackGroups:u,dataStartIndex:c,dataEndIndex:l})),d},tP=function(e){var r=Ue(e),n=Fe(r,!1,!0);return{tooltipTicks:n,orderedTooltipTicks:Za(n,function(i){return i.coordinate}),tooltipAxis:r,tooltipAxisBandSize:An(r,n)}},ol=function(e){var r=e.children,n=e.defaultShowTooltip,i=Ae(r,Ft),a=0,o=0;return e.data&&e.data.length!==0&&(o=e.data.length-1),i&&i.props&&(i.props.startIndex>=0&&(a=i.props.startIndex),i.props.endIndex>=0&&(o=i.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:a,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!n}},rP=function(e){return!e||!e.length?!1:e.some(function(r){var n=Ke(r&&r.type);return n&&n.indexOf("Bar")>=0})},ul=function(e){return e==="horizontal"?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:e==="vertical"?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:e==="centric"?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},nP=function(e,r){var n=e.props,i=e.graphicalItems,a=e.xAxisMap,o=a===void 0?{}:a,u=e.yAxisMap,c=u===void 0?{}:u,l=n.width,s=n.height,f=n.children,p=n.margin||{},d=Ae(f,Ft),m=Ae(f,Tt),y=Object.keys(c).reduce(function(h,g){var A=c[g],S=A.orientation;return!A.mirror&&!A.hide?_(_({},h),{},K({},S,h[S]+A.width)):h},{left:p.left||0,right:p.right||0}),v=Object.keys(o).reduce(function(h,g){var A=o[g],S=A.orientation;return!A.mirror&&!A.hide?_(_({},h),{},K({},S,Se(h,"".concat(S))+A.height)):h},{top:p.top||0,bottom:p.bottom||0}),b=_(_({},v),y),O=b.bottom;d&&(b.bottom+=d.props.height||Ft.defaultProps.height),m&&r&&(b=Ey(b,i,n,r));var x=l-b.left-b.right,w=s-b.top-b.bottom;return _(_({brushBottom:O},b),{},{width:Math.max(x,0),height:Math.max(w,0)})},iP=function(e,r){if(r==="xAxis")return e[r].width;if(r==="yAxis")return e[r].height},pf=function(e){var r=e.chartName,n=e.GraphicalChild,i=e.defaultTooltipEventType,a=i===void 0?"axis":i,o=e.validateTooltipEventTypes,u=o===void 0?["axis"]:o,c=e.axisComponents,l=e.legendContent,s=e.formatAxisMap,f=e.defaultProps,p=function(b,O){var x=O.graphicalItems,w=O.stackGroups,h=O.offset,g=O.updateId,A=O.dataStartIndex,S=O.dataEndIndex,j=b.barSize,T=b.layout,E=b.barGap,$=b.barCategoryGap,C=b.maxBarSize,k=ul(T),I=k.numericAxisName,M=k.cateAxisName,D=rP(x),R=[];return x.forEach(function(W,F){var q=Pi(b.data,{graphicalItems:[W],dataStartIndex:A,dataEndIndex:S}),L=W.type.defaultProps!==void 0?_(_({},W.type.defaultProps),W.props):W.props,V=L.dataKey,ae=L.maxBarSize,fe=L["".concat(I,"Id")],ke=L["".concat(M,"Id")],ir={},je=c.reduce(function(nt,it){var Si=O["".concat(it.axisType,"Map")],Po=L["".concat(it.axisType,"Id")];Si&&Si[Po]||it.axisType==="zAxis"||dt(!1);var So=Si[Po];return _(_({},nt),{},K(K({},it.axisType,So),"".concat(it.axisType,"Ticks"),Fe(So)))},ir),tt=je[M],en=je["".concat(M,"Ticks")],gt=w&&w[fe]&&w[fe].hasStack&&Ry(W,w[fe].stackGroups),ar=Ke(W.type).indexOf("Bar")>=0,rt=An(tt,en),bt=[],or=D&&Sy({barSize:j,stackGroups:w,totalSize:iP(je,M)});if(ar){var ur,Ot,cr=Y(ae)?C:ae,xt=(ur=(Ot=An(tt,en,!0))!==null&&Ot!==void 0?Ot:cr)!==null&&ur!==void 0?ur:0;bt=jy({barGap:E,barCategoryGap:$,bandSize:xt!==rt?xt:rt,sizeList:or[ke],maxBarSize:cr}),xt!==rt&&(bt=bt.map(function(nt){return _(_({},nt),{},{position:_(_({},nt.position),{},{offset:nt.position.offset-xt/2})})}))}var tn=W&&W.type&&W.type.getComposedData;tn&&R.push({props:_(_({},tn(_(_({},je),{},{displayedData:q,props:b,dataKey:V,item:W,bandSize:rt,barPosition:bt,offset:h,stackedData:gt,layout:T,dataStartIndex:A,dataEndIndex:S}))),{},K(K(K({key:W.key||"item-".concat(F)},I,je[I]),M,je[M]),"animationId",g)),childIndex:Yp(W,b.children),item:W})}),R},d=function(b,O){var x=b.props,w=b.dataStartIndex,h=b.dataEndIndex,g=b.updateId;if(!Io({props:x}))return null;var A=x.children,S=x.layout,j=x.stackOffset,T=x.data,E=x.reverseStackOrder,$=ul(S),C=$.numericAxisName,k=$.cateAxisName,I=_e(A,n),M=Ny(T,I,"".concat(C,"Id"),"".concat(k,"Id"),j,E),D=c.reduce(function(L,V){var ae="".concat(V.axisType,"Map");return _(_({},L),{},K({},ae,eP(x,_(_({},V),{},{graphicalItems:I,stackGroups:V.axisType===C&&M,dataStartIndex:w,dataEndIndex:h}))))},{}),R=nP(_(_({},D),{},{props:x,graphicalItems:I}),O==null?void 0:O.legendBBox);Object.keys(D).forEach(function(L){D[L]=s(x,D[L],R,L.replace("Map",""),r)});var W=D["".concat(k,"Map")],F=tP(W),q=p(x,_(_({},D),{},{dataStartIndex:w,dataEndIndex:h,updateId:g,graphicalItems:I,stackGroups:M,offset:R}));return _(_({formattedGraphicalItems:q,graphicalItems:I,offset:R,stackGroups:M},F),D)},m=function(v){function b(O){var x,w,h;return RA(this,b),h=zA(this,b,[O]),K(h,"eventEmitterSymbol",Symbol("rechartsEventEmitter")),K(h,"accessibilityManager",new PA),K(h,"handleLegendBBoxUpdate",function(g){if(g){var A=h.state,S=A.dataStartIndex,j=A.dataEndIndex,T=A.updateId;h.setState(_({legendBBox:g},d({props:h.props,dataStartIndex:S,dataEndIndex:j,updateId:T},_(_({},h.state),{},{legendBBox:g}))))}}),K(h,"handleReceiveSyncEvent",function(g,A,S){if(h.props.syncId===g){if(S===h.eventEmitterSymbol&&typeof h.props.syncMethod!="function")return;h.applySyncEvent(A)}}),K(h,"handleBrushChange",function(g){var A=g.startIndex,S=g.endIndex;if(A!==h.state.dataStartIndex||S!==h.state.dataEndIndex){var j=h.state.updateId;h.setState(function(){return _({dataStartIndex:A,dataEndIndex:S},d({props:h.props,dataStartIndex:A,dataEndIndex:S,updateId:j},h.state))}),h.triggerSyncEvent({dataStartIndex:A,dataEndIndex:S})}}),K(h,"handleMouseEnter",function(g){var A=h.getMouseInfo(g);if(A){var S=_(_({},A),{},{isTooltipActive:!0});h.setState(S),h.triggerSyncEvent(S);var j=h.props.onMouseEnter;X(j)&&j(S,g)}}),K(h,"triggeredAfterMouseMove",function(g){var A=h.getMouseInfo(g),S=A?_(_({},A),{},{isTooltipActive:!0}):{isTooltipActive:!1};h.setState(S),h.triggerSyncEvent(S);var j=h.props.onMouseMove;X(j)&&j(S,g)}),K(h,"handleItemMouseEnter",function(g){h.setState(function(){return{isTooltipActive:!0,activeItem:g,activePayload:g.tooltipPayload,activeCoordinate:g.tooltipPosition||{x:g.cx,y:g.cy}}})}),K(h,"handleItemMouseLeave",function(){h.setState(function(){return{isTooltipActive:!1}})}),K(h,"handleMouseMove",function(g){g.persist(),h.throttleTriggeredAfterMouseMove(g)}),K(h,"handleMouseLeave",function(g){h.throttleTriggeredAfterMouseMove.cancel();var A={isTooltipActive:!1};h.setState(A),h.triggerSyncEvent(A);var S=h.props.onMouseLeave;X(S)&&S(A,g)}),K(h,"handleOuterEvent",function(g){var A=Up(g),S=Se(h.props,"".concat(A));if(A&&X(S)){var j,T;/.*touch.*/i.test(A)?T=h.getMouseInfo(g.changedTouches[0]):T=h.getMouseInfo(g),S((j=T)!==null&&j!==void 0?j:{},g)}}),K(h,"handleClick",function(g){var A=h.getMouseInfo(g);if(A){var S=_(_({},A),{},{isTooltipActive:!0});h.setState(S),h.triggerSyncEvent(S);var j=h.props.onClick;X(j)&&j(S,g)}}),K(h,"handleMouseDown",function(g){var A=h.props.onMouseDown;if(X(A)){var S=h.getMouseInfo(g);A(S,g)}}),K(h,"handleMouseUp",function(g){var A=h.props.onMouseUp;if(X(A)){var S=h.getMouseInfo(g);A(S,g)}}),K(h,"handleTouchMove",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&h.throttleTriggeredAfterMouseMove(g.changedTouches[0])}),K(h,"handleTouchStart",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&h.handleMouseDown(g.changedTouches[0])}),K(h,"handleTouchEnd",function(g){g.changedTouches!=null&&g.changedTouches.length>0&&h.handleMouseUp(g.changedTouches[0])}),K(h,"triggerSyncEvent",function(g){h.props.syncId!==void 0&&Ni.emit(Bi,h.props.syncId,g,h.eventEmitterSymbol)}),K(h,"applySyncEvent",function(g){var A=h.props,S=A.layout,j=A.syncMethod,T=h.state.updateId,E=g.dataStartIndex,$=g.dataEndIndex;if(g.dataStartIndex!==void 0||g.dataEndIndex!==void 0)h.setState(_({dataStartIndex:E,dataEndIndex:$},d({props:h.props,dataStartIndex:E,dataEndIndex:$,updateId:T},h.state)));else if(g.activeTooltipIndex!==void 0){var C=g.chartX,k=g.chartY,I=g.activeTooltipIndex,M=h.state,D=M.offset,R=M.tooltipTicks;if(!D)return;if(typeof j=="function")I=j(R,g);else if(j==="value"){I=-1;for(var W=0;W<R.length;W++)if(R[W].value===g.activeLabel){I=W;break}}var F=_(_({},D),{},{x:D.left,y:D.top}),q=Math.min(C,F.x+F.width),L=Math.min(k,F.y+F.height),V=R[I]&&R[I].value,ae=Ua(h.state,h.props.data,I),fe=R[I]?{x:S==="horizontal"?R[I].coordinate:q,y:S==="horizontal"?L:R[I].coordinate}:sf;h.setState(_(_({},g),{},{activeLabel:V,activeCoordinate:fe,activePayload:ae,activeTooltipIndex:I}))}else h.setState(g)}),K(h,"renderCursor",function(g){var A,S=h.state,j=S.isTooltipActive,T=S.activeCoordinate,E=S.activePayload,$=S.offset,C=S.activeTooltipIndex,k=S.tooltipAxisBandSize,I=h.getTooltipEventType(),M=(A=g.props.active)!==null&&A!==void 0?A:j,D=h.props.layout,R=g.key||"_recharts-cursor";return P.createElement(IA,{key:R,activeCoordinate:T,activePayload:E,activeTooltipIndex:C,chartName:r,element:g,isActive:M,layout:D,offset:$,tooltipAxisBandSize:k,tooltipEventType:I})}),K(h,"renderPolarAxis",function(g,A,S){var j=Se(g,"type.axisType"),T=Se(h.state,"".concat(j,"Map")),E=g.type.defaultProps,$=E!==void 0?_(_({},E),g.props):g.props,C=T&&T[$["".concat(j,"Id")]];return B.cloneElement(g,_(_({},C),{},{className:U(j,C.className),key:g.key||"".concat(A,"-").concat(S),ticks:Fe(C,!0)}))}),K(h,"renderPolarGrid",function(g){var A=g.props,S=A.radialLines,j=A.polarAngles,T=A.polarRadius,E=h.state,$=E.radiusAxisMap,C=E.angleAxisMap,k=Ue($),I=Ue(C),M=I.cx,D=I.cy,R=I.innerRadius,W=I.outerRadius;return B.cloneElement(g,{polarAngles:Array.isArray(j)?j:Fe(I,!0).map(function(F){return F.coordinate}),polarRadius:Array.isArray(T)?T:Fe(k,!0).map(function(F){return F.coordinate}),cx:M,cy:D,innerRadius:R,outerRadius:W,key:g.key||"polar-grid",radialLines:S})}),K(h,"renderLegend",function(){var g=h.state.formattedGraphicalItems,A=h.props,S=A.children,j=A.width,T=A.height,E=h.props.margin||{},$=j-(E.left||0)-(E.right||0),C=Xl({children:S,formattedGraphicalItems:g,legendWidth:$,legendContent:l});if(!C)return null;var k=C.item,I=rl(C,kA);return B.cloneElement(k,_(_({},I),{},{chartWidth:j,chartHeight:T,margin:E,onBBoxUpdate:h.handleLegendBBoxUpdate}))}),K(h,"renderTooltip",function(){var g,A=h.props,S=A.children,j=A.accessibilityLayer,T=Ae(S,ze);if(!T)return null;var E=h.state,$=E.isTooltipActive,C=E.activeCoordinate,k=E.activePayload,I=E.activeLabel,M=E.offset,D=(g=T.props.active)!==null&&g!==void 0?g:$;return B.cloneElement(T,{viewBox:_(_({},M),{},{x:M.left,y:M.top}),active:D,label:I,payload:D?k:[],coordinate:C,accessibilityLayer:j})}),K(h,"renderBrush",function(g){var A=h.props,S=A.margin,j=A.data,T=h.state,E=T.offset,$=T.dataStartIndex,C=T.dataEndIndex,k=T.updateId;return B.cloneElement(g,{key:g.key||"_recharts-brush",onChange:un(h.handleBrushChange,g.props.onChange),data:j,x:N(g.props.x)?g.props.x:E.left,y:N(g.props.y)?g.props.y:E.top+E.height+E.brushBottom-(S.bottom||0),width:N(g.props.width)?g.props.width:E.width,startIndex:$,endIndex:C,updateId:"brush-".concat(k)})}),K(h,"renderReferenceElement",function(g,A,S){if(!g)return null;var j=h,T=j.clipPathId,E=h.state,$=E.xAxisMap,C=E.yAxisMap,k=E.offset,I=g.type.defaultProps||{},M=g.props,D=M.xAxisId,R=D===void 0?I.xAxisId:D,W=M.yAxisId,F=W===void 0?I.yAxisId:W;return B.cloneElement(g,{key:g.key||"".concat(A,"-").concat(S),xAxis:$[R],yAxis:C[F],viewBox:{x:k.left,y:k.top,width:k.width,height:k.height},clipPathId:T})}),K(h,"renderActivePoints",function(g){var A=g.item,S=g.activePoint,j=g.basePoint,T=g.childIndex,E=g.isRange,$=[],C=A.props.key,k=A.item.type.defaultProps!==void 0?_(_({},A.item.type.defaultProps),A.item.props):A.item.props,I=k.activeDot,M=k.dataKey,D=_(_({index:T,dataKey:M,cx:S.x,cy:S.y,r:4,fill:lo(A.item),strokeWidth:2,stroke:"#fff",payload:S.payload,value:S.value},G(I,!1)),vn(I));return $.push(b.renderActiveDot(I,D,"".concat(C,"-activePoint-").concat(T))),j?$.push(b.renderActiveDot(I,_(_({},D),{},{cx:j.x,cy:j.y}),"".concat(C,"-basePoint-").concat(T))):E&&$.push(null),$}),K(h,"renderGraphicChild",function(g,A,S){var j=h.filterFormatItem(g,A,S);if(!j)return null;var T=h.getTooltipEventType(),E=h.state,$=E.isTooltipActive,C=E.tooltipAxis,k=E.activeTooltipIndex,I=E.activeLabel,M=h.props.children,D=Ae(M,ze),R=j.props,W=R.points,F=R.isRange,q=R.baseLine,L=j.item.type.defaultProps!==void 0?_(_({},j.item.type.defaultProps),j.item.props):j.item.props,V=L.activeDot,ae=L.hide,fe=L.activeBar,ke=L.activeShape,ir=!!(!ae&&$&&D&&(V||fe||ke)),je={};T!=="axis"&&D&&D.props.trigger==="click"?je={onClick:un(h.handleItemMouseEnter,g.props.onClick)}:T!=="axis"&&(je={onMouseLeave:un(h.handleItemMouseLeave,g.props.onMouseLeave),onMouseEnter:un(h.handleItemMouseEnter,g.props.onMouseEnter)});var tt=B.cloneElement(g,_(_({},j.props),je));function en(it){return typeof C.dataKey=="function"?C.dataKey(it.payload):null}if(ir)if(k>=0){var gt,ar;if(C.dataKey&&!C.allowDuplicatedCategory){var rt=typeof C.dataKey=="function"?en:"payload.".concat(C.dataKey.toString());gt=Wi(W,rt,I),ar=F&&q&&Wi(q,rt,I)}else gt=W==null?void 0:W[k],ar=F&&q&&q[k];if(ke||fe){var bt=g.props.activeIndex!==void 0?g.props.activeIndex:k;return[B.cloneElement(g,_(_(_({},j.props),je),{},{activeIndex:bt})),null,null]}if(!Y(gt))return[tt].concat(Zt(h.renderActivePoints({item:j,activePoint:gt,basePoint:ar,childIndex:k,isRange:F})))}else{var or,ur=(or=h.getItemByXY(h.state.activeCoordinate))!==null&&or!==void 0?or:{graphicalItem:tt},Ot=ur.graphicalItem,cr=Ot.item,xt=cr===void 0?g:cr,tn=Ot.childIndex,nt=_(_(_({},j.props),je),{},{activeIndex:tn});return[B.cloneElement(xt,nt),null,null]}return F?[tt,null,null]:[tt,null]}),K(h,"renderCustomized",function(g,A,S){return B.cloneElement(g,_(_({key:"recharts-customized-".concat(S)},h.props),h.state))}),K(h,"renderMap",{CartesianGrid:{handler:pn,once:!0},ReferenceArea:{handler:h.renderReferenceElement},ReferenceLine:{handler:pn},ReferenceDot:{handler:h.renderReferenceElement},XAxis:{handler:pn},YAxis:{handler:pn},Brush:{handler:h.renderBrush,once:!0},Bar:{handler:h.renderGraphicChild},Line:{handler:h.renderGraphicChild},Area:{handler:h.renderGraphicChild},Radar:{handler:h.renderGraphicChild},RadialBar:{handler:h.renderGraphicChild},Scatter:{handler:h.renderGraphicChild},Pie:{handler:h.renderGraphicChild},Funnel:{handler:h.renderGraphicChild},Tooltip:{handler:h.renderCursor,once:!0},PolarGrid:{handler:h.renderPolarGrid,once:!0},PolarAngleAxis:{handler:h.renderPolarAxis},PolarRadiusAxis:{handler:h.renderPolarAxis},Customized:{handler:h.renderCustomized}}),h.clipPathId="".concat((x=O.id)!==null&&x!==void 0?x:Qr("recharts"),"-clip"),h.throttleTriggeredAfterMouseMove=cl(h.triggeredAfterMouseMove,(w=O.throttleDelay)!==null&&w!==void 0?w:1e3/60),h.state={},h}return KA(b,v),LA(b,[{key:"componentDidMount",value:function(){var x,w;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:(x=this.props.margin.left)!==null&&x!==void 0?x:0,top:(w=this.props.margin.top)!==null&&w!==void 0?w:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var x=this.props,w=x.children,h=x.data,g=x.height,A=x.layout,S=Ae(w,ze);if(S){var j=S.props.defaultIndex;if(!(typeof j!="number"||j<0||j>this.state.tooltipTicks.length-1)){var T=this.state.tooltipTicks[j]&&this.state.tooltipTicks[j].value,E=Ua(this.state,h,j,T),$=this.state.tooltipTicks[j].coordinate,C=(this.state.offset.top+g)/2,k=A==="horizontal",I=k?{x:$,y:C}:{y:$,x:C},M=this.state.formattedGraphicalItems.find(function(R){var W=R.item;return W.type.name==="Scatter"});M&&(I=_(_({},I),M.props.points[j].tooltipPosition),E=M.props.points[j].tooltipPayload);var D={activeTooltipIndex:j,isTooltipActive:!0,activeLabel:T,activePayload:E,activeCoordinate:I};this.setState(D),this.renderCursor(S),this.accessibilityManager.setIndex(j)}}}},{key:"getSnapshotBeforeUpdate",value:function(x,w){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==w.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==x.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==x.margin){var h,g;this.accessibilityManager.setDetails({offset:{left:(h=this.props.margin.left)!==null&&h!==void 0?h:0,top:(g=this.props.margin.top)!==null&&g!==void 0?g:0}})}return null}},{key:"componentDidUpdate",value:function(x){Ki([Ae(x.children,ze)],[Ae(this.props.children,ze)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var x=Ae(this.props.children,ze);if(x&&typeof x.props.shared=="boolean"){var w=x.props.shared?"axis":"item";return u.indexOf(w)>=0?w:a}return a}},{key:"getMouseInfo",value:function(x){if(!this.container)return null;var w=this.container,h=w.getBoundingClientRect(),g=Tv(h),A={chartX:Math.round(x.pageX-g.left),chartY:Math.round(x.pageY-g.top)},S=h.width/w.offsetWidth||1,j=this.inRange(A.chartX,A.chartY,S);if(!j)return null;var T=this.state,E=T.xAxisMap,$=T.yAxisMap,C=this.getTooltipEventType();if(C!=="axis"&&E&&$){var k=Ue(E).scale,I=Ue($).scale,M=k&&k.invert?k.invert(A.chartX):null,D=I&&I.invert?I.invert(A.chartY):null;return _(_({},A),{},{xValue:M,yValue:D})}var R=al(this.state,this.props.data,this.props.layout,j);return R?_(_({},A),R):null}},{key:"inRange",value:function(x,w){var h=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,g=this.props.layout,A=x/h,S=w/h;if(g==="horizontal"||g==="vertical"){var j=this.state.offset,T=A>=j.left&&A<=j.left+j.width&&S>=j.top&&S<=j.top+j.height;return T?{x:A,y:S}:null}var E=this.state,$=E.angleAxisMap,C=E.radiusAxisMap;if($&&C){var k=Ue($);return Eu({x:A,y:S},k)}return null}},{key:"parseEventsOfWrapper",value:function(){var x=this.props.children,w=this.getTooltipEventType(),h=Ae(x,ze),g={};h&&w==="axis"&&(h.props.trigger==="click"?g={onClick:this.handleClick}:g={onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd});var A=vn(this.props,this.handleOuterEvent);return _(_({},A),g)}},{key:"addListener",value:function(){Ni.on(Bi,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){Ni.removeListener(Bi,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(x,w,h){for(var g=this.state.formattedGraphicalItems,A=0,S=g.length;A<S;A++){var j=g[A];if(j.item===x||j.props.key===x.key||w===Ke(j.item.type)&&h===j.childIndex)return j}return null}},{key:"renderClipPath",value:function(){var x=this.clipPathId,w=this.state.offset,h=w.left,g=w.top,A=w.height,S=w.width;return P.createElement("defs",null,P.createElement("clipPath",{id:x},P.createElement("rect",{x:h,y:g,height:A,width:S})))}},{key:"getXScales",value:function(){var x=this.state.xAxisMap;return x?Object.entries(x).reduce(function(w,h){var g=tl(h,2),A=g[0],S=g[1];return _(_({},w),{},K({},A,S.scale))},{}):null}},{key:"getYScales",value:function(){var x=this.state.yAxisMap;return x?Object.entries(x).reduce(function(w,h){var g=tl(h,2),A=g[0],S=g[1];return _(_({},w),{},K({},A,S.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(x){var w;return(w=this.state.xAxisMap)===null||w===void 0||(w=w[x])===null||w===void 0?void 0:w.scale}},{key:"getYScaleByAxisId",value:function(x){var w;return(w=this.state.yAxisMap)===null||w===void 0||(w=w[x])===null||w===void 0?void 0:w.scale}},{key:"getItemByXY",value:function(x){var w=this.state,h=w.formattedGraphicalItems,g=w.activeItem;if(h&&h.length)for(var A=0,S=h.length;A<S;A++){var j=h[A],T=j.props,E=j.item,$=E.type.defaultProps!==void 0?_(_({},E.type.defaultProps),E.props):E.props,C=Ke(E.type);if(C==="Bar"){var k=(T.data||[]).find(function(R){return hb(x,R)});if(k)return{graphicalItem:j,payload:k}}else if(C==="RadialBar"){var I=(T.data||[]).find(function(R){return Eu(x,R)});if(I)return{graphicalItem:j,payload:I}}else if(mi(j,g)||gi(j,g)||Gr(j,g)){var M=KO({graphicalItem:j,activeTooltipItem:g,itemData:$.data}),D=$.activeIndex===void 0?M:$.activeIndex;return{graphicalItem:_(_({},j),{},{childIndex:D}),payload:Gr(j,g)?$.data[M]:j.props.data[M]}}}return null}},{key:"render",value:function(){var x=this;if(!Io(this))return null;var w=this.props,h=w.children,g=w.className,A=w.width,S=w.height,j=w.style,T=w.compact,E=w.title,$=w.desc,C=rl(w,CA),k=G(C,!1);if(T)return P.createElement(Mc,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},P.createElement(qi,$t({},k,{width:A,height:S,title:E,desc:$}),this.renderClipPath(),Co(h,this.renderMap)));if(this.props.accessibilityLayer){var I,M;k.tabIndex=(I=this.props.tabIndex)!==null&&I!==void 0?I:0,k.role=(M=this.props.role)!==null&&M!==void 0?M:"application",k.onKeyDown=function(R){x.accessibilityManager.keyboardEvent(R)},k.onFocus=function(){x.accessibilityManager.focus()}}var D=this.parseEventsOfWrapper();return P.createElement(Mc,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},P.createElement("div",$t({className:U("recharts-wrapper",g),style:_({position:"relative",cursor:"default",width:A,height:S},j)},D,{ref:function(W){x.container=W}}),P.createElement(qi,$t({},k,{width:A,height:S,title:E,desc:$,style:UA}),this.renderClipPath(),Co(h,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}])}(B.Component);K(m,"displayName",r),K(m,"defaultProps",_({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},f)),K(m,"getDerivedStateFromProps",function(v,b){var O=v.dataKey,x=v.data,w=v.children,h=v.width,g=v.height,A=v.layout,S=v.stackOffset,j=v.margin,T=b.dataStartIndex,E=b.dataEndIndex;if(b.updateId===void 0){var $=ol(v);return _(_(_({},$),{},{updateId:0},d(_(_({props:v},$),{},{updateId:0}),b)),{},{prevDataKey:O,prevData:x,prevWidth:h,prevHeight:g,prevLayout:A,prevStackOffset:S,prevMargin:j,prevChildren:w})}if(O!==b.prevDataKey||x!==b.prevData||h!==b.prevWidth||g!==b.prevHeight||A!==b.prevLayout||S!==b.prevStackOffset||!_t(j,b.prevMargin)){var C=ol(v),k={chartX:b.chartX,chartY:b.chartY,isTooltipActive:b.isTooltipActive},I=_(_({},al(b,x,A)),{},{updateId:b.updateId+1}),M=_(_(_({},C),k),I);return _(_(_({},M),d(_({props:v},M),b)),{},{prevDataKey:O,prevData:x,prevWidth:h,prevHeight:g,prevLayout:A,prevStackOffset:S,prevMargin:j,prevChildren:w})}if(!Ki(w,b.prevChildren)){var D,R,W,F,q=Ae(w,Ft),L=q&&(D=(R=q.props)===null||R===void 0?void 0:R.startIndex)!==null&&D!==void 0?D:T,V=q&&(W=(F=q.props)===null||F===void 0?void 0:F.endIndex)!==null&&W!==void 0?W:E,ae=L!==T||V!==E,fe=!Y(x),ke=fe&&!ae?b.updateId:b.updateId+1;return _(_({updateId:ke},d(_(_({props:v},b),{},{updateId:ke,dataStartIndex:L,dataEndIndex:V}),b)),{},{prevChildren:w,dataStartIndex:L,dataEndIndex:V})}return null}),K(m,"renderActiveDot",function(v,b,O){var x;return B.isValidElement(v)?x=B.cloneElement(v,b):X(v)?x=v(b):x=P.createElement(fo,b),P.createElement(Z,{className:"recharts-active-dot",key:O},x)});var y=B.forwardRef(function(b,O){return P.createElement(m,$t({},b,{ref:O}))});return y.displayName=m.displayName,y},pP=pf({chartName:"BarChart",GraphicalChild:rr,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:wo},{axisType:"yAxis",AxisComp:Ao}],formatAxisMap:y0}),dP=pf({chartName:"PieChart",GraphicalChild:et,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:yi},{axisType:"radiusAxis",AxisComp:vi}],formatAxisMap:Hy,defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});export{pP as B,oo as C,Tt as L,dP as P,fP as R,ze as T,wo as X,Ao as Y,et as a,Jw as b,rr as c};
