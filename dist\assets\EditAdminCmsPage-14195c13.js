import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as q}from"./vendor-851db8c1.js";import{u as F}from"./react-hook-form-687afde5.js";import{o as V}from"./yup-2824f222.js";import{c as G,a as o}from"./yup-54691517.js";import{M as A,A as M,G as R,t as _,l as K,b as H,c as O,d as z}from"./index-08a5dc5b.js";import{D as B}from"./index-4464771d.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let I=new A;const Ae=({activeId:p,setSidebar:d})=>{var w,C,N;const P=G({page:o().required(),key:o().required(),type:o().required(),value:o()}).required(),u=[{key:"text",value:"Text"},{key:"image",value:"Image"},{key:"number",value:"Number"},{key:"kvp",value:"Key-Value Pair"},{key:"image-list",value:"Image List"},{key:"captioned-image-list",value:"Captioned Image List"},{key:"team-list",value:"Team List"}],{dispatch:y}=a.useContext(M),{dispatch:g}=a.useContext(R),[U,D]=a.useState((w=u[0])==null?void 0:w.key),[r,x]=a.useState(""),[f,L]=a.useState(""),[b,i]=a.useState(!1),$=q(),{register:l,handleSubmit:h,setError:v,setValue:n,formState:{errors:k}}=F({resolver:V(P)}),j=async e=>{var E;i(!0);let m=new A;i(!0);try{m.setTable("cms");const s=await m.cmsEdit(f,e.page,e.key,e.type,r);if(!s.error)await K(m,{user_id:localStorage.getItem("user"),activity_type:H.club_ui,action_type:O.UPDATE,data:{cms_id:f,page:e.page,key:e.key,type:e.type,old_value:(E=s.model)==null?void 0:E.content_value,new_value:r},club_id:null,description:`Admin updated CMS content: ${e.page}/${e.key}`}),$("/admin/cms"),z(g,"Updated");else if(s.validation){const T=Object.keys(s.validation);for(let c=0;c<T.length;c++){const S=T[c];v(S,{type:"manual",message:s.validation[S]})}}}catch(s){console.log("Error",s),v("page",{type:"manual",message:s.message}),_(y,s.message)}i(!1)};return a.useEffect(()=>{g({type:"SETPATH",payload:{path:"cms"}}),async function(){try{I.setTable("cms");const e=await I.callRestAPI({id:p},"GET");console.log("result: ",e),e.error||(L(e.model.id),n("page",e.model.page),n("type",e.model.content_type),n("key",e.model.content_key),x(e.model.content_value))}catch(e){console.log("Error",e),_(y,e.message)}}()},[p]),t.jsxs("div",{className:"mx-auto rounded",children:[t.jsxs("div",{className:"flex items-center justify-between gap-4 border-b border-b-[#E0E0E0] p-3",children:[t.jsx("div",{className:"flex items-center gap-3",children:t.jsx("span",{className:"text-lg font-semibold",children:"Edit User"})}),t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsx("button",{className:"flex items-center rounded-md border border-[#C6C6C6] px-3 py-2 shadow-sm hover:bg-[#f4f4f4]",onClick:()=>d(!1),children:"Cancel"}),t.jsx("button",{className:"flex items-center rounded-md bg-[#4F46E5] px-3 py-2 text-white shadow-sm",onClick:async()=>{await h(j)(),d(!1)},disabled:b,children:b?"Saving...":"Save"})]})]}),t.jsxs("form",{className:"w-full p-4 text-left",onSubmit:h(j),children:[t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"page",children:"Page"}),t.jsx("input",{type:"text",placeholder:"Page",...l("page"),className:"focus:shadow-outline} mb-3 w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none"})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",htmlFor:"key",children:"Content Identifier"}),t.jsx("input",{type:"text",placeholder:"Content Identifier",...l("key"),className:`focus:shadow-outline w-full appearance-none rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none ${(C=k.key)!=null&&C.message?"border-red-500":""}`}),t.jsx("p",{className:"text-xs italic text-red-500",children:(N=k.key)==null?void 0:N.message})]}),t.jsxs("div",{className:"mb-4",children:[t.jsx("label",{className:"mb-2 block text-sm font-bold text-gray-700",children:"Content Type"}),t.jsx("select",{name:"type",id:"type",className:"focus:shadow-outline mb-3  w-full rounded border px-3 py-2 leading-tight text-gray-700 shadow focus:outline-none",...l("type",{onChange:e=>D(e.target.value)}),children:u.map(e=>t.jsx("option",{name:e.name,value:e.key,children:e.value},e.key))})]}),t.jsx(B,{contentValue:r,contentType:U,setContentValue:x})]})]})};export{Ae as default};
