import{_ as Gr,i as Bn,a as uo,b as Xe,c as rh,d as ic,e as ac,f as Gn,k as oc,g as fo,h as co,j as ih,l as ah,m as sc,n as uc,o as fc,p as oh,q as Xr,r as cc,s as sh,t as lc,u as uh,v as dc,w as fh,x as ch,y as lh,z as hc,A as dh,B as hh,C as gh,D as mh,E as bh,F as ph,G as yh,H as vh,Q as _h,I as xh,J as wh}from"../@craftjs/core-d3c11b68.js";import{r as T,g as Oe,a as Mh}from"../vendor-851db8c1.js";import{P as x}from"../@fortawesome/react-fontawesome-13437837.js";var gc={exports:{}},Qr={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $h=T,Th=Symbol.for("react.element"),Ch=Symbol.for("react.fragment"),kh=Object.prototype.hasOwnProperty,Sh=$h.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Oh={key:!0,ref:!0,__self:!0,__source:!0};function mc(e,t,n){var r,i={},a=null,o=null;n!==void 0&&(a=""+n),t.key!==void 0&&(a=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)kh.call(t,r)&&!Oh.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Th,type:e,key:a,ref:o,props:i,_owner:Sh.current}}Qr.Fragment=Ch;Qr.jsx=mc;Qr.jsxs=mc;gc.exports=Qr;var $=gc.exports,Ah=Gr,Uh=Bn,Ph="[object Symbol]";function Dh(e){return typeof e=="symbol"||Uh(e)&&Ah(e)==Ph}var Zr=Dh,Fh=uo;function Rh(e,t){return Fh(e,t)}var Ih=Rh;const b9=Oe(Ih);var Nh=Xe,Eh=Zr,Yh=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Lh=/^\w*$/;function Wh(e,t){if(Nh(e))return!1;var n=typeof e;return n=="number"||n=="symbol"||n=="boolean"||e==null||Eh(e)?!0:Lh.test(e)||!Yh.test(e)||t!=null&&e in Object(t)}var lo=Wh,bc=rh,jh="Expected a function";function ho(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new TypeError(jh);var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],a=n.cache;if(a.has(i))return a.get(i);var o=e.apply(this,r);return n.cache=a.set(i,o)||a,o};return n.cache=new(ho.Cache||bc),n}ho.Cache=bc;var pc=ho;const p9=Oe(pc);var qh=pc,zh=500;function Hh(e){var t=qh(e,function(r){return n.size===zh&&n.clear(),r}),n=t.cache;return t}var Vh=Hh,Bh=Vh,Gh=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Xh=/\\(\\)?/g,Qh=Bh(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(Gh,function(n,r,i,a){t.push(i?a.replace(Xh,"$1"):r||n)}),t}),Zh=Qh;function Jh(e,t){for(var n=-1,r=e==null?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i}var Jr=Jh,Ys=ic,Kh=Jr,e1=Xe,t1=Zr,n1=1/0,Ls=Ys?Ys.prototype:void 0,Ws=Ls?Ls.toString:void 0;function yc(e){if(typeof e=="string")return e;if(e1(e))return Kh(e,yc)+"";if(t1(e))return Ws?Ws.call(e):"";var t=e+"";return t=="0"&&1/e==-n1?"-0":t}var r1=yc,i1=r1;function a1(e){return e==null?"":i1(e)}var o1=a1,s1=Xe,u1=lo,f1=Zh,c1=o1;function l1(e,t){return s1(e)?e:u1(e,t)?[e]:f1(c1(e))}var Kt=l1,d1=Zr,h1=1/0;function g1(e){if(typeof e=="string"||d1(e))return e;var t=e+"";return t=="0"&&1/e==-h1?"-0":t}var en=g1,m1=Kt,b1=en;function p1(e,t){t=m1(t,e);for(var n=0,r=t.length;e!=null&&n<r;)e=e[b1(t[n++])];return n&&n==r?e:void 0}var Xn=p1,y1=Xn;function v1(e,t,n){var r=e==null?void 0:y1(e,t);return r===void 0?n:r}var vc=v1;const br=Oe(vc);var _1=Gr,x1=Xe,w1=Bn,M1="[object String]";function $1(e){return typeof e=="string"||!x1(e)&&w1(e)&&_1(e)==M1}var T1=$1;const C1=Oe(T1);var k1=Gr,S1=Bn,O1="[object Number]";function A1(e){return typeof e=="number"||S1(e)&&k1(e)==O1}var U1=A1;const js=Oe(U1);function P1(e,t,n){var r=-1,i=e.length;t<0&&(t=-t>i?0:i+t),n=n>i?i:n,n<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var a=Array(i);++r<i;)a[r]=e[r+t];return a}var D1=P1;function ue(e){return function(){return e}}const _c=Math.cos,pr=Math.sin,je=Math.sqrt,qs=1e-12,yr=Math.PI,Kr=2*yr,Sa=Math.PI,Oa=2*Sa,pt=1e-6,F1=Oa-pt;function xc(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=arguments[t]+e[t]}function R1(e){let t=Math.floor(e);if(!(t>=0))throw new Error(`invalid digits: ${e}`);if(t>15)return xc;const n=10**t;return function(r){this._+=r[0];for(let i=1,a=r.length;i<a;++i)this._+=Math.round(arguments[i]*n)/n+r[i]}}class I1{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=t==null?xc:R1(t)}moveTo(t,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}`}closePath(){this._x1!==null&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,n){this._append`L${this._x1=+t},${this._y1=+n}`}quadraticCurveTo(t,n,r,i){this._append`Q${+t},${+n},${this._x1=+r},${this._y1=+i}`}bezierCurveTo(t,n,r,i,a,o){this._append`C${+t},${+n},${+r},${+i},${this._x1=+a},${this._y1=+o}`}arcTo(t,n,r,i,a){if(t=+t,n=+n,r=+r,i=+i,a=+a,a<0)throw new Error(`negative radius: ${a}`);let o=this._x1,s=this._y1,u=r-t,f=i-n,l=o-t,c=s-n,d=l*l+c*c;if(this._x1===null)this._append`M${this._x1=t},${this._y1=n}`;else if(d>pt)if(!(Math.abs(c*u-f*l)>pt)||!a)this._append`L${this._x1=t},${this._y1=n}`;else{let g=r-o,b=i-s,p=u*u+f*f,y=g*g+b*b,v=Math.sqrt(p),O=Math.sqrt(d),_=a*Math.tan((Sa-Math.acos((p+d-y)/(2*v*O)))/2),k=_/O,A=_/v;Math.abs(k-1)>pt&&this._append`L${t+k*l},${n+k*c}`,this._append`A${a},${a},0,0,${+(c*g>l*b)},${this._x1=t+A*u},${this._y1=n+A*f}`}}arc(t,n,r,i,a,o){if(t=+t,n=+n,r=+r,o=!!o,r<0)throw new Error(`negative radius: ${r}`);let s=r*Math.cos(i),u=r*Math.sin(i),f=t+s,l=n+u,c=1^o,d=o?i-a:a-i;this._x1===null?this._append`M${f},${l}`:(Math.abs(this._x1-f)>pt||Math.abs(this._y1-l)>pt)&&this._append`L${f},${l}`,r&&(d<0&&(d=d%Oa+Oa),d>F1?this._append`A${r},${r},0,1,${c},${t-s},${n-u}A${r},${r},0,1,${c},${this._x1=f},${this._y1=l}`:d>pt&&this._append`A${r},${r},0,${+(d>=Sa)},${c},${this._x1=t+r*Math.cos(a)},${this._y1=n+r*Math.sin(a)}`)}rect(t,n,r,i){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+n}h${r=+r}v${+i}h${-r}Z`}toString(){return this._}}function go(e){let t=3;return e.digits=function(n){if(!arguments.length)return t;if(n==null)t=null;else{const r=Math.floor(n);if(!(r>=0))throw new RangeError(`invalid digits: ${n}`);t=r}return e},()=>new I1(t)}function mo(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function wc(e){this._context=e}wc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t);break}}};function bo(e){return new wc(e)}function Mc(e){return e[0]}function $c(e){return e[1]}function N1(e,t){var n=ue(!0),r=null,i=bo,a=null,o=go(s);e=typeof e=="function"?e:e===void 0?Mc:ue(e),t=typeof t=="function"?t:t===void 0?$c:ue(t);function s(u){var f,l=(u=mo(u)).length,c,d=!1,g;for(r==null&&(a=i(g=o())),f=0;f<=l;++f)!(f<l&&n(c=u[f],f,u))===d&&((d=!d)?a.lineStart():a.lineEnd()),d&&a.point(+e(c,f,u),+t(c,f,u));if(g)return a=null,g+""||null}return s.x=function(u){return arguments.length?(e=typeof u=="function"?u:ue(+u),s):e},s.y=function(u){return arguments.length?(t=typeof u=="function"?u:ue(+u),s):t},s.defined=function(u){return arguments.length?(n=typeof u=="function"?u:ue(!!u),s):n},s.curve=function(u){return arguments.length?(i=u,r!=null&&(a=i(r)),s):i},s.context=function(u){return arguments.length?(u==null?r=a=null:a=i(r=u),s):r},s}function y9(e,t,n){var r=null,i=ue(!0),a=null,o=bo,s=null,u=go(f);e=typeof e=="function"?e:e===void 0?Mc:ue(+e),t=typeof t=="function"?t:ue(t===void 0?0:+t),n=typeof n=="function"?n:n===void 0?$c:ue(+n);function f(c){var d,g,b,p=(c=mo(c)).length,y,v=!1,O,_=new Array(p),k=new Array(p);for(a==null&&(s=o(O=u())),d=0;d<=p;++d){if(!(d<p&&i(y=c[d],d,c))===v)if(v=!v)g=d,s.areaStart(),s.lineStart();else{for(s.lineEnd(),s.lineStart(),b=d-1;b>=g;--b)s.point(_[b],k[b]);s.lineEnd(),s.areaEnd()}v&&(_[d]=+e(y,d,c),k[d]=+t(y,d,c),s.point(r?+r(y,d,c):_[d],n?+n(y,d,c):k[d]))}if(O)return s=null,O+""||null}function l(){return N1().defined(i).curve(o).context(a)}return f.x=function(c){return arguments.length?(e=typeof c=="function"?c:ue(+c),r=null,f):e},f.x0=function(c){return arguments.length?(e=typeof c=="function"?c:ue(+c),f):e},f.x1=function(c){return arguments.length?(r=c==null?null:typeof c=="function"?c:ue(+c),f):r},f.y=function(c){return arguments.length?(t=typeof c=="function"?c:ue(+c),n=null,f):t},f.y0=function(c){return arguments.length?(t=typeof c=="function"?c:ue(+c),f):t},f.y1=function(c){return arguments.length?(n=c==null?null:typeof c=="function"?c:ue(+c),f):n},f.lineX0=f.lineY0=function(){return l().x(e).y(t)},f.lineY1=function(){return l().x(e).y(n)},f.lineX1=function(){return l().x(r).y(t)},f.defined=function(c){return arguments.length?(i=typeof c=="function"?c:ue(!!c),f):i},f.curve=function(c){return arguments.length?(o=c,a!=null&&(s=o(a)),f):o},f.context=function(c){return arguments.length?(c==null?a=s=null:s=o(a=c),f):a},f}class Tc{constructor(t,n){this._context=t,this._x=n}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line}point(t,n){switch(t=+t,n=+n,this._point){case 0:{this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break}case 1:this._point=2;default:{this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,n,t,n):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+n)/2,t,this._y0,t,n);break}}this._x0=t,this._y0=n}}function v9(e){return new Tc(e,!0)}function _9(e){return new Tc(e,!1)}const E1={draw(e,t){const n=je(t/yr);e.moveTo(n,0),e.arc(0,0,n,0,Kr)}},x9={draw(e,t){const n=je(t/5)/2;e.moveTo(-3*n,-n),e.lineTo(-n,-n),e.lineTo(-n,-3*n),e.lineTo(n,-3*n),e.lineTo(n,-n),e.lineTo(3*n,-n),e.lineTo(3*n,n),e.lineTo(n,n),e.lineTo(n,3*n),e.lineTo(-n,3*n),e.lineTo(-n,n),e.lineTo(-3*n,n),e.closePath()}},Cc=je(1/3),Y1=Cc*2,w9={draw(e,t){const n=je(t/Y1),r=n*Cc;e.moveTo(0,-n),e.lineTo(r,0),e.lineTo(0,n),e.lineTo(-r,0),e.closePath()}},M9={draw(e,t){const n=je(t),r=-n/2;e.rect(r,r,n,n)}},L1=.8908130915292852,kc=pr(yr/10)/pr(7*yr/10),W1=pr(Kr/10)*kc,j1=-_c(Kr/10)*kc,$9={draw(e,t){const n=je(t*L1),r=W1*n,i=j1*n;e.moveTo(0,-n),e.lineTo(r,i);for(let a=1;a<5;++a){const o=Kr*a/5,s=_c(o),u=pr(o);e.lineTo(u*n,-s*n),e.lineTo(s*r-u*i,u*r+s*i)}e.closePath()}},ea=je(3),T9={draw(e,t){const n=-je(t/(ea*3));e.moveTo(0,n*2),e.lineTo(-ea*n,-n),e.lineTo(ea*n,-n),e.closePath()}},Ae=-.5,Ue=je(3)/2,Aa=1/je(12),q1=(Aa/2+1)*3,C9={draw(e,t){const n=je(t/q1),r=n/2,i=n*Aa,a=r,o=n*Aa+n,s=-a,u=o;e.moveTo(r,i),e.lineTo(a,o),e.lineTo(s,u),e.lineTo(Ae*r-Ue*i,Ue*r+Ae*i),e.lineTo(Ae*a-Ue*o,Ue*a+Ae*o),e.lineTo(Ae*s-Ue*u,Ue*s+Ae*u),e.lineTo(Ae*r+Ue*i,Ae*i-Ue*r),e.lineTo(Ae*a+Ue*o,Ae*o-Ue*a),e.lineTo(Ae*s+Ue*u,Ae*u-Ue*s),e.closePath()}};function k9(e,t){let n=null,r=go(i);e=typeof e=="function"?e:ue(e||E1),t=typeof t=="function"?t:ue(t===void 0?64:+t);function i(){let a;if(n||(n=a=r()),e.apply(this,arguments).draw(n,+t.apply(this,arguments)),a)return n=null,a+""||null}return i.type=function(a){return arguments.length?(e=typeof a=="function"?a:ue(a),i):e},i.size=function(a){return arguments.length?(t=typeof a=="function"?a:ue(+a),i):t},i.context=function(a){return arguments.length?(n=a??null,i):n},i}function lt(){}function vr(e,t,n){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+n)/6)}function ei(e){this._context=e}ei.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:vr(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:vr(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function z1(e){return new ei(e)}function Sc(e){this._context=e}Sc.prototype={areaStart:lt,areaEnd:lt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x2,this._y2),this._context.closePath();break}case 2:{this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break}case 3:{this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:vr(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function H1(e){return new Sc(e)}function Oc(e){this._context=e}Oc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var n=(this._x0+4*this._x1+e)/6,r=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(n,r):this._context.moveTo(n,r);break;case 3:this._point=4;default:vr(this,e,t);break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};function V1(e){return new Oc(e)}function Ac(e,t){this._basis=new ei(e),this._beta=t}Ac.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var e=this._x,t=this._y,n=e.length-1;if(n>0)for(var r=e[0],i=t[0],a=e[n]-r,o=t[n]-i,s=-1,u;++s<=n;)u=s/n,this._basis.point(this._beta*e[s]+(1-this._beta)*(r+u*a),this._beta*t[s]+(1-this._beta)*(i+u*o));this._x=this._y=null,this._basis.lineEnd()},point:function(e,t){this._x.push(+e),this._y.push(+t)}};const B1=function e(t){function n(r){return t===1?new ei(r):new Ac(r,t)}return n.beta=function(r){return e(+r)},n}(.85);function _r(e,t,n){e._context.bezierCurveTo(e._x1+e._k*(e._x2-e._x0),e._y1+e._k*(e._y2-e._y0),e._x2+e._k*(e._x1-t),e._y2+e._k*(e._y1-n),e._x2,e._y2)}function po(e,t){this._context=e,this._k=(1-t)/6}po.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:_r(this,this._x1,this._y1);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2,this._x1=e,this._y1=t;break;case 2:this._point=3;default:_r(this,e,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const G1=function e(t){function n(r){return new po(r,t)}return n.tension=function(r){return e(+r)},n}(0);function yo(e,t){this._context=e,this._k=(1-t)/6}yo.prototype={areaStart:lt,areaEnd:lt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._x3=e,this._y3=t;break;case 1:this._point=2,this._context.moveTo(this._x4=e,this._y4=t);break;case 2:this._point=3,this._x5=e,this._y5=t;break;default:_r(this,e,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const X1=function e(t){function n(r){return new yo(r,t)}return n.tension=function(r){return e(+r)},n}(0);function vo(e,t){this._context=e,this._k=(1-t)/6}vo.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:_r(this,e,t);break}this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const Q1=function e(t){function n(r){return new vo(r,t)}return n.tension=function(r){return e(+r)},n}(0);function _o(e,t,n){var r=e._x1,i=e._y1,a=e._x2,o=e._y2;if(e._l01_a>qs){var s=2*e._l01_2a+3*e._l01_a*e._l12_a+e._l12_2a,u=3*e._l01_a*(e._l01_a+e._l12_a);r=(r*s-e._x0*e._l12_2a+e._x2*e._l01_2a)/u,i=(i*s-e._y0*e._l12_2a+e._y2*e._l01_2a)/u}if(e._l23_a>qs){var f=2*e._l23_2a+3*e._l23_a*e._l12_a+e._l12_2a,l=3*e._l23_a*(e._l23_a+e._l12_a);a=(a*f+e._x1*e._l23_2a-t*e._l12_2a)/l,o=(o*f+e._y1*e._l23_2a-n*e._l12_2a)/l}e._context.bezierCurveTo(r,i,a,o,e._x2,e._y2)}function Uc(e,t){this._context=e,this._alpha=t}Uc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2);break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){if(e=+e,t=+t,this._point){var n=this._x2-e,r=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(n*n+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3;default:_o(this,e,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const Z1=function e(t){function n(r){return t?new Uc(r,t):new po(r,0)}return n.alpha=function(r){return e(+r)},n}(.5);function Pc(e,t){this._context=e,this._alpha=t}Pc.prototype={areaStart:lt,areaEnd:lt,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:{this._context.moveTo(this._x3,this._y3),this._context.closePath();break}case 2:{this._context.lineTo(this._x3,this._y3),this._context.closePath();break}case 3:{this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5);break}}},point:function(e,t){if(e=+e,t=+t,this._point){var n=this._x2-e,r=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(n*n+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=e,this._y3=t;break;case 1:this._point=2,this._context.moveTo(this._x4=e,this._y4=t);break;case 2:this._point=3,this._x5=e,this._y5=t;break;default:_o(this,e,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const J1=function e(t){function n(r){return t?new Pc(r,t):new yo(r,0)}return n.alpha=function(r){return e(+r)},n}(.5);function Dc(e,t){this._context=e,this._alpha=t}Dc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||this._line!==0&&this._point===3)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){if(e=+e,t=+t,this._point){var n=this._x2-e,r=this._y2-t;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(n*n+r*r,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:_o(this,e,t);break}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=e,this._y0=this._y1,this._y1=this._y2,this._y2=t}};const K1=function e(t){function n(r){return t?new Dc(r,t):new vo(r,0)}return n.alpha=function(r){return e(+r)},n}(.5);function Fc(e){this._context=e}Fc.prototype={areaStart:lt,areaEnd:lt,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e=+e,t=+t,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function eg(e){return new Fc(e)}function zs(e){return e<0?-1:1}function Hs(e,t,n){var r=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(r||i<0&&-0),o=(n-e._y1)/(i||r<0&&-0),s=(a*i+o*r)/(r+i);return(zs(a)+zs(o))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs(s))||0}function Vs(e,t){var n=e._x1-e._x0;return n?(3*(e._y1-e._y0)/n-t)/2:t}function ta(e,t,n){var r=e._x0,i=e._y0,a=e._x1,o=e._y1,s=(a-r)/3;e._context.bezierCurveTo(r+s,i+s*t,a-s,o-s*n,a,o)}function xr(e){this._context=e}xr.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:ta(this,this._t0,Vs(this,this._t0));break}(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var n=NaN;if(e=+e,t=+t,!(e===this._x1&&t===this._y1)){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,ta(this,Vs(this,n=Hs(this,e,t)),n);break;default:ta(this,this._t0,n=Hs(this,e,t));break}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=n}}};function Rc(e){this._context=new Ic(e)}(Rc.prototype=Object.create(xr.prototype)).point=function(e,t){xr.prototype.point.call(this,t,e)};function Ic(e){this._context=e}Ic.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,n,r,i,a){this._context.bezierCurveTo(t,e,r,n,a,i)}};function tg(e){return new xr(e)}function ng(e){return new Rc(e)}function Nc(e){this._context=e}Nc.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,n=e.length;if(n)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),n===2)this._context.lineTo(e[1],t[1]);else for(var r=Bs(e),i=Bs(t),a=0,o=1;o<n;++a,++o)this._context.bezierCurveTo(r[0][a],i[0][a],r[1][a],i[1][a],e[o],t[o]);(this._line||this._line!==0&&n===1)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}};function Bs(e){var t,n=e.length-1,r,i=new Array(n),a=new Array(n),o=new Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(a[n-1]=(e[n]+i[n-1])/2,t=0;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function rg(e){return new Nc(e)}function ti(e,t){this._context=e,this._t=t}ti.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&this._point===2&&this._context.lineTo(this._x,this._y),(this._line||this._line!==0&&this._point===1)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e=+e,t=+t,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:{if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var n=this._x*(1-this._t)+e*this._t;this._context.lineTo(n,this._y),this._context.lineTo(n,t)}break}}this._x=e,this._y=t}};function ig(e){return new ti(e,.5)}function ag(e){return new ti(e,0)}function og(e){return new ti(e,1)}function Sn(e,t){if((o=e.length)>1)for(var n=1,r,i,a=e[t[0]],o,s=a.length;n<o;++n)for(i=a,a=e[t[n]],r=0;r<s;++r)a[r][1]+=a[r][0]=isNaN(i[r][1])?i[r][0]:i[r][1]}function Gs(e){for(var t=e.length,n=new Array(t);--t>=0;)n[t]=t;return n}function sg(e,t){return e[t]}function ug(e){const t=[];return t.key=e,t}function S9(){var e=ue([]),t=Gs,n=Sn,r=sg;function i(a){var o=Array.from(e.apply(this,arguments),ug),s,u=o.length,f=-1,l;for(const c of a)for(s=0,++f;s<u;++s)(o[s][f]=[0,+r(c,o[s].key,f,a)]).data=c;for(s=0,l=mo(t(o));s<u;++s)o[l[s]].index=s;return n(o,l),o}return i.keys=function(a){return arguments.length?(e=typeof a=="function"?a:ue(Array.from(a)),i):e},i.value=function(a){return arguments.length?(r=typeof a=="function"?a:ue(+a),i):r},i.order=function(a){return arguments.length?(t=a==null?Gs:typeof a=="function"?a:ue(Array.from(a)),i):t},i.offset=function(a){return arguments.length?(n=a??Sn,i):n},i}function O9(e,t){if((r=e.length)>0){for(var n,r,i=0,a=e[0].length,o;i<a;++i){for(o=n=0;n<r;++n)o+=e[n][i][1]||0;if(o)for(n=0;n<r;++n)e[n][i][1]/=o}Sn(e,t)}}function A9(e,t){if((i=e.length)>0){for(var n=0,r=e[t[0]],i,a=r.length;n<a;++n){for(var o=0,s=0;o<i;++o)s+=e[o][n][1]||0;r[n][1]+=r[n][0]=-s/2}Sn(e,t)}}function U9(e,t){if(!(!((o=e.length)>0)||!((a=(i=e[t[0]]).length)>0))){for(var n=0,r=1,i,a,o;r<a;++r){for(var s=0,u=0,f=0;s<o;++s){for(var l=e[t[s]],c=l[r][1]||0,d=l[r-1][1]||0,g=(c-d)/2,b=0;b<s;++b){var p=e[t[b]],y=p[r][1]||0,v=p[r-1][1]||0;g+=y-v}u+=c,f+=g*c}i[r-1][1]+=i[r-1][0]=n,u&&(n-=f/u)}i[r-1][1]+=i[r-1][0]=n,Sn(e,t)}}var fg=ac,cg=uo,lg=1,dg=2;function hg(e,t,n,r){var i=n.length,a=i,o=!r;if(e==null)return!a;for(e=Object(e);i--;){var s=n[i];if(o&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++i<a;){s=n[i];var u=s[0],f=e[u],l=s[1];if(o&&s[2]){if(f===void 0&&!(u in e))return!1}else{var c=new fg;if(r)var d=r(f,l,u,e,t,c);if(!(d===void 0?cg(l,f,lg|dg,r,c):d))return!1}}return!0}var gg=hg,mg=Gn;function bg(e){return e===e&&!mg(e)}var Ec=bg,pg=Ec,yg=oc;function vg(e){for(var t=yg(e),n=t.length;n--;){var r=t[n],i=e[r];t[n]=[r,i,pg(i)]}return t}var _g=vg;function xg(e,t){return function(n){return n==null?!1:n[e]===t&&(t!==void 0||e in Object(n))}}var Yc=xg,wg=gg,Mg=_g,$g=Yc;function Tg(e){var t=Mg(e);return t.length==1&&t[0][2]?$g(t[0][0],t[0][1]):function(n){return n===e||wg(n,e,t)}}var Cg=Tg;function kg(e,t){return e!=null&&t in Object(e)}var Sg=kg,Og=Kt,Ag=fo,Ug=Xe,Pg=co,Dg=ih,Fg=en;function Rg(e,t,n){t=Og(t,e);for(var r=-1,i=t.length,a=!1;++r<i;){var o=Fg(t[r]);if(!(a=e!=null&&n(e,o)))break;e=e[o]}return a||++r!=i?a:(i=e==null?0:e.length,!!i&&Dg(i)&&Pg(o,i)&&(Ug(e)||Ag(e)))}var Ig=Rg,Ng=Sg,Eg=Ig;function Yg(e,t){return e!=null&&Eg(e,t,Ng)}var Lc=Yg,Lg=uo,Wg=vc,jg=Lc,qg=lo,zg=Ec,Hg=Yc,Vg=en,Bg=1,Gg=2;function Xg(e,t){return qg(e)&&zg(t)?Hg(Vg(e),t):function(n){var r=Wg(n,e);return r===void 0&&r===t?jg(n,e):Lg(t,r,Bg|Gg)}}var Qg=Xg;function Zg(e){return e}var ni=Zg;function Jg(e){return function(t){return t==null?void 0:t[e]}}var Kg=Jg,em=Xn;function tm(e){return function(t){return em(t,e)}}var nm=tm,rm=Kg,im=nm,am=lo,om=en;function sm(e){return am(e)?rm(om(e)):im(e)}var um=sm,fm=Cg,cm=Qg,lm=ni,dm=Xe,hm=um;function gm(e){return typeof e=="function"?e:e==null?lm:typeof e=="object"?dm(e)?cm(e[0],e[1]):fm(e):hm(e)}var xo=gm;function mm(e,t,n,r){for(var i=e.length,a=n+(r?1:-1);r?a--:++a<i;)if(t(e[a],a,e))return a;return-1}var bm=mm;function pm(e){return e!==e}var ym=pm;function vm(e,t,n){for(var r=n-1,i=e.length;++r<i;)if(e[r]===t)return r;return-1}var _m=vm,xm=bm,wm=ym,Mm=_m;function $m(e,t,n){return t===t?Mm(e,t,n):xm(e,wm,n)}var Tm=$m,Cm=Tm;function km(e,t){var n=e==null?0:e.length;return!!n&&Cm(e,t,0)>-1}var Wc=km;function Sm(e,t,n){for(var r=-1,i=e==null?0:e.length;++r<i;)if(n(t,e[r]))return!0;return!1}var jc=Sm;function Om(){}var Am=Om,na=ah,Um=Am,Pm=sc,Dm=1/0,Fm=na&&1/Pm(new na([,-0]))[1]==Dm?function(e){return new na(e)}:Um,Rm=Fm,Im=uc,Nm=Wc,Em=jc,Ym=fc,Lm=Rm,Wm=sc,jm=200;function qm(e,t,n){var r=-1,i=Nm,a=e.length,o=!0,s=[],u=s;if(n)o=!1,i=Em;else if(a>=jm){var f=t?null:Lm(e);if(f)return Wm(f);o=!1,i=Ym,u=new Im}else u=t?[]:s;e:for(;++r<a;){var l=e[r],c=t?t(l):l;if(l=n||l!==0?l:0,o&&c===c){for(var d=u.length;d--;)if(u[d]===c)continue e;t&&u.push(c),s.push(l)}else i(u,c,n)||(u!==s&&u.push(c),s.push(l))}return s}var zm=qm,Hm=xo,Vm=zm;function Bm(e,t){return e&&e.length?Vm(e,Hm(t)):[]}var Gm=Bm;const P9=Oe(Gm);var Xs=ic,Xm=fo,Qm=Xe,Qs=Xs?Xs.isConcatSpreadable:void 0;function Zm(e){return Qm(e)||Xm(e)||!!(Qs&&e&&e[Qs])}var Jm=Zm,Km=oh,eb=Jm;function qc(e,t,n,r,i){var a=-1,o=e.length;for(n||(n=eb),i||(i=[]);++a<o;){var s=e[a];t>0&&n(s)?t>1?qc(s,t-1,n,r,i):Km(i,s):r||(i[i.length]=s)}return i}var zc=qc;function tb(e){return function(t,n,r){for(var i=-1,a=Object(t),o=r(t),s=o.length;s--;){var u=o[e?s:++i];if(n(a[u],u,a)===!1)break}return t}}var nb=tb,rb=nb,ib=rb(),Hc=ib,ab=Hc,ob=oc;function sb(e,t){return e&&ab(e,t,ob)}var ub=sb,fb=Xr;function cb(e,t){return function(n,r){if(n==null)return n;if(!fb(n))return e(n,r);for(var i=n.length,a=t?i:-1,o=Object(n);(t?a--:++a<i)&&r(o[a],a,o)!==!1;);return n}}var lb=cb,db=ub,hb=lb,gb=hb(db),Vc=gb,mb=Vc,bb=Xr;function pb(e,t){var n=-1,r=bb(e)?Array(e.length):[];return mb(e,function(i,a,o){r[++n]=t(i,a,o)}),r}var yb=pb;function vb(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}var _b=vb,Zs=Zr;function xb(e,t){if(e!==t){var n=e!==void 0,r=e===null,i=e===e,a=Zs(e),o=t!==void 0,s=t===null,u=t===t,f=Zs(t);if(!s&&!f&&!a&&e>t||a&&o&&u&&!s&&!f||r&&o&&u||!n&&u||!i)return 1;if(!r&&!a&&!f&&e<t||f&&n&&i&&!r&&!a||s&&n&&i||!o&&i||!u)return-1}return 0}var wb=xb,Mb=wb;function $b(e,t,n){for(var r=-1,i=e.criteria,a=t.criteria,o=i.length,s=n.length;++r<o;){var u=Mb(i[r],a[r]);if(u){if(r>=s)return u;var f=n[r];return u*(f=="desc"?-1:1)}}return e.index-t.index}var Tb=$b,ra=Jr,Cb=Xn,kb=xo,Sb=yb,Ob=_b,Ab=cc,Ub=Tb,Pb=ni,Db=Xe;function Fb(e,t,n){t.length?t=ra(t,function(a){return Db(a)?function(o){return Cb(o,a.length===1?a[0]:a)}:a}):t=[Pb];var r=-1;t=ra(t,Ab(kb));var i=Sb(e,function(a,o,s){var u=ra(t,function(f){return f(a)});return{criteria:u,index:++r,value:a}});return Ob(i,function(a,o){return Ub(a,o,n)})}var Rb=Fb;function Ib(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}var Nb=Ib,Eb=Nb,Js=Math.max;function Yb(e,t,n){return t=Js(t===void 0?e.length-1:t,0),function(){for(var r=arguments,i=-1,a=Js(r.length-t,0),o=Array(a);++i<a;)o[i]=r[t+i];i=-1;for(var s=Array(t+1);++i<t;)s[i]=r[i];return s[t]=n(o),Eb(e,this,s)}}var Bc=Yb;function Lb(e){return function(){return e}}var Wb=Lb,jb=Wb,Ks=sh,qb=ni,zb=Ks?function(e,t){return Ks(e,"toString",{configurable:!0,enumerable:!1,value:jb(t),writable:!0})}:qb,Hb=zb,Vb=800,Bb=16,Gb=Date.now;function Xb(e){var t=0,n=0;return function(){var r=Gb(),i=Bb-(r-n);if(n=r,i>0){if(++t>=Vb)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}var Qb=Xb,Zb=Hb,Jb=Qb,Kb=Jb(Zb),Gc=Kb,e2=ni,t2=Bc,n2=Gc;function r2(e,t){return n2(t2(e,t,e2),e+"")}var wo=r2,i2=lc,a2=Xr,o2=co,s2=Gn;function u2(e,t,n){if(!s2(n))return!1;var r=typeof t;return(r=="number"?a2(n)&&o2(t,n.length):r=="string"&&t in n)?i2(n[t],e):!1}var Xc=u2,f2=zc,c2=Rb,l2=wo,eu=Xc,d2=l2(function(e,t){if(e==null)return[];var n=t.length;return n>1&&eu(e,t[0],t[1])?t=[]:n>2&&eu(t[0],t[1],t[2])&&(t=[t[0]]),c2(e,f2(t,1),[])}),h2=d2;const D9=Oe(h2);function ft(e,t){return e==null||t==null?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function g2(e,t){return e==null||t==null?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function Mo(e){let t,n,r;e.length!==2?(t=ft,n=(s,u)=>ft(e(s),u),r=(s,u)=>e(s)-u):(t=e===ft||e===g2?e:m2,n=e,r=e);function i(s,u,f=0,l=s.length){if(f<l){if(t(u,u)!==0)return l;do{const c=f+l>>>1;n(s[c],u)<0?f=c+1:l=c}while(f<l)}return f}function a(s,u,f=0,l=s.length){if(f<l){if(t(u,u)!==0)return l;do{const c=f+l>>>1;n(s[c],u)<=0?f=c+1:l=c}while(f<l)}return f}function o(s,u,f=0,l=s.length){const c=i(s,u,f,l-1);return c>f&&r(s[c-1],u)>-r(s[c],u)?c-1:c}return{left:i,center:o,right:a}}function m2(){return 0}function Qc(e){return e===null?NaN:+e}function*b2(e,t){if(t===void 0)for(let n of e)n!=null&&(n=+n)>=n&&(yield n);else{let n=-1;for(let r of e)(r=t(r,++n,e))!=null&&(r=+r)>=r&&(yield r)}}const p2=Mo(ft),y2=p2.right;Mo(Qc).center;const Qn=y2;class tu extends Map{constructor(t,n=x2){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:n}}),t!=null)for(const[r,i]of t)this.set(r,i)}get(t){return super.get(nu(this,t))}has(t){return super.has(nu(this,t))}set(t,n){return super.set(v2(this,t),n)}delete(t){return super.delete(_2(this,t))}}function nu({_intern:e,_key:t},n){const r=t(n);return e.has(r)?e.get(r):n}function v2({_intern:e,_key:t},n){const r=t(n);return e.has(r)?e.get(r):(e.set(r,n),n)}function _2({_intern:e,_key:t},n){const r=t(n);return e.has(r)&&(n=e.get(r),e.delete(r)),n}function x2(e){return e!==null&&typeof e=="object"?e.valueOf():e}function w2(e=ft){if(e===ft)return Zc;if(typeof e!="function")throw new TypeError("compare is not a function");return(t,n)=>{const r=e(t,n);return r||r===0?r:(e(n,n)===0)-(e(t,t)===0)}}function Zc(e,t){return(e==null||!(e>=e))-(t==null||!(t>=t))||(e<t?-1:e>t?1:0)}const M2=Math.sqrt(50),$2=Math.sqrt(10),T2=Math.sqrt(2);function wr(e,t,n){const r=(t-e)/Math.max(0,n),i=Math.floor(Math.log10(r)),a=r/Math.pow(10,i),o=a>=M2?10:a>=$2?5:a>=T2?2:1;let s,u,f;return i<0?(f=Math.pow(10,-i)/o,s=Math.round(e*f),u=Math.round(t*f),s/f<e&&++s,u/f>t&&--u,f=-f):(f=Math.pow(10,i)*o,s=Math.round(e/f),u=Math.round(t/f),s*f<e&&++s,u*f>t&&--u),u<s&&.5<=n&&n<2?wr(e,t,n*2):[s,u,f]}function Ua(e,t,n){if(t=+t,e=+e,n=+n,!(n>0))return[];if(e===t)return[e];const r=t<e,[i,a,o]=r?wr(t,e,n):wr(e,t,n);if(!(a>=i))return[];const s=a-i+1,u=new Array(s);if(r)if(o<0)for(let f=0;f<s;++f)u[f]=(a-f)/-o;else for(let f=0;f<s;++f)u[f]=(a-f)*o;else if(o<0)for(let f=0;f<s;++f)u[f]=(i+f)/-o;else for(let f=0;f<s;++f)u[f]=(i+f)*o;return u}function Pa(e,t,n){return t=+t,e=+e,n=+n,wr(e,t,n)[2]}function Da(e,t,n){t=+t,e=+e,n=+n;const r=t<e,i=r?Pa(t,e,n):Pa(e,t,n);return(r?-1:1)*(i<0?1/-i:i)}function ru(e,t){let n;if(t===void 0)for(const r of e)r!=null&&(n<r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of e)(i=t(i,++r,e))!=null&&(n<i||n===void 0&&i>=i)&&(n=i)}return n}function iu(e,t){let n;if(t===void 0)for(const r of e)r!=null&&(n>r||n===void 0&&r>=r)&&(n=r);else{let r=-1;for(let i of e)(i=t(i,++r,e))!=null&&(n>i||n===void 0&&i>=i)&&(n=i)}return n}function Jc(e,t,n=0,r=1/0,i){if(t=Math.floor(t),n=Math.floor(Math.max(0,n)),r=Math.floor(Math.min(e.length-1,r)),!(n<=t&&t<=r))return e;for(i=i===void 0?Zc:w2(i);r>n;){if(r-n>600){const u=r-n+1,f=t-n+1,l=Math.log(u),c=.5*Math.exp(2*l/3),d=.5*Math.sqrt(l*c*(u-c)/u)*(f-u/2<0?-1:1),g=Math.max(n,Math.floor(t-f*c/u+d)),b=Math.min(r,Math.floor(t+(u-f)*c/u+d));Jc(e,t,g,b,i)}const a=e[t];let o=n,s=r;for(sn(e,n,t),i(e[r],a)>0&&sn(e,n,r);o<s;){for(sn(e,o,s),++o,--s;i(e[o],a)<0;)++o;for(;i(e[s],a)>0;)--s}i(e[n],a)===0?sn(e,n,s):(++s,sn(e,s,r)),s<=t&&(n=s+1),t<=s&&(r=s-1)}return e}function sn(e,t,n){const r=e[t];e[t]=e[n],e[n]=r}function C2(e,t,n){if(e=Float64Array.from(b2(e,n)),!(!(r=e.length)||isNaN(t=+t))){if(t<=0||r<2)return iu(e);if(t>=1)return ru(e);var r,i=(r-1)*t,a=Math.floor(i),o=ru(Jc(e,a).subarray(0,a+1)),s=iu(e.subarray(a+1));return o+(s-o)*(i-a)}}function k2(e,t,n=Qc){if(!(!(r=e.length)||isNaN(t=+t))){if(t<=0||r<2)return+n(e[0],0,e);if(t>=1)return+n(e[r-1],r-1,e);var r,i=(r-1)*t,a=Math.floor(i),o=+n(e[a],a,e),s=+n(e[a+1],a+1,e);return o+(s-o)*(i-a)}}function S2(e,t,n){e=+e,t=+t,n=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+n;for(var r=-1,i=Math.max(0,Math.ceil((t-e)/n))|0,a=new Array(i);++r<i;)a[r]=e+r*n;return a}function Re(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e);break}return this}function nt(e,t){switch(arguments.length){case 0:break;case 1:{typeof e=="function"?this.interpolator(e):this.range(e);break}default:{this.domain(e),typeof t=="function"?this.interpolator(t):this.range(t);break}}return this}const au=Symbol("implicit");function $o(){var e=new tu,t=[],n=[],r=au;function i(a){let o=e.get(a);if(o===void 0){if(r!==au)return r;e.set(a,o=t.push(a)-1)}return n[o%n.length]}return i.domain=function(a){if(!arguments.length)return t.slice();t=[],e=new tu;for(const o of a)e.has(o)||e.set(o,t.push(o)-1);return i},i.range=function(a){return arguments.length?(n=Array.from(a),i):n.slice()},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return $o(t,n).unknown(r)},Re.apply(i,arguments),i}function Mr(){var e=$o().unknown(void 0),t=e.domain,n=e.range,r=0,i=1,a,o,s=!1,u=0,f=0,l=.5;delete e.unknown;function c(){var d=t().length,g=i<r,b=g?i:r,p=g?r:i;a=(p-b)/Math.max(1,d-u+f*2),s&&(a=Math.floor(a)),b+=(p-b-a*(d-u))*l,o=a*(1-u),s&&(b=Math.round(b),o=Math.round(o));var y=S2(d).map(function(v){return b+a*v});return n(g?y.reverse():y)}return e.domain=function(d){return arguments.length?(t(d),c()):t()},e.range=function(d){return arguments.length?([r,i]=d,r=+r,i=+i,c()):[r,i]},e.rangeRound=function(d){return[r,i]=d,r=+r,i=+i,s=!0,c()},e.bandwidth=function(){return o},e.step=function(){return a},e.round=function(d){return arguments.length?(s=!!d,c()):s},e.padding=function(d){return arguments.length?(u=Math.min(1,f=+d),c()):u},e.paddingInner=function(d){return arguments.length?(u=Math.min(1,d),c()):u},e.paddingOuter=function(d){return arguments.length?(f=+d,c()):f},e.align=function(d){return arguments.length?(l=Math.max(0,Math.min(1,d)),c()):l},e.copy=function(){return Mr(t(),[r,i]).round(s).paddingInner(u).paddingOuter(f).align(l)},Re.apply(c(),arguments)}function Kc(e){var t=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return Kc(t())},e}function F9(){return Kc(Mr.apply(null,arguments).paddingInner(1))}function ri(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function To(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function tn(){}var Mt=.7,jt=1/Mt,Et="\\s*([+-]?\\d+)\\s*",On="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Ve="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",O2=/^#([0-9a-f]{3,8})$/,A2=new RegExp(`^rgb\\(${Et},${Et},${Et}\\)$`),U2=new RegExp(`^rgb\\(${Ve},${Ve},${Ve}\\)$`),P2=new RegExp(`^rgba\\(${Et},${Et},${Et},${On}\\)$`),D2=new RegExp(`^rgba\\(${Ve},${Ve},${Ve},${On}\\)$`),F2=new RegExp(`^hsl\\(${On},${Ve},${Ve}\\)$`),R2=new RegExp(`^hsla\\(${On},${Ve},${Ve},${On}\\)$`),ou={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};ri(tn,An,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:su,formatHex:su,formatHex8:I2,formatHsl:N2,formatRgb:uu,toString:uu});function su(){return this.rgb().formatHex()}function I2(){return this.rgb().formatHex8()}function N2(){return tl(this).formatHsl()}function uu(){return this.rgb().formatRgb()}function An(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=O2.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?fu(t):n===3?new Me(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?ir(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?ir(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=A2.exec(e))?new Me(t[1],t[2],t[3],1):(t=U2.exec(e))?new Me(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=P2.exec(e))?ir(t[1],t[2],t[3],t[4]):(t=D2.exec(e))?ir(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=F2.exec(e))?du(t[1],t[2]/100,t[3]/100,1):(t=R2.exec(e))?du(t[1],t[2]/100,t[3]/100,t[4]):ou.hasOwnProperty(e)?fu(ou[e]):e==="transparent"?new Me(NaN,NaN,NaN,0):null}function fu(e){return new Me(e>>16&255,e>>8&255,e&255,1)}function ir(e,t,n,r){return r<=0&&(e=t=n=NaN),new Me(e,t,n,r)}function el(e){return e instanceof tn||(e=An(e)),e?(e=e.rgb(),new Me(e.r,e.g,e.b,e.opacity)):new Me}function qt(e,t,n,r){return arguments.length===1?el(e):new Me(e,t,n,r??1)}function Me(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}ri(Me,qt,To(tn,{brighter(e){return e=e==null?jt:Math.pow(jt,e),new Me(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Mt:Math.pow(Mt,e),new Me(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Me(xt(this.r),xt(this.g),xt(this.b),$r(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:cu,formatHex:cu,formatHex8:E2,formatRgb:lu,toString:lu}));function cu(){return`#${yt(this.r)}${yt(this.g)}${yt(this.b)}`}function E2(){return`#${yt(this.r)}${yt(this.g)}${yt(this.b)}${yt((isNaN(this.opacity)?1:this.opacity)*255)}`}function lu(){const e=$r(this.opacity);return`${e===1?"rgb(":"rgba("}${xt(this.r)}, ${xt(this.g)}, ${xt(this.b)}${e===1?")":`, ${e})`}`}function $r(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function xt(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function yt(e){return e=xt(e),(e<16?"0":"")+e.toString(16)}function du(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Ee(e,t,n,r)}function tl(e){if(e instanceof Ee)return new Ee(e.h,e.s,e.l,e.opacity);if(e instanceof tn||(e=An(e)),!e)return new Ee;if(e instanceof Ee)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,i=Math.min(t,n,r),a=Math.max(t,n,r),o=NaN,s=a-i,u=(a+i)/2;return s?(t===a?o=(n-r)/s+(n<r)*6:n===a?o=(r-t)/s+2:o=(t-n)/s+4,s/=u<.5?a+i:2-a-i,o*=60):s=u>0&&u<1?0:o,new Ee(o,s,u,e.opacity)}function Y2(e,t,n,r){return arguments.length===1?tl(e):new Ee(e,t,n,r??1)}function Ee(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}ri(Ee,Y2,To(tn,{brighter(e){return e=e==null?jt:Math.pow(jt,e),new Ee(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Mt:Math.pow(Mt,e),new Ee(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,i=2*n-r;return new Me(ia(e>=240?e-240:e+120,i,r),ia(e,i,r),ia(e<120?e+240:e-120,i,r),this.opacity)},clamp(){return new Ee(hu(this.h),ar(this.s),ar(this.l),$r(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=$r(this.opacity);return`${e===1?"hsl(":"hsla("}${hu(this.h)}, ${ar(this.s)*100}%, ${ar(this.l)*100}%${e===1?")":`, ${e})`}`}}));function hu(e){return e=(e||0)%360,e<0?e+360:e}function ar(e){return Math.max(0,Math.min(1,e||0))}function ia(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const L2=Math.PI/180,W2=180/Math.PI;var nl=-.14861,Co=1.78277,ko=-.29227,ii=-.90649,Un=1.97294,gu=Un*ii,mu=Un*Co,bu=Co*ko-ii*nl;function j2(e){if(e instanceof wt)return new wt(e.h,e.s,e.l,e.opacity);e instanceof Me||(e=el(e));var t=e.r/255,n=e.g/255,r=e.b/255,i=(bu*r+gu*t-mu*n)/(bu+gu-mu),a=r-i,o=(Un*(n-i)-ko*a)/ii,s=Math.sqrt(o*o+a*a)/(Un*i*(1-i)),u=s?Math.atan2(o,a)*W2-120:NaN;return new wt(u<0?u+360:u,s,i,e.opacity)}function Be(e,t,n,r){return arguments.length===1?j2(e):new wt(e,t,n,r??1)}function wt(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}ri(wt,Be,To(tn,{brighter(e){return e=e==null?jt:Math.pow(jt,e),new wt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Mt:Math.pow(Mt,e),new wt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=isNaN(this.h)?0:(this.h+120)*L2,t=+this.l,n=isNaN(this.s)?0:this.s*t*(1-t),r=Math.cos(e),i=Math.sin(e);return new Me(255*(t+n*(nl*r+Co*i)),255*(t+n*(ko*r+ii*i)),255*(t+n*(Un*r)),this.opacity)}}));function q2(e,t,n,r,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*n+(1+3*e+3*a-3*o)*r+o*i)/6}function z2(e){var t=e.length-1;return function(n){var r=n<=0?n=0:n>=1?(n=1,t-1):Math.floor(n*t),i=e[r],a=e[r+1],o=r>0?e[r-1]:2*i-a,s=r<t-1?e[r+2]:2*a-i;return q2((n-r/t)*t,o,i,a,s)}}const ai=e=>()=>e;function rl(e,t){return function(n){return e+n*t}}function H2(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function V2(e,t){var n=t-e;return n?rl(e,n>180||n<-180?n-360*Math.round(n/360):n):ai(isNaN(e)?t:e)}function B2(e){return(e=+e)==1?Yt:function(t,n){return n-t?H2(t,n,e):ai(isNaN(t)?n:t)}}function Yt(e,t){var n=t-e;return n?rl(e,n):ai(isNaN(e)?t:e)}const pu=function e(t){var n=B2(t);function r(i,a){var o=n((i=qt(i)).r,(a=qt(a)).r),s=n(i.g,a.g),u=n(i.b,a.b),f=Yt(i.opacity,a.opacity);return function(l){return i.r=o(l),i.g=s(l),i.b=u(l),i.opacity=f(l),i+""}}return r.gamma=e,r}(1);function G2(e){return function(t){var n=t.length,r=new Array(n),i=new Array(n),a=new Array(n),o,s;for(o=0;o<n;++o)s=qt(t[o]),r[o]=s.r||0,i[o]=s.g||0,a[o]=s.b||0;return r=e(r),i=e(i),a=e(a),s.opacity=1,function(u){return s.r=r(u),s.g=i(u),s.b=a(u),s+""}}}var X2=G2(z2);function Q2(e,t){t||(t=[]);var n=e?Math.min(t.length,e.length):0,r=t.slice(),i;return function(a){for(i=0;i<n;++i)r[i]=e[i]*(1-a)+t[i]*a;return r}}function Z2(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function J2(e,t){var n=t?t.length:0,r=e?Math.min(n,e.length):0,i=new Array(r),a=new Array(n),o;for(o=0;o<r;++o)i[o]=nn(e[o],t[o]);for(;o<n;++o)a[o]=t[o];return function(s){for(o=0;o<r;++o)a[o]=i[o](s);return a}}function K2(e,t){var n=new Date;return e=+e,t=+t,function(r){return n.setTime(e*(1-r)+t*r),n}}function Tr(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}function ep(e,t){var n={},r={},i;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(i in t)i in e?n[i]=nn(e[i],t[i]):r[i]=t[i];return function(a){for(i in n)r[i]=n[i](a);return r}}var Fa=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,aa=new RegExp(Fa.source,"g");function tp(e){return function(){return e}}function np(e){return function(t){return e(t)+""}}function il(e,t){var n=Fa.lastIndex=aa.lastIndex=0,r,i,a,o=-1,s=[],u=[];for(e=e+"",t=t+"";(r=Fa.exec(e))&&(i=aa.exec(t));)(a=i.index)>n&&(a=t.slice(n,a),s[o]?s[o]+=a:s[++o]=a),(r=r[0])===(i=i[0])?s[o]?s[o]+=i:s[++o]=i:(s[++o]=null,u.push({i:o,x:Tr(r,i)})),n=aa.lastIndex;return n<t.length&&(a=t.slice(n),s[o]?s[o]+=a:s[++o]=a),s.length<2?u[0]?np(u[0].x):tp(t):(t=u.length,function(f){for(var l=0,c;l<t;++l)s[(c=u[l]).i]=c.x(f);return s.join("")})}function nn(e,t){var n=typeof t,r;return t==null||n==="boolean"?ai(t):(n==="number"?Tr:n==="string"?(r=An(t))?(t=r,pu):il:t instanceof An?pu:t instanceof Date?K2:Z2(t)?Q2:Array.isArray(t)?J2:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?ep:Tr)(e,t)}function So(e,t){return e=+e,t=+t,function(n){return Math.round(e*(1-n)+t*n)}}function al(e){return function t(n){n=+n;function r(i,a){var o=e((i=Be(i)).h,(a=Be(a)).h),s=Yt(i.s,a.s),u=Yt(i.l,a.l),f=Yt(i.opacity,a.opacity);return function(l){return i.h=o(l),i.s=s(l),i.l=u(Math.pow(l,n)),i.opacity=f(l),i+""}}return r.gamma=t,r}(1)}al(V2);var Oo=al(Yt);function rp(e,t){t===void 0&&(t=e,e=nn);for(var n=0,r=t.length-1,i=t[0],a=new Array(r<0?0:r);n<r;)a[n]=e(i,i=t[++n]);return function(o){var s=Math.max(0,Math.min(r-1,Math.floor(o*=r)));return a[s](o-s)}}function ip(e){return function(){return e}}function Cr(e){return+e}var yu=[0,1];function $e(e){return e}function Ra(e,t){return(t-=e=+e)?function(n){return(n-e)/t}:ip(isNaN(t)?NaN:.5)}function ap(e,t){var n;return e>t&&(n=e,e=t,t=n),function(r){return Math.max(e,Math.min(t,r))}}function op(e,t,n){var r=e[0],i=e[1],a=t[0],o=t[1];return i<r?(r=Ra(i,r),a=n(o,a)):(r=Ra(r,i),a=n(a,o)),function(s){return a(r(s))}}function sp(e,t,n){var r=Math.min(e.length,t.length)-1,i=new Array(r),a=new Array(r),o=-1;for(e[r]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<r;)i[o]=Ra(e[o],e[o+1]),a[o]=n(t[o],t[o+1]);return function(s){var u=Qn(e,s,1,r)-1;return a[u](i[u](s))}}function Zn(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function oi(){var e=yu,t=yu,n=nn,r,i,a,o=$e,s,u,f;function l(){var d=Math.min(e.length,t.length);return o!==$e&&(o=ap(e[0],e[d-1])),s=d>2?sp:op,u=f=null,c}function c(d){return d==null||isNaN(d=+d)?a:(u||(u=s(e.map(r),t,n)))(r(o(d)))}return c.invert=function(d){return o(i((f||(f=s(t,e.map(r),Tr)))(d)))},c.domain=function(d){return arguments.length?(e=Array.from(d,Cr),l()):e.slice()},c.range=function(d){return arguments.length?(t=Array.from(d),l()):t.slice()},c.rangeRound=function(d){return t=Array.from(d),n=So,l()},c.clamp=function(d){return arguments.length?(o=d?!0:$e,l()):o!==$e},c.interpolate=function(d){return arguments.length?(n=d,l()):n},c.unknown=function(d){return arguments.length?(a=d,c):a},function(d,g){return r=d,i=g,l()}}function Ao(){return oi()($e,$e)}function up(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function kr(e,t){if((n=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var n,r=e.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+e.slice(n+1)]}function zt(e){return e=kr(Math.abs(e)),e?e[1]:NaN}function fp(e,t){return function(n,r){for(var i=n.length,a=[],o=0,s=e[0],u=0;i>0&&s>0&&(u+s+1>r&&(s=Math.max(1,r-u)),a.push(n.substring(i-=s,i+s)),!((u+=s+1)>r));)s=e[o=(o+1)%e.length];return a.reverse().join(t)}}function cp(e){return function(t){return t.replace(/[0-9]/g,function(n){return e[+n]})}}var lp=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Pn(e){if(!(t=lp.exec(e)))throw new Error("invalid format: "+e);var t;return new Uo({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}Pn.prototype=Uo.prototype;function Uo(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}Uo.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function dp(e){e:for(var t=e.length,n=1,r=-1,i;n<t;++n)switch(e[n]){case".":r=i=n;break;case"0":r===0&&(r=n),i=n;break;default:if(!+e[n])break e;r>0&&(r=0);break}return r>0?e.slice(0,r)+e.slice(i+1):e}var ol;function hp(e,t){var n=kr(e,t);if(!n)return e+"";var r=n[0],i=n[1],a=i-(ol=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=r.length;return a===o?r:a>o?r+new Array(a-o+1).join("0"):a>0?r.slice(0,a)+"."+r.slice(a):"0."+new Array(1-a).join("0")+kr(e,Math.max(0,t+a-1))[0]}function vu(e,t){var n=kr(e,t);if(!n)return e+"";var r=n[0],i=n[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}const _u={"%":(e,t)=>(e*100).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:up,e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>vu(e*100,t),r:vu,s:hp,X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function xu(e){return e}var wu=Array.prototype.map,Mu=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function gp(e){var t=e.grouping===void 0||e.thousands===void 0?xu:fp(wu.call(e.grouping,Number),e.thousands+""),n=e.currency===void 0?"":e.currency[0]+"",r=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?xu:cp(wu.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",s=e.minus===void 0?"−":e.minus+"",u=e.nan===void 0?"NaN":e.nan+"";function f(c){c=Pn(c);var d=c.fill,g=c.align,b=c.sign,p=c.symbol,y=c.zero,v=c.width,O=c.comma,_=c.precision,k=c.trim,A=c.type;A==="n"?(O=!0,A="g"):_u[A]||(_===void 0&&(_=12),k=!0,A="g"),(y||d==="0"&&g==="=")&&(y=!0,d="0",g="=");var C=p==="$"?n:p==="#"&&/[boxX]/.test(A)?"0"+A.toLowerCase():"",F=p==="$"?r:/[%p]/.test(A)?o:"",Y=_u[A],E=/[defgprs%]/.test(A);_=_===void 0?6:/[gprs]/.test(A)?Math.max(1,Math.min(21,_)):Math.max(0,Math.min(20,_));function L(w){var q=C,D=F,U,N,W;if(A==="c")D=Y(w)+D,w="";else{w=+w;var j=w<0||1/w<0;if(w=isNaN(w)?u:Y(Math.abs(w),_),k&&(w=dp(w)),j&&+w==0&&b!=="+"&&(j=!1),q=(j?b==="("?b:s:b==="-"||b==="("?"":b)+q,D=(A==="s"?Mu[8+ol/3]:"")+D+(j&&b==="("?")":""),E){for(U=-1,N=w.length;++U<N;)if(W=w.charCodeAt(U),48>W||W>57){D=(W===46?i+w.slice(U+1):w.slice(U))+D,w=w.slice(0,U);break}}}O&&!y&&(w=t(w,1/0));var z=q.length+w.length+D.length,R=z<v?new Array(v-z+1).join(d):"";switch(O&&y&&(w=t(R+w,R.length?v-D.length:1/0),R=""),g){case"<":w=q+w+D+R;break;case"=":w=q+R+w+D;break;case"^":w=R.slice(0,z=R.length>>1)+q+w+D+R.slice(z);break;default:w=R+q+w+D;break}return a(w)}return L.toString=function(){return c+""},L}function l(c,d){var g=f((c=Pn(c),c.type="f",c)),b=Math.max(-8,Math.min(8,Math.floor(zt(d)/3)))*3,p=Math.pow(10,-b),y=Mu[8+b/3];return function(v){return g(p*v)+y}}return{format:f,formatPrefix:l}}var or,Po,sl;mp({thousands:",",grouping:[3],currency:["$",""]});function mp(e){return or=gp(e),Po=or.format,sl=or.formatPrefix,or}function bp(e){return Math.max(0,-zt(Math.abs(e)))}function pp(e,t){return Math.max(0,Math.max(-8,Math.min(8,Math.floor(zt(t)/3)))*3-zt(Math.abs(e)))}function yp(e,t){return e=Math.abs(e),t=Math.abs(t)-e,Math.max(0,zt(t)-zt(e))+1}function vp(e,t,n,r){var i=Da(e,t,n),a;switch(r=Pn(r??",f"),r.type){case"s":{var o=Math.max(Math.abs(e),Math.abs(t));return r.precision==null&&!isNaN(a=pp(i,o))&&(r.precision=a),sl(r,o)}case"":case"e":case"g":case"p":case"r":{r.precision==null&&!isNaN(a=yp(i,Math.max(Math.abs(e),Math.abs(t))))&&(r.precision=a-(r.type==="e"));break}case"f":case"%":{r.precision==null&&!isNaN(a=bp(i))&&(r.precision=a-(r.type==="%")*2);break}}return Po(r)}function gt(e){var t=e.domain;return e.ticks=function(n){var r=t();return Ua(r[0],r[r.length-1],n??10)},e.tickFormat=function(n,r){var i=t();return vp(i[0],i[i.length-1],n??10,r)},e.nice=function(n){n==null&&(n=10);var r=t(),i=0,a=r.length-1,o=r[i],s=r[a],u,f,l=10;for(s<o&&(f=o,o=s,s=f,f=i,i=a,a=f);l-- >0;){if(f=Pa(o,s,n),f===u)return r[i]=o,r[a]=s,t(r);if(f>0)o=Math.floor(o/f)*f,s=Math.ceil(s/f)*f;else if(f<0)o=Math.ceil(o*f)/f,s=Math.floor(s*f)/f;else break;u=f}return e},e}function si(){var e=Ao();return e.copy=function(){return Zn(e,si())},Re.apply(e,arguments),gt(e)}function _p(e){var t;function n(r){return r==null||isNaN(r=+r)?t:r}return n.invert=n,n.domain=n.range=function(r){return arguments.length?(e=Array.from(r,Cr),n):e.slice()},n.unknown=function(r){return arguments.length?(t=r,n):t},n.copy=function(){return _p(e).unknown(t)},e=arguments.length?Array.from(e,Cr):[0,1],gt(n)}function ul(e,t){e=e.slice();var n=0,r=e.length-1,i=e[n],a=e[r],o;return a<i&&(o=n,n=r,r=o,o=i,i=a,a=o),e[n]=t.floor(i),e[r]=t.ceil(a),e}function $u(e){return Math.log(e)}function Tu(e){return Math.exp(e)}function xp(e){return-Math.log(-e)}function wp(e){return-Math.exp(-e)}function Mp(e){return isFinite(e)?+("1e"+e):e<0?0:e}function $p(e){return e===10?Mp:e===Math.E?Math.exp:t=>Math.pow(e,t)}function Tp(e){return e===Math.E?Math.log:e===10&&Math.log10||e===2&&Math.log2||(e=Math.log(e),t=>Math.log(t)/e)}function Cu(e){return(t,n)=>-e(-t,n)}function Do(e){const t=e($u,Tu),n=t.domain;let r=10,i,a;function o(){return i=Tp(r),a=$p(r),n()[0]<0?(i=Cu(i),a=Cu(a),e(xp,wp)):e($u,Tu),t}return t.base=function(s){return arguments.length?(r=+s,o()):r},t.domain=function(s){return arguments.length?(n(s),o()):n()},t.ticks=s=>{const u=n();let f=u[0],l=u[u.length-1];const c=l<f;c&&([f,l]=[l,f]);let d=i(f),g=i(l),b,p;const y=s==null?10:+s;let v=[];if(!(r%1)&&g-d<y){if(d=Math.floor(d),g=Math.ceil(g),f>0){for(;d<=g;++d)for(b=1;b<r;++b)if(p=d<0?b/a(-d):b*a(d),!(p<f)){if(p>l)break;v.push(p)}}else for(;d<=g;++d)for(b=r-1;b>=1;--b)if(p=d>0?b/a(-d):b*a(d),!(p<f)){if(p>l)break;v.push(p)}v.length*2<y&&(v=Ua(f,l,y))}else v=Ua(d,g,Math.min(g-d,y)).map(a);return c?v.reverse():v},t.tickFormat=(s,u)=>{if(s==null&&(s=10),u==null&&(u=r===10?"s":","),typeof u!="function"&&(!(r%1)&&(u=Pn(u)).precision==null&&(u.trim=!0),u=Po(u)),s===1/0)return u;const f=Math.max(1,r*s/t.ticks().length);return l=>{let c=l/a(Math.round(i(l)));return c*r<r-.5&&(c*=r),c<=f?u(l):""}},t.nice=()=>n(ul(n(),{floor:s=>a(Math.floor(i(s))),ceil:s=>a(Math.ceil(i(s)))})),t}function Cp(){const e=Do(oi()).domain([1,10]);return e.copy=()=>Zn(e,Cp()).base(e.base()),Re.apply(e,arguments),e}function ku(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function Su(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function Fo(e){var t=1,n=e(ku(t),Su(t));return n.constant=function(r){return arguments.length?e(ku(t=+r),Su(t)):t},gt(n)}function kp(){var e=Fo(oi());return e.copy=function(){return Zn(e,kp()).constant(e.constant())},Re.apply(e,arguments)}function Ou(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function Sp(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function Op(e){return e<0?-e*e:e*e}function Ro(e){var t=e($e,$e),n=1;function r(){return n===1?e($e,$e):n===.5?e(Sp,Op):e(Ou(n),Ou(1/n))}return t.exponent=function(i){return arguments.length?(n=+i,r()):n},gt(t)}function fl(){var e=Ro(oi());return e.copy=function(){return Zn(e,fl()).exponent(e.exponent())},Re.apply(e,arguments),e}function R9(){return fl.apply(null,arguments).exponent(.5)}function Au(e){return Math.sign(e)*e*e}function Ap(e){return Math.sign(e)*Math.sqrt(Math.abs(e))}function Up(){var e=Ao(),t=[0,1],n=!1,r;function i(a){var o=Ap(e(a));return isNaN(o)?r:n?Math.round(o):o}return i.invert=function(a){return e.invert(Au(a))},i.domain=function(a){return arguments.length?(e.domain(a),i):e.domain()},i.range=function(a){return arguments.length?(e.range((t=Array.from(a,Cr)).map(Au)),i):t.slice()},i.rangeRound=function(a){return i.range(a).round(!0)},i.round=function(a){return arguments.length?(n=!!a,i):n},i.clamp=function(a){return arguments.length?(e.clamp(a),i):e.clamp()},i.unknown=function(a){return arguments.length?(r=a,i):r},i.copy=function(){return Up(e.domain(),t).round(n).clamp(e.clamp()).unknown(r)},Re.apply(i,arguments),gt(i)}function Pp(){var e=[],t=[],n=[],r;function i(){var o=0,s=Math.max(1,t.length);for(n=new Array(s-1);++o<s;)n[o-1]=k2(e,o/s);return a}function a(o){return o==null||isNaN(o=+o)?r:t[Qn(n,o)]}return a.invertExtent=function(o){var s=t.indexOf(o);return s<0?[NaN,NaN]:[s>0?n[s-1]:e[0],s<n.length?n[s]:e[e.length-1]]},a.domain=function(o){if(!arguments.length)return e.slice();e=[];for(let s of o)s!=null&&!isNaN(s=+s)&&e.push(s);return e.sort(ft),i()},a.range=function(o){return arguments.length?(t=Array.from(o),i()):t.slice()},a.unknown=function(o){return arguments.length?(r=o,a):r},a.quantiles=function(){return n.slice()},a.copy=function(){return Pp().domain(e).range(t).unknown(r)},Re.apply(a,arguments)}function cl(){var e=0,t=1,n=1,r=[.5],i=[0,1],a;function o(u){return u!=null&&u<=u?i[Qn(r,u,0,n)]:a}function s(){var u=-1;for(r=new Array(n);++u<n;)r[u]=((u+1)*t-(u-n)*e)/(n+1);return o}return o.domain=function(u){return arguments.length?([e,t]=u,e=+e,t=+t,s()):[e,t]},o.range=function(u){return arguments.length?(n=(i=Array.from(u)).length-1,s()):i.slice()},o.invertExtent=function(u){var f=i.indexOf(u);return f<0?[NaN,NaN]:f<1?[e,r[0]]:f>=n?[r[n-1],t]:[r[f-1],r[f]]},o.unknown=function(u){return arguments.length&&(a=u),o},o.thresholds=function(){return r.slice()},o.copy=function(){return cl().domain([e,t]).range(i).unknown(a)},Re.apply(gt(o),arguments)}function Dp(){var e=[.5],t=[0,1],n,r=1;function i(a){return a!=null&&a<=a?t[Qn(e,a,0,r)]:n}return i.domain=function(a){return arguments.length?(e=Array.from(a),r=Math.min(e.length,t.length-1),i):e.slice()},i.range=function(a){return arguments.length?(t=Array.from(a),r=Math.min(e.length,t.length-1),i):t.slice()},i.invertExtent=function(a){var o=t.indexOf(a);return[e[o-1],e[o]]},i.unknown=function(a){return arguments.length?(n=a,i):n},i.copy=function(){return Dp().domain(e).range(t).unknown(n)},Re.apply(i,arguments)}const oa=new Date,sa=new Date;function be(e,t,n,r){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=a=>(e(a=new Date(+a)),a),i.ceil=a=>(e(a=new Date(a-1)),t(a,1),e(a),a),i.round=a=>{const o=i(a),s=i.ceil(a);return a-o<s-a?o:s},i.offset=(a,o)=>(t(a=new Date(+a),o==null?1:Math.floor(o)),a),i.range=(a,o,s)=>{const u=[];if(a=i.ceil(a),s=s==null?1:Math.floor(s),!(a<o)||!(s>0))return u;let f;do u.push(f=new Date(+a)),t(a,s),e(a);while(f<a&&a<o);return u},i.filter=a=>be(o=>{if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},(o,s)=>{if(o>=o)if(s<0)for(;++s<=0;)for(;t(o,-1),!a(o););else for(;--s>=0;)for(;t(o,1),!a(o););}),n&&(i.count=(a,o)=>(oa.setTime(+a),sa.setTime(+o),e(oa),e(sa),Math.floor(n(oa,sa))),i.every=a=>(a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(r?o=>r(o)%a===0:o=>i.count(0,o)%a===0):i)),i}const Sr=be(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);Sr.every=e=>(e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?be(t=>{t.setTime(Math.floor(t/e)*e)},(t,n)=>{t.setTime(+t+n*e)},(t,n)=>(n-t)/e):Sr);Sr.range;const Ze=1e3,Fe=Ze*60,Je=Fe*60,Ke=Je*24,Io=Ke*7,Uu=Ke*30,ua=Ke*365,vt=be(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+t*Ze)},(e,t)=>(t-e)/Ze,e=>e.getUTCSeconds());vt.range;const No=be(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Ze)},(e,t)=>{e.setTime(+e+t*Fe)},(e,t)=>(t-e)/Fe,e=>e.getMinutes());No.range;const Eo=be(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+t*Fe)},(e,t)=>(t-e)/Fe,e=>e.getUTCMinutes());Eo.range;const Yo=be(e=>{e.setTime(e-e.getMilliseconds()-e.getSeconds()*Ze-e.getMinutes()*Fe)},(e,t)=>{e.setTime(+e+t*Je)},(e,t)=>(t-e)/Je,e=>e.getHours());Yo.range;const Lo=be(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+t*Je)},(e,t)=>(t-e)/Je,e=>e.getUTCHours());Lo.range;const Jn=be(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*Fe)/Ke,e=>e.getDate()-1);Jn.range;const ui=be(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Ke,e=>e.getUTCDate()-1);ui.range;const ll=be(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/Ke,e=>Math.floor(e/Ke));ll.range;function Ct(e){return be(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(t,n)=>{t.setDate(t.getDate()+n*7)},(t,n)=>(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*Fe)/Io)}const fi=Ct(0),Or=Ct(1),Fp=Ct(2),Rp=Ct(3),Ht=Ct(4),Ip=Ct(5),Np=Ct(6);fi.range;Or.range;Fp.range;Rp.range;Ht.range;Ip.range;Np.range;function kt(e){return be(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCDate(t.getUTCDate()+n*7)},(t,n)=>(n-t)/Io)}const ci=kt(0),Ar=kt(1),Ep=kt(2),Yp=kt(3),Vt=kt(4),Lp=kt(5),Wp=kt(6);ci.range;Ar.range;Ep.range;Yp.range;Vt.range;Lp.range;Wp.range;const Wo=be(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());Wo.range;const jo=be(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());jo.range;const et=be(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());et.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:be(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,n)=>{t.setFullYear(t.getFullYear()+n*e)});et.range;const tt=be(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());tt.every=e=>!isFinite(e=Math.floor(e))||!(e>0)?null:be(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,n)=>{t.setUTCFullYear(t.getUTCFullYear()+n*e)});tt.range;function dl(e,t,n,r,i,a){const o=[[vt,1,Ze],[vt,5,5*Ze],[vt,15,15*Ze],[vt,30,30*Ze],[a,1,Fe],[a,5,5*Fe],[a,15,15*Fe],[a,30,30*Fe],[i,1,Je],[i,3,3*Je],[i,6,6*Je],[i,12,12*Je],[r,1,Ke],[r,2,2*Ke],[n,1,Io],[t,1,Uu],[t,3,3*Uu],[e,1,ua]];function s(f,l,c){const d=l<f;d&&([f,l]=[l,f]);const g=c&&typeof c.range=="function"?c:u(f,l,c),b=g?g.range(f,+l+1):[];return d?b.reverse():b}function u(f,l,c){const d=Math.abs(l-f)/c,g=Mo(([,,y])=>y).right(o,d);if(g===o.length)return e.every(Da(f/ua,l/ua,c));if(g===0)return Sr.every(Math.max(Da(f,l,c),1));const[b,p]=o[d/o[g-1][2]<o[g][2]/d?g-1:g];return b.every(p)}return[s,u]}const[jp,qp]=dl(tt,jo,ci,ll,Lo,Eo),[zp,Hp]=dl(et,Wo,fi,Jn,Yo,No);function fa(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function ca(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function un(e,t,n){return{y:e,m:t,d:n,H:0,M:0,S:0,L:0}}function Vp(e){var t=e.dateTime,n=e.date,r=e.time,i=e.periods,a=e.days,o=e.shortDays,s=e.months,u=e.shortMonths,f=fn(i),l=cn(i),c=fn(a),d=cn(a),g=fn(o),b=cn(o),p=fn(s),y=cn(s),v=fn(u),O=cn(u),_={a:j,A:z,b:R,B:K,c:null,d:Nu,e:Nu,f:my,g:Ty,G:ky,H:dy,I:hy,j:gy,L:hl,m:by,M:py,p:he,q:J,Q:Lu,s:Wu,S:yy,u:vy,U:_y,V:xy,w:wy,W:My,x:null,X:null,y:$y,Y:Cy,Z:Sy,"%":Yu},k={a:ge,A:ee,b:ae,B:ce,c:null,d:Eu,e:Eu,f:Py,g:jy,G:zy,H:Oy,I:Ay,j:Uy,L:ml,m:Dy,M:Fy,p:oe,q:me,Q:Lu,s:Wu,S:Ry,u:Iy,U:Ny,V:Ey,w:Yy,W:Ly,x:null,X:null,y:Wy,Y:qy,Z:Hy,"%":Yu},A={a:L,A:w,b:q,B:D,c:U,d:Ru,e:Ru,f:uy,g:Fu,G:Du,H:Iu,I:Iu,j:iy,L:sy,m:ry,M:ay,p:E,q:ny,Q:cy,s:ly,S:oy,u:Zp,U:Jp,V:Kp,w:Qp,W:ey,x:N,X:W,y:Fu,Y:Du,Z:ty,"%":fy};_.x=C(n,_),_.X=C(r,_),_.c=C(t,_),k.x=C(n,k),k.X=C(r,k),k.c=C(t,k);function C(m,M){return function(S){var h=[],V=-1,I=0,B=m.length,X,fe,Ie;for(S instanceof Date||(S=new Date(+S));++V<B;)m.charCodeAt(V)===37&&(h.push(m.slice(I,V)),(fe=Pu[X=m.charAt(++V)])!=null?X=m.charAt(++V):fe=X==="e"?" ":"0",(Ie=M[X])&&(X=Ie(S,fe)),h.push(X),I=V+1);return h.push(m.slice(I,V)),h.join("")}}function F(m,M){return function(S){var h=un(1900,void 0,1),V=Y(h,m,S+="",0),I,B;if(V!=S.length)return null;if("Q"in h)return new Date(h.Q);if("s"in h)return new Date(h.s*1e3+("L"in h?h.L:0));if(M&&!("Z"in h)&&(h.Z=0),"p"in h&&(h.H=h.H%12+h.p*12),h.m===void 0&&(h.m="q"in h?h.q:0),"V"in h){if(h.V<1||h.V>53)return null;"w"in h||(h.w=1),"Z"in h?(I=ca(un(h.y,0,1)),B=I.getUTCDay(),I=B>4||B===0?Ar.ceil(I):Ar(I),I=ui.offset(I,(h.V-1)*7),h.y=I.getUTCFullYear(),h.m=I.getUTCMonth(),h.d=I.getUTCDate()+(h.w+6)%7):(I=fa(un(h.y,0,1)),B=I.getDay(),I=B>4||B===0?Or.ceil(I):Or(I),I=Jn.offset(I,(h.V-1)*7),h.y=I.getFullYear(),h.m=I.getMonth(),h.d=I.getDate()+(h.w+6)%7)}else("W"in h||"U"in h)&&("w"in h||(h.w="u"in h?h.u%7:"W"in h?1:0),B="Z"in h?ca(un(h.y,0,1)).getUTCDay():fa(un(h.y,0,1)).getDay(),h.m=0,h.d="W"in h?(h.w+6)%7+h.W*7-(B+5)%7:h.w+h.U*7-(B+6)%7);return"Z"in h?(h.H+=h.Z/100|0,h.M+=h.Z%100,ca(h)):fa(h)}}function Y(m,M,S,h){for(var V=0,I=M.length,B=S.length,X,fe;V<I;){if(h>=B)return-1;if(X=M.charCodeAt(V++),X===37){if(X=M.charAt(V++),fe=A[X in Pu?M.charAt(V++):X],!fe||(h=fe(m,S,h))<0)return-1}else if(X!=S.charCodeAt(h++))return-1}return h}function E(m,M,S){var h=f.exec(M.slice(S));return h?(m.p=l.get(h[0].toLowerCase()),S+h[0].length):-1}function L(m,M,S){var h=g.exec(M.slice(S));return h?(m.w=b.get(h[0].toLowerCase()),S+h[0].length):-1}function w(m,M,S){var h=c.exec(M.slice(S));return h?(m.w=d.get(h[0].toLowerCase()),S+h[0].length):-1}function q(m,M,S){var h=v.exec(M.slice(S));return h?(m.m=O.get(h[0].toLowerCase()),S+h[0].length):-1}function D(m,M,S){var h=p.exec(M.slice(S));return h?(m.m=y.get(h[0].toLowerCase()),S+h[0].length):-1}function U(m,M,S){return Y(m,t,M,S)}function N(m,M,S){return Y(m,n,M,S)}function W(m,M,S){return Y(m,r,M,S)}function j(m){return o[m.getDay()]}function z(m){return a[m.getDay()]}function R(m){return u[m.getMonth()]}function K(m){return s[m.getMonth()]}function he(m){return i[+(m.getHours()>=12)]}function J(m){return 1+~~(m.getMonth()/3)}function ge(m){return o[m.getUTCDay()]}function ee(m){return a[m.getUTCDay()]}function ae(m){return u[m.getUTCMonth()]}function ce(m){return s[m.getUTCMonth()]}function oe(m){return i[+(m.getUTCHours()>=12)]}function me(m){return 1+~~(m.getUTCMonth()/3)}return{format:function(m){var M=C(m+="",_);return M.toString=function(){return m},M},parse:function(m){var M=F(m+="",!1);return M.toString=function(){return m},M},utcFormat:function(m){var M=C(m+="",k);return M.toString=function(){return m},M},utcParse:function(m){var M=F(m+="",!0);return M.toString=function(){return m},M}}}var Pu={"-":"",_:" ",0:"0"},ve=/^\s*\d+/,Bp=/^%/,Gp=/[\\^$*+?|[\]().{}]/g;function ne(e,t,n){var r=e<0?"-":"",i=(r?-e:e)+"",a=i.length;return r+(a<n?new Array(n-a+1).join(t)+i:i)}function Xp(e){return e.replace(Gp,"\\$&")}function fn(e){return new RegExp("^(?:"+e.map(Xp).join("|")+")","i")}function cn(e){return new Map(e.map((t,n)=>[t.toLowerCase(),n]))}function Qp(e,t,n){var r=ve.exec(t.slice(n,n+1));return r?(e.w=+r[0],n+r[0].length):-1}function Zp(e,t,n){var r=ve.exec(t.slice(n,n+1));return r?(e.u=+r[0],n+r[0].length):-1}function Jp(e,t,n){var r=ve.exec(t.slice(n,n+2));return r?(e.U=+r[0],n+r[0].length):-1}function Kp(e,t,n){var r=ve.exec(t.slice(n,n+2));return r?(e.V=+r[0],n+r[0].length):-1}function ey(e,t,n){var r=ve.exec(t.slice(n,n+2));return r?(e.W=+r[0],n+r[0].length):-1}function Du(e,t,n){var r=ve.exec(t.slice(n,n+4));return r?(e.y=+r[0],n+r[0].length):-1}function Fu(e,t,n){var r=ve.exec(t.slice(n,n+2));return r?(e.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function ty(e,t,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(n,n+6));return r?(e.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function ny(e,t,n){var r=ve.exec(t.slice(n,n+1));return r?(e.q=r[0]*3-3,n+r[0].length):-1}function ry(e,t,n){var r=ve.exec(t.slice(n,n+2));return r?(e.m=r[0]-1,n+r[0].length):-1}function Ru(e,t,n){var r=ve.exec(t.slice(n,n+2));return r?(e.d=+r[0],n+r[0].length):-1}function iy(e,t,n){var r=ve.exec(t.slice(n,n+3));return r?(e.m=0,e.d=+r[0],n+r[0].length):-1}function Iu(e,t,n){var r=ve.exec(t.slice(n,n+2));return r?(e.H=+r[0],n+r[0].length):-1}function ay(e,t,n){var r=ve.exec(t.slice(n,n+2));return r?(e.M=+r[0],n+r[0].length):-1}function oy(e,t,n){var r=ve.exec(t.slice(n,n+2));return r?(e.S=+r[0],n+r[0].length):-1}function sy(e,t,n){var r=ve.exec(t.slice(n,n+3));return r?(e.L=+r[0],n+r[0].length):-1}function uy(e,t,n){var r=ve.exec(t.slice(n,n+6));return r?(e.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function fy(e,t,n){var r=Bp.exec(t.slice(n,n+1));return r?n+r[0].length:-1}function cy(e,t,n){var r=ve.exec(t.slice(n));return r?(e.Q=+r[0],n+r[0].length):-1}function ly(e,t,n){var r=ve.exec(t.slice(n));return r?(e.s=+r[0],n+r[0].length):-1}function Nu(e,t){return ne(e.getDate(),t,2)}function dy(e,t){return ne(e.getHours(),t,2)}function hy(e,t){return ne(e.getHours()%12||12,t,2)}function gy(e,t){return ne(1+Jn.count(et(e),e),t,3)}function hl(e,t){return ne(e.getMilliseconds(),t,3)}function my(e,t){return hl(e,t)+"000"}function by(e,t){return ne(e.getMonth()+1,t,2)}function py(e,t){return ne(e.getMinutes(),t,2)}function yy(e,t){return ne(e.getSeconds(),t,2)}function vy(e){var t=e.getDay();return t===0?7:t}function _y(e,t){return ne(fi.count(et(e)-1,e),t,2)}function gl(e){var t=e.getDay();return t>=4||t===0?Ht(e):Ht.ceil(e)}function xy(e,t){return e=gl(e),ne(Ht.count(et(e),e)+(et(e).getDay()===4),t,2)}function wy(e){return e.getDay()}function My(e,t){return ne(Or.count(et(e)-1,e),t,2)}function $y(e,t){return ne(e.getFullYear()%100,t,2)}function Ty(e,t){return e=gl(e),ne(e.getFullYear()%100,t,2)}function Cy(e,t){return ne(e.getFullYear()%1e4,t,4)}function ky(e,t){var n=e.getDay();return e=n>=4||n===0?Ht(e):Ht.ceil(e),ne(e.getFullYear()%1e4,t,4)}function Sy(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+ne(t/60|0,"0",2)+ne(t%60,"0",2)}function Eu(e,t){return ne(e.getUTCDate(),t,2)}function Oy(e,t){return ne(e.getUTCHours(),t,2)}function Ay(e,t){return ne(e.getUTCHours()%12||12,t,2)}function Uy(e,t){return ne(1+ui.count(tt(e),e),t,3)}function ml(e,t){return ne(e.getUTCMilliseconds(),t,3)}function Py(e,t){return ml(e,t)+"000"}function Dy(e,t){return ne(e.getUTCMonth()+1,t,2)}function Fy(e,t){return ne(e.getUTCMinutes(),t,2)}function Ry(e,t){return ne(e.getUTCSeconds(),t,2)}function Iy(e){var t=e.getUTCDay();return t===0?7:t}function Ny(e,t){return ne(ci.count(tt(e)-1,e),t,2)}function bl(e){var t=e.getUTCDay();return t>=4||t===0?Vt(e):Vt.ceil(e)}function Ey(e,t){return e=bl(e),ne(Vt.count(tt(e),e)+(tt(e).getUTCDay()===4),t,2)}function Yy(e){return e.getUTCDay()}function Ly(e,t){return ne(Ar.count(tt(e)-1,e),t,2)}function Wy(e,t){return ne(e.getUTCFullYear()%100,t,2)}function jy(e,t){return e=bl(e),ne(e.getUTCFullYear()%100,t,2)}function qy(e,t){return ne(e.getUTCFullYear()%1e4,t,4)}function zy(e,t){var n=e.getUTCDay();return e=n>=4||n===0?Vt(e):Vt.ceil(e),ne(e.getUTCFullYear()%1e4,t,4)}function Hy(){return"+0000"}function Yu(){return"%"}function Lu(e){return+e}function Wu(e){return Math.floor(+e/1e3)}var Ft,pl,yl;Vy({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function Vy(e){return Ft=Vp(e),pl=Ft.format,Ft.parse,yl=Ft.utcFormat,Ft.utcParse,Ft}function By(e){return new Date(e)}function Gy(e){return e instanceof Date?+e:+new Date(+e)}function qo(e,t,n,r,i,a,o,s,u,f){var l=Ao(),c=l.invert,d=l.domain,g=f(".%L"),b=f(":%S"),p=f("%I:%M"),y=f("%I %p"),v=f("%a %d"),O=f("%b %d"),_=f("%B"),k=f("%Y");function A(C){return(u(C)<C?g:s(C)<C?b:o(C)<C?p:a(C)<C?y:r(C)<C?i(C)<C?v:O:n(C)<C?_:k)(C)}return l.invert=function(C){return new Date(c(C))},l.domain=function(C){return arguments.length?d(Array.from(C,Gy)):d().map(By)},l.ticks=function(C){var F=d();return e(F[0],F[F.length-1],C??10)},l.tickFormat=function(C,F){return F==null?A:f(F)},l.nice=function(C){var F=d();return(!C||typeof C.range!="function")&&(C=t(F[0],F[F.length-1],C??10)),C?d(ul(F,C)):l},l.copy=function(){return Zn(l,qo(e,t,n,r,i,a,o,s,u,f))},l}function I9(){return Re.apply(qo(zp,Hp,et,Wo,fi,Jn,Yo,No,vt,pl).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function N9(){return Re.apply(qo(jp,qp,tt,jo,ci,ui,Lo,Eo,vt,yl).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function li(){var e=0,t=1,n,r,i,a,o=$e,s=!1,u;function f(c){return c==null||isNaN(c=+c)?u:o(i===0?.5:(c=(a(c)-n)*i,s?Math.max(0,Math.min(1,c)):c))}f.domain=function(c){return arguments.length?([e,t]=c,n=a(e=+e),r=a(t=+t),i=n===r?0:1/(r-n),f):[e,t]},f.clamp=function(c){return arguments.length?(s=!!c,f):s},f.interpolator=function(c){return arguments.length?(o=c,f):o};function l(c){return function(d){var g,b;return arguments.length?([g,b]=d,o=c(g,b),f):[o(0),o(1)]}}return f.range=l(nn),f.rangeRound=l(So),f.unknown=function(c){return arguments.length?(u=c,f):u},function(c){return a=c,n=c(e),r=c(t),i=n===r?0:1/(r-n),f}}function mt(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function vl(){var e=gt(li()($e));return e.copy=function(){return mt(e,vl())},nt.apply(e,arguments)}function Xy(){var e=Do(li()).domain([1,10]);return e.copy=function(){return mt(e,Xy()).base(e.base())},nt.apply(e,arguments)}function Qy(){var e=Fo(li());return e.copy=function(){return mt(e,Qy()).constant(e.constant())},nt.apply(e,arguments)}function _l(){var e=Ro(li());return e.copy=function(){return mt(e,_l()).exponent(e.exponent())},nt.apply(e,arguments)}function E9(){return _l.apply(null,arguments).exponent(.5)}function Zy(){var e=[],t=$e;function n(r){if(r!=null&&!isNaN(r=+r))return t((Qn(e,r,1)-1)/(e.length-1))}return n.domain=function(r){if(!arguments.length)return e.slice();e=[];for(let i of r)i!=null&&!isNaN(i=+i)&&e.push(i);return e.sort(ft),n},n.interpolator=function(r){return arguments.length?(t=r,n):t},n.range=function(){return e.map((r,i)=>t(i/(e.length-1)))},n.quantiles=function(r){return Array.from({length:r+1},(i,a)=>C2(e,a/r))},n.copy=function(){return Zy(t).domain(e)},nt.apply(n,arguments)}function di(){var e=0,t=.5,n=1,r=1,i,a,o,s,u,f=$e,l,c=!1,d;function g(p){return isNaN(p=+p)?d:(p=.5+((p=+l(p))-a)*(r*p<r*a?s:u),f(c?Math.max(0,Math.min(1,p)):p))}g.domain=function(p){return arguments.length?([e,t,n]=p,i=l(e=+e),a=l(t=+t),o=l(n=+n),s=i===a?0:.5/(a-i),u=a===o?0:.5/(o-a),r=a<i?-1:1,g):[e,t,n]},g.clamp=function(p){return arguments.length?(c=!!p,g):c},g.interpolator=function(p){return arguments.length?(f=p,g):f};function b(p){return function(y){var v,O,_;return arguments.length?([v,O,_]=y,f=rp(p,[v,O,_]),g):[f(0),f(.5),f(1)]}}return g.range=b(nn),g.rangeRound=b(So),g.unknown=function(p){return arguments.length?(d=p,g):d},function(p){return l=p,i=p(e),a=p(t),o=p(n),s=i===a?0:.5/(a-i),u=a===o?0:.5/(o-a),r=a<i?-1:1,g}}function Ia(){var e=gt(di()($e));return e.copy=function(){return mt(e,Ia())},nt.apply(e,arguments)}function Jy(){var e=Do(di()).domain([.1,1,10]);return e.copy=function(){return mt(e,Jy()).base(e.base())},nt.apply(e,arguments)}function Ky(){var e=Fo(di());return e.copy=function(){return mt(e,Ky()).constant(e.constant())},nt.apply(e,arguments)}function xl(){var e=Ro(di());return e.copy=function(){return mt(e,xl()).exponent(e.exponent())},nt.apply(e,arguments)}function Y9(){return xl.apply(null,arguments).exponent(.5)}function ev(e){var t=e==null?0:e.length;return t?e[t-1]:void 0}var wl=ev;const H=Oe(wl);var tv=Gr,nv=uh,rv=Bn,iv="[object Object]",av=Function.prototype,ov=Object.prototype,Ml=av.toString,sv=ov.hasOwnProperty,uv=Ml.call(Object);function fv(e){if(!rv(e)||tv(e)!=iv)return!1;var t=nv(e);if(t===null)return!0;var n=sv.call(t,"constructor")&&t.constructor;return typeof n=="function"&&n instanceof n&&Ml.call(n)==uv}var zo=fv;const cv=Oe(zo);var lv=Xn,dv=D1;function hv(e,t){return t.length<2?e:lv(e,dv(t,0,-1))}var gv=hv,mv=Kt,bv=wl,pv=gv,yv=en;function vv(e,t){return t=mv(t,e),e=pv(e,t),e==null||delete e[yv(bv(t))]}var _v=vv,xv=zo;function wv(e){return xv(e)?void 0:e}var Mv=wv,$v=zc;function Tv(e){var t=e==null?0:e.length;return t?$v(e,1):[]}var Cv=Tv,kv=Cv,Sv=Bc,Ov=Gc;function Av(e){return Ov(Sv(e,void 0,kv),e+"")}var $l=Av,Uv=Jr,Pv=ch,Dv=_v,Fv=Kt,Rv=dc,Iv=Mv,Nv=$l,Ev=fh,Yv=1,Lv=2,Wv=4,jv=Nv(function(e,t){var n={};if(e==null)return n;var r=!1;t=Uv(t,function(a){return a=Fv(a,e),r||(r=a.length>1),a}),Rv(e,Ev(e),n),r&&(n=Pv(n,Yv|Lv|Wv,Iv));for(var i=t.length;i--;)Dv(n,t[i]);return n}),qv=jv;const Tl=Oe(qv);var Ho=er(),Q=e=>Kn(e,Ho),Vo=er();Q.write=e=>Kn(e,Vo);var hi=er();Q.onStart=e=>Kn(e,hi);var Bo=er();Q.onFrame=e=>Kn(e,Bo);var Go=er();Q.onFinish=e=>Kn(e,Go);var Lt=[];Q.setTimeout=(e,t)=>{const n=Q.now()+t,r=()=>{const a=Lt.findIndex(o=>o.cancel==r);~a&&Lt.splice(a,1),ut-=~a?1:0},i={time:n,handler:e,cancel:r};return Lt.splice(Cl(n),0,i),ut+=1,kl(),i};var Cl=e=>~(~Lt.findIndex(t=>t.time>e)||~Lt.length);Q.cancel=e=>{hi.delete(e),Bo.delete(e),Go.delete(e),Ho.delete(e),Vo.delete(e)};Q.sync=e=>{Na=!0,Q.batchedUpdates(e),Na=!1};Q.throttle=e=>{let t;function n(){try{e(...t)}finally{t=null}}function r(...i){t=i,Q.onStart(n)}return r.handler=e,r.cancel=()=>{hi.delete(n),t=null},r};var Xo=typeof window<"u"?window.requestAnimationFrame:()=>{};Q.use=e=>Xo=e;Q.now=typeof performance<"u"?()=>performance.now():Date.now;Q.batchedUpdates=e=>e();Q.catch=console.error;Q.frameLoop="always";Q.advance=()=>{Q.frameLoop!=="demand"?console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"):Ol()};var st=-1,ut=0,Na=!1;function Kn(e,t){Na?(t.delete(e),e(0)):(t.add(e),kl())}function kl(){st<0&&(st=0,Q.frameLoop!=="demand"&&Xo(Sl))}function zv(){st=-1}function Sl(){~st&&(Xo(Sl),Q.batchedUpdates(Ol))}function Ol(){const e=st;st=Q.now();const t=Cl(st);if(t&&(Al(Lt.splice(0,t),n=>n.handler()),ut-=t),!ut){zv();return}hi.flush(),Ho.flush(e?Math.min(64,st-e):16.667),Bo.flush(),Vo.flush(),Go.flush()}function er(){let e=new Set,t=e;return{add(n){ut+=t==e&&!e.has(n)?1:0,e.add(n)},delete(n){return ut-=t==e&&e.has(n)?1:0,e.delete(n)},flush(n){t.size&&(e=new Set,ut-=t.size,Al(t,r=>r(n)&&e.add(r)),ut+=e.size,t=e)}}}function Al(e,t){e.forEach(n=>{try{t(n)}catch(r){Q.catch(r)}})}var Hv=Object.defineProperty,Vv=(e,t)=>{for(var n in t)Hv(e,n,{get:t[n],enumerable:!0})},We={};Vv(We,{assign:()=>Gv,colors:()=>ct,createStringInterpolator:()=>Zo,skipAnimation:()=>Pl,to:()=>Ul,willAdvance:()=>Jo});function Ea(){}var Bv=(e,t,n)=>Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0}),P={arr:Array.isArray,obj:e=>!!e&&e.constructor.name==="Object",fun:e=>typeof e=="function",str:e=>typeof e=="string",num:e=>typeof e=="number",und:e=>e===void 0};function Qe(e,t){if(P.arr(e)){if(!P.arr(t)||e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}return e===t}var G=(e,t)=>e.forEach(t);function Ge(e,t,n){if(P.arr(e)){for(let r=0;r<e.length;r++)t.call(n,e[r],`${r}`);return}for(const r in e)e.hasOwnProperty(r)&&t.call(n,e[r],r)}var Te=e=>P.und(e)?[]:P.arr(e)?e:[e];function $n(e,t){if(e.size){const n=Array.from(e);e.clear(),G(n,t)}}var Mn=(e,...t)=>$n(e,n=>n(...t)),Qo=()=>typeof window>"u"||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent),Zo,Ul,ct=null,Pl=!1,Jo=Ea,Gv=e=>{e.to&&(Ul=e.to),e.now&&(Q.now=e.now),e.colors!==void 0&&(ct=e.colors),e.skipAnimation!=null&&(Pl=e.skipAnimation),e.createStringInterpolator&&(Zo=e.createStringInterpolator),e.requestAnimationFrame&&Q.use(e.requestAnimationFrame),e.batchedUpdates&&(Q.batchedUpdates=e.batchedUpdates),e.willAdvance&&(Jo=e.willAdvance),e.frameLoop&&(Q.frameLoop=e.frameLoop)},Tn=new Set,De=[],la=[],Ur=0,gi={get idle(){return!Tn.size&&!De.length},start(e){Ur>e.priority?(Tn.add(e),Q.onStart(Xv)):(Dl(e),Q(Ya))},advance:Ya,sort(e){if(Ur)Q.onFrame(()=>gi.sort(e));else{const t=De.indexOf(e);~t&&(De.splice(t,1),Fl(e))}},clear(){De=[],Tn.clear()}};function Xv(){Tn.forEach(Dl),Tn.clear(),Q(Ya)}function Dl(e){De.includes(e)||Fl(e)}function Fl(e){De.splice(Qv(De,t=>t.priority>e.priority),0,e)}function Ya(e){const t=la;for(let n=0;n<De.length;n++){const r=De[n];Ur=r.priority,r.idle||(Jo(r),r.advance(e),r.idle||t.push(r))}return Ur=0,la=De,la.length=0,De=t,De.length>0}function Qv(e,t){const n=e.findIndex(t);return n<0?e.length:n}var Zv=(e,t,n)=>Math.min(Math.max(n,e),t),Jv={transparent:0,aliceblue:4042850303,antiquewhite:4209760255,aqua:16777215,aquamarine:2147472639,azure:4043309055,beige:4126530815,bisque:4293182719,black:255,blanchedalmond:4293643775,blue:65535,blueviolet:2318131967,brown:2771004159,burlywood:3736635391,burntsienna:3934150143,cadetblue:1604231423,chartreuse:2147418367,chocolate:3530104575,coral:4286533887,cornflowerblue:1687547391,cornsilk:4294499583,crimson:3692313855,cyan:16777215,darkblue:35839,darkcyan:9145343,darkgoldenrod:3095792639,darkgray:2846468607,darkgreen:6553855,darkgrey:2846468607,darkkhaki:3182914559,darkmagenta:2332068863,darkolivegreen:1433087999,darkorange:4287365375,darkorchid:2570243327,darkred:2332033279,darksalmon:3918953215,darkseagreen:2411499519,darkslateblue:1211993087,darkslategray:793726975,darkslategrey:793726975,darkturquoise:13554175,darkviolet:2483082239,deeppink:4279538687,deepskyblue:12582911,dimgray:1768516095,dimgrey:1768516095,dodgerblue:512819199,firebrick:2988581631,floralwhite:4294635775,forestgreen:579543807,fuchsia:4278255615,gainsboro:3705462015,ghostwhite:4177068031,gold:4292280575,goldenrod:3668254975,gray:2155905279,green:8388863,greenyellow:2919182335,grey:2155905279,honeydew:4043305215,hotpink:4285117695,indianred:3445382399,indigo:1258324735,ivory:4294963455,khaki:4041641215,lavender:3873897215,lavenderblush:4293981695,lawngreen:2096890111,lemonchiffon:4294626815,lightblue:2916673279,lightcoral:4034953471,lightcyan:3774873599,lightgoldenrodyellow:4210742015,lightgray:3553874943,lightgreen:2431553791,lightgrey:3553874943,lightpink:4290167295,lightsalmon:4288707327,lightseagreen:548580095,lightskyblue:2278488831,lightslategray:2005441023,lightslategrey:2005441023,lightsteelblue:2965692159,lightyellow:4294959359,lime:16711935,limegreen:852308735,linen:4210091775,magenta:4278255615,maroon:2147483903,mediumaquamarine:1724754687,mediumblue:52735,mediumorchid:3126187007,mediumpurple:2473647103,mediumseagreen:1018393087,mediumslateblue:2070474495,mediumspringgreen:16423679,mediumturquoise:1221709055,mediumvioletred:3340076543,midnightblue:421097727,mintcream:4127193855,mistyrose:4293190143,moccasin:4293178879,navajowhite:4292783615,navy:33023,oldlace:4260751103,olive:2155872511,olivedrab:1804477439,orange:4289003775,orangered:4282712319,orchid:3664828159,palegoldenrod:4008225535,palegreen:2566625535,paleturquoise:2951671551,palevioletred:3681588223,papayawhip:4293907967,peachpuff:4292524543,peru:3448061951,pink:4290825215,plum:3718307327,powderblue:2967529215,purple:2147516671,rebeccapurple:1714657791,red:4278190335,rosybrown:3163525119,royalblue:1097458175,saddlebrown:2336560127,salmon:4202722047,sandybrown:4104413439,seagreen:780883967,seashell:4294307583,sienna:2689740287,silver:3233857791,skyblue:2278484991,slateblue:1784335871,slategray:1887473919,slategrey:1887473919,snow:4294638335,springgreen:16744447,steelblue:1182971135,tan:3535047935,teal:8421631,thistle:3636451583,tomato:4284696575,turquoise:1088475391,violet:4001558271,wheat:4125012991,white:4294967295,whitesmoke:4126537215,yellow:4294902015,yellowgreen:2597139199},Ye="[-+]?\\d*\\.?\\d+",Pr=Ye+"%";function mi(...e){return"\\(\\s*("+e.join(")\\s*,\\s*(")+")\\s*\\)"}var Kv=new RegExp("rgb"+mi(Ye,Ye,Ye)),e3=new RegExp("rgba"+mi(Ye,Ye,Ye,Ye)),t3=new RegExp("hsl"+mi(Ye,Pr,Pr)),n3=new RegExp("hsla"+mi(Ye,Pr,Pr,Ye)),r3=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,i3=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,a3=/^#([0-9a-fA-F]{6})$/,o3=/^#([0-9a-fA-F]{8})$/;function s3(e){let t;return typeof e=="number"?e>>>0===e&&e>=0&&e<=4294967295?e:null:(t=a3.exec(e))?parseInt(t[1]+"ff",16)>>>0:ct&&ct[e]!==void 0?ct[e]:(t=Kv.exec(e))?(Rt(t[1])<<24|Rt(t[2])<<16|Rt(t[3])<<8|255)>>>0:(t=e3.exec(e))?(Rt(t[1])<<24|Rt(t[2])<<16|Rt(t[3])<<8|zu(t[4]))>>>0:(t=r3.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+"ff",16)>>>0:(t=o3.exec(e))?parseInt(t[1],16)>>>0:(t=i3.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+t[4]+t[4],16)>>>0:(t=t3.exec(e))?(ju(qu(t[1]),sr(t[2]),sr(t[3]))|255)>>>0:(t=n3.exec(e))?(ju(qu(t[1]),sr(t[2]),sr(t[3]))|zu(t[4]))>>>0:null}function da(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function ju(e,t,n){const r=n<.5?n*(1+t):n+t-n*t,i=2*n-r,a=da(i,r,e+1/3),o=da(i,r,e),s=da(i,r,e-1/3);return Math.round(a*255)<<24|Math.round(o*255)<<16|Math.round(s*255)<<8}function Rt(e){const t=parseInt(e,10);return t<0?0:t>255?255:t}function qu(e){return(parseFloat(e)%360+360)%360/360}function zu(e){const t=parseFloat(e);return t<0?0:t>1?255:Math.round(t*255)}function sr(e){const t=parseFloat(e);return t<0?0:t>100?1:t/100}function Hu(e){let t=s3(e);if(t===null)return e;t=t||0;const n=(t&4278190080)>>>24,r=(t&16711680)>>>16,i=(t&65280)>>>8,a=(t&255)/255;return`rgba(${n}, ${r}, ${i}, ${a})`}var Dn=(e,t,n)=>{if(P.fun(e))return e;if(P.arr(e))return Dn({range:e,output:t,extrapolate:n});if(P.str(e.output[0]))return Zo(e);const r=e,i=r.output,a=r.range||[0,1],o=r.extrapolateLeft||r.extrapolate||"extend",s=r.extrapolateRight||r.extrapolate||"extend",u=r.easing||(f=>f);return f=>{const l=f3(f,a);return u3(f,a[l],a[l+1],i[l],i[l+1],u,o,s,r.map)}};function u3(e,t,n,r,i,a,o,s,u){let f=u?u(e):e;if(f<t){if(o==="identity")return f;o==="clamp"&&(f=t)}if(f>n){if(s==="identity")return f;s==="clamp"&&(f=n)}return r===i?r:t===n?e<=t?r:i:(t===-1/0?f=-f:n===1/0?f=f-t:f=(f-t)/(n-t),f=a(f),r===-1/0?f=-f:i===1/0?f=f+r:f=f*(i-r)+r,f)}function f3(e,t){for(var n=1;n<t.length-1&&!(t[n]>=e);++n);return n-1}var c3=(e,t="end")=>n=>{n=t==="end"?Math.min(n,.999):Math.max(n,.001);const r=n*e,i=t==="end"?Math.floor(r):Math.ceil(r);return Zv(0,1,i/e)},Dr=1.70158,ur=Dr*1.525,Vu=Dr+1,Bu=2*Math.PI/3,Gu=2*Math.PI/4.5,fr=e=>e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375,l3={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>1-(1-e)*(1-e),easeInOutQuad:e=>e<.5?2*e*e:1-Math.pow(-2*e+2,2)/2,easeInCubic:e=>e*e*e,easeOutCubic:e=>1-Math.pow(1-e,3),easeInOutCubic:e=>e<.5?4*e*e*e:1-Math.pow(-2*e+2,3)/2,easeInQuart:e=>e*e*e*e,easeOutQuart:e=>1-Math.pow(1-e,4),easeInOutQuart:e=>e<.5?8*e*e*e*e:1-Math.pow(-2*e+2,4)/2,easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>1-Math.pow(1-e,5),easeInOutQuint:e=>e<.5?16*e*e*e*e*e:1-Math.pow(-2*e+2,5)/2,easeInSine:e=>1-Math.cos(e*Math.PI/2),easeOutSine:e=>Math.sin(e*Math.PI/2),easeInOutSine:e=>-(Math.cos(Math.PI*e)-1)/2,easeInExpo:e=>e===0?0:Math.pow(2,10*e-10),easeOutExpo:e=>e===1?1:1-Math.pow(2,-10*e),easeInOutExpo:e=>e===0?0:e===1?1:e<.5?Math.pow(2,20*e-10)/2:(2-Math.pow(2,-20*e+10))/2,easeInCirc:e=>1-Math.sqrt(1-Math.pow(e,2)),easeOutCirc:e=>Math.sqrt(1-Math.pow(e-1,2)),easeInOutCirc:e=>e<.5?(1-Math.sqrt(1-Math.pow(2*e,2)))/2:(Math.sqrt(1-Math.pow(-2*e+2,2))+1)/2,easeInBack:e=>Vu*e*e*e-Dr*e*e,easeOutBack:e=>1+Vu*Math.pow(e-1,3)+Dr*Math.pow(e-1,2),easeInOutBack:e=>e<.5?Math.pow(2*e,2)*((ur+1)*2*e-ur)/2:(Math.pow(2*e-2,2)*((ur+1)*(e*2-2)+ur)+2)/2,easeInElastic:e=>e===0?0:e===1?1:-Math.pow(2,10*e-10)*Math.sin((e*10-10.75)*Bu),easeOutElastic:e=>e===0?0:e===1?1:Math.pow(2,-10*e)*Math.sin((e*10-.75)*Bu)+1,easeInOutElastic:e=>e===0?0:e===1?1:e<.5?-(Math.pow(2,20*e-10)*Math.sin((20*e-11.125)*Gu))/2:Math.pow(2,-20*e+10)*Math.sin((20*e-11.125)*Gu)/2+1,easeInBounce:e=>1-fr(1-e),easeOutBounce:fr,easeInOutBounce:e=>e<.5?(1-fr(1-2*e))/2:(1+fr(2*e-1))/2,steps:c3},Fn=Symbol.for("FluidValue.get"),Bt=Symbol.for("FluidValue.observers"),Pe=e=>!!(e&&e[Fn]),ke=e=>e&&e[Fn]?e[Fn]():e,Xu=e=>e[Bt]||null;function d3(e,t){e.eventObserved?e.eventObserved(t):e(t)}function Rn(e,t){const n=e[Bt];n&&n.forEach(r=>{d3(r,t)})}var Rl=class{constructor(e){if(!e&&!(e=this.get))throw Error("Unknown getter");h3(this,e)}},h3=(e,t)=>Il(e,Fn,t);function rn(e,t){if(e[Fn]){let n=e[Bt];n||Il(e,Bt,n=new Set),n.has(t)||(n.add(t),e.observerAdded&&e.observerAdded(n.size,t))}return t}function In(e,t){const n=e[Bt];if(n&&n.has(t)){const r=n.size-1;r?n.delete(t):e[Bt]=null,e.observerRemoved&&e.observerRemoved(r,t)}}var Il=(e,t,n)=>Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0}),gr=/[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,g3=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi,Qu=new RegExp(`(${gr.source})(%|[a-z]+)`,"i"),m3=/rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi,bi=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/,Nl=e=>{const[t,n]=b3(e);if(!t||Qo())return e;const r=window.getComputedStyle(document.documentElement).getPropertyValue(t);if(r)return r.trim();if(n&&n.startsWith("--")){const i=window.getComputedStyle(document.documentElement).getPropertyValue(n);return i||e}else{if(n&&bi.test(n))return Nl(n);if(n)return n}return e},b3=e=>{const t=bi.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]},ha,p3=(e,t,n,r,i)=>`rgba(${Math.round(t)}, ${Math.round(n)}, ${Math.round(r)}, ${i})`,El=e=>{ha||(ha=ct?new RegExp(`(${Object.keys(ct).join("|")})(?!\\w)`,"g"):/^\b$/);const t=e.output.map(a=>ke(a).replace(bi,Nl).replace(g3,Hu).replace(ha,Hu)),n=t.map(a=>a.match(gr).map(Number)),i=n[0].map((a,o)=>n.map(s=>{if(!(o in s))throw Error('The arity of each "output" value must be equal');return s[o]})).map(a=>Dn({...e,output:a}));return a=>{var u;const o=!Qu.test(t[0])&&((u=t.find(f=>Qu.test(f)))==null?void 0:u.replace(gr,""));let s=0;return t[0].replace(gr,()=>`${i[s++](a)}${o||""}`).replace(m3,p3)}},Ko="react-spring: ",Yl=e=>{const t=e;let n=!1;if(typeof t!="function")throw new TypeError(`${Ko}once requires a function parameter`);return(...r)=>{n||(t(...r),n=!0)}},y3=Yl(console.warn);function v3(){y3(`${Ko}The "interpolate" function is deprecated in v9 (use "to" instead)`)}var _3=Yl(console.warn);function x3(){_3(`${Ko}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`)}function pi(e){return P.str(e)&&(e[0]=="#"||/\d/.test(e)||!Qo()&&bi.test(e)||e in(ct||{}))}var _t=Qo()?T.useEffect:T.useLayoutEffect,w3=()=>{const e=T.useRef(!1);return _t(()=>(e.current=!0,()=>{e.current=!1}),[]),e};function es(){const e=T.useState()[1],t=w3();return()=>{t.current&&e(Math.random())}}function M3(e,t){const[n]=T.useState(()=>({inputs:t,result:e()})),r=T.useRef(),i=r.current;let a=i;return a?t&&a.inputs&&$3(t,a.inputs)||(a={inputs:t,result:e()}):a=n,T.useEffect(()=>{r.current=a,i==n&&(n.inputs=n.result=void 0)},[a]),a.result}function $3(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}var ts=e=>T.useEffect(e,T3),T3=[];function La(e){const t=T.useRef();return T.useEffect(()=>{t.current=e}),t.current}var Nn=Symbol.for("Animated:node"),C3=e=>!!e&&e[Nn]===e,He=e=>e&&e[Nn],ns=(e,t)=>Bv(e,Nn,t),yi=e=>e&&e[Nn]&&e[Nn].getPayload(),Ll=class{constructor(){ns(this,this)}getPayload(){return this.payload||[]}},tr=class extends Ll{constructor(e){super(),this._value=e,this.done=!0,this.durationProgress=0,P.num(this._value)&&(this.lastPosition=this._value)}static create(e){return new tr(e)}getPayload(){return[this]}getValue(){return this._value}setValue(e,t){return P.num(e)&&(this.lastPosition=e,t&&(e=Math.round(e/t)*t,this.done&&(this.lastPosition=e))),this._value===e?!1:(this._value=e,!0)}reset(){const{done:e}=this;this.done=!1,P.num(this._value)&&(this.elapsedTime=0,this.durationProgress=0,this.lastPosition=this._value,e&&(this.lastVelocity=null),this.v0=null)}},En=class extends tr{constructor(e){super(0),this._string=null,this._toString=Dn({output:[e,e]})}static create(e){return new En(e)}getValue(){const e=this._string;return e??(this._string=this._toString(this._value))}setValue(e){if(P.str(e)){if(e==this._string)return!1;this._string=e,this._value=1}else if(super.setValue(e))this._string=null;else return!1;return!0}reset(e){e&&(this._toString=Dn({output:[this.getValue(),e]})),this._value=0,super.reset()}},Fr={dependencies:null},vi=class extends Ll{constructor(e){super(),this.source=e,this.setValue(e)}getValue(e){const t={};return Ge(this.source,(n,r)=>{C3(n)?t[r]=n.getValue(e):Pe(n)?t[r]=ke(n):e||(t[r]=n)}),t}setValue(e){this.source=e,this.payload=this._makePayload(e)}reset(){this.payload&&G(this.payload,e=>e.reset())}_makePayload(e){if(e){const t=new Set;return Ge(e,this._addToPayload,t),Array.from(t)}}_addToPayload(e){Fr.dependencies&&Pe(e)&&Fr.dependencies.add(e);const t=yi(e);t&&G(t,n=>this.add(n))}},Wl=class extends vi{constructor(e){super(e)}static create(e){return new Wl(e)}getValue(){return this.source.map(e=>e.getValue())}setValue(e){const t=this.getPayload();return e.length==t.length?t.map((n,r)=>n.setValue(e[r])).some(Boolean):(super.setValue(e.map(k3)),!0)}};function k3(e){return(pi(e)?En:tr).create(e)}function Wa(e){const t=He(e);return t?t.constructor:P.arr(e)?Wl:pi(e)?En:tr}var Zu=(e,t)=>{const n=!P.fun(e)||e.prototype&&e.prototype.isReactComponent;return T.forwardRef((r,i)=>{const a=T.useRef(null),o=n&&T.useCallback(b=>{a.current=A3(i,b)},[i]),[s,u]=O3(r,t),f=es(),l=()=>{const b=a.current;if(n&&!b)return;(b?t.applyAnimatedValues(b,s.getValue(!0)):!1)===!1&&f()},c=new S3(l,u),d=T.useRef();_t(()=>(d.current=c,G(u,b=>rn(b,c)),()=>{d.current&&(G(d.current.deps,b=>In(b,d.current)),Q.cancel(d.current.update))})),T.useEffect(l,[]),ts(()=>()=>{const b=d.current;G(b.deps,p=>In(p,b))});const g=t.getComponentProps(s.getValue());return T.createElement(e,{...g,ref:o})})},S3=class{constructor(e,t){this.update=e,this.deps=t}eventObserved(e){e.type=="change"&&Q.write(this.update)}};function O3(e,t){const n=new Set;return Fr.dependencies=n,e.style&&(e={...e,style:t.createAnimatedStyle(e.style)}),e=new vi(e),Fr.dependencies=null,[e,n]}function A3(e,t){return e&&(P.fun(e)?e(t):e.current=t),t}var Ju=Symbol.for("AnimatedComponent"),U3=(e,{applyAnimatedValues:t=()=>!1,createAnimatedStyle:n=i=>new vi(i),getComponentProps:r=i=>i}={})=>{const i={applyAnimatedValues:t,createAnimatedStyle:n,getComponentProps:r},a=o=>{const s=Ku(o)||"Anonymous";return P.str(o)?o=a[o]||(a[o]=Zu(o,i)):o=o[Ju]||(o[Ju]=Zu(o,i)),o.displayName=`Animated(${s})`,o};return Ge(e,(o,s)=>{P.arr(e)&&(s=Ku(o)),a[s]=a(o)}),{animated:a}},Ku=e=>P.str(e)?e:e&&P.str(e.displayName)?e.displayName:P.fun(e)&&e.name||null;function Se(e,...t){return P.fun(e)?e(...t):e}var Cn=(e,t)=>e===!0||!!(t&&e&&(P.fun(e)?e(t):Te(e).includes(t))),jl=(e,t)=>P.obj(e)?t&&e[t]:e,ql=(e,t)=>e.default===!0?e[t]:e.default?e.default[t]:void 0,P3=e=>e,_i=(e,t=P3)=>{let n=D3;e.default&&e.default!==!0&&(e=e.default,n=Object.keys(e));const r={};for(const i of n){const a=t(e[i],i);P.und(a)||(r[i]=a)}return r},D3=["config","onProps","onStart","onChange","onPause","onResume","onRest"],F3={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function R3(e){const t={};let n=0;if(Ge(e,(r,i)=>{F3[i]||(t[i]=r,n++)}),n)return t}function rs(e){const t=R3(e);if(t){const n={to:t};return Ge(e,(r,i)=>i in t||(n[i]=r)),n}return{...e}}function Yn(e){return e=ke(e),P.arr(e)?e.map(Yn):pi(e)?We.createStringInterpolator({range:[0,1],output:[e,e]})(1):e}function zl(e){for(const t in e)return!0;return!1}function ja(e){return P.fun(e)||P.arr(e)&&P.obj(e[0])}function qa(e,t){var n;(n=e.ref)==null||n.delete(e),t==null||t.delete(e)}function Hl(e,t){var n;t&&e.ref!==t&&((n=e.ref)==null||n.delete(e),t.add(e),e.ref=t)}var is={default:{tension:170,friction:26},gentle:{tension:120,friction:14},wobbly:{tension:180,friction:12},stiff:{tension:210,friction:20},slow:{tension:280,friction:60},molasses:{tension:280,friction:120}},za={...is.default,mass:1,damping:1,easing:l3.linear,clamp:!1},I3=class{constructor(){this.velocity=0,Object.assign(this,za)}};function N3(e,t,n){n&&(n={...n},ef(n,t),t={...n,...t}),ef(e,t),Object.assign(e,t);for(const o in za)e[o]==null&&(e[o]=za[o]);let{frequency:r,damping:i}=e;const{mass:a}=e;return P.und(r)||(r<.01&&(r=.01),i<0&&(i=0),e.tension=Math.pow(2*Math.PI/r,2)*a,e.friction=4*Math.PI*i*a/r),e}function ef(e,t){if(!P.und(t.decay))e.duration=void 0;else{const n=!P.und(t.tension)||!P.und(t.friction);(n||!P.und(t.frequency)||!P.und(t.damping)||!P.und(t.mass))&&(e.duration=void 0,e.decay=void 0),n&&(e.frequency=void 0)}}var tf=[],E3=class{constructor(){this.changed=!1,this.values=tf,this.toValues=null,this.fromValues=tf,this.config=new I3,this.immediate=!1}};function Vl(e,{key:t,props:n,defaultProps:r,state:i,actions:a}){return new Promise((o,s)=>{let u,f,l=Cn(n.cancel??(r==null?void 0:r.cancel),t);if(l)g();else{P.und(n.pause)||(i.paused=Cn(n.pause,t));let b=r==null?void 0:r.pause;b!==!0&&(b=i.paused||Cn(b,t)),u=Se(n.delay||0,t),b?(i.resumeQueue.add(d),a.pause()):(a.resume(),d())}function c(){i.resumeQueue.add(d),i.timeouts.delete(f),f.cancel(),u=f.time-Q.now()}function d(){u>0&&!We.skipAnimation?(i.delayed=!0,f=Q.setTimeout(g,u),i.pauseQueue.add(c),i.timeouts.add(f)):g()}function g(){i.delayed&&(i.delayed=!1),i.pauseQueue.delete(c),i.timeouts.delete(f),e<=(i.cancelId||0)&&(l=!0);try{a.start({...n,callId:e,cancel:l},o)}catch(b){s(b)}}})}var as=(e,t)=>t.length==1?t[0]:t.some(n=>n.cancelled)?Wt(e.get()):t.every(n=>n.noop)?Bl(e.get()):Ne(e.get(),t.every(n=>n.finished)),Bl=e=>({value:e,noop:!0,finished:!0,cancelled:!1}),Ne=(e,t,n=!1)=>({value:e,finished:t,cancelled:n}),Wt=e=>({value:e,cancelled:!0,finished:!1});function Gl(e,t,n,r){const{callId:i,parentId:a,onRest:o}=t,{asyncTo:s,promise:u}=n;return!a&&e===s&&!t.reset?u:n.promise=(async()=>{n.asyncId=i,n.asyncTo=e;const f=_i(t,(y,v)=>v==="onRest"?void 0:y);let l,c;const d=new Promise((y,v)=>(l=y,c=v)),g=y=>{const v=i<=(n.cancelId||0)&&Wt(r)||i!==n.asyncId&&Ne(r,!1);if(v)throw y.result=v,c(y),y},b=(y,v)=>{const O=new nf,_=new rf;return(async()=>{if(We.skipAnimation)throw Ln(n),_.result=Ne(r,!1),c(_),_;g(O);const k=P.obj(y)?{...y}:{...v,to:y};k.parentId=i,Ge(f,(C,F)=>{P.und(k[F])&&(k[F]=C)});const A=await r.start(k);return g(O),n.paused&&await new Promise(C=>{n.resumeQueue.add(C)}),A})()};let p;if(We.skipAnimation)return Ln(n),Ne(r,!1);try{let y;P.arr(e)?y=(async v=>{for(const O of v)await b(O)})(e):y=Promise.resolve(e(b,r.stop.bind(r))),await Promise.all([y.then(l),d]),p=Ne(r.get(),!0,!1)}catch(y){if(y instanceof nf)p=y.result;else if(y instanceof rf)p=y.result;else throw y}finally{i==n.asyncId&&(n.asyncId=a,n.asyncTo=a?s:void 0,n.promise=a?u:void 0)}return P.fun(o)&&Q.batchedUpdates(()=>{o(p,r,r.item)}),p})()}function Ln(e,t){$n(e.timeouts,n=>n.cancel()),e.pauseQueue.clear(),e.resumeQueue.clear(),e.asyncId=e.asyncTo=e.promise=void 0,t&&(e.cancelId=t)}var nf=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},rf=class extends Error{constructor(){super("SkipAnimationSignal")}},Ha=e=>e instanceof os,Y3=1,os=class extends Rl{constructor(){super(...arguments),this.id=Y3++,this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){const e=He(this);return e&&e.getValue()}to(...e){return We.to(this,e)}interpolate(...e){return v3(),We.to(this,e)}toJSON(){return this.get()}observerAdded(e){e==1&&this._attach()}observerRemoved(e){e==0&&this._detach()}_attach(){}_detach(){}_onChange(e,t=!1){Rn(this,{type:"change",parent:this,value:e,idle:t})}_onPriorityChange(e){this.idle||gi.sort(this),Rn(this,{type:"priority",parent:this,priority:e})}},$t=Symbol.for("SpringPhase"),Xl=1,Va=2,Ba=4,ga=e=>(e[$t]&Xl)>0,ot=e=>(e[$t]&Va)>0,ln=e=>(e[$t]&Ba)>0,af=(e,t)=>t?e[$t]|=Va|Xl:e[$t]&=~Va,of=(e,t)=>t?e[$t]|=Ba:e[$t]&=~Ba,L3=class extends os{constructor(e,t){if(super(),this.animation=new E3,this.defaultProps={},this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._pendingCalls=new Set,this._lastCallId=0,this._lastToId=0,this._memoizedDuration=0,!P.und(e)||!P.und(t)){const n=P.obj(e)?{...e}:{...t,from:e};P.und(n.default)&&(n.default=!0),this.start(n)}}get idle(){return!(ot(this)||this._state.asyncTo)||ln(this)}get goal(){return ke(this.animation.to)}get velocity(){const e=He(this);return e instanceof tr?e.lastVelocity||0:e.getPayload().map(t=>t.lastVelocity||0)}get hasAnimated(){return ga(this)}get isAnimating(){return ot(this)}get isPaused(){return ln(this)}get isDelayed(){return this._state.delayed}advance(e){let t=!0,n=!1;const r=this.animation;let{toValues:i}=r;const{config:a}=r,o=yi(r.to);!o&&Pe(r.to)&&(i=Te(ke(r.to))),r.values.forEach((f,l)=>{if(f.done)return;const c=f.constructor==En?1:o?o[l].lastPosition:i[l];let d=r.immediate,g=c;if(!d){if(g=f.lastPosition,a.tension<=0){f.done=!0;return}let b=f.elapsedTime+=e;const p=r.fromValues[l],y=f.v0!=null?f.v0:f.v0=P.arr(a.velocity)?a.velocity[l]:a.velocity;let v;const O=a.precision||(p==c?.005:Math.min(1,Math.abs(c-p)*.001));if(P.und(a.duration))if(a.decay){const _=a.decay===!0?.998:a.decay,k=Math.exp(-(1-_)*b);g=p+y/(1-_)*(1-k),d=Math.abs(f.lastPosition-g)<=O,v=y*k}else{v=f.lastVelocity==null?y:f.lastVelocity;const _=a.restVelocity||O/10,k=a.clamp?0:a.bounce,A=!P.und(k),C=p==c?f.v0>0:p<c;let F,Y=!1;const E=1,L=Math.ceil(e/E);for(let w=0;w<L&&(F=Math.abs(v)>_,!(!F&&(d=Math.abs(c-g)<=O,d)));++w){A&&(Y=g==c||g>c==C,Y&&(v=-v*k,g=c));const q=-a.tension*1e-6*(g-c),D=-a.friction*.001*v,U=(q+D)/a.mass;v=v+U*E,g=g+v*E}}else{let _=1;a.duration>0&&(this._memoizedDuration!==a.duration&&(this._memoizedDuration=a.duration,f.durationProgress>0&&(f.elapsedTime=a.duration*f.durationProgress,b=f.elapsedTime+=e)),_=(a.progress||0)+b/this._memoizedDuration,_=_>1?1:_<0?0:_,f.durationProgress=_),g=p+a.easing(_)*(c-p),v=(g-f.lastPosition)/e,d=_==1}f.lastVelocity=v,Number.isNaN(g)&&(console.warn("Got NaN while animating:",this),d=!0)}o&&!o[l].done&&(d=!1),d?f.done=!0:t=!1,f.setValue(g,a.round)&&(n=!0)});const s=He(this),u=s.getValue();if(t){const f=ke(r.to);(u!==f||n)&&!a.decay?(s.setValue(f),this._onChange(f)):n&&a.decay&&this._onChange(u),this._stop()}else n&&this._onChange(u)}set(e){return Q.batchedUpdates(()=>{this._stop(),this._focus(e),this._set(e)}),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(ot(this)){const{to:e,config:t}=this.animation;Q.batchedUpdates(()=>{this._onStart(),t.decay||this._set(e,!1),this._stop()})}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,t){let n;return P.und(e)?(n=this.queue||[],this.queue=[]):n=[P.obj(e)?e:{...t,to:e}],Promise.all(n.map(r=>this._update(r))).then(r=>as(this,r))}stop(e){const{to:t}=this.animation;return this._focus(this.get()),Ln(this._state,e&&this._lastCallId),Q.batchedUpdates(()=>this._stop(t,e)),this}reset(){this._update({reset:!0})}eventObserved(e){e.type=="change"?this._start():e.type=="priority"&&(this.priority=e.priority+1)}_prepareNode(e){const t=this.key||"";let{to:n,from:r}=e;n=P.obj(n)?n[t]:n,(n==null||ja(n))&&(n=void 0),r=P.obj(r)?r[t]:r,r==null&&(r=void 0);const i={to:n,from:r};return ga(this)||(e.reverse&&([n,r]=[r,n]),r=ke(r),P.und(r)?He(this)||this._set(n):this._set(r)),i}_update({...e},t){const{key:n,defaultProps:r}=this;e.default&&Object.assign(r,_i(e,(o,s)=>/^on/.test(s)?jl(o,n):o)),uf(this,e,"onProps"),hn(this,"onProps",e,this);const i=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");const a=this._state;return Vl(++this._lastCallId,{key:n,props:e,defaultProps:r,state:a,actions:{pause:()=>{ln(this)||(of(this,!0),Mn(a.pauseQueue),hn(this,"onPause",Ne(this,dn(this,this.animation.to)),this))},resume:()=>{ln(this)&&(of(this,!1),ot(this)&&this._resume(),Mn(a.resumeQueue),hn(this,"onResume",Ne(this,dn(this,this.animation.to)),this))},start:this._merge.bind(this,i)}}).then(o=>{if(e.loop&&o.finished&&!(t&&o.noop)){const s=Ql(e);if(s)return this._update(s,!0)}return o})}_merge(e,t,n){if(t.cancel)return this.stop(!0),n(Wt(this));const r=!P.und(e.to),i=!P.und(e.from);if(r||i)if(t.callId>this._lastToId)this._lastToId=t.callId;else return n(Wt(this));const{key:a,defaultProps:o,animation:s}=this,{to:u,from:f}=s;let{to:l=u,from:c=f}=e;i&&!r&&(!t.default||P.und(l))&&(l=c),t.reverse&&([l,c]=[c,l]);const d=!Qe(c,f);d&&(s.from=c),c=ke(c);const g=!Qe(l,u);g&&this._focus(l);const b=ja(t.to),{config:p}=s,{decay:y,velocity:v}=p;(r||i)&&(p.velocity=0),t.config&&!b&&N3(p,Se(t.config,a),t.config!==o.config?Se(o.config,a):void 0);let O=He(this);if(!O||P.und(l))return n(Ne(this,!0));const _=P.und(t.reset)?i&&!t.default:!P.und(c)&&Cn(t.reset,a),k=_?c:this.get(),A=Yn(l),C=P.num(A)||P.arr(A)||pi(A),F=!b&&(!C||Cn(o.immediate||t.immediate,a));if(g){const w=Wa(l);if(w!==O.constructor)if(F)O=this._set(A);else throw Error(`Cannot animate between ${O.constructor.name} and ${w.name}, as the "to" prop suggests`)}const Y=O.constructor;let E=Pe(l),L=!1;if(!E){const w=_||!ga(this)&&d;(g||w)&&(L=Qe(Yn(k),A),E=!L),(!Qe(s.immediate,F)&&!F||!Qe(p.decay,y)||!Qe(p.velocity,v))&&(E=!0)}if(L&&ot(this)&&(s.changed&&!_?E=!0:E||this._stop(u)),!b&&((E||Pe(u))&&(s.values=O.getPayload(),s.toValues=Pe(l)?null:Y==En?[1]:Te(A)),s.immediate!=F&&(s.immediate=F,!F&&!_&&this._set(u)),E)){const{onRest:w}=s;G(j3,D=>uf(this,t,D));const q=Ne(this,dn(this,u));Mn(this._pendingCalls,q),this._pendingCalls.add(n),s.changed&&Q.batchedUpdates(()=>{var D;s.changed=!_,w==null||w(q,this),_?Se(o.onRest,q):(D=s.onStart)==null||D.call(s,q,this)})}_&&this._set(k),b?n(Gl(t.to,t,this._state,this)):E?this._start():ot(this)&&!g?this._pendingCalls.add(n):n(Bl(k))}_focus(e){const t=this.animation;e!==t.to&&(Xu(this)&&this._detach(),t.to=e,Xu(this)&&this._attach())}_attach(){let e=0;const{to:t}=this.animation;Pe(t)&&(rn(t,this),Ha(t)&&(e=t.priority+1)),this.priority=e}_detach(){const{to:e}=this.animation;Pe(e)&&In(e,this)}_set(e,t=!0){const n=ke(e);if(!P.und(n)){const r=He(this);if(!r||!Qe(n,r.getValue())){const i=Wa(n);!r||r.constructor!=i?ns(this,i.create(n)):r.setValue(n),r&&Q.batchedUpdates(()=>{this._onChange(n,t)})}}return He(this)}_onStart(){const e=this.animation;e.changed||(e.changed=!0,hn(this,"onStart",Ne(this,dn(this,e.to)),this))}_onChange(e,t){t||(this._onStart(),Se(this.animation.onChange,e,this)),Se(this.defaultProps.onChange,e,this),super._onChange(e,t)}_start(){const e=this.animation;He(this).reset(ke(e.to)),e.immediate||(e.fromValues=e.values.map(t=>t.lastPosition)),ot(this)||(af(this,!0),ln(this)||this._resume())}_resume(){We.skipAnimation?this.finish():gi.start(this)}_stop(e,t){if(ot(this)){af(this,!1);const n=this.animation;G(n.values,i=>{i.done=!0}),n.toValues&&(n.onChange=n.onPause=n.onResume=void 0),Rn(this,{type:"idle",parent:this});const r=t?Wt(this.get()):Ne(this.get(),dn(this,e??n.to));Mn(this._pendingCalls,r),n.changed&&(n.changed=!1,hn(this,"onRest",r,this))}}};function dn(e,t){const n=Yn(t),r=Yn(e.get());return Qe(r,n)}function Ql(e,t=e.loop,n=e.to){const r=Se(t);if(r){const i=r!==!0&&rs(r),a=(i||e).reverse,o=!i||i.reset;return Wn({...e,loop:t,default:!1,pause:void 0,to:!a||ja(n)?n:void 0,from:o?e.from:void 0,reset:o,...i})}}function Wn(e){const{to:t,from:n}=e=rs(e),r=new Set;return P.obj(t)&&sf(t,r),P.obj(n)&&sf(n,r),e.keys=r.size?Array.from(r):null,e}function W3(e){const t=Wn(e);return P.und(t.default)&&(t.default=_i(t)),t}function sf(e,t){Ge(e,(n,r)=>n!=null&&t.add(r))}var j3=["onStart","onRest","onChange","onPause","onResume"];function uf(e,t,n){e.animation[n]=t[n]!==ql(t,n)?jl(t[n],e.key):void 0}function hn(e,t,...n){var r,i,a,o;(i=(r=e.animation)[t])==null||i.call(r,...n),(o=(a=e.defaultProps)[t])==null||o.call(a,...n)}var q3=["onStart","onChange","onRest"],z3=1,Zl=class{constructor(e,t){this.id=z3++,this.springs={},this.queue=[],this._lastAsyncId=0,this._active=new Set,this._changed=new Set,this._started=!1,this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._events={onStart:new Map,onChange:new Map,onRest:new Map},this._onFrame=this._onFrame.bind(this),t&&(this._flush=t),e&&this.start({default:!0,...e})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every(e=>e.idle&&!e.isDelayed&&!e.isPaused)}get item(){return this._item}set item(e){this._item=e}get(){const e={};return this.each((t,n)=>e[n]=t.get()),e}set(e){for(const t in e){const n=e[t];P.und(n)||this.springs[t].set(n)}}update(e){return e&&this.queue.push(Wn(e)),this}start(e){let{queue:t}=this;return e?t=Te(e).map(Wn):this.queue=[],this._flush?this._flush(this,t):(nd(this,t),Ga(this,t))}stop(e,t){if(e!==!!e&&(t=e),t){const n=this.springs;G(Te(t),r=>n[r].stop(!!e))}else Ln(this._state,this._lastAsyncId),this.each(n=>n.stop(!!e));return this}pause(e){if(P.und(e))this.start({pause:!0});else{const t=this.springs;G(Te(e),n=>t[n].pause())}return this}resume(e){if(P.und(e))this.start({pause:!1});else{const t=this.springs;G(Te(e),n=>t[n].resume())}return this}each(e){Ge(this.springs,e)}_onFrame(){const{onStart:e,onChange:t,onRest:n}=this._events,r=this._active.size>0,i=this._changed.size>0;(r&&!this._started||i&&!this._started)&&(this._started=!0,$n(e,([s,u])=>{u.value=this.get(),s(u,this,this._item)}));const a=!r&&this._started,o=i||a&&n.size?this.get():null;i&&t.size&&$n(t,([s,u])=>{u.value=o,s(u,this,this._item)}),a&&(this._started=!1,$n(n,([s,u])=>{u.value=o,s(u,this,this._item)}))}eventObserved(e){if(e.type=="change")this._changed.add(e.parent),e.idle||this._active.add(e.parent);else if(e.type=="idle")this._active.delete(e.parent);else return;Q.onFrame(this._onFrame)}};function Ga(e,t){return Promise.all(t.map(n=>Jl(e,n))).then(n=>as(e,n))}async function Jl(e,t,n){const{keys:r,to:i,from:a,loop:o,onRest:s,onResolve:u}=t,f=P.obj(t.default)&&t.default;o&&(t.loop=!1),i===!1&&(t.to=null),a===!1&&(t.from=null);const l=P.arr(i)||P.fun(i)?i:void 0;l?(t.to=void 0,t.onRest=void 0,f&&(f.onRest=void 0)):G(q3,p=>{const y=t[p];if(P.fun(y)){const v=e._events[p];t[p]=({finished:O,cancelled:_})=>{const k=v.get(y);k?(O||(k.finished=!1),_&&(k.cancelled=!0)):v.set(y,{value:null,finished:O||!1,cancelled:_||!1})},f&&(f[p]=t[p])}});const c=e._state;t.pause===!c.paused?(c.paused=t.pause,Mn(t.pause?c.pauseQueue:c.resumeQueue)):c.paused&&(t.pause=!0);const d=(r||Object.keys(e.springs)).map(p=>e.springs[p].start(t)),g=t.cancel===!0||ql(t,"cancel")===!0;(l||g&&c.asyncId)&&d.push(Vl(++e._lastAsyncId,{props:t,state:c,actions:{pause:Ea,resume:Ea,start(p,y){g?(Ln(c,e._lastAsyncId),y(Wt(e))):(p.onRest=s,y(Gl(l,p,c,e)))}}})),c.paused&&await new Promise(p=>{c.resumeQueue.add(p)});const b=as(e,await Promise.all(d));if(o&&b.finished&&!(n&&b.noop)){const p=Ql(t,o,i);if(p)return nd(e,[p]),Jl(e,p,!0)}return u&&Q.batchedUpdates(()=>u(b,e,e.item)),b}function Xa(e,t){const n={...e.springs};return t&&G(Te(t),r=>{P.und(r.keys)&&(r=Wn(r)),P.obj(r.to)||(r={...r,to:void 0}),td(n,r,i=>ed(i))}),Kl(e,n),n}function Kl(e,t){Ge(t,(n,r)=>{e.springs[r]||(e.springs[r]=n,rn(n,e))})}function ed(e,t){const n=new L3;return n.key=e,t&&rn(n,t),n}function td(e,t,n){t.keys&&G(t.keys,r=>{(e[r]||(e[r]=n(r)))._prepareNode(t)})}function nd(e,t){G(t,n=>{td(e.springs,n,r=>ed(r,e))})}var nr=({children:e,...t})=>{const n=T.useContext(Rr),r=t.pause||!!n.pause,i=t.immediate||!!n.immediate;t=M3(()=>({pause:r,immediate:i}),[r,i]);const{Provider:a}=Rr;return T.createElement(a,{value:t},e)},Rr=H3(nr,{});nr.Provider=Rr.Provider;nr.Consumer=Rr.Consumer;function H3(e,t){return Object.assign(e,T.createContext(t)),e.Provider._context=e,e.Consumer._context=e,e}var rd=()=>{const e=[],t=function(r){x3();const i=[];return G(e,(a,o)=>{if(P.und(r))i.push(a.start());else{const s=n(r,a,o);s&&i.push(a.start(s))}}),i};t.current=e,t.add=function(r){e.includes(r)||e.push(r)},t.delete=function(r){const i=e.indexOf(r);~i&&e.splice(i,1)},t.pause=function(){return G(e,r=>r.pause(...arguments)),this},t.resume=function(){return G(e,r=>r.resume(...arguments)),this},t.set=function(r){G(e,(i,a)=>{const o=P.fun(r)?r(a,i):r;o&&i.set(o)})},t.start=function(r){const i=[];return G(e,(a,o)=>{if(P.und(r))i.push(a.start());else{const s=this._getProps(r,a,o);s&&i.push(a.start(s))}}),i},t.stop=function(){return G(e,r=>r.stop(...arguments)),this},t.update=function(r){return G(e,(i,a)=>i.update(this._getProps(r,i,a))),this};const n=function(r,i,a){return P.fun(r)?r(a,i):r};return t._getProps=n,t};function V3(e,t,n){const r=P.fun(t)&&t;r&&!n&&(n=[]);const i=T.useMemo(()=>r||arguments.length==3?rd():void 0,[]),a=T.useRef(0),o=es(),s=T.useMemo(()=>({ctrls:[],queue:[],flush(v,O){const _=Xa(v,O);return a.current>0&&!s.queue.length&&!Object.keys(_).some(A=>!v.springs[A])?Ga(v,O):new Promise(A=>{Kl(v,_),s.queue.push(()=>{A(Ga(v,O))}),o()})}}),[]),u=T.useRef([...s.ctrls]),f=[],l=La(e)||0;T.useMemo(()=>{G(u.current.slice(e,l),v=>{qa(v,i),v.stop(!0)}),u.current.length=e,c(l,e)},[e]),T.useMemo(()=>{c(0,Math.min(l,e))},n);function c(v,O){for(let _=v;_<O;_++){const k=u.current[_]||(u.current[_]=new Zl(null,s.flush)),A=r?r(_,k):t[_];A&&(f[_]=W3(A))}}const d=u.current.map((v,O)=>Xa(v,f[O])),g=T.useContext(nr),b=La(g),p=g!==b&&zl(g);_t(()=>{a.current++,s.ctrls=u.current;const{queue:v}=s;v.length&&(s.queue=[],G(v,O=>O())),G(u.current,(O,_)=>{i==null||i.add(O),p&&O.start({default:g});const k=f[_];k&&(Hl(O,k.ref),O.ref?O.queue.push(k):O.start(k))})}),ts(()=>()=>{G(s.ctrls,v=>v.stop(!0))});const y=d.map(v=>({...v}));return i?[y,i]:y}function rt(e,t){const n=P.fun(e),[[r],i]=V3(1,n?e:[e],n?t||[]:t);return n||arguments.length==2?[r,i]:r}function ss(e,t,n){const r=P.fun(t)&&t,{reset:i,sort:a,trail:o=0,expires:s=!0,exitBeforeEnter:u=!1,onDestroyed:f,ref:l,config:c}=r?r():t,d=T.useMemo(()=>r||arguments.length==3?rd():void 0,[]),g=Te(e),b=[],p=T.useRef(null),y=i?null:p.current;_t(()=>{p.current=b}),ts(()=>(G(b,U=>{d==null||d.add(U.ctrl),U.ctrl.ref=d}),()=>{G(p.current,U=>{U.expired&&clearTimeout(U.expirationId),qa(U.ctrl,d),U.ctrl.stop(!0)})}));const v=G3(g,r?r():t,y),O=i&&p.current||[];_t(()=>G(O,({ctrl:U,item:N,key:W})=>{qa(U,d),Se(f,N,W)}));const _=[];if(y&&G(y,(U,N)=>{U.expired?(clearTimeout(U.expirationId),O.push(U)):(N=_[N]=v.indexOf(U.key),~N&&(b[N]=U))}),G(g,(U,N)=>{b[N]||(b[N]={key:v[N],item:U,phase:"mount",ctrl:new Zl},b[N].ctrl.item=U)}),_.length){let U=-1;const{leave:N}=r?r():t;G(_,(W,j)=>{const z=y[j];~W?(U=b.indexOf(z),b[U]={...z,item:g[W]}):N&&b.splice(++U,0,z)})}P.fun(a)&&b.sort((U,N)=>a(U.item,N.item));let k=-o;const A=es(),C=_i(t),F=new Map,Y=T.useRef(new Map),E=T.useRef(!1);G(b,(U,N)=>{const W=U.key,j=U.phase,z=r?r():t;let R,K;const he=Se(z.delay||0,W);if(j=="mount")R=z.enter,K="enter";else{const ae=v.indexOf(W)<0;if(j!="leave")if(ae)R=z.leave,K="leave";else if(R=z.update)K="update";else return;else if(!ae)R=z.enter,K="enter";else return}if(R=Se(R,U.item,N),R=P.obj(R)?rs(R):{to:R},!R.config){const ae=c||C.config;R.config=Se(ae,U.item,N,K)}k+=o;const J={...C,delay:he+k,ref:l,immediate:z.immediate,reset:!1,...R};if(K=="enter"&&P.und(J.from)){const ae=r?r():t,ce=P.und(ae.initial)||y?ae.from:ae.initial;J.from=Se(ce,U.item,N)}const{onResolve:ge}=J;J.onResolve=ae=>{Se(ge,ae);const ce=p.current,oe=ce.find(me=>me.key===W);if(oe&&!(ae.cancelled&&oe.phase!="update")&&oe.ctrl.idle){const me=ce.every(m=>m.ctrl.idle);if(oe.phase=="leave"){const m=Se(s,oe.item);if(m!==!1){const M=m===!0?0:m;if(oe.expired=!0,!me&&M>0){M<=2147483647&&(oe.expirationId=setTimeout(A,M));return}}}me&&ce.some(m=>m.expired)&&(Y.current.delete(oe),u&&(E.current=!0),A())}};const ee=Xa(U.ctrl,J);K==="leave"&&u?Y.current.set(U,{phase:K,springs:ee,payload:J}):F.set(U,{phase:K,springs:ee,payload:J})});const L=T.useContext(nr),w=La(L),q=L!==w&&zl(L);_t(()=>{q&&G(b,U=>{U.ctrl.start({default:L})})},[L]),G(F,(U,N)=>{if(Y.current.size){const W=b.findIndex(j=>j.key===N.key);b.splice(W,1)}}),_t(()=>{G(Y.current.size?Y.current:F,({phase:U,payload:N},W)=>{const{ctrl:j}=W;W.phase=U,d==null||d.add(j),q&&U=="enter"&&j.start({default:L}),N&&(Hl(j,N.ref),(j.ref||d)&&!E.current?j.update(N):(j.start(N),E.current&&(E.current=!1)))})},i?void 0:n);const D=U=>T.createElement(T.Fragment,null,b.map((N,W)=>{const{springs:j}=F.get(N)||N.ctrl,z=U({...j},N.item,N,W);return z&&z.type?T.createElement(z.type,{...z.props,key:P.str(N.key)||P.num(N.key)?N.key:N.ctrl.id,ref:z.ref}):z}));return d?[D,d]:D}var B3=1;function G3(e,{key:t,keys:n=t},r){if(n===null){const i=new Set;return e.map(a=>{const o=r&&r.find(s=>s.item===a&&s.phase!=="leave"&&!i.has(s));return o?(i.add(o),o.key):B3++})}return P.und(n)?e:P.fun(n)?e.map(n):Te(n)}var id=class extends os{constructor(e,t){super(),this.source=e,this.idle=!0,this._active=new Set,this.calc=Dn(...t);const n=this._get(),r=Wa(n);ns(this,r.create(n))}advance(e){const t=this._get(),n=this.get();Qe(t,n)||(He(this).setValue(t),this._onChange(t,this.idle)),!this.idle&&ff(this._active)&&ma(this)}_get(){const e=P.arr(this.source)?this.source.map(ke):Te(ke(this.source));return this.calc(...e)}_start(){this.idle&&!ff(this._active)&&(this.idle=!1,G(yi(this),e=>{e.done=!1}),We.skipAnimation?(Q.batchedUpdates(()=>this.advance()),ma(this)):gi.start(this))}_attach(){let e=1;G(Te(this.source),t=>{Pe(t)&&rn(t,this),Ha(t)&&(t.idle||this._active.add(t),e=Math.max(e,t.priority+1))}),this.priority=e,this._start()}_detach(){G(Te(this.source),e=>{Pe(e)&&In(e,this)}),this._active.clear(),ma(this)}eventObserved(e){e.type=="change"?e.idle?this.advance():(this._active.add(e.parent),this._start()):e.type=="idle"?this._active.delete(e.parent):e.type=="priority"&&(this.priority=Te(this.source).reduce((t,n)=>Math.max(t,(Ha(n)?n.priority:0)+1),0))}};function X3(e){return e.idle!==!1}function ff(e){return!e.size||Array.from(e).every(X3)}function ma(e){e.idle||(e.idle=!0,G(yi(e),t=>{t.done=!0}),Rn(e,{type:"idle",parent:e}))}var jn=(e,...t)=>new id(e,t);We.assign({createStringInterpolator:El,to:(e,t)=>new id(e,t)});var ad=/^--/;function Q3(e,t){return t==null||typeof t=="boolean"||t===""?"":typeof t=="number"&&t!==0&&!ad.test(e)&&!(kn.hasOwnProperty(e)&&kn[e])?t+"px":(""+t).trim()}var cf={};function Z3(e,t){if(!e.nodeType||!e.setAttribute)return!1;const n=e.nodeName==="filter"||e.parentNode&&e.parentNode.nodeName==="filter",{className:r,style:i,children:a,scrollTop:o,scrollLeft:s,viewBox:u,...f}=t,l=Object.values(f),c=Object.keys(f).map(d=>n||e.hasAttribute(d)?d:cf[d]||(cf[d]=d.replace(/([A-Z])/g,g=>"-"+g.toLowerCase())));a!==void 0&&(e.textContent=a);for(const d in i)if(i.hasOwnProperty(d)){const g=Q3(d,i[d]);ad.test(d)?e.style.setProperty(d,g):e.style[d]=g}c.forEach((d,g)=>{e.setAttribute(d,l[g])}),r!==void 0&&(e.className=r),o!==void 0&&(e.scrollTop=o),s!==void 0&&(e.scrollLeft=s),u!==void 0&&e.setAttribute("viewBox",u)}var kn={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},J3=(e,t)=>e+t.charAt(0).toUpperCase()+t.substring(1),K3=["Webkit","Ms","Moz","O"];kn=Object.keys(kn).reduce((e,t)=>(K3.forEach(n=>e[J3(n,t)]=e[t]),e),kn);var e_=/^(matrix|translate|scale|rotate|skew)/,t_=/^(translate)/,n_=/^(rotate|skew)/,ba=(e,t)=>P.num(e)&&e!==0?e+t:e,mr=(e,t)=>P.arr(e)?e.every(n=>mr(n,t)):P.num(e)?e===t:parseFloat(e)===t,r_=class extends vi{constructor({x:e,y:t,z:n,...r}){const i=[],a=[];(e||t||n)&&(i.push([e||0,t||0,n||0]),a.push(o=>[`translate3d(${o.map(s=>ba(s,"px")).join(",")})`,mr(o,0)])),Ge(r,(o,s)=>{if(s==="transform")i.push([o||""]),a.push(u=>[u,u===""]);else if(e_.test(s)){if(delete r[s],P.und(o))return;const u=t_.test(s)?"px":n_.test(s)?"deg":"";i.push(Te(o)),a.push(s==="rotate3d"?([f,l,c,d])=>[`rotate3d(${f},${l},${c},${ba(d,u)})`,mr(d,0)]:f=>[`${s}(${f.map(l=>ba(l,u)).join(",")})`,mr(f,s.startsWith("scale")?1:0)])}}),i.length&&(r.transform=new i_(i,a)),super(r)}},i_=class extends Rl{constructor(e,t){super(),this.inputs=e,this.transforms=t,this._value=null}get(){return this._value||(this._value=this._get())}_get(){let e="",t=!0;return G(this.inputs,(n,r)=>{const i=ke(n[0]),[a,o]=this.transforms[r](P.arr(i)?i:n.map(ke));e+=" "+a,t=t&&o}),t?"none":e}observerAdded(e){e==1&&G(this.inputs,t=>G(t,n=>Pe(n)&&rn(n,this)))}observerRemoved(e){e==0&&G(this.inputs,t=>G(t,n=>Pe(n)&&In(n,this)))}eventObserved(e){e.type=="change"&&(this._value=null),Rn(this,e)}},a_=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"];We.assign({batchedUpdates:Mh.unstable_batchedUpdates,createStringInterpolator:El,colors:Jv});var o_=U3(a_,{applyAnimatedValues:Z3,createAnimatedStyle:e=>new r_(e),getComponentProps:({scrollTop:e,scrollLeft:t,...n})=>n}),le=o_.animated;function Tt(){return Tt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Tt.apply(this,arguments)}var s_={pointerEvents:"none",position:"absolute",zIndex:10,top:0,left:0},lf=function(e,t){return"translate("+e+"px, "+t+"px)"},od=T.memo(function(e){var t,n=e.position,r=e.anchor,i=e.children,a=pe(),o=qe(),s=o.animate,u=o.config,f=Yd(),l=f[0],c=f[1],d=T.useRef(!1),g=void 0,b=!1,p=c.width>0&&c.height>0,y=Math.round(n[0]),v=Math.round(n[1]);p&&(r==="top"?(y-=c.width/2,v-=c.height+14):r==="right"?(y+=14,v-=c.height/2):r==="bottom"?(y-=c.width/2,v+=14):r==="left"?(y-=c.width+14,v-=c.height/2):r==="center"&&(y-=c.width/2,v-=c.height/2),g={transform:lf(y,v)},d.current||(b=!0),d.current=[y,v]);var O=rt({to:g,config:u,immediate:!s||b}),_=Tt({},s_,a.tooltip.wrapper,{transform:(t=O.transform)!=null?t:lf(y,v),opacity:O.transform?1:0});return $.jsx(le.div,{ref:l,style:_,children:i})});od.displayName="TooltipWrapper";var u_=T.memo(function(e){var t=e.size,n=t===void 0?12:t,r=e.color,i=e.style;return $.jsx("span",{style:Tt({display:"block",width:n,height:n,background:r},i===void 0?{}:i)})}),f_=T.memo(function(e){var t,n=e.id,r=e.value,i=e.format,a=e.enableChip,o=a!==void 0&&a,s=e.color,u=e.renderContent,f=pe(),l=Wd(i);if(typeof u=="function")t=u();else{var c=r;l!==void 0&&c!==void 0&&(c=l(c)),t=$.jsxs("div",{style:f.tooltip.basic,children:[o&&$.jsx(u_,{color:s,style:f.tooltip.chip}),c!==void 0?$.jsxs("span",{children:[n,": ",$.jsx("strong",{children:""+c})]}):n]})}return $.jsx("div",{style:f.tooltip.container,children:t})}),c_={width:"100%",borderCollapse:"collapse"},l_=T.memo(function(e){var t,n=e.title,r=e.rows,i=r===void 0?[]:r,a=e.renderContent,o=pe();return i.length?(t=typeof a=="function"?a():$.jsxs("div",{children:[n&&n,$.jsx("table",{style:Tt({},c_,o.tooltip.table),children:$.jsx("tbody",{children:i.map(function(s,u){return $.jsx("tr",{children:s.map(function(f,l){return $.jsx("td",{style:o.tooltip.tableCell,children:f},l)})},u)})})})]}),$.jsx("div",{style:o.tooltip.container,children:t})):null});l_.displayName="TableTooltip";var Qa=T.memo(function(e){var t=e.x0,n=e.x1,r=e.y0,i=e.y1,a=pe(),o=qe(),s=o.animate,u=o.config,f=T.useMemo(function(){return Tt({},a.crosshair.line,{pointerEvents:"none"})},[a.crosshair.line]),l=rt({x1:t,x2:n,y1:r,y2:i,config:u,immediate:!s});return $.jsx(le.line,Tt({},l,{fill:"none",style:f}))});Qa.displayName="CrosshairLine";var d_=T.memo(function(e){var t,n,r=e.width,i=e.height,a=e.type,o=e.x,s=e.y;return a==="cross"?(t={x0:o,x1:o,y0:0,y1:i},n={x0:0,x1:r,y0:s,y1:s}):a==="top-left"?(t={x0:o,x1:o,y0:0,y1:s},n={x0:0,x1:o,y0:s,y1:s}):a==="top"?t={x0:o,x1:o,y0:0,y1:s}:a==="top-right"?(t={x0:o,x1:o,y0:0,y1:s},n={x0:o,x1:r,y0:s,y1:s}):a==="right"?n={x0:o,x1:r,y0:s,y1:s}:a==="bottom-right"?(t={x0:o,x1:o,y0:s,y1:i},n={x0:o,x1:r,y0:s,y1:s}):a==="bottom"?t={x0:o,x1:o,y0:s,y1:i}:a==="bottom-left"?(t={x0:o,x1:o,y0:s,y1:i},n={x0:0,x1:o,y0:s,y1:s}):a==="left"?n={x0:0,x1:o,y0:s,y1:s}:a==="x"?t={x0:o,x1:o,y0:0,y1:i}:a==="y"&&(n={x0:0,x1:r,y0:s,y1:s}),$.jsxs($.Fragment,{children:[t&&$.jsx(Qa,{x0:t.x0,x1:t.x1,y0:t.y0,y1:t.y1}),n&&$.jsx(Qa,{x0:n.x0,x1:n.x1,y0:n.y0,y1:n.y1})]})});d_.displayName="Crosshair";var sd=T.createContext({showTooltipAt:function(){},showTooltipFromEvent:function(){},hideTooltip:function(){}}),Za={isVisible:!1,position:[null,null],content:null,anchor:null},ud=T.createContext(Za),h_=function(e){var t=T.useState(Za),n=t[0],r=t[1],i=T.useCallback(function(s,u,f){var l=u[0],c=u[1];f===void 0&&(f="top"),r({isVisible:!0,position:[l,c],anchor:f,content:s})},[r]),a=T.useCallback(function(s,u,f){f===void 0&&(f="top");var l=e.current.getBoundingClientRect(),c=e.current.offsetWidth,d=c===l.width?1:c/l.width,g="touches"in u?u.touches[0]:u,b=g.clientX,p=g.clientY,y=(b-l.left)*d,v=(p-l.top)*d;f!=="left"&&f!=="right"||(f=y<l.width/2?"right":"left"),r({isVisible:!0,position:[y,v],anchor:f,content:s})},[e,r]),o=T.useCallback(function(){r(Za)},[r]);return{actions:T.useMemo(function(){return{showTooltipAt:i,showTooltipFromEvent:a,hideTooltip:o}},[i,a,o]),state:n}},g_=function(){var e=T.useContext(sd);if(e===void 0)throw new Error("useTooltip must be used within a TooltipProvider");return e},m_=function(){var e=T.useContext(ud);if(e===void 0)throw new Error("useTooltipState must be used within a TooltipProvider");return e},b_=function(e){return e.isVisible},p_=function(){var e=m_();return b_(e)?$.jsx(od,{position:e.position,anchor:e.anchor,children:e.content}):null},y_=function(e){var t=e.container,n=e.children,r=h_(t),i=r.actions,a=r.state;return $.jsx(sd.Provider,{value:i,children:$.jsx(ud.Provider,{value:a,children:n})})},v_=lh,__=lc;function x_(e,t,n){(n!==void 0&&!__(e[t],n)||n===void 0&&!(t in e))&&v_(e,t,n)}var fd=x_,w_=Xr,M_=Bn;function $_(e){return M_(e)&&w_(e)}var cd=$_;function T_(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}var ld=T_,C_=dc,k_=hc;function S_(e){return C_(e,k_(e))}var O_=S_,df=fd,A_=dh,U_=hh,P_=gh,D_=mh,hf=fo,gf=Xe,F_=cd,R_=bh,I_=ph,N_=Gn,E_=zo,Y_=yh,mf=ld,L_=O_;function W_(e,t,n,r,i,a,o){var s=mf(e,n),u=mf(t,n),f=o.get(u);if(f){df(e,n,f);return}var l=a?a(s,u,n+"",e,t,o):void 0,c=l===void 0;if(c){var d=gf(u),g=!d&&R_(u),b=!d&&!g&&Y_(u);l=u,d||g||b?gf(s)?l=s:F_(s)?l=P_(s):g?(c=!1,l=A_(u,!0)):b?(c=!1,l=U_(u,!0)):l=[]:E_(u)||hf(u)?(l=s,hf(s)?l=L_(s):(!N_(s)||I_(s))&&(l=D_(u))):c=!1}c&&(o.set(u,l),i(l,u,r,a,o),o.delete(u)),df(e,n,l)}var j_=W_,q_=ac,z_=fd,H_=Hc,V_=j_,B_=Gn,G_=hc,X_=ld;function dd(e,t,n,r,i){e!==t&&H_(t,function(a,o){if(i||(i=new q_),B_(a))V_(e,t,o,n,dd,r,i);else{var s=r?r(X_(e,o),a,o+"",e,t,i):void 0;s===void 0&&(s=a),z_(e,o,s)}},G_)}var Q_=dd,Z_=wo,J_=Xc;function K_(e){return Z_(function(t,n){var r=-1,i=n.length,a=i>1?n[i-1]:void 0,o=i>2?n[2]:void 0;for(a=e.length>3&&typeof a=="function"?(i--,a):void 0,o&&J_(n[0],n[1],o)&&(a=i<3?void 0:a,i=1),t=Object(t);++r<i;){var s=n[r];s&&e(t,s,r,a)}return t})}var e5=K_,t5=Q_,n5=e5,r5=n5(function(e,t,n){t5(e,t,n)}),i5=r5;const a5=Oe(i5);var o5=vh,s5=Kt,u5=co,bf=Gn,f5=en;function c5(e,t,n,r){if(!bf(e))return e;t=s5(t,e);for(var i=-1,a=t.length,o=a-1,s=e;s!=null&&++i<a;){var u=f5(t[i]),f=n;if(u==="__proto__"||u==="constructor"||u==="prototype")return e;if(i!=o){var l=s[u];f=r?r(l,u,s):void 0,f===void 0&&(f=bf(l)?l:u5(t[i+1])?[]:{})}o5(s,u,f),s=s[u]}return e}var hd=c5,l5=hd;function d5(e,t,n){return e==null?e:l5(e,t,n)}var h5=d5;const g5=Oe(h5);function Z(e){for(var t=e.length/6|0,n=new Array(t),r=0;r<t;)n[r]="#"+e.slice(r*6,++r*6);return n}const gd=Z("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf"),md=Z("7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666"),bd=Z("1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666"),pd=Z("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928"),yd=Z("fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2"),vd=Z("b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc"),_d=Z("e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999"),xd=Z("66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3"),us=Z("8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"),m5=Z("4e79a7f28e2ce1575976b7b259a14fedc949af7aa1ff9da79c755fbab0ab"),se=e=>X2(e[e.length-1]);var xi=new Array(3).concat("d8b365f5f5f55ab4ac","a6611adfc27d80cdc1018571","a6611adfc27df5f5f580cdc1018571","8c510ad8b365f6e8c3c7eae55ab4ac01665e","8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e","8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e","8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e","5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30","5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30").map(Z);const b5=se(xi);var wi=new Array(3).concat("af8dc3f7f7f77fbf7b","7b3294c2a5cfa6dba0008837","7b3294c2a5cff7f7f7a6dba0008837","762a83af8dc3e7d4e8d9f0d37fbf7b1b7837","762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837","762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837","762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837","40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b","40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b").map(Z);const p5=se(wi);var Mi=new Array(3).concat("e9a3c9f7f7f7a1d76a","d01c8bf1b6dab8e1864dac26","d01c8bf1b6daf7f7f7b8e1864dac26","c51b7de9a3c9fde0efe6f5d0a1d76a4d9221","c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221","c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221","c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221","8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419","8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419").map(Z);const y5=se(Mi);var $i=new Array(3).concat("998ec3f7f7f7f1a340","5e3c99b2abd2fdb863e66101","5e3c99b2abd2f7f7f7fdb863e66101","542788998ec3d8daebfee0b6f1a340b35806","542788998ec3d8daebf7f7f7fee0b6f1a340b35806","5427888073acb2abd2d8daebfee0b6fdb863e08214b35806","5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806","2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08","2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08").map(Z);const v5=se($i);var Ti=new Array(3).concat("ef8a62f7f7f767a9cf","ca0020f4a58292c5de0571b0","ca0020f4a582f7f7f792c5de0571b0","b2182bef8a62fddbc7d1e5f067a9cf2166ac","b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac","b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac","b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac","67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061","67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061").map(Z);const _5=se(Ti);var Ci=new Array(3).concat("ef8a62ffffff999999","ca0020f4a582bababa404040","ca0020f4a582ffffffbababa404040","b2182bef8a62fddbc7e0e0e09999994d4d4d","b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d","b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d","b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d","67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a","67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a").map(Z);const x5=se(Ci);var ki=new Array(3).concat("fc8d59ffffbf91bfdb","d7191cfdae61abd9e92c7bb6","d7191cfdae61ffffbfabd9e92c7bb6","d73027fc8d59fee090e0f3f891bfdb4575b4","d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4","d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4","d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4","a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695","a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695").map(Z);const w5=se(ki);var Si=new Array(3).concat("fc8d59ffffbf91cf60","d7191cfdae61a6d96a1a9641","d7191cfdae61ffffbfa6d96a1a9641","d73027fc8d59fee08bd9ef8b91cf601a9850","d73027fc8d59fee08bffffbfd9ef8b91cf601a9850","d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850","d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850","a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837","a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837").map(Z);const M5=se(Si);var Oi=new Array(3).concat("fc8d59ffffbf99d594","d7191cfdae61abdda42b83ba","d7191cfdae61ffffbfabdda42b83ba","d53e4ffc8d59fee08be6f59899d5943288bd","d53e4ffc8d59fee08bffffbfe6f59899d5943288bd","d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd","d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd","9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2","9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2").map(Z);const $5=se(Oi);var Ai=new Array(3).concat("e5f5f999d8c92ca25f","edf8fbb2e2e266c2a4238b45","edf8fbb2e2e266c2a42ca25f006d2c","edf8fbccece699d8c966c2a42ca25f006d2c","edf8fbccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b").map(Z);const T5=se(Ai);var Ui=new Array(3).concat("e0ecf49ebcda8856a7","edf8fbb3cde38c96c688419d","edf8fbb3cde38c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b").map(Z);const C5=se(Ui);var Pi=new Array(3).concat("e0f3dba8ddb543a2ca","f0f9e8bae4bc7bccc42b8cbe","f0f9e8bae4bc7bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081").map(Z);const k5=se(Pi);var Di=new Array(3).concat("fee8c8fdbb84e34a33","fef0d9fdcc8afc8d59d7301f","fef0d9fdcc8afc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000").map(Z);const S5=se(Di);var Fi=new Array(3).concat("ece2f0a6bddb1c9099","f6eff7bdc9e167a9cf02818a","f6eff7bdc9e167a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636").map(Z);const O5=se(Fi);var Ri=new Array(3).concat("ece7f2a6bddb2b8cbe","f1eef6bdc9e174a9cf0570b0","f1eef6bdc9e174a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858").map(Z);const A5=se(Ri);var Ii=new Array(3).concat("e7e1efc994c7dd1c77","f1eef6d7b5d8df65b0ce1256","f1eef6d7b5d8df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f").map(Z);const U5=se(Ii);var Ni=new Array(3).concat("fde0ddfa9fb5c51b8a","feebe2fbb4b9f768a1ae017e","feebe2fbb4b9f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a").map(Z);const P5=se(Ni);var Ei=new Array(3).concat("edf8b17fcdbb2c7fb8","ffffcca1dab441b6c4225ea8","ffffcca1dab441b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58").map(Z);const D5=se(Ei);var Yi=new Array(3).concat("f7fcb9addd8e31a354","ffffccc2e69978c679238443","ffffccc2e69978c67931a354006837","ffffccd9f0a3addd8e78c67931a354006837","ffffccd9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529").map(Z);const F5=se(Yi);var Li=new Array(3).concat("fff7bcfec44fd95f0e","ffffd4fed98efe9929cc4c02","ffffd4fed98efe9929d95f0e993404","ffffd4fee391fec44ffe9929d95f0e993404","ffffd4fee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506").map(Z);const R5=se(Li);var Wi=new Array(3).concat("ffeda0feb24cf03b20","ffffb2fecc5cfd8d3ce31a1c","ffffb2fecc5cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026").map(Z);const I5=se(Wi);var ji=new Array(3).concat("deebf79ecae13182bd","eff3ffbdd7e76baed62171b5","eff3ffbdd7e76baed63182bd08519c","eff3ffc6dbef9ecae16baed63182bd08519c","eff3ffc6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b").map(Z);const N5=se(ji);var qi=new Array(3).concat("e5f5e0a1d99b31a354","edf8e9bae4b374c476238b45","edf8e9bae4b374c47631a354006d2c","edf8e9c7e9c0a1d99b74c47631a354006d2c","edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b").map(Z);const E5=se(qi);var zi=new Array(3).concat("f0f0f0bdbdbd636363","f7f7f7cccccc969696525252","f7f7f7cccccc969696636363252525","f7f7f7d9d9d9bdbdbd969696636363252525","f7f7f7d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000").map(Z);const Y5=se(zi);var Hi=new Array(3).concat("efedf5bcbddc756bb1","f2f0f7cbc9e29e9ac86a51a3","f2f0f7cbc9e29e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d").map(Z);const L5=se(Hi);var Vi=new Array(3).concat("fee0d2fc9272de2d26","fee5d9fcae91fb6a4acb181d","fee5d9fcae91fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d").map(Z);const W5=se(Vi);var Bi=new Array(3).concat("fee6cefdae6be6550d","feeddefdbe85fd8d3cd94701","feeddefdbe85fd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704").map(Z);const j5=se(Bi);function q5(e){return e=Math.max(0,Math.min(1,e)),"rgb("+Math.max(0,Math.min(255,Math.round(-4.54-e*(35.34-e*(2381.73-e*(6402.7-e*(7024.72-e*2710.57)))))))+", "+Math.max(0,Math.min(255,Math.round(32.49+e*(170.73+e*(52.82-e*(131.46-e*(176.58-e*67.37)))))))+", "+Math.max(0,Math.min(255,Math.round(81.24+e*(442.36-e*(2482.43-e*(6167.24-e*(6614.94-e*2475.67)))))))+")"}const z5=Oo(Be(300,.5,0),Be(-240,.5,1));var H5=Oo(Be(-100,.75,.35),Be(80,1.5,.8)),V5=Oo(Be(260,.75,.35),Be(80,1.5,.8)),cr=Be();function B5(e){(e<0||e>1)&&(e-=Math.floor(e));var t=Math.abs(e-.5);return cr.h=360*e-100,cr.s=1.5-1.5*t,cr.l=.8-.9*t,cr+""}var lr=qt(),G5=Math.PI/3,X5=Math.PI*2/3;function Q5(e){var t;return e=(.5-e)*Math.PI,lr.r=255*(t=Math.sin(e))*t,lr.g=255*(t=Math.sin(e+G5))*t,lr.b=255*(t=Math.sin(e+X5))*t,lr+""}function Z5(e){return e=Math.max(0,Math.min(1,e)),"rgb("+Math.max(0,Math.min(255,Math.round(34.61+e*(1172.33-e*(10793.56-e*(33300.12-e*(38394.49-e*14825.05)))))))+", "+Math.max(0,Math.min(255,Math.round(23.31+e*(557.33+e*(1225.33-e*(3574.96-e*(1073.77+e*707.56)))))))+", "+Math.max(0,Math.min(255,Math.round(27.2+e*(3211.1-e*(15327.97-e*(27814-e*(22569.18-e*6838.66)))))))+")"}function Gi(e){var t=e.length;return function(n){return e[Math.max(0,Math.min(t-1,Math.floor(n*t)))]}}const J5=Gi(Z("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725"));var K5=Gi(Z("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf")),e8=Gi(Z("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4")),t8=Gi(Z("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921")),n8=uc,r8=Wc,i8=jc,a8=Jr,o8=cc,s8=fc,u8=200;function f8(e,t,n,r){var i=-1,a=r8,o=!0,s=e.length,u=[],f=t.length;if(!s)return u;n&&(t=a8(t,o8(n))),r?(a=i8,o=!1):t.length>=u8&&(a=s8,o=!1,t=new n8(t));e:for(;++i<s;){var l=e[i],c=n==null?l:n(l);if(l=r||l!==0?l:0,o&&c===c){for(var d=f;d--;)if(t[d]===c)continue e;u.push(l)}else a(t,c,r)||u.push(l)}return u}var c8=f8,l8=c8,d8=wo,h8=cd,g8=d8(function(e,t){return h8(e)?l8(e,t):[]}),m8=g8;const wd=Oe(m8);function b8(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function Ir(e,t){if((n=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var n,r=e.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+e.slice(n+1)]}function p8(e){return e=Ir(Math.abs(e)),e?e[1]:NaN}function y8(e,t){return function(n,r){for(var i=n.length,a=[],o=0,s=e[0],u=0;i>0&&s>0&&(u+s+1>r&&(s=Math.max(1,r-u)),a.push(n.substring(i-=s,i+s)),!((u+=s+1)>r));)s=e[o=(o+1)%e.length];return a.reverse().join(t)}}function v8(e){return function(t){return t.replace(/[0-9]/g,function(n){return e[+n]})}}var _8=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Ja(e){if(!(t=_8.exec(e)))throw new Error("invalid format: "+e);var t;return new fs({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}Ja.prototype=fs.prototype;function fs(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}fs.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function x8(e){e:for(var t=e.length,n=1,r=-1,i;n<t;++n)switch(e[n]){case".":r=i=n;break;case"0":r===0&&(r=n),i=n;break;default:if(!+e[n])break e;r>0&&(r=0);break}return r>0?e.slice(0,r)+e.slice(i+1):e}var Md;function w8(e,t){var n=Ir(e,t);if(!n)return e+"";var r=n[0],i=n[1],a=i-(Md=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=r.length;return a===o?r:a>o?r+new Array(a-o+1).join("0"):a>0?r.slice(0,a)+"."+r.slice(a):"0."+new Array(1-a).join("0")+Ir(e,Math.max(0,t+a-1))[0]}function pf(e,t){var n=Ir(e,t);if(!n)return e+"";var r=n[0],i=n[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}const yf={"%":function(e,t){return(e*100).toFixed(t)},b:function(e){return Math.round(e).toString(2)},c:function(e){return e+""},d:b8,e:function(e,t){return e.toExponential(t)},f:function(e,t){return e.toFixed(t)},g:function(e,t){return e.toPrecision(t)},o:function(e){return Math.round(e).toString(8)},p:function(e,t){return pf(e*100,t)},r:pf,s:w8,X:function(e){return Math.round(e).toString(16).toUpperCase()},x:function(e){return Math.round(e).toString(16)}};function vf(e){return e}var _f=Array.prototype.map,xf=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function M8(e){var t=e.grouping===void 0||e.thousands===void 0?vf:y8(_f.call(e.grouping,Number),e.thousands+""),n=e.currency===void 0?"":e.currency[0]+"",r=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?vf:v8(_f.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",s=e.minus===void 0?"-":e.minus+"",u=e.nan===void 0?"NaN":e.nan+"";function f(c){c=Ja(c);var d=c.fill,g=c.align,b=c.sign,p=c.symbol,y=c.zero,v=c.width,O=c.comma,_=c.precision,k=c.trim,A=c.type;A==="n"?(O=!0,A="g"):yf[A]||(_===void 0&&(_=12),k=!0,A="g"),(y||d==="0"&&g==="=")&&(y=!0,d="0",g="=");var C=p==="$"?n:p==="#"&&/[boxX]/.test(A)?"0"+A.toLowerCase():"",F=p==="$"?r:/[%p]/.test(A)?o:"",Y=yf[A],E=/[defgprs%]/.test(A);_=_===void 0?6:/[gprs]/.test(A)?Math.max(1,Math.min(21,_)):Math.max(0,Math.min(20,_));function L(w){var q=C,D=F,U,N,W;if(A==="c")D=Y(w)+D,w="";else{w=+w;var j=w<0||1/w<0;if(w=isNaN(w)?u:Y(Math.abs(w),_),k&&(w=x8(w)),j&&+w==0&&b!=="+"&&(j=!1),q=(j?b==="("?b:s:b==="-"||b==="("?"":b)+q,D=(A==="s"?xf[8+Md/3]:"")+D+(j&&b==="("?")":""),E){for(U=-1,N=w.length;++U<N;)if(W=w.charCodeAt(U),48>W||W>57){D=(W===46?i+w.slice(U+1):w.slice(U))+D,w=w.slice(0,U);break}}}O&&!y&&(w=t(w,1/0));var z=q.length+w.length+D.length,R=z<v?new Array(v-z+1).join(d):"";switch(O&&y&&(w=t(R+w,R.length?v-D.length:1/0),R=""),g){case"<":w=q+w+D+R;break;case"=":w=q+R+w+D;break;case"^":w=R.slice(0,z=R.length>>1)+q+w+D+R.slice(z);break;default:w=R+q+w+D;break}return a(w)}return L.toString=function(){return c+""},L}function l(c,d){var g=f((c=Ja(c),c.type="f",c)),b=Math.max(-8,Math.min(8,Math.floor(p8(d)/3)))*3,p=Math.pow(10,-b),y=xf[8+b/3];return function(v){return g(p*v)+y}}return{format:f,formatPrefix:l}}var dr,$d;$8({decimal:".",thousands:",",grouping:[3],currency:["$",""],minus:"-"});function $8(e){return dr=M8(e),$d=dr.format,dr.formatPrefix,dr}var pa=new Date,ya=new Date;function it(e,t,n,r){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=function(a){return e(a=new Date(+a)),a},i.ceil=function(a){return e(a=new Date(a-1)),t(a,1),e(a),a},i.round=function(a){var o=i(a),s=i.ceil(a);return a-o<s-a?o:s},i.offset=function(a,o){return t(a=new Date(+a),o==null?1:Math.floor(o)),a},i.range=function(a,o,s){var u=[],f;if(a=i.ceil(a),s=s==null?1:Math.floor(s),!(a<o)||!(s>0))return u;do u.push(f=new Date(+a)),t(a,s),e(a);while(f<a&&a<o);return u},i.filter=function(a){return it(function(o){if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},function(o,s){if(o>=o)if(s<0)for(;++s<=0;)for(;t(o,-1),!a(o););else for(;--s>=0;)for(;t(o,1),!a(o););})},n&&(i.count=function(a,o){return pa.setTime(+a),ya.setTime(+o),e(pa),e(ya),Math.floor(n(pa,ya))},i.every=function(a){return a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(r?function(o){return r(o)%a===0}:function(o){return i.count(0,o)%a===0}):i}),i}const T8=1e3,cs=T8*60,C8=cs*60,ls=C8*24,Td=ls*7;var Cd=it(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*cs)/ls,e=>e.getDate()-1);const kd=Cd;Cd.range;function St(e){return it(function(t){t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},function(t,n){t.setDate(t.getDate()+n*7)},function(t,n){return(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*cs)/Td})}var Sd=St(0),Nr=St(1),k8=St(2),S8=St(3),Gt=St(4),O8=St(5),A8=St(6);Sd.range;Nr.range;k8.range;S8.range;Gt.range;O8.range;A8.range;var ds=it(function(e){e.setMonth(0,1),e.setHours(0,0,0,0)},function(e,t){e.setFullYear(e.getFullYear()+t)},function(e,t){return t.getFullYear()-e.getFullYear()},function(e){return e.getFullYear()});ds.every=function(e){return!isFinite(e=Math.floor(e))||!(e>0)?null:it(function(t){t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},function(t,n){t.setFullYear(t.getFullYear()+n*e)})};const qn=ds;ds.range;var Od=it(function(e){e.setUTCHours(0,0,0,0)},function(e,t){e.setUTCDate(e.getUTCDate()+t)},function(e,t){return(t-e)/ls},function(e){return e.getUTCDate()-1});const Ad=Od;Od.range;function Ot(e){return it(function(t){t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCDate(t.getUTCDate()+n*7)},function(t,n){return(n-t)/Td})}var Ud=Ot(0),Er=Ot(1),U8=Ot(2),P8=Ot(3),Xt=Ot(4),D8=Ot(5),F8=Ot(6);Ud.range;Er.range;U8.range;P8.range;Xt.range;D8.range;F8.range;var hs=it(function(e){e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},function(e,t){e.setUTCFullYear(e.getUTCFullYear()+t)},function(e,t){return t.getUTCFullYear()-e.getUTCFullYear()},function(e){return e.getUTCFullYear()});hs.every=function(e){return!isFinite(e=Math.floor(e))||!(e>0)?null:it(function(t){t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCFullYear(t.getUTCFullYear()+n*e)})};const zn=hs;hs.range;function va(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function _a(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function gn(e,t,n){return{y:e,m:t,d:n,H:0,M:0,S:0,L:0}}function R8(e){var t=e.dateTime,n=e.date,r=e.time,i=e.periods,a=e.days,o=e.shortDays,s=e.months,u=e.shortMonths,f=mn(i),l=bn(i),c=mn(a),d=bn(a),g=mn(o),b=bn(o),p=mn(s),y=bn(s),v=mn(u),O=bn(u),_={a:j,A:z,b:R,B:K,c:null,d:kf,e:kf,f:i6,g:g6,G:b6,H:t6,I:n6,j:r6,L:Pd,m:a6,M:o6,p:he,q:J,Q:Af,s:Uf,S:s6,u:u6,U:f6,V:c6,w:l6,W:d6,x:null,X:null,y:h6,Y:m6,Z:p6,"%":Of},k={a:ge,A:ee,b:ae,B:ce,c:null,d:Sf,e:Sf,f:x6,g:U6,G:D6,H:y6,I:v6,j:_6,L:Fd,m:w6,M:M6,p:oe,q:me,Q:Af,s:Uf,S:$6,u:T6,U:C6,V:k6,w:S6,W:O6,x:null,X:null,y:A6,Y:P6,Z:F6,"%":Of},A={a:L,A:w,b:q,B:D,c:U,d:Tf,e:Tf,f:Z8,g:$f,G:Mf,H:Cf,I:Cf,j:B8,L:Q8,m:V8,M:G8,p:E,q:H8,Q:K8,s:e6,S:X8,u:L8,U:W8,V:j8,w:Y8,W:q8,x:N,X:W,y:$f,Y:Mf,Z:z8,"%":J8};_.x=C(n,_),_.X=C(r,_),_.c=C(t,_),k.x=C(n,k),k.X=C(r,k),k.c=C(t,k);function C(m,M){return function(S){var h=[],V=-1,I=0,B=m.length,X,fe,Ie;for(S instanceof Date||(S=new Date(+S));++V<B;)m.charCodeAt(V)===37&&(h.push(m.slice(I,V)),(fe=wf[X=m.charAt(++V)])!=null?X=m.charAt(++V):fe=X==="e"?" ":"0",(Ie=M[X])&&(X=Ie(S,fe)),h.push(X),I=V+1);return h.push(m.slice(I,V)),h.join("")}}function F(m,M){return function(S){var h=gn(1900,void 0,1),V=Y(h,m,S+="",0),I,B;if(V!=S.length)return null;if("Q"in h)return new Date(h.Q);if("s"in h)return new Date(h.s*1e3+("L"in h?h.L:0));if(M&&!("Z"in h)&&(h.Z=0),"p"in h&&(h.H=h.H%12+h.p*12),h.m===void 0&&(h.m="q"in h?h.q:0),"V"in h){if(h.V<1||h.V>53)return null;"w"in h||(h.w=1),"Z"in h?(I=_a(gn(h.y,0,1)),B=I.getUTCDay(),I=B>4||B===0?Er.ceil(I):Er(I),I=Ad.offset(I,(h.V-1)*7),h.y=I.getUTCFullYear(),h.m=I.getUTCMonth(),h.d=I.getUTCDate()+(h.w+6)%7):(I=va(gn(h.y,0,1)),B=I.getDay(),I=B>4||B===0?Nr.ceil(I):Nr(I),I=kd.offset(I,(h.V-1)*7),h.y=I.getFullYear(),h.m=I.getMonth(),h.d=I.getDate()+(h.w+6)%7)}else("W"in h||"U"in h)&&("w"in h||(h.w="u"in h?h.u%7:"W"in h?1:0),B="Z"in h?_a(gn(h.y,0,1)).getUTCDay():va(gn(h.y,0,1)).getDay(),h.m=0,h.d="W"in h?(h.w+6)%7+h.W*7-(B+5)%7:h.w+h.U*7-(B+6)%7);return"Z"in h?(h.H+=h.Z/100|0,h.M+=h.Z%100,_a(h)):va(h)}}function Y(m,M,S,h){for(var V=0,I=M.length,B=S.length,X,fe;V<I;){if(h>=B)return-1;if(X=M.charCodeAt(V++),X===37){if(X=M.charAt(V++),fe=A[X in wf?M.charAt(V++):X],!fe||(h=fe(m,S,h))<0)return-1}else if(X!=S.charCodeAt(h++))return-1}return h}function E(m,M,S){var h=f.exec(M.slice(S));return h?(m.p=l.get(h[0].toLowerCase()),S+h[0].length):-1}function L(m,M,S){var h=g.exec(M.slice(S));return h?(m.w=b.get(h[0].toLowerCase()),S+h[0].length):-1}function w(m,M,S){var h=c.exec(M.slice(S));return h?(m.w=d.get(h[0].toLowerCase()),S+h[0].length):-1}function q(m,M,S){var h=v.exec(M.slice(S));return h?(m.m=O.get(h[0].toLowerCase()),S+h[0].length):-1}function D(m,M,S){var h=p.exec(M.slice(S));return h?(m.m=y.get(h[0].toLowerCase()),S+h[0].length):-1}function U(m,M,S){return Y(m,t,M,S)}function N(m,M,S){return Y(m,n,M,S)}function W(m,M,S){return Y(m,r,M,S)}function j(m){return o[m.getDay()]}function z(m){return a[m.getDay()]}function R(m){return u[m.getMonth()]}function K(m){return s[m.getMonth()]}function he(m){return i[+(m.getHours()>=12)]}function J(m){return 1+~~(m.getMonth()/3)}function ge(m){return o[m.getUTCDay()]}function ee(m){return a[m.getUTCDay()]}function ae(m){return u[m.getUTCMonth()]}function ce(m){return s[m.getUTCMonth()]}function oe(m){return i[+(m.getUTCHours()>=12)]}function me(m){return 1+~~(m.getUTCMonth()/3)}return{format:function(m){var M=C(m+="",_);return M.toString=function(){return m},M},parse:function(m){var M=F(m+="",!1);return M.toString=function(){return m},M},utcFormat:function(m){var M=C(m+="",k);return M.toString=function(){return m},M},utcParse:function(m){var M=F(m+="",!0);return M.toString=function(){return m},M}}}var wf={"-":"",_:" ",0:"0"},_e=/^\s*\d+/,I8=/^%/,N8=/[\\^$*+?|[\]().{}]/g;function re(e,t,n){var r=e<0?"-":"",i=(r?-e:e)+"",a=i.length;return r+(a<n?new Array(n-a+1).join(t)+i:i)}function E8(e){return e.replace(N8,"\\$&")}function mn(e){return new RegExp("^(?:"+e.map(E8).join("|")+")","i")}function bn(e){return new Map(e.map((t,n)=>[t.toLowerCase(),n]))}function Y8(e,t,n){var r=_e.exec(t.slice(n,n+1));return r?(e.w=+r[0],n+r[0].length):-1}function L8(e,t,n){var r=_e.exec(t.slice(n,n+1));return r?(e.u=+r[0],n+r[0].length):-1}function W8(e,t,n){var r=_e.exec(t.slice(n,n+2));return r?(e.U=+r[0],n+r[0].length):-1}function j8(e,t,n){var r=_e.exec(t.slice(n,n+2));return r?(e.V=+r[0],n+r[0].length):-1}function q8(e,t,n){var r=_e.exec(t.slice(n,n+2));return r?(e.W=+r[0],n+r[0].length):-1}function Mf(e,t,n){var r=_e.exec(t.slice(n,n+4));return r?(e.y=+r[0],n+r[0].length):-1}function $f(e,t,n){var r=_e.exec(t.slice(n,n+2));return r?(e.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function z8(e,t,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(n,n+6));return r?(e.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function H8(e,t,n){var r=_e.exec(t.slice(n,n+1));return r?(e.q=r[0]*3-3,n+r[0].length):-1}function V8(e,t,n){var r=_e.exec(t.slice(n,n+2));return r?(e.m=r[0]-1,n+r[0].length):-1}function Tf(e,t,n){var r=_e.exec(t.slice(n,n+2));return r?(e.d=+r[0],n+r[0].length):-1}function B8(e,t,n){var r=_e.exec(t.slice(n,n+3));return r?(e.m=0,e.d=+r[0],n+r[0].length):-1}function Cf(e,t,n){var r=_e.exec(t.slice(n,n+2));return r?(e.H=+r[0],n+r[0].length):-1}function G8(e,t,n){var r=_e.exec(t.slice(n,n+2));return r?(e.M=+r[0],n+r[0].length):-1}function X8(e,t,n){var r=_e.exec(t.slice(n,n+2));return r?(e.S=+r[0],n+r[0].length):-1}function Q8(e,t,n){var r=_e.exec(t.slice(n,n+3));return r?(e.L=+r[0],n+r[0].length):-1}function Z8(e,t,n){var r=_e.exec(t.slice(n,n+6));return r?(e.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function J8(e,t,n){var r=I8.exec(t.slice(n,n+1));return r?n+r[0].length:-1}function K8(e,t,n){var r=_e.exec(t.slice(n));return r?(e.Q=+r[0],n+r[0].length):-1}function e6(e,t,n){var r=_e.exec(t.slice(n));return r?(e.s=+r[0],n+r[0].length):-1}function kf(e,t){return re(e.getDate(),t,2)}function t6(e,t){return re(e.getHours(),t,2)}function n6(e,t){return re(e.getHours()%12||12,t,2)}function r6(e,t){return re(1+kd.count(qn(e),e),t,3)}function Pd(e,t){return re(e.getMilliseconds(),t,3)}function i6(e,t){return Pd(e,t)+"000"}function a6(e,t){return re(e.getMonth()+1,t,2)}function o6(e,t){return re(e.getMinutes(),t,2)}function s6(e,t){return re(e.getSeconds(),t,2)}function u6(e){var t=e.getDay();return t===0?7:t}function f6(e,t){return re(Sd.count(qn(e)-1,e),t,2)}function Dd(e){var t=e.getDay();return t>=4||t===0?Gt(e):Gt.ceil(e)}function c6(e,t){return e=Dd(e),re(Gt.count(qn(e),e)+(qn(e).getDay()===4),t,2)}function l6(e){return e.getDay()}function d6(e,t){return re(Nr.count(qn(e)-1,e),t,2)}function h6(e,t){return re(e.getFullYear()%100,t,2)}function g6(e,t){return e=Dd(e),re(e.getFullYear()%100,t,2)}function m6(e,t){return re(e.getFullYear()%1e4,t,4)}function b6(e,t){var n=e.getDay();return e=n>=4||n===0?Gt(e):Gt.ceil(e),re(e.getFullYear()%1e4,t,4)}function p6(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+re(t/60|0,"0",2)+re(t%60,"0",2)}function Sf(e,t){return re(e.getUTCDate(),t,2)}function y6(e,t){return re(e.getUTCHours(),t,2)}function v6(e,t){return re(e.getUTCHours()%12||12,t,2)}function _6(e,t){return re(1+Ad.count(zn(e),e),t,3)}function Fd(e,t){return re(e.getUTCMilliseconds(),t,3)}function x6(e,t){return Fd(e,t)+"000"}function w6(e,t){return re(e.getUTCMonth()+1,t,2)}function M6(e,t){return re(e.getUTCMinutes(),t,2)}function $6(e,t){return re(e.getUTCSeconds(),t,2)}function T6(e){var t=e.getUTCDay();return t===0?7:t}function C6(e,t){return re(Ud.count(zn(e)-1,e),t,2)}function Rd(e){var t=e.getUTCDay();return t>=4||t===0?Xt(e):Xt.ceil(e)}function k6(e,t){return e=Rd(e),re(Xt.count(zn(e),e)+(zn(e).getUTCDay()===4),t,2)}function S6(e){return e.getUTCDay()}function O6(e,t){return re(Er.count(zn(e)-1,e),t,2)}function A6(e,t){return re(e.getUTCFullYear()%100,t,2)}function U6(e,t){return e=Rd(e),re(e.getUTCFullYear()%100,t,2)}function P6(e,t){return re(e.getUTCFullYear()%1e4,t,4)}function D6(e,t){var n=e.getUTCDay();return e=n>=4||n===0?Xt(e):Xt.ceil(e),re(e.getUTCFullYear()%1e4,t,4)}function F6(){return"+0000"}function Of(){return"%"}function Af(e){return+e}function Uf(e){return Math.floor(+e/1e3)}var It,Id;R6({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function R6(e){return It=R8(e),Id=It.format,It.parse,It.utcFormat,It.utcParse,It}var I6=Xn,N6=hd,E6=Kt;function Y6(e,t,n){for(var r=-1,i=t.length,a={};++r<i;){var o=t[r],s=I6(e,o);n(s,o)&&N6(a,E6(o,e),s)}return a}var L6=Y6,W6=L6,j6=Lc;function q6(e,t){return W6(e,t,function(n,r){return j6(e,r)})}var z6=q6,H6=z6,V6=$l;V6(function(e,t){return e==null?{}:H6(e,t)});var B6={background:"transparent",text:{fontFamily:"sans-serif",fontSize:11,fill:"#333333",outlineWidth:0,outlineColor:"transparent",outlineOpacity:1},axis:{domain:{line:{stroke:"transparent",strokeWidth:1}},ticks:{line:{stroke:"#777777",strokeWidth:1},text:{}},legend:{text:{fontSize:12}}},grid:{line:{stroke:"#dddddd",strokeWidth:1}},legends:{hidden:{symbol:{fill:"#333333",opacity:.6},text:{fill:"#333333",opacity:.6}},text:{},ticks:{line:{stroke:"#777777",strokeWidth:1},text:{fontSize:10}},title:{text:{}}},labels:{text:{}},markers:{lineColor:"#000000",lineStrokeWidth:1,text:{}},dots:{text:{}},tooltip:{container:{background:"white",color:"inherit",fontSize:"inherit",borderRadius:"2px",boxShadow:"0 1px 2px rgba(0, 0, 0, 0.25)",padding:"5px 9px"},basic:{whiteSpace:"pre",display:"flex",alignItems:"center"},chip:{marginRight:7},table:{},tableCell:{padding:"3px 5px"},tableCellValue:{fontWeight:"bold"}},crosshair:{line:{stroke:"#000000",strokeWidth:1,strokeOpacity:.75,strokeDasharray:"6 6"}},annotations:{text:{fontSize:13,outlineWidth:2,outlineColor:"#ffffff",outlineOpacity:1},link:{stroke:"#000000",strokeWidth:1,outlineWidth:2,outlineColor:"#ffffff",outlineOpacity:1},outline:{fill:"none",stroke:"#000000",strokeWidth:2,outlineWidth:2,outlineColor:"#ffffff",outlineOpacity:1},symbol:{fill:"#000000",outlineWidth:2,outlineColor:"#ffffff",outlineOpacity:1}}};function dt(){return dt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},dt.apply(this,arguments)}function gs(e,t){if(e==null)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}var G6=["axis.ticks.text","axis.legend.text","legends.title.text","legends.text","legends.ticks.text","legends.title.text","labels.text","dots.text","markers.text","annotations.text"],X6=function(e,t){return dt({},t,e)},Q6=function(e,t){var n=a5({},e,t);return G6.forEach(function(r){g5(n,r,X6(br(n,r),n.text))}),n},Nd=T.createContext(),Ed=function(e){var t=e.children,n=e.animate,r=n===void 0||n,i=e.config,a=i===void 0?"default":i,o=T.useMemo(function(){var s=C1(a)?is[a]:a;return{animate:r,config:s}},[r,a]);return $.jsx(Nd.Provider,{value:o,children:t})},Yr={animate:x.bool,motionConfig:x.oneOfType([x.oneOf(Object.keys(is)),x.shape({mass:x.number,tension:x.number,friction:x.number,clamp:x.bool,precision:x.number,velocity:x.number,duration:x.number,easing:x.func})])};Ed.propTypes={children:x.node.isRequired,animate:Yr.animate,config:Yr.motionConfig};var qe=function(){return T.useContext(Nd)},Z6=function(e){var t=qe(),n=t.animate,r=t.config,i=function(s){var u=T.useRef();return T.useEffect(function(){u.current=s},[s]),u.current}(e),a=T.useMemo(function(){return il(i,e)},[i,e]),o=rt({from:{value:0},to:{value:1},reset:!0,config:r,immediate:!n}).value;return jn(o,a)},J6={nivo:["#d76445","#f47560","#e8c1a0","#97e3d5","#61cdbb","#00b0a7"],BrBG:H(xi),PRGn:H(wi),PiYG:H(Mi),PuOr:H($i),RdBu:H(Ti),RdGy:H(Ci),RdYlBu:H(ki),RdYlGn:H(Si),spectral:H(Oi),blues:H(ji),greens:H(qi),greys:H(zi),oranges:H(Bi),purples:H(Hi),reds:H(Vi),BuGn:H(Ai),BuPu:H(Ui),GnBu:H(Pi),OrRd:H(Di),PuBuGn:H(Fi),PuBu:H(Ri),PuRd:H(Ii),RdPu:H(Ni),YlGnBu:H(Ei),YlGn:H(Yi),YlOrBr:H(Li),YlOrRd:H(Wi)},K6=Object.keys(J6);H(xi),H(wi),H(Mi),H($i),H(Ti),H(Ci),H(ki),H(Si),H(Oi),H(ji),H(qi),H(zi),H(Bi),H(Hi),H(Vi),H(Ai),H(Ui),H(Pi),H(Di),H(Fi),H(Ri),H(Ii),H(Ni),H(Ei),H(Yi),H(Li),H(Wi);x.oneOfType([x.oneOf(K6),x.func,x.arrayOf(x.string)]);var e7={basis:z1,basisClosed:H1,basisOpen:V1,bundle:B1,cardinal:G1,cardinalClosed:X1,cardinalOpen:Q1,catmullRom:Z1,catmullRomClosed:J1,catmullRomOpen:K1,linear:bo,linearClosed:eg,monotoneX:tg,monotoneY:ng,natural:rg,step:ig,stepAfter:og,stepBefore:ag},ms=Object.keys(e7);ms.filter(function(e){return e.endsWith("Closed")});wd(ms,"bundle","basisClosed","basisOpen","cardinalClosed","cardinalOpen","catmullRomClosed","catmullRomOpen","linearClosed");wd(ms,"bundle","basisClosed","basisOpen","cardinalClosed","cardinalOpen","catmullRomClosed","catmullRomOpen","linearClosed");x.shape({top:x.number,right:x.number,bottom:x.number,left:x.number}).isRequired;var t7=["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"];x.oneOf(t7);$o(us);var n7={top:0,right:0,bottom:0,left:0},r7=function(e,t,n){return n===void 0&&(n={}),T.useMemo(function(){var r=dt({},n7,n);return{margin:r,innerWidth:e-r.left-r.right,innerHeight:t-r.top-r.bottom,outerWidth:e,outerHeight:t}},[e,t,n.top,n.right,n.bottom,n.left])},Yd=function(){var e=T.useRef(null),t=T.useState({left:0,top:0,width:0,height:0}),n=t[0],r=t[1],i=T.useState(function(){return typeof ResizeObserver>"u"?null:new ResizeObserver(function(a){var o=a[0];return r(o.contentRect)})})[0];return T.useEffect(function(){return e.current&&i!==null&&i.observe(e.current),function(){i!==null&&i.disconnect()}},[]),[e,n]},i7=function(e){return T.useMemo(function(){return Q6(B6,e)},[e])},Ld=function(e){return typeof e=="function"?e:typeof e=="string"?e.indexOf("time:")===0?Id(e.slice("5")):$d(e):function(t){return""+t}},Wd=function(e){return T.useMemo(function(){return Ld(e)},[e])},jd=T.createContext(),a7={},qd=function(e){var t=e.theme,n=t===void 0?a7:t,r=e.children,i=i7(n);return $.jsx(jd.Provider,{value:i,children:r})};qd.propTypes={children:x.node.isRequired,theme:x.object};var pe=function(){return T.useContext(jd)},o7=["outlineWidth","outlineColor","outlineOpacity"],zd=function(e){return e.outlineWidth,e.outlineColor,e.outlineOpacity,gs(e,o7)},Hd=function(e){var t=e.children,n=e.condition,r=e.wrapper;return n?T.cloneElement(r,{},t):t};Hd.propTypes={children:x.node.isRequired,condition:x.bool.isRequired,wrapper:x.element.isRequired};var s7={position:"relative"},Vd=function(e){var t=e.children,n=e.theme,r=e.renderWrapper,i=r===void 0||r,a=e.isInteractive,o=a===void 0||a,s=e.animate,u=e.motionConfig,f=T.useRef(null);return $.jsx(qd,{theme:n,children:$.jsx(Ed,{animate:s,config:u,children:$.jsx(y_,{container:f,children:$.jsxs(Hd,{condition:i,wrapper:$.jsx("div",{style:s7,ref:f}),children:[t,o&&$.jsx(p_,{})]})})})})};Vd.propTypes={children:x.element.isRequired,isInteractive:x.bool,renderWrapper:x.bool,theme:x.object,animate:x.bool,motionConfig:x.oneOfType([x.string,Yr.motionConfig])};x.func.isRequired,x.bool,x.bool,x.object.isRequired,x.bool.isRequired,x.oneOfType([x.string,Yr.motionConfig]);var Bd=function(e){var t=e.children,n=Yd(),r=n[0],i=n[1],a=i.width>0&&i.height>0;return $.jsx("div",{ref:r,style:{width:"100%",height:"100%"},children:a&&t({width:i.width,height:i.height})})};Bd.propTypes={children:x.func.isRequired};var u7=["id","colors"],Gd=function(e){var t=e.id,n=e.colors,r=gs(e,u7);return $.jsx("linearGradient",dt({id:t,x1:0,x2:0,y1:0,y2:1},r,{children:n.map(function(i){var a=i.offset,o=i.color,s=i.opacity;return $.jsx("stop",{offset:a+"%",stopColor:o,stopOpacity:s!==void 0?s:1},a)})}))};Gd.propTypes={id:x.string.isRequired,colors:x.arrayOf(x.shape({offset:x.number.isRequired,color:x.string.isRequired,opacity:x.number})).isRequired,gradientTransform:x.string};var f7={linearGradient:Gd},pn={color:"#000000",background:"#ffffff",size:4,padding:4,stagger:!1},Ka=T.memo(function(e){var t=e.id,n=e.background,r=n===void 0?pn.background:n,i=e.color,a=i===void 0?pn.color:i,o=e.size,s=o===void 0?pn.size:o,u=e.padding,f=u===void 0?pn.padding:u,l=e.stagger,c=l===void 0?pn.stagger:l,d=s+f,g=s/2,b=f/2;return c===!0&&(d=2*s+2*f),$.jsxs("pattern",{id:t,width:d,height:d,patternUnits:"userSpaceOnUse",children:[$.jsx("rect",{width:d,height:d,fill:r}),$.jsx("circle",{cx:b+g,cy:b+g,r:g,fill:a}),c&&$.jsx("circle",{cx:1.5*f+s+g,cy:1.5*f+s+g,r:g,fill:a})]})});Ka.displayName="PatternDots",Ka.propTypes={id:x.string.isRequired,color:x.string.isRequired,background:x.string.isRequired,size:x.number.isRequired,padding:x.number.isRequired,stagger:x.bool.isRequired};var eo=function(e){return e*Math.PI/180},c7=function(e){return 180*e/Math.PI},l7=function(e,t){return{x:Math.cos(e)*t,y:Math.sin(e)*t}},d7=function(e){var t=e%360;return t<0&&(t+=360),t},h7={svg:{align:{left:"start",center:"middle",right:"end",start:"start",middle:"middle",end:"end"},baseline:{top:"text-before-edge",center:"central",bottom:"alphabetic"}},canvas:{align:{left:"left",center:"center",right:"right",start:"left",middle:"center",end:"right"},baseline:{top:"top",center:"middle",bottom:"bottom"}}},yn={spacing:5,rotation:0,background:"#000000",color:"#ffffff",lineWidth:2},to=T.memo(function(e){var t=e.id,n=e.spacing,r=n===void 0?yn.spacing:n,i=e.rotation,a=i===void 0?yn.rotation:i,o=e.background,s=o===void 0?yn.background:o,u=e.color,f=u===void 0?yn.color:u,l=e.lineWidth,c=l===void 0?yn.lineWidth:l,d=Math.round(a)%360,g=Math.abs(r);d>180?d-=360:d>90?d-=180:d<-180?d+=360:d<-90&&(d+=180);var b,p=g,y=g;return d===0?b=`
                M 0 0 L `+p+` 0
                M 0 `+y+" L "+p+" "+y+`
            `:d===90?b=`
                M 0 0 L 0 `+y+`
                M `+p+" 0 L "+p+" "+y+`
            `:(p=Math.abs(g/Math.sin(eo(d))),y=g/Math.sin(eo(90-d)),b=d>0?`
                    M 0 `+-y+" L "+2*p+" "+y+`
                    M `+-p+" "+-y+" L "+p+" "+y+`
                    M `+-p+" 0 L "+p+" "+2*y+`
                `:`
                    M `+-p+" "+y+" L "+p+" "+-y+`
                    M `+-p+" "+2*y+" L "+2*p+" "+-y+`
                    M 0 `+2*y+" L "+2*p+` 0
                `),$.jsxs("pattern",{id:t,width:p,height:y,patternUnits:"userSpaceOnUse",children:[$.jsx("rect",{width:p,height:y,fill:s,stroke:"rgba(255, 0, 0, 0.1)",strokeWidth:0}),$.jsx("path",{d:b,strokeWidth:c,stroke:f,strokeLinecap:"square"})]})});to.displayName="PatternLines",to.propTypes={id:x.string.isRequired,spacing:x.number.isRequired,rotation:x.number.isRequired,background:x.string.isRequired,color:x.string.isRequired,lineWidth:x.number.isRequired};var vn={color:"#000000",background:"#ffffff",size:4,padding:4,stagger:!1},no=T.memo(function(e){var t=e.id,n=e.color,r=n===void 0?vn.color:n,i=e.background,a=i===void 0?vn.background:i,o=e.size,s=o===void 0?vn.size:o,u=e.padding,f=u===void 0?vn.padding:u,l=e.stagger,c=l===void 0?vn.stagger:l,d=s+f,g=f/2;return c===!0&&(d=2*s+2*f),$.jsxs("pattern",{id:t,width:d,height:d,patternUnits:"userSpaceOnUse",children:[$.jsx("rect",{width:d,height:d,fill:a}),$.jsx("rect",{x:g,y:g,width:s,height:s,fill:r}),c&&$.jsx("rect",{x:1.5*f+s,y:1.5*f+s,width:s,height:s,fill:r})]})});no.displayName="PatternSquares",no.propTypes={id:x.string.isRequired,color:x.string.isRequired,background:x.string.isRequired,size:x.number.isRequired,padding:x.number.isRequired,stagger:x.bool.isRequired};var g7={patternDots:Ka,patternLines:to,patternSquares:no},m7=["type"],ro=dt({},f7,g7),Xd=function(e){var t=e.defs;return!t||t.length<1?null:$.jsx("defs",{"aria-hidden":!0,children:t.map(function(n){var r=n.type,i=gs(n,m7);return ro[r]?T.createElement(ro[r],dt({key:i.id},i)):null})})};Xd.propTypes={defs:x.arrayOf(x.shape({type:x.oneOf(Object.keys(ro)).isRequired,id:x.string.isRequired}))};var b7=T.memo(Xd),Qd=function(e){var t=e.width,n=e.height,r=e.margin,i=e.defs,a=e.children,o=e.role,s=e.ariaLabel,u=e.ariaLabelledBy,f=e.ariaDescribedBy,l=e.isFocusable,c=pe();return $.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:t,height:n,role:o,"aria-label":s,"aria-labelledby":u,"aria-describedby":f,focusable:l,tabIndex:l?0:void 0,children:[$.jsx(b7,{defs:i}),$.jsx("rect",{width:t,height:n,fill:c.background}),$.jsx("g",{transform:"translate("+r.left+","+r.top+")",children:a})]})};Qd.propTypes={width:x.number.isRequired,height:x.number.isRequired,margin:x.shape({top:x.number.isRequired,left:x.number.isRequired}).isRequired,defs:x.array,children:x.oneOfType([x.arrayOf(x.node),x.node]).isRequired,role:x.string,isFocusable:x.bool,ariaLabel:x.string,ariaLabelledBy:x.string,ariaDescribedBy:x.string};var Zd=function(e){var t=e.size,n=e.color,r=e.borderWidth,i=e.borderColor;return $.jsx("circle",{r:t/2,fill:n,stroke:i,strokeWidth:r,style:{pointerEvents:"none"}})};Zd.propTypes={size:x.number.isRequired,color:x.string.isRequired,borderWidth:x.number.isRequired,borderColor:x.string.isRequired};var p7=T.memo(Zd),Jd=function(e){var t=e.x,n=e.y,r=e.symbol,i=r===void 0?p7:r,a=e.size,o=e.datum,s=e.color,u=e.borderWidth,f=e.borderColor,l=e.label,c=e.labelTextAnchor,d=c===void 0?"middle":c,g=e.labelYOffset,b=g===void 0?-12:g,p=pe(),y=qe(),v=y.animate,O=y.config,_=rt({transform:"translate("+t+", "+n+")",config:O,immediate:!v});return $.jsxs(le.g,{transform:_.transform,style:{pointerEvents:"none"},children:[T.createElement(i,{size:a,color:s,datum:o,borderWidth:u,borderColor:f}),l&&$.jsx("text",{textAnchor:d,y:b,style:zd(p.dots.text),children:l})]})};Jd.propTypes={x:x.number.isRequired,y:x.number.isRequired,datum:x.object.isRequired,size:x.number.isRequired,color:x.string.isRequired,borderWidth:x.number.isRequired,borderColor:x.string.isRequired,symbol:x.oneOfType([x.func,x.object]),label:x.oneOfType([x.string,x.number]),labelTextAnchor:x.oneOf(["start","middle","end"]),labelYOffset:x.number};T.memo(Jd);var Kd=function(e){var t=e.width,n=e.height,r=e.axis,i=e.scale,a=e.value,o=e.lineStyle,s=e.textStyle,u=e.legend,f=e.legendNode,l=e.legendPosition,c=l===void 0?"top-right":l,d=e.legendOffsetX,g=d===void 0?14:d,b=e.legendOffsetY,p=b===void 0?14:b,y=e.legendOrientation,v=y===void 0?"horizontal":y,O=pe(),_=0,k=0,A=0,C=0;if(r==="y"?(A=i(a),k=t):(_=i(a),C=n),u&&!f){var F=function(Y){var E=Y.axis,L=Y.width,w=Y.height,q=Y.position,D=Y.offsetX,U=Y.offsetY,N=Y.orientation,W=0,j=0,z=N==="vertical"?-90:0,R="start";if(E==="x")switch(q){case"top-left":W=-D,j=U,R="end";break;case"top":j=-U,R=N==="horizontal"?"middle":"start";break;case"top-right":W=D,j=U,R=N==="horizontal"?"start":"end";break;case"right":W=D,j=w/2,R=N==="horizontal"?"start":"middle";break;case"bottom-right":W=D,j=w-U,R="start";break;case"bottom":j=w+U,R=N==="horizontal"?"middle":"end";break;case"bottom-left":j=w-U,W=-D,R=N==="horizontal"?"end":"start";break;case"left":W=-D,j=w/2,R=N==="horizontal"?"end":"middle"}else switch(q){case"top-left":W=D,j=-U,R="start";break;case"top":W=L/2,j=-U,R=N==="horizontal"?"middle":"start";break;case"top-right":W=L-D,j=-U,R=N==="horizontal"?"end":"start";break;case"right":W=L+D,R=N==="horizontal"?"start":"middle";break;case"bottom-right":W=L-D,j=U,R="end";break;case"bottom":W=L/2,j=U,R=N==="horizontal"?"middle":"end";break;case"bottom-left":W=D,j=U,R=N==="horizontal"?"start":"end";break;case"left":W=-D,R=N==="horizontal"?"end":"middle"}return{x:W,y:j,rotation:z,textAnchor:R}}({axis:r,width:t,height:n,position:c,offsetX:g,offsetY:p,orientation:v});f=$.jsx("text",{transform:"translate("+F.x+", "+F.y+") rotate("+F.rotation+")",textAnchor:F.textAnchor,dominantBaseline:"central",style:s,children:u})}return $.jsxs("g",{transform:"translate("+_+", "+A+")",children:[$.jsx("line",{x1:0,x2:k,y1:0,y2:C,stroke:O.markers.lineColor,strokeWidth:O.markers.lineStrokeWidth,style:o}),f]})};Kd.propTypes={width:x.number.isRequired,height:x.number.isRequired,axis:x.oneOf(["x","y"]).isRequired,scale:x.func.isRequired,value:x.oneOfType([x.number,x.string,x.instanceOf(Date)]).isRequired,lineStyle:x.object,textStyle:x.object,legend:x.string,legendPosition:x.oneOf(["top-left","top","top-right","right","bottom-right","bottom","bottom-left","left"]),legendOffsetX:x.number.isRequired,legendOffsetY:x.number.isRequired,legendOrientation:x.oneOf(["horizontal","vertical"]).isRequired};var y7=T.memo(Kd),e0=function(e){var t=e.markers,n=e.width,r=e.height,i=e.xScale,a=e.yScale;return t&&t.length!==0?t.map(function(o,s){return $.jsx(y7,dt({},o,{width:n,height:r,scale:o.axis==="y"?a:i}),s)}):null};e0.propTypes={width:x.number.isRequired,height:x.number.isRequired,xScale:x.func.isRequired,yScale:x.func.isRequired,markers:x.arrayOf(x.shape({axis:x.oneOf(["x","y"]).isRequired,value:x.oneOfType([x.number,x.string,x.instanceOf(Date)]).isRequired,lineStyle:x.object,textStyle:x.object}))};T.memo(e0);var v7=function(e){return _h(e)?e:function(t){return br(t,e)}},_7=function(e){return T.useMemo(function(){return v7(e)},[e])},xa=new Date,wa=new Date;function at(e,t,n,r){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=function(a){return e(a=new Date(+a)),a},i.ceil=function(a){return e(a=new Date(a-1)),t(a,1),e(a),a},i.round=function(a){var o=i(a),s=i.ceil(a);return a-o<s-a?o:s},i.offset=function(a,o){return t(a=new Date(+a),o==null?1:Math.floor(o)),a},i.range=function(a,o,s){var u=[],f;if(a=i.ceil(a),s=s==null?1:Math.floor(s),!(a<o)||!(s>0))return u;do u.push(f=new Date(+a)),t(a,s),e(a);while(f<a&&a<o);return u},i.filter=function(a){return at(function(o){if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},function(o,s){if(o>=o)if(s<0)for(;++s<=0;)for(;t(o,-1),!a(o););else for(;--s>=0;)for(;t(o,1),!a(o););})},n&&(i.count=function(a,o){return xa.setTime(+a),wa.setTime(+o),e(xa),e(wa),Math.floor(n(xa,wa))},i.every=function(a){return a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(r?function(o){return r(o)%a===0}:function(o){return i.count(0,o)%a===0}):i}),i}const x7=1e3,bs=x7*60,w7=bs*60,ps=w7*24,t0=ps*7;var n0=at(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*bs)/ps,e=>e.getDate()-1);const r0=n0;n0.range;function At(e){return at(function(t){t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},function(t,n){t.setDate(t.getDate()+n*7)},function(t,n){return(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*bs)/t0})}var i0=At(0),Lr=At(1),M7=At(2),$7=At(3),Qt=At(4),T7=At(5),C7=At(6);i0.range;Lr.range;M7.range;$7.range;Qt.range;T7.range;C7.range;var ys=at(function(e){e.setMonth(0,1),e.setHours(0,0,0,0)},function(e,t){e.setFullYear(e.getFullYear()+t)},function(e,t){return t.getFullYear()-e.getFullYear()},function(e){return e.getFullYear()});ys.every=function(e){return!isFinite(e=Math.floor(e))||!(e>0)?null:at(function(t){t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},function(t,n){t.setFullYear(t.getFullYear()+n*e)})};const Hn=ys;ys.range;var a0=at(function(e){e.setUTCHours(0,0,0,0)},function(e,t){e.setUTCDate(e.getUTCDate()+t)},function(e,t){return(t-e)/ps},function(e){return e.getUTCDate()-1});const o0=a0;a0.range;function Ut(e){return at(function(t){t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCDate(t.getUTCDate()+n*7)},function(t,n){return(n-t)/t0})}var s0=Ut(0),Wr=Ut(1),k7=Ut(2),S7=Ut(3),Zt=Ut(4),O7=Ut(5),A7=Ut(6);s0.range;Wr.range;k7.range;S7.range;Zt.range;O7.range;A7.range;var vs=at(function(e){e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},function(e,t){e.setUTCFullYear(e.getUTCFullYear()+t)},function(e,t){return t.getUTCFullYear()-e.getUTCFullYear()},function(e){return e.getUTCFullYear()});vs.every=function(e){return!isFinite(e=Math.floor(e))||!(e>0)?null:at(function(t){t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCFullYear(t.getUTCFullYear()+n*e)})};const Vn=vs;vs.range;function Ma(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function $a(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function _n(e,t,n){return{y:e,m:t,d:n,H:0,M:0,S:0,L:0}}function U7(e){var t=e.dateTime,n=e.date,r=e.time,i=e.periods,a=e.days,o=e.shortDays,s=e.months,u=e.shortMonths,f=xn(i),l=wn(i),c=xn(a),d=wn(a),g=xn(o),b=wn(o),p=xn(s),y=wn(s),v=xn(u),O=wn(u),_={a:j,A:z,b:R,B:K,c:null,d:Nf,e:Nf,f:ex,g:cx,G:dx,H:Z7,I:J7,j:K7,L:u0,m:tx,M:nx,p:he,q:J,Q:Lf,s:Wf,S:rx,u:ix,U:ax,V:ox,w:sx,W:ux,x:null,X:null,y:fx,Y:lx,Z:hx,"%":Yf},k={a:ge,A:ee,b:ae,B:ce,c:null,d:Ef,e:Ef,f:px,g:kx,G:Ox,H:gx,I:mx,j:bx,L:c0,m:yx,M:vx,p:oe,q:me,Q:Lf,s:Wf,S:_x,u:xx,U:wx,V:Mx,w:$x,W:Tx,x:null,X:null,y:Cx,Y:Sx,Z:Ax,"%":Yf},A={a:L,A:w,b:q,B:D,c:U,d:Rf,e:Rf,f:B7,g:Ff,G:Df,H:If,I:If,j:q7,L:V7,m:j7,M:z7,p:E,q:W7,Q:X7,s:Q7,S:H7,u:I7,U:N7,V:E7,w:R7,W:Y7,x:N,X:W,y:Ff,Y:Df,Z:L7,"%":G7};_.x=C(n,_),_.X=C(r,_),_.c=C(t,_),k.x=C(n,k),k.X=C(r,k),k.c=C(t,k);function C(m,M){return function(S){var h=[],V=-1,I=0,B=m.length,X,fe,Ie;for(S instanceof Date||(S=new Date(+S));++V<B;)m.charCodeAt(V)===37&&(h.push(m.slice(I,V)),(fe=Pf[X=m.charAt(++V)])!=null?X=m.charAt(++V):fe=X==="e"?" ":"0",(Ie=M[X])&&(X=Ie(S,fe)),h.push(X),I=V+1);return h.push(m.slice(I,V)),h.join("")}}function F(m,M){return function(S){var h=_n(1900,void 0,1),V=Y(h,m,S+="",0),I,B;if(V!=S.length)return null;if("Q"in h)return new Date(h.Q);if("s"in h)return new Date(h.s*1e3+("L"in h?h.L:0));if(M&&!("Z"in h)&&(h.Z=0),"p"in h&&(h.H=h.H%12+h.p*12),h.m===void 0&&(h.m="q"in h?h.q:0),"V"in h){if(h.V<1||h.V>53)return null;"w"in h||(h.w=1),"Z"in h?(I=$a(_n(h.y,0,1)),B=I.getUTCDay(),I=B>4||B===0?Wr.ceil(I):Wr(I),I=o0.offset(I,(h.V-1)*7),h.y=I.getUTCFullYear(),h.m=I.getUTCMonth(),h.d=I.getUTCDate()+(h.w+6)%7):(I=Ma(_n(h.y,0,1)),B=I.getDay(),I=B>4||B===0?Lr.ceil(I):Lr(I),I=r0.offset(I,(h.V-1)*7),h.y=I.getFullYear(),h.m=I.getMonth(),h.d=I.getDate()+(h.w+6)%7)}else("W"in h||"U"in h)&&("w"in h||(h.w="u"in h?h.u%7:"W"in h?1:0),B="Z"in h?$a(_n(h.y,0,1)).getUTCDay():Ma(_n(h.y,0,1)).getDay(),h.m=0,h.d="W"in h?(h.w+6)%7+h.W*7-(B+5)%7:h.w+h.U*7-(B+6)%7);return"Z"in h?(h.H+=h.Z/100|0,h.M+=h.Z%100,$a(h)):Ma(h)}}function Y(m,M,S,h){for(var V=0,I=M.length,B=S.length,X,fe;V<I;){if(h>=B)return-1;if(X=M.charCodeAt(V++),X===37){if(X=M.charAt(V++),fe=A[X in Pf?M.charAt(V++):X],!fe||(h=fe(m,S,h))<0)return-1}else if(X!=S.charCodeAt(h++))return-1}return h}function E(m,M,S){var h=f.exec(M.slice(S));return h?(m.p=l.get(h[0].toLowerCase()),S+h[0].length):-1}function L(m,M,S){var h=g.exec(M.slice(S));return h?(m.w=b.get(h[0].toLowerCase()),S+h[0].length):-1}function w(m,M,S){var h=c.exec(M.slice(S));return h?(m.w=d.get(h[0].toLowerCase()),S+h[0].length):-1}function q(m,M,S){var h=v.exec(M.slice(S));return h?(m.m=O.get(h[0].toLowerCase()),S+h[0].length):-1}function D(m,M,S){var h=p.exec(M.slice(S));return h?(m.m=y.get(h[0].toLowerCase()),S+h[0].length):-1}function U(m,M,S){return Y(m,t,M,S)}function N(m,M,S){return Y(m,n,M,S)}function W(m,M,S){return Y(m,r,M,S)}function j(m){return o[m.getDay()]}function z(m){return a[m.getDay()]}function R(m){return u[m.getMonth()]}function K(m){return s[m.getMonth()]}function he(m){return i[+(m.getHours()>=12)]}function J(m){return 1+~~(m.getMonth()/3)}function ge(m){return o[m.getUTCDay()]}function ee(m){return a[m.getUTCDay()]}function ae(m){return u[m.getUTCMonth()]}function ce(m){return s[m.getUTCMonth()]}function oe(m){return i[+(m.getUTCHours()>=12)]}function me(m){return 1+~~(m.getUTCMonth()/3)}return{format:function(m){var M=C(m+="",_);return M.toString=function(){return m},M},parse:function(m){var M=F(m+="",!1);return M.toString=function(){return m},M},utcFormat:function(m){var M=C(m+="",k);return M.toString=function(){return m},M},utcParse:function(m){var M=F(m+="",!0);return M.toString=function(){return m},M}}}var Pf={"-":"",_:" ",0:"0"},xe=/^\s*\d+/,P7=/^%/,D7=/[\\^$*+?|[\]().{}]/g;function ie(e,t,n){var r=e<0?"-":"",i=(r?-e:e)+"",a=i.length;return r+(a<n?new Array(n-a+1).join(t)+i:i)}function F7(e){return e.replace(D7,"\\$&")}function xn(e){return new RegExp("^(?:"+e.map(F7).join("|")+")","i")}function wn(e){return new Map(e.map((t,n)=>[t.toLowerCase(),n]))}function R7(e,t,n){var r=xe.exec(t.slice(n,n+1));return r?(e.w=+r[0],n+r[0].length):-1}function I7(e,t,n){var r=xe.exec(t.slice(n,n+1));return r?(e.u=+r[0],n+r[0].length):-1}function N7(e,t,n){var r=xe.exec(t.slice(n,n+2));return r?(e.U=+r[0],n+r[0].length):-1}function E7(e,t,n){var r=xe.exec(t.slice(n,n+2));return r?(e.V=+r[0],n+r[0].length):-1}function Y7(e,t,n){var r=xe.exec(t.slice(n,n+2));return r?(e.W=+r[0],n+r[0].length):-1}function Df(e,t,n){var r=xe.exec(t.slice(n,n+4));return r?(e.y=+r[0],n+r[0].length):-1}function Ff(e,t,n){var r=xe.exec(t.slice(n,n+2));return r?(e.y=+r[0]+(+r[0]>68?1900:2e3),n+r[0].length):-1}function L7(e,t,n){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(n,n+6));return r?(e.Z=r[1]?0:-(r[2]+(r[3]||"00")),n+r[0].length):-1}function W7(e,t,n){var r=xe.exec(t.slice(n,n+1));return r?(e.q=r[0]*3-3,n+r[0].length):-1}function j7(e,t,n){var r=xe.exec(t.slice(n,n+2));return r?(e.m=r[0]-1,n+r[0].length):-1}function Rf(e,t,n){var r=xe.exec(t.slice(n,n+2));return r?(e.d=+r[0],n+r[0].length):-1}function q7(e,t,n){var r=xe.exec(t.slice(n,n+3));return r?(e.m=0,e.d=+r[0],n+r[0].length):-1}function If(e,t,n){var r=xe.exec(t.slice(n,n+2));return r?(e.H=+r[0],n+r[0].length):-1}function z7(e,t,n){var r=xe.exec(t.slice(n,n+2));return r?(e.M=+r[0],n+r[0].length):-1}function H7(e,t,n){var r=xe.exec(t.slice(n,n+2));return r?(e.S=+r[0],n+r[0].length):-1}function V7(e,t,n){var r=xe.exec(t.slice(n,n+3));return r?(e.L=+r[0],n+r[0].length):-1}function B7(e,t,n){var r=xe.exec(t.slice(n,n+6));return r?(e.L=Math.floor(r[0]/1e3),n+r[0].length):-1}function G7(e,t,n){var r=P7.exec(t.slice(n,n+1));return r?n+r[0].length:-1}function X7(e,t,n){var r=xe.exec(t.slice(n));return r?(e.Q=+r[0],n+r[0].length):-1}function Q7(e,t,n){var r=xe.exec(t.slice(n));return r?(e.s=+r[0],n+r[0].length):-1}function Nf(e,t){return ie(e.getDate(),t,2)}function Z7(e,t){return ie(e.getHours(),t,2)}function J7(e,t){return ie(e.getHours()%12||12,t,2)}function K7(e,t){return ie(1+r0.count(Hn(e),e),t,3)}function u0(e,t){return ie(e.getMilliseconds(),t,3)}function ex(e,t){return u0(e,t)+"000"}function tx(e,t){return ie(e.getMonth()+1,t,2)}function nx(e,t){return ie(e.getMinutes(),t,2)}function rx(e,t){return ie(e.getSeconds(),t,2)}function ix(e){var t=e.getDay();return t===0?7:t}function ax(e,t){return ie(i0.count(Hn(e)-1,e),t,2)}function f0(e){var t=e.getDay();return t>=4||t===0?Qt(e):Qt.ceil(e)}function ox(e,t){return e=f0(e),ie(Qt.count(Hn(e),e)+(Hn(e).getDay()===4),t,2)}function sx(e){return e.getDay()}function ux(e,t){return ie(Lr.count(Hn(e)-1,e),t,2)}function fx(e,t){return ie(e.getFullYear()%100,t,2)}function cx(e,t){return e=f0(e),ie(e.getFullYear()%100,t,2)}function lx(e,t){return ie(e.getFullYear()%1e4,t,4)}function dx(e,t){var n=e.getDay();return e=n>=4||n===0?Qt(e):Qt.ceil(e),ie(e.getFullYear()%1e4,t,4)}function hx(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+ie(t/60|0,"0",2)+ie(t%60,"0",2)}function Ef(e,t){return ie(e.getUTCDate(),t,2)}function gx(e,t){return ie(e.getUTCHours(),t,2)}function mx(e,t){return ie(e.getUTCHours()%12||12,t,2)}function bx(e,t){return ie(1+o0.count(Vn(e),e),t,3)}function c0(e,t){return ie(e.getUTCMilliseconds(),t,3)}function px(e,t){return c0(e,t)+"000"}function yx(e,t){return ie(e.getUTCMonth()+1,t,2)}function vx(e,t){return ie(e.getUTCMinutes(),t,2)}function _x(e,t){return ie(e.getUTCSeconds(),t,2)}function xx(e){var t=e.getUTCDay();return t===0?7:t}function wx(e,t){return ie(s0.count(Vn(e)-1,e),t,2)}function l0(e){var t=e.getUTCDay();return t>=4||t===0?Zt(e):Zt.ceil(e)}function Mx(e,t){return e=l0(e),ie(Zt.count(Vn(e),e)+(Vn(e).getUTCDay()===4),t,2)}function $x(e){return e.getUTCDay()}function Tx(e,t){return ie(Wr.count(Vn(e)-1,e),t,2)}function Cx(e,t){return ie(e.getUTCFullYear()%100,t,2)}function kx(e,t){return e=l0(e),ie(e.getUTCFullYear()%100,t,2)}function Sx(e,t){return ie(e.getUTCFullYear()%1e4,t,4)}function Ox(e,t){var n=e.getUTCDay();return e=n>=4||n===0?Zt(e):Zt.ceil(e),ie(e.getUTCFullYear()%1e4,t,4)}function Ax(){return"+0000"}function Yf(){return"%"}function Lf(e){return+e}function Wf(e){return Math.floor(+e/1e3)}var Nt,d0;Ux({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});function Ux(e){return Nt=U7(e),d0=Nt.format,Nt.parse,Nt.utcFormat,Nt.utcParse,Nt}function Px(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)}function jr(e,t){if((n=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var n,r=e.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+e.slice(n+1)]}function Dx(e){return e=jr(Math.abs(e)),e?e[1]:NaN}function Fx(e,t){return function(n,r){for(var i=n.length,a=[],o=0,s=e[0],u=0;i>0&&s>0&&(u+s+1>r&&(s=Math.max(1,r-u)),a.push(n.substring(i-=s,i+s)),!((u+=s+1)>r));)s=e[o=(o+1)%e.length];return a.reverse().join(t)}}function Rx(e){return function(t){return t.replace(/[0-9]/g,function(n){return e[+n]})}}var Ix=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function io(e){if(!(t=Ix.exec(e)))throw new Error("invalid format: "+e);var t;return new _s({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}io.prototype=_s.prototype;function _s(e){this.fill=e.fill===void 0?" ":e.fill+"",this.align=e.align===void 0?">":e.align+"",this.sign=e.sign===void 0?"-":e.sign+"",this.symbol=e.symbol===void 0?"":e.symbol+"",this.zero=!!e.zero,this.width=e.width===void 0?void 0:+e.width,this.comma=!!e.comma,this.precision=e.precision===void 0?void 0:+e.precision,this.trim=!!e.trim,this.type=e.type===void 0?"":e.type+""}_s.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(this.width===void 0?"":Math.max(1,this.width|0))+(this.comma?",":"")+(this.precision===void 0?"":"."+Math.max(0,this.precision|0))+(this.trim?"~":"")+this.type};function Nx(e){e:for(var t=e.length,n=1,r=-1,i;n<t;++n)switch(e[n]){case".":r=i=n;break;case"0":r===0&&(r=n),i=n;break;default:if(!+e[n])break e;r>0&&(r=0);break}return r>0?e.slice(0,r)+e.slice(i+1):e}var h0;function Ex(e,t){var n=jr(e,t);if(!n)return e+"";var r=n[0],i=n[1],a=i-(h0=Math.max(-8,Math.min(8,Math.floor(i/3)))*3)+1,o=r.length;return a===o?r:a>o?r+new Array(a-o+1).join("0"):a>0?r.slice(0,a)+"."+r.slice(a):"0."+new Array(1-a).join("0")+jr(e,Math.max(0,t+a-1))[0]}function jf(e,t){var n=jr(e,t);if(!n)return e+"";var r=n[0],i=n[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")}const qf={"%":function(e,t){return(e*100).toFixed(t)},b:function(e){return Math.round(e).toString(2)},c:function(e){return e+""},d:Px,e:function(e,t){return e.toExponential(t)},f:function(e,t){return e.toFixed(t)},g:function(e,t){return e.toPrecision(t)},o:function(e){return Math.round(e).toString(8)},p:function(e,t){return jf(e*100,t)},r:jf,s:Ex,X:function(e){return Math.round(e).toString(16).toUpperCase()},x:function(e){return Math.round(e).toString(16)}};function zf(e){return e}var Hf=Array.prototype.map,Vf=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];function Yx(e){var t=e.grouping===void 0||e.thousands===void 0?zf:Fx(Hf.call(e.grouping,Number),e.thousands+""),n=e.currency===void 0?"":e.currency[0]+"",r=e.currency===void 0?"":e.currency[1]+"",i=e.decimal===void 0?".":e.decimal+"",a=e.numerals===void 0?zf:Rx(Hf.call(e.numerals,String)),o=e.percent===void 0?"%":e.percent+"",s=e.minus===void 0?"-":e.minus+"",u=e.nan===void 0?"NaN":e.nan+"";function f(c){c=io(c);var d=c.fill,g=c.align,b=c.sign,p=c.symbol,y=c.zero,v=c.width,O=c.comma,_=c.precision,k=c.trim,A=c.type;A==="n"?(O=!0,A="g"):qf[A]||(_===void 0&&(_=12),k=!0,A="g"),(y||d==="0"&&g==="=")&&(y=!0,d="0",g="=");var C=p==="$"?n:p==="#"&&/[boxX]/.test(A)?"0"+A.toLowerCase():"",F=p==="$"?r:/[%p]/.test(A)?o:"",Y=qf[A],E=/[defgprs%]/.test(A);_=_===void 0?6:/[gprs]/.test(A)?Math.max(1,Math.min(21,_)):Math.max(0,Math.min(20,_));function L(w){var q=C,D=F,U,N,W;if(A==="c")D=Y(w)+D,w="";else{w=+w;var j=w<0||1/w<0;if(w=isNaN(w)?u:Y(Math.abs(w),_),k&&(w=Nx(w)),j&&+w==0&&b!=="+"&&(j=!1),q=(j?b==="("?b:s:b==="-"||b==="("?"":b)+q,D=(A==="s"?Vf[8+h0/3]:"")+D+(j&&b==="("?")":""),E){for(U=-1,N=w.length;++U<N;)if(W=w.charCodeAt(U),48>W||W>57){D=(W===46?i+w.slice(U+1):w.slice(U))+D,w=w.slice(0,U);break}}}O&&!y&&(w=t(w,1/0));var z=q.length+w.length+D.length,R=z<v?new Array(v-z+1).join(d):"";switch(O&&y&&(w=t(R+w,R.length?v-D.length:1/0),R=""),g){case"<":w=q+w+D+R;break;case"=":w=q+R+w+D;break;case"^":w=R.slice(0,z=R.length>>1)+q+w+D+R.slice(z);break;default:w=R+q+w+D;break}return a(w)}return L.toString=function(){return c+""},L}function l(c,d){var g=f((c=io(c),c.type="f",c)),b=Math.max(-8,Math.min(8,Math.floor(Dx(d)/3)))*3,p=Math.pow(10,-b),y=Vf[8+b/3];return function(v){return g(p*v)+y}}return{format:f,formatPrefix:l}}var hr,g0;Lx({decimal:".",thousands:",",grouping:[3],currency:["$",""],minus:"-"});function Lx(e){return hr=Yx(e),g0=hr.format,hr.formatPrefix,hr}var Bf=xh;Bf&&Bf.isDate;var Ta=new Date,Ca=new Date;function ye(e,t,n,r){function i(a){return e(a=arguments.length===0?new Date:new Date(+a)),a}return i.floor=function(a){return e(a=new Date(+a)),a},i.ceil=function(a){return e(a=new Date(a-1)),t(a,1),e(a),a},i.round=function(a){var o=i(a),s=i.ceil(a);return a-o<s-a?o:s},i.offset=function(a,o){return t(a=new Date(+a),o==null?1:Math.floor(o)),a},i.range=function(a,o,s){var u=[],f;if(a=i.ceil(a),s=s==null?1:Math.floor(s),!(a<o)||!(s>0))return u;do u.push(f=new Date(+a)),t(a,s),e(a);while(f<a&&a<o);return u},i.filter=function(a){return ye(function(o){if(o>=o)for(;e(o),!a(o);)o.setTime(o-1)},function(o,s){if(o>=o)if(s<0)for(;++s<=0;)for(;t(o,-1),!a(o););else for(;--s>=0;)for(;t(o,1),!a(o););})},n&&(i.count=function(a,o){return Ta.setTime(+a),Ca.setTime(+o),e(Ta),e(Ca),Math.floor(n(Ta,Ca))},i.every=function(a){return a=Math.floor(a),!isFinite(a)||!(a>0)?null:a>1?i.filter(r?function(o){return r(o)%a===0}:function(o){return i.count(0,o)%a===0}):i}),i}var qr=ye(function(){},function(e,t){e.setTime(+e+t)},function(e,t){return t-e});qr.every=function(e){return e=Math.floor(e),!isFinite(e)||!(e>0)?null:e>1?ye(function(t){t.setTime(Math.floor(t/e)*e)},function(t,n){t.setTime(+t+n*e)},function(t,n){return(n-t)/e}):qr};const Gf=qr;qr.range;var zr=1e3,Jt=6e4,Hr=36e5,m0=6048e5,b0=ye(function(e){e.setTime(e-e.getMilliseconds())},function(e,t){e.setTime(+e+t*zr)},function(e,t){return(t-e)/zr},function(e){return e.getUTCSeconds()});const Xf=b0;b0.range;var p0=ye(function(e){e.setTime(e-e.getMilliseconds()-e.getSeconds()*zr)},function(e,t){e.setTime(+e+t*Jt)},function(e,t){return(t-e)/Jt},function(e){return e.getMinutes()});const Wx=p0;p0.range;var y0=ye(function(e){e.setTime(e-e.getMilliseconds()-e.getSeconds()*zr-e.getMinutes()*Jt)},function(e,t){e.setTime(+e+t*Hr)},function(e,t){return(t-e)/Hr},function(e){return e.getHours()});const jx=y0;y0.range;function Pt(e){return ye(function(t){t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},function(t,n){t.setDate(t.getDate()+n*7)},function(t,n){return(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*Jt)/m0})}var ao=Pt(0),v0=Pt(1),_0=Pt(2),x0=Pt(3),w0=Pt(4),M0=Pt(5),$0=Pt(6);ao.range;v0.range;_0.range;x0.range;w0.range;M0.range;$0.range;var T0=ye(function(e){e.setDate(1),e.setHours(0,0,0,0)},function(e,t){e.setMonth(e.getMonth()+t)},function(e,t){return t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12},function(e){return e.getMonth()});const qx=T0;T0.range;var xs=ye(function(e){e.setMonth(0,1),e.setHours(0,0,0,0)},function(e,t){e.setFullYear(e.getFullYear()+t)},function(e,t){return t.getFullYear()-e.getFullYear()},function(e){return e.getFullYear()});xs.every=function(e){return!isFinite(e=Math.floor(e))||!(e>0)?null:ye(function(t){t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},function(t,n){t.setFullYear(t.getFullYear()+n*e)})};const zx=xs;xs.range;var C0=ye(function(e){e.setUTCSeconds(0,0)},function(e,t){e.setTime(+e+t*Jt)},function(e,t){return(t-e)/Jt},function(e){return e.getUTCMinutes()});const Hx=C0;C0.range;var k0=ye(function(e){e.setUTCMinutes(0,0,0)},function(e,t){e.setTime(+e+t*Hr)},function(e,t){return(t-e)/Hr},function(e){return e.getUTCHours()});const Vx=k0;k0.range;function Dt(e){return ye(function(t){t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCDate(t.getUTCDate()+n*7)},function(t,n){return(n-t)/m0})}var oo=Dt(0),S0=Dt(1),O0=Dt(2),A0=Dt(3),U0=Dt(4),P0=Dt(5),D0=Dt(6);oo.range;S0.range;O0.range;A0.range;U0.range;P0.range;D0.range;var F0=ye(function(e){e.setUTCDate(1),e.setUTCHours(0,0,0,0)},function(e,t){e.setUTCMonth(e.getUTCMonth()+t)},function(e,t){return t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12},function(e){return e.getUTCMonth()});const Bx=F0;F0.range;var ws=ye(function(e){e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},function(e,t){e.setUTCFullYear(e.getUTCFullYear()+t)},function(e,t){return t.getUTCFullYear()-e.getUTCFullYear()},function(e){return e.getUTCFullYear()});ws.every=function(e){return!isFinite(e=Math.floor(e))||!(e>0)?null:ye(function(t){t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCFullYear(t.getUTCFullYear()+n*e)})};const Gx=ws;ws.range;var Qf=function(e){var t=e;return t.type="band",t},R0=function(e){var t=e.bandwidth();if(t===0)return e;var n=t/2;return e.round()&&(n=Math.round(n)),function(r){var i;return((i=e(r))!=null?i:0)+n}},I0={millisecond:[Gf,Gf],second:[Xf,Xf],minute:[Wx,Hx],hour:[jx,Vx],day:[ye(function(e){return e.setHours(0,0,0,0)},function(e,t){return e.setDate(e.getDate()+t)},function(e,t){return(t.getTime()-e.getTime())/864e5},function(e){return Math.floor(e.getTime()/864e5)}),ye(function(e){return e.setUTCHours(0,0,0,0)},function(e,t){return e.setUTCDate(e.getUTCDate()+t)},function(e,t){return(t.getTime()-e.getTime())/864e5},function(e){return Math.floor(e.getTime()/864e5)})],week:[ao,oo],sunday:[ao,oo],monday:[v0,S0],tuesday:[_0,O0],wednesday:[x0,A0],thursday:[w0,U0],friday:[M0,P0],saturday:[$0,D0],month:[qx,Bx],year:[zx,Gx]},Xx=Object.keys(I0),Qx=new RegExp("^every\\s*(\\d+)?\\s*("+Xx.join("|")+")s?$","i"),N0=function(e,t){if(Array.isArray(t))return t;if(typeof t=="string"&&"useUTC"in e){var n=t.match(Qx);if(n){var r=n[1],i=n[2],a=I0[i][e.useUTC?1:0];if(i==="day"){var o,s,u=e.domain(),f=u[0],l=u[1],c=new Date(l);return c.setDate(c.getDate()+1),(o=(s=a.every(Number(r??1)))==null?void 0:s.range(f,c))!=null?o:[]}if(r===void 0)return e.ticks(a);var d=a.every(Number(r));if(d)return e.ticks(d)}throw new Error("Invalid tickValues: "+t)}if("ticks"in e){if(t===void 0)return e.ticks();if(typeof(g=t)=="number"&&isFinite(g)&&Math.floor(g)===g)return e.ticks(t)}var g;return e.domain()};function Le(){return Le=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Le.apply(this,arguments)}var Zx=function(e){var t,n=e.axis,r=e.scale,i=e.ticksPosition,a=e.tickValues,o=e.tickSize,s=e.tickPadding,u=e.tickRotation,f=e.truncateTickAt,l=e.engine,c=l===void 0?"svg":l,d=N0(r,a),g=h7[c],b="bandwidth"in r?R0(r):r,p={lineX:0,lineY:0},y={textX:0,textY:0},v=typeof document=="object"&&document.dir==="rtl",O=g.align.center,_=g.baseline.center;return n==="x"?(t=function(k){var A;return{x:(A=b(k))!=null?A:0,y:0}},p.lineY=o*(i==="after"?1:-1),y.textY=(o+s)*(i==="after"?1:-1),_=i==="after"?g.baseline.top:g.baseline.bottom,u===0?O=g.align.center:i==="after"&&u<0||i==="before"&&u>0?(O=g.align[v?"left":"right"],_=g.baseline.center):(i==="after"&&u>0||i==="before"&&u<0)&&(O=g.align[v?"right":"left"],_=g.baseline.center)):(t=function(k){var A;return{x:0,y:(A=b(k))!=null?A:0}},p.lineX=o*(i==="after"?1:-1),y.textX=(o+s)*(i==="after"?1:-1),O=i==="after"?g.align.left:g.align.right),{ticks:d.map(function(k){var A=typeof k=="string"?function(C){var F=String(C).length;return f&&f>0&&F>f?""+String(C).slice(0,f).concat("..."):""+C}(k):k;return Le({key:k instanceof Date?""+k.valueOf():""+k,value:A},t(k),p,y)}),textAlign:O,textBaseline:_}},Jx=function(e,t){if(e===void 0||typeof e=="function")return e;if(t.type==="time"){var n=d0(e);return function(r){return n(r instanceof Date?r:new Date(r))}}return g0(e)},Zf=function(e){var t,n=e.width,r=e.height,i=e.scale,a=e.axis,o=e.values,s=(t=o,(Array.isArray(t)?o:void 0)||N0(i,o)),u="bandwidth"in i?R0(i):i,f=a==="x"?s.map(function(l){var c,d;return{key:l instanceof Date?""+l.valueOf():""+l,x1:(c=u(l))!=null?c:0,x2:(d=u(l))!=null?d:0,y1:0,y2:r}}):s.map(function(l){var c,d;return{key:l instanceof Date?""+l.valueOf():""+l,x1:0,x2:n,y1:(c=u(l))!=null?c:0,y2:(d=u(l))!=null?d:0}});return f},Kx=T.memo(function(e){var t,n=e.value,r=e.format,i=e.lineX,a=e.lineY,o=e.onClick,s=e.textBaseline,u=e.textAnchor,f=e.animatedProps,l=pe(),c=l.axis.ticks.line,d=l.axis.ticks.text,g=(t=r==null?void 0:r(n))!=null?t:n,b=T.useMemo(function(){var p={opacity:f.opacity};return o?{style:Le({},p,{cursor:"pointer"}),onClick:function(y){return o(y,g)}}:{style:p}},[f.opacity,o,g]);return $.jsxs(le.g,Le({transform:f.transform},b,{children:[$.jsx("line",{x1:0,x2:i,y1:0,y2:a,style:c}),d.outlineWidth>0&&$.jsx(le.text,{dominantBaseline:s,textAnchor:u,transform:f.textTransform,style:d,strokeWidth:2*d.outlineWidth,stroke:d.outlineColor,strokeLinejoin:"round",children:""+g}),$.jsx(le.text,{dominantBaseline:s,textAnchor:u,transform:f.textTransform,style:zd(d),children:""+g})]}))}),e4=function(e){var t=e.axis,n=e.scale,r=e.x,i=r===void 0?0:r,a=e.y,o=a===void 0?0:a,s=e.length,u=e.ticksPosition,f=e.tickValues,l=e.tickSize,c=l===void 0?5:l,d=e.tickPadding,g=d===void 0?5:d,b=e.tickRotation,p=b===void 0?0:b,y=e.format,v=e.renderTick,O=v===void 0?Kx:v,_=e.truncateTickAt,k=e.legend,A=e.legendPosition,C=A===void 0?"end":A,F=e.legendOffset,Y=F===void 0?0:F,E=e.onClick,L=e.ariaHidden,w=pe(),q=w.axis.legend.text,D=T.useMemo(function(){return Jx(y,n)},[y,n]),U=Zx({axis:t,scale:n,ticksPosition:u,tickValues:f,tickSize:c,tickPadding:g,tickRotation:p,truncateTickAt:_}),N=U.ticks,W=U.textAlign,j=U.textBaseline,z=null;if(k!==void 0){var R,K=0,he=0,J=0;t==="y"?(J=-90,K=Y,C==="start"?(R="start",he=s):C==="middle"?(R="middle",he=s/2):C==="end"&&(R="end")):(he=Y,C==="start"?R="start":C==="middle"?(R="middle",K=s/2):C==="end"&&(R="end",K=s)),z=$.jsxs($.Fragment,{children:[q.outlineWidth>0&&$.jsx("text",{transform:"translate("+K+", "+he+") rotate("+J+")",textAnchor:R,style:Le({dominantBaseline:"central"},q),strokeWidth:2*q.outlineWidth,stroke:q.outlineColor,strokeLinejoin:"round",children:k}),$.jsx("text",{transform:"translate("+K+", "+he+") rotate("+J+")",textAnchor:R,style:Le({dominantBaseline:"central"},q),children:k})]})}var ge=qe(),ee=ge.animate,ae=ge.config,ce=rt({transform:"translate("+i+","+o+")",lineX2:t==="x"?s:0,lineY2:t==="x"?0:s,config:ae,immediate:!ee}),oe=T.useCallback(function(M){return{opacity:1,transform:"translate("+M.x+","+M.y+")",textTransform:"translate("+M.textX+","+M.textY+") rotate("+p+")"}},[p]),me=T.useCallback(function(M){return{opacity:0,transform:"translate("+M.x+","+M.y+")",textTransform:"translate("+M.textX+","+M.textY+") rotate("+p+")"}},[p]),m=ss(N,{keys:function(M){return M.key},initial:oe,from:me,enter:oe,update:oe,leave:{opacity:0},config:ae,immediate:!ee});return $.jsxs(le.g,{transform:ce.transform,"aria-hidden":L,children:[m(function(M,S,h,V){return T.createElement(O,Le({tickIndex:V,format:D,rotate:p,textBaseline:j,textAnchor:W,truncateTickAt:_,animatedProps:M},S,E?{onClick:E}:{}))}),$.jsx(le.line,{style:w.axis.domain.line,x1:0,x2:ce.lineX2,y1:0,y2:ce.lineY2}),z]})},t4=T.memo(e4),n4=["top","right","bottom","left"],r4=T.memo(function(e){var t=e.xScale,n=e.yScale,r=e.width,i=e.height,a={top:e.top,right:e.right,bottom:e.bottom,left:e.left};return $.jsx($.Fragment,{children:n4.map(function(o){var s=a[o];if(!s)return null;var u=o==="top"||o==="bottom";return $.jsx(t4,Le({},s,{axis:u?"x":"y",x:o==="right"?r:0,y:o==="bottom"?i:0,scale:u?t:n,length:u?r:i,ticksPosition:o==="top"||o==="left"?"before":"after",truncateTickAt:s.truncateTickAt}),o)})})}),i4=T.memo(function(e){var t=e.animatedProps,n=pe();return $.jsx(le.line,Le({},t,n.grid.line))}),Jf=T.memo(function(e){var t=e.lines,n=qe(),r=n.animate,i=n.config,a=ss(t,{keys:function(o){return o.key},initial:function(o){return{opacity:1,x1:o.x1,x2:o.x2,y1:o.y1,y2:o.y2}},from:function(o){return{opacity:0,x1:o.x1,x2:o.x2,y1:o.y1,y2:o.y2}},enter:function(o){return{opacity:1,x1:o.x1,x2:o.x2,y1:o.y1,y2:o.y2}},update:function(o){return{opacity:1,x1:o.x1,x2:o.x2,y1:o.y1,y2:o.y2}},leave:{opacity:0},config:i,immediate:!r});return $.jsx("g",{children:a(function(o,s){return T.createElement(i4,Le({},s,{key:s.key,animatedProps:o}))})})}),a4=T.memo(function(e){var t=e.width,n=e.height,r=e.xScale,i=e.yScale,a=e.xValues,o=e.yValues,s=T.useMemo(function(){return!!r&&Zf({width:t,height:n,scale:r,axis:"x",values:a})},[r,a,t,n]),u=T.useMemo(function(){return!!i&&Zf({width:t,height:n,scale:i,axis:"y",values:o})},[n,t,i,o]);return $.jsxs($.Fragment,{children:[s&&$.jsx(Jf,{lines:s}),u&&$.jsx(Jf,{lines:u})]})});function Vr(){return Vr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Vr.apply(this,arguments)}function Kf(e,t){(t==null||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o4(e,t){var n=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(i,a){if(i){if(typeof i=="string")return Kf(i,a);var o=Object.prototype.toString.call(i).slice(8,-1);return o==="Object"&&i.constructor&&(o=i.constructor.name),o==="Map"||o==="Set"?Array.from(i):o==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?Kf(i,a):void 0}}(e))||t&&e&&typeof e.length=="number"){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var s4={nivo:["#e8c1a0","#f47560","#f1e15b","#e8a838","#61cdbb","#97e3d5"],category10:gd,accent:md,dark2:bd,paired:pd,pastel1:yd,pastel2:vd,set1:_d,set2:xd,set3:us,tableau10:m5},u4={brown_blueGreen:xi,purpleRed_green:wi,pink_yellowGreen:Mi,purple_orange:$i,red_blue:Ti,red_grey:Ci,red_yellow_blue:ki,red_yellow_green:Si,spectral:Oi},f4={brown_blueGreen:b5,purpleRed_green:p5,pink_yellowGreen:y5,purple_orange:v5,red_blue:_5,red_grey:x5,red_yellow_blue:w5,red_yellow_green:M5,spectral:$5},c4={blues:ji,greens:qi,greys:zi,oranges:Bi,purples:Hi,reds:Vi,blue_green:Ai,blue_purple:Ui,green_blue:Pi,orange_red:Di,purple_blue_green:Fi,purple_blue:Ri,purple_red:Ii,red_purple:Ni,yellow_green_blue:Ei,yellow_green:Yi,yellow_orange_brown:Li,yellow_orange_red:Wi},l4={blues:N5,greens:E5,greys:Y5,oranges:j5,purples:L5,reds:W5,turbo:Z5,viridis:J5,inferno:e8,magma:K5,plasma:t8,cividis:q5,warm:H5,cool:V5,cubehelixDefault:z5,blue_green:T5,blue_purple:C5,green_blue:k5,orange_red:S5,purple_blue_green:O5,purple_blue:A5,purple_red:U5,red_purple:P5,yellow_green_blue:D5,yellow_green:F5,yellow_orange_brown:R5,yellow_orange_red:I5};Vr({},s4,u4,c4);var d4={rainbow:B5,sinebow:Q5},Ms=Vr({},f4,l4,d4),h4=function(e,t){if(typeof e=="function")return e;if(cv(e)){if(function(u){return u.theme!==void 0}(e)){if(t===void 0)throw new Error("Unable to use color from theme as no theme was provided");var n=br(t,e.theme);if(n===void 0)throw new Error("Color from theme is undefined at path: '"+e.theme+"'");return function(){return n}}if(function(u){return u.from!==void 0}(e)){var r=function(u){return br(u,e.from)};if(Array.isArray(e.modifiers)){for(var i,a=[],o=function(){var u=i.value,f=u[0],l=u[1];if(f==="brighter")a.push(function(c){return c.brighter(l)});else if(f==="darker")a.push(function(c){return c.darker(l)});else{if(f!=="opacity")throw new Error("Invalid color modifier: '"+f+"', must be one of: 'brighter', 'darker', 'opacity'");a.push(function(c){return c.opacity=l,c})}},s=o4(e.modifiers);!(i=s()).done;)o();return a.length===0?r:function(u){return a.reduce(function(f,l){return l(f)},qt(r(u))).toString()}}return r}throw new Error("Invalid color spec, you should either specify 'theme' or 'from' when using a config object")}return function(){return e}},ec=function(e,t){return T.useMemo(function(){return h4(e,t)},[e,t])};x.oneOfType([x.string,x.func,x.shape({theme:x.string.isRequired}),x.shape({from:x.string.isRequired,modifiers:x.arrayOf(x.array)})]);var g4={scheme:"turbo"},m4=function(e,t){var n=e.minValue,r=e.maxValue,i=n!==void 0?n:t.min,a=r!==void 0?r:t.max,o=vl().domain([i,a]).clamp(!0);if("colors"in e)o.range(e.colors);else if("interpolator"in e)o.interpolator(e.interpolator);else{var s,u=(s=e.scheme)!=null?s:g4.scheme;o.interpolator(Ms[u])}return o},tc={scheme:"red_yellow_blue",divergeAt:.5},b4=function(e,t){var n,r=e.minValue,i=e.maxValue,a=r!==void 0?r:t.min,o=i!==void 0?i:t.max,s=[a,a+(o-a)/2,o],u=.5-((n=e.divergeAt)!=null?n:tc.divergeAt),f=Ia().domain(s).clamp(!0),l=function(g){return String(g)};if("colors"in e)l=Ia().domain(s.map(function(g){return g-u*(o-a)})).range(e.colors).interpolator();else if("interpolator"in e)l=e.interpolator;else{var c,d=(c=e.scheme)!=null?c:tc.scheme;l=Ms[d]}return f.interpolator(function(g){return l(g+u)})},nc={scheme:"turbo",steps:7},p4=function(e,t){var n=cl().domain(e.domain||[t.min,t.max]).nice();if("colors"in e)n.range(e.colors);else{var r=e.scheme||nc.scheme,i=e.steps===void 0?nc.steps:e.steps,a=Ms[r],o=Array.from({length:i}).map(function(s,u){return a(u*(1/(i-1)))});n.range(o)}return n},y4=function(e,t){if(function(n){return n.type==="sequential"}(e))return m4(e,t);if(function(n){return n.type==="diverging"}(e))return b4(e,t);if(function(n){return n.type==="quantize"}(e))return p4(e,t);throw new Error("Invalid continuous color scale config")},v4=function(e,t){t===void 0&&(t=16);var n=e.domain();if("thresholds"in e){var r=[],i=si().domain(n).range([0,1]);return e.range().forEach(function(o,s){var u=e.invertExtent(o),f=u[0],l=u[1];r.push({key:s+".0",offset:i(f),stopColor:o}),r.push({key:s+".1",offset:i(l),stopColor:o})}),r}var a=e.copy();return n.length===2?a.domain([0,1]):n.length===3&&a.domain([0,.5,1]),a.ticks(t).map(function(o){return{key:""+o,offset:o,stopColor:""+a(o)}})};function Br(){return Br=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Br.apply(this,arguments)}var de={length:200,thickness:16,direction:"row",tickPosition:"after",tickSize:4,tickSpacing:3,tickOverlap:!1,tickFormat:function(e){return""+e},titleAlign:"start",titleOffset:4},_4=function(e){var t=e.anchor,n=e.translateX,r=e.translateY,i=e.containerWidth,a=e.containerHeight,o=e.width,s=e.height,u=n,f=r;switch(t){case"top":u+=(i-o)/2;break;case"top-right":u+=i-o;break;case"right":u+=i-o,f+=(a-s)/2;break;case"bottom-right":u+=i-o,f+=a-s;break;case"bottom":u+=(i-o)/2,f+=a-s;break;case"bottom-left":f+=a-s;break;case"left":f+=(a-s)/2;break;case"center":u+=(i-o)/2,f+=(a-s)/2}return{x:u,y:f}},x4=function(e){var t,n=e.scale,r=e.ticks,i=e.length,a=i===void 0?de.length:i,o=e.thickness,s=o===void 0?de.thickness:o,u=e.direction,f=u===void 0?de.direction:u,l=e.tickPosition,c=l===void 0?de.tickPosition:l,d=e.tickSize,g=d===void 0?de.tickSize:d,b=e.tickSpacing,p=b===void 0?de.tickSpacing:b,y=e.tickOverlap,v=y===void 0?de.tickOverlap:y,O=e.tickFormat,_=O===void 0?de.tickFormat:O,k=e.title,A=e.titleAlign,C=A===void 0?de.titleAlign:A,F=e.titleOffset,Y=F===void 0?de.titleOffset:F,E=f==="column"?[].concat(n.domain()).reverse():n.domain(),L=si().domain(E);E.length===2?L.range([0,a]):E.length===3&&L.range([0,a/2,a]),t="thresholds"in n?[E[0]].concat(n.thresholds(),[E[1]]):Array.isArray(r)?r:n.ticks(r);var w,q,D,U,N,W,j=v4(n,32),z=Ld(_),R=[],K=0,he=0;if(f==="row"){var J,ge,ee;w=a,q=s,he=1;var ae;N=0,D=C==="start"?0:C==="middle"?a/2:a,c==="before"?(J=-g,ge=v?s:0,ee=-g-p,ae="alphabetic",U=s+Y,W="hanging"):(J=v?0:s,ee=(ge=s+g)+p,ae="hanging",U=-Y,W="alphabetic"),t.forEach(function(M){var S=L(M);R.push({x1:S,y1:J,x2:S,y2:ge,text:z(M),textX:S,textY:ee,textHorizontalAlign:"middle",textVerticalAlign:ae})})}else{var ce,oe,me,m;w=s,q=a,K=1,N=-90,U=C==="start"?a:C==="middle"?a/2:0,c==="before"?(oe=v?s:0,me=(ce=-g)-p,m="end",D=s+Y,W="hanging"):(ce=v?0:s,me=(oe=s+g)+p,m="start",D=-Y,W="alphabetic"),t.forEach(function(M){var S=L(M);R.push({x1:ce,y1:S,x2:oe,y2:S,text:z(M),textX:me,textY:S,textHorizontalAlign:m,textVerticalAlign:"central"})})}return{width:w,height:q,gradientX1:0,gradientY1:K,gradientX2:he,gradientY2:0,colorStops:j,ticks:R,titleText:k,titleX:D,titleY:U,titleRotation:N,titleHorizontalAlign:C,titleVerticalAlign:W}},w4=function(e){var t=e.scale,n=e.ticks,r=e.length,i=r===void 0?de.length:r,a=e.thickness,o=a===void 0?de.thickness:a,s=e.direction,u=s===void 0?de.direction:s,f=e.tickPosition,l=f===void 0?de.tickPosition:f,c=e.tickSize,d=c===void 0?de.tickSize:c,g=e.tickSpacing,b=g===void 0?de.tickSpacing:g,p=e.tickOverlap,y=p===void 0?de.tickOverlap:p,v=e.tickFormat,O=v===void 0?de.tickFormat:v,_=e.title,k=e.titleAlign,A=k===void 0?de.titleAlign:k,C=e.titleOffset,F=x4({scale:t,ticks:n,length:i,thickness:o,direction:u,tickPosition:l,tickSize:d,tickSpacing:b,tickOverlap:y,tickFormat:O,title:_,titleAlign:A,titleOffset:C===void 0?de.titleOffset:C}),Y=F.width,E=F.height,L=F.gradientX1,w=F.gradientY1,q=F.gradientX2,D=F.gradientY2,U=F.ticks,N=F.colorStops,W=F.titleText,j=F.titleX,z=F.titleY,R=F.titleRotation,K=F.titleVerticalAlign,he=F.titleHorizontalAlign,J=pe(),ge="ContinuousColorsLegendSvgGradient."+u+"."+N.map(function(ee){return ee.offset}).join("_");return $.jsxs("g",{children:[$.jsx("defs",{children:$.jsx("linearGradient",{id:ge,x1:L,y1:w,x2:q,y2:D,children:N.map(function(ee){return $.jsx("stop",Br({},ee))})})}),W&&$.jsx("text",{transform:"translate("+j+", "+z+") rotate("+R+")",textAnchor:he,dominantBaseline:K,style:J.legends.title.text,children:W}),$.jsx("rect",{width:Y,height:E,fill:"url(#"+ge}),U.map(function(ee,ae){return $.jsxs(T.Fragment,{children:[$.jsx("line",{x1:ee.x1,y1:ee.y1,x2:ee.x2,y2:ee.y2,style:J.legends.ticks.line}),$.jsx("text",{x:ee.textX,y:ee.textY,textAnchor:ee.textHorizontalAlign,dominantBaseline:ee.textVerticalAlign,style:J.legends.ticks.text,children:ee.text})]},ae)})]})},M4=["containerWidth","containerHeight","anchor","translateX","translateY","length","thickness","direction"],$4=function(e){var t,n,r=e.containerWidth,i=e.containerHeight,a=e.anchor,o=e.translateX,s=o===void 0?0:o,u=e.translateY,f=u===void 0?0:u,l=e.length,c=l===void 0?de.length:l,d=e.thickness,g=d===void 0?de.thickness:d,b=e.direction,p=b===void 0?de.direction:b,y=function(k,A){if(k==null)return{};var C,F,Y={},E=Object.keys(k);for(F=0;F<E.length;F++)C=E[F],A.indexOf(C)>=0||(Y[C]=k[C]);return Y}(e,M4);p==="row"?(t=c,n=g):(t=g,n=c);var v=_4({anchor:a,translateX:s,translateY:f,containerWidth:r,containerHeight:i,width:t,height:n}),O=v.x,_=v.y;return $.jsx("g",{transform:"translate("+O+", "+_+")",children:$.jsx(w4,Br({length:c,thickness:g,direction:p},y))})},T4=Vc;function C4(e,t){var n=[];return T4(e,function(r,i,a){t(r,i,a)&&n.push(r)}),n}var k4=C4,S4=wh,O4=k4,A4=xo,U4=Xe;function P4(e,t){var n=U4(e)?S4:O4;return n(e,A4(t))}var D4=P4;const F4=Oe(D4);function ht(){return ht=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ht.apply(this,arguments)}var so={dotSize:4,noteWidth:120,noteTextOffset:8,animate:!0},R4=function(e){var t=typeof e;return T.isValidElement(e)||t==="string"||t==="function"||t==="object"},$s=function(e){return e.type==="circle"},I4=function(e){return e.type==="dot"},Ts=function(e){return e.type==="rect"},N4=function(e){var t=e.data,n=e.annotations,r=e.getPosition,i=e.getDimensions;return n.reduce(function(a,o){var s=o.offset||0;return[].concat(a,F4(t,o.match).map(function(u){var f=r(u),l=i(u);return($s(o)||Ts(o))&&(l.size=l.size+2*s,l.width=l.width+2*s,l.height=l.height+2*s),ht({},Tl(o,["match","offset"]),f,l,{size:o.size||l.size,datum:u})}))},[])},E4=function(e,t,n,r){var i=Math.atan2(r-t,n-e);return d7(c7(i))},Y4=function(e){var t,n,r=e.x,i=e.y,a=e.noteX,o=e.noteY,s=e.noteWidth,u=s===void 0?so.noteWidth:s,f=e.noteTextOffset,l=f===void 0?so.noteTextOffset:f;if(js(a))t=r+a;else{if(a.abs===void 0)throw new Error("noteX should be either a number or an object containing an 'abs' property");t=a.abs}if(js(o))n=i+o;else{if(o.abs===void 0)throw new Error("noteY should be either a number or an object containing an 'abs' property");n=o.abs}var c=r,d=i,g=E4(r,i,t,n);if($s(e)){var b=l7(eo(g),e.size/2);c+=b.x,d+=b.y}if(Ts(e)){var p=Math.round((g+90)/45)%8;p===0&&(d-=e.height/2),p===1&&(c+=e.width/2,d-=e.height/2),p===2&&(c+=e.width/2),p===3&&(c+=e.width/2,d+=e.height/2),p===4&&(d+=e.height/2),p===5&&(c-=e.width/2,d+=e.height/2),p===6&&(c-=e.width/2),p===7&&(c-=e.width/2,d-=e.height/2)}var y=t,v=t;return(g+90)%360>180?(y-=u,v-=u):v+=u,{points:[[c,d],[t,n],[v,n]],text:[y,n-l],angle:g+90}},L4=function(e){var t=e.data,n=e.annotations,r=e.getPosition,i=e.getDimensions;return T.useMemo(function(){return N4({data:t,annotations:n,getPosition:r,getDimensions:i})},[t,n,r,i])},W4=function(e){return T.useMemo(function(){return Y4(e)},[e])},j4=function(e){var t=e.datum,n=e.x,r=e.y,i=e.note,a=pe(),o=qe(),s=o.animate,u=o.config,f=rt({x:n,y:r,config:u,immediate:!s});return typeof i=="function"?T.createElement(i,{x:n,y:r,datum:t}):$.jsxs($.Fragment,{children:[a.annotations.text.outlineWidth>0&&$.jsx(le.text,{x:f.x,y:f.y,style:ht({},a.annotations.text,{strokeLinejoin:"round",strokeWidth:2*a.annotations.text.outlineWidth,stroke:a.annotations.text.outlineColor}),children:i}),$.jsx(le.text,{x:f.x,y:f.y,style:Tl(a.annotations.text,["outlineWidth","outlineColor"]),children:i})]})},rc=function(e){var t=e.points,n=e.isOutline,r=n!==void 0&&n,i=pe(),a=T.useMemo(function(){var u=t[0];return t.slice(1).reduce(function(f,l){return f+" L"+l[0]+","+l[1]},"M"+u[0]+","+u[1])},[t]),o=Z6(a);if(r&&i.annotations.link.outlineWidth<=0)return null;var s=ht({},i.annotations.link);return r&&(s.strokeLinecap="square",s.strokeWidth=i.annotations.link.strokeWidth+2*i.annotations.link.outlineWidth,s.stroke=i.annotations.link.outlineColor,s.opacity=i.annotations.link.outlineOpacity),$.jsx(le.path,{fill:"none",d:o,style:s})},q4=function(e){var t=e.x,n=e.y,r=e.size,i=pe(),a=qe(),o=a.animate,s=a.config,u=rt({x:t,y:n,radius:r/2,config:s,immediate:!o});return $.jsxs($.Fragment,{children:[i.annotations.outline.outlineWidth>0&&$.jsx(le.circle,{cx:u.x,cy:u.y,r:u.radius,style:ht({},i.annotations.outline,{fill:"none",strokeWidth:i.annotations.outline.strokeWidth+2*i.annotations.outline.outlineWidth,stroke:i.annotations.outline.outlineColor,opacity:i.annotations.outline.outlineOpacity})}),$.jsx(le.circle,{cx:u.x,cy:u.y,r:u.radius,style:i.annotations.outline})]})},z4=function(e){var t=e.x,n=e.y,r=e.size,i=r===void 0?so.dotSize:r,a=pe(),o=qe(),s=o.animate,u=o.config,f=rt({x:t,y:n,radius:i/2,config:u,immediate:!s});return $.jsxs($.Fragment,{children:[a.annotations.outline.outlineWidth>0&&$.jsx(le.circle,{cx:f.x,cy:f.y,r:f.radius,style:ht({},a.annotations.outline,{fill:"none",strokeWidth:2*a.annotations.outline.outlineWidth,stroke:a.annotations.outline.outlineColor,opacity:a.annotations.outline.outlineOpacity})}),$.jsx(le.circle,{cx:f.x,cy:f.y,r:f.radius,style:a.annotations.symbol})]})},H4=function(e){var t=e.x,n=e.y,r=e.width,i=e.height,a=e.borderRadius,o=a===void 0?6:a,s=pe(),u=qe(),f=u.animate,l=u.config,c=rt({x:t-r/2,y:n-i/2,width:r,height:i,config:l,immediate:!f});return $.jsxs($.Fragment,{children:[s.annotations.outline.outlineWidth>0&&$.jsx(le.rect,{x:c.x,y:c.y,rx:o,ry:o,width:c.width,height:c.height,style:ht({},s.annotations.outline,{fill:"none",strokeWidth:s.annotations.outline.strokeWidth+2*s.annotations.outline.outlineWidth,stroke:s.annotations.outline.outlineColor,opacity:s.annotations.outline.outlineOpacity})}),$.jsx(le.rect,{x:c.x,y:c.y,rx:o,ry:o,width:c.width,height:c.height,style:s.annotations.outline})]})},V4=function(e){var t=e.datum,n=e.x,r=e.y,i=e.note,a=W4(e);if(!R4(i))throw new Error("note should be a valid react element");return $.jsxs($.Fragment,{children:[$.jsx(rc,{points:a.points,isOutline:!0}),$s(e)&&$.jsx(q4,{x:n,y:r,size:e.size}),I4(e)&&$.jsx(z4,{x:n,y:r,size:e.size}),Ts(e)&&$.jsx(H4,{x:n,y:r,width:e.width,height:e.height,borderRadius:e.borderRadius}),$.jsx(rc,{points:a.points}),$.jsx(j4,{datum:t,x:a.text[0],y:a.text[1],note:i})]})};function Ce(){return Ce=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ce.apply(this,arguments)}function B4(e,t){if(e==null)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}var we={layers:["grid","axes","cells","legends","annotations"],forceSquare:!1,xInnerPadding:0,xOuterPadding:0,yInnerPadding:0,yOuterPadding:0,sizeVariation:!1,opacity:1,activeOpacity:1,inactiveOpacity:.15,borderWidth:0,borderColor:{from:"color",modifiers:[["darker",.8]]},enableGridX:!1,enableGridY:!1,enableLabels:!0,label:"formattedValue",labelTextColor:{from:"color",modifiers:[["darker",2]]},colors:{type:"sequential",scheme:"brown_blueGreen"},emptyColor:"#000000",legends:[],annotations:[],isInteractive:!0,hoverTarget:"rowColumn",tooltip:T.memo(function(e){var t=e.cell;return t.formattedValue===null?null:$.jsx(f_,{id:t.serieId+" - "+t.data.x,value:t.formattedValue,enableChip:!0,color:t.color})}),animate:!0,motionConfig:"gentle"},te=Ce({},we,{axisTop:{},axisRight:null,axisBottom:null,axisLeft:{},borderRadius:0,cellComponent:"rect"});Ce({},we,{axisTop:{},axisRight:null,axisBottom:null,axisLeft:{},renderCell:"rect",pixelRatio:typeof window<"u"&&window.devicePixelRatio||1});var G4=function(e){var t=e.width,n=e.height,r=e.rows,i=e.columns,a=t,o=n,s=0,u=0;if(e.forceSquare){var f=Math.max(t/i,0),l=Math.max(n/r,0),c=Math.min(f,l);s=(t-(a=c*i))/2,u=(n-(o=c*r))/2}return{offsetX:s,offsetY:u,width:a,height:o}},X4=function(e){var t=e.data,n=e.width,r=e.height,i=e.xInnerPadding,a=e.xOuterPadding,o=e.yInnerPadding,s=e.yOuterPadding,u=e.forceSquare,f=new Set,l=[],c=[],d=[];t.forEach(function(Y){l.push(Y.id),Y.data.forEach(function(E){f.add(E.x);var L=null;E.y!==void 0&&E.y!==null&&(c.push(E.y),L=E.y),d.push({id:Y.id+"."+E.x,serieId:Y.id,value:L,data:E})})});var g=Array.from(f),b=G4({width:n,height:r,columns:g.length,rows:l.length,forceSquare:u}),p=b.width,y=b.height,v=b.offsetX,O=b.offsetY,_=Qf(Mr().domain(g).range([0,p]).paddingOuter(a).paddingInner(i)),k=Qf(Mr().domain(l).range([0,y]).paddingOuter(s).paddingInner(o)),A=_.bandwidth(),C=k.bandwidth(),F=d.map(function(Y){return Ce({},Y,{x:_(Y.data.x)+A/2,y:k(Y.serieId)+C/2,width:A,height:C})});return{width:p,height:y,offsetX:v,offsetY:O,xScale:_,yScale:k,minValue:Math.min.apply(Math,c),maxValue:Math.max.apply(Math,c),cells:F}},Q4=function(e,t,n){if(!e)return function(){return 1};var r=si().domain(e.values?e.values:[t,n]).range(e.sizes);return function(i){return i===null?1:r(i)}},Z4=function(e){return{x:e.x,y:e.y}},J4=function(e){return{size:Math.max(e.width,e.height),width:e.width,height:e.height}},K4=function(e){var t=e.data,n=e.width,r=e.height,i=e.xInnerPadding,a=e.xOuterPadding,o=e.yInnerPadding,s=e.yOuterPadding,u=e.forceSquare;return T.useMemo(function(){return X4({data:t,width:n,height:r,xInnerPadding:i,xOuterPadding:a,yInnerPadding:o,yOuterPadding:s,forceSquare:u})},[t,n,r,i,a,o,s,u])},e9={cell:function(e,t){return e.id===t.id},row:function(e,t){return e.serieId===t.serieId},column:function(e,t){return e.data.x===t.data.x},rowColumn:function(e,t){return e.serieId===t.serieId||e.data.x===t.data.x}},t9=function(e){var t,n,r,i=e.cells,a=e.minValue,o=e.maxValue,s=e.sizeVariation,u=e.colors,f=e.emptyColor,l=e.opacity,c=e.activeOpacity,d=e.inactiveOpacity,g=e.borderColor,b=e.label,p=e.labelTextColor,y=e.valueFormat,v=e.activeIds,O=T.useMemo(function(){return Q4(t,n,r)},[t=s,n=a,r=o]),_=T.useMemo(function(){return typeof u=="function"?null:y4(u,{min:a,max:o})},[u,a,o]),k=T.useCallback(function(L){if(L.value!==null){if(typeof u=="function")return u(L);if(_!==null)return _(L.value)}return f},[u,_,f]),A=pe(),C=ec(g,A),F=ec(p,A),Y=Wd(y),E=_7(b);return{cells:T.useMemo(function(){return i.map(function(L){var w=l;v.length>0&&(w=v.includes(L.id)?c:d);var q=O(L.value),D=Ce({},L,{width:L.width*q,height:L.height*q,formattedValue:L.value!==null?Y(L.value):null,opacity:w});return D.label=E(D),D.color=k(D),D.borderColor=C(D),D.labelTextColor=F(D),D})},[i,O,k,C,F,Y,E,v,l,c,d]),colorScale:_}},n9=function(e){var t=e.data,n=e.valueFormat,r=e.width,i=e.height,a=e.xOuterPadding,o=a===void 0?we.xOuterPadding:a,s=e.xInnerPadding,u=s===void 0?we.xInnerPadding:s,f=e.yOuterPadding,l=f===void 0?we.yOuterPadding:f,c=e.yInnerPadding,d=c===void 0?we.yInnerPadding:c,g=e.forceSquare,b=g===void 0?we.forceSquare:g,p=e.sizeVariation,y=p===void 0?we.sizeVariation:p,v=e.colors,O=v===void 0?we.colors:v,_=e.emptyColor,k=_===void 0?we.emptyColor:_,A=e.opacity,C=A===void 0?we.opacity:A,F=e.activeOpacity,Y=F===void 0?we.activeOpacity:F,E=e.inactiveOpacity,L=E===void 0?we.inactiveOpacity:E,w=e.borderColor,q=w===void 0?we.borderColor:w,D=e.label,U=D===void 0?we.label:D,N=e.labelTextColor,W=N===void 0?we.labelTextColor:N,j=e.hoverTarget,z=j===void 0?we.hoverTarget:j,R=T.useState(null),K=R[0],he=R[1],J=K4({data:t,width:r,height:i,xOuterPadding:o,xInnerPadding:u,yOuterPadding:l,yInnerPadding:d,forceSquare:b}),ge=J.width,ee=J.height,ae=J.offsetX,ce=J.offsetY,oe=J.cells,me=J.xScale,m=J.yScale,M=J.minValue,S=J.maxValue,h=T.useMemo(function(){if(!K)return[];var I=e9[z];return oe.filter(function(B){return I(B,K)}).map(function(B){return B.id})},[oe,K,z]),V=t9({cells:oe,minValue:M,maxValue:S,sizeVariation:y,colors:O,emptyColor:k,opacity:C,activeOpacity:Y,inactiveOpacity:L,borderColor:q,label:U,labelTextColor:W,valueFormat:n,activeIds:h});return{width:ge,height:ee,offsetX:ae,offsetY:ce,cells:V.cells,xScale:me,yScale:m,colorScale:V.colorScale,activeCell:K,setActiveCell:he}},r9=function(e,t){return L4({data:e,annotations:t,getPosition:Z4,getDimensions:J4})},i9=T.memo(function(e){var t=e.cell,n=e.borderWidth,r=e.borderRadius,i=e.animatedProps,a=e.onMouseEnter,o=e.onMouseMove,s=e.onMouseLeave,u=e.onClick,f=e.enableLabels,l=pe(),c=T.useMemo(function(){return{onMouseEnter:a?a(t):void 0,onMouseMove:o?o(t):void 0,onMouseLeave:s?s(t):void 0,onClick:u?u(t):void 0}},[t,a,o,s,u]);return $.jsxs(le.g,Ce({"data-testid":"cell."+t.id,style:{cursor:"pointer"},opacity:i.opacity},c,{transform:jn([i.x,i.y,i.scale],function(d,g,b){return"translate("+d+", "+g+") scale("+b+")"}),children:[$.jsx(le.rect,{transform:jn([i.width,i.height],function(d,g){return"translate("+-.5*d+", "+-.5*g+")"}),fill:i.color,width:i.width,height:i.height,stroke:i.borderColor,strokeWidth:n,rx:r,ry:r},t.id),f&&$.jsx(le.text,{textAnchor:"middle",dominantBaseline:"central",fill:i.labelTextColor,style:Ce({},l.labels.text,{fill:void 0,userSelect:"none"}),children:t.label})]}))}),a9=T.memo(function(e){var t=e.cell,n=e.borderWidth,r=e.animatedProps,i=e.onMouseEnter,a=e.onMouseMove,o=e.onMouseLeave,s=e.onClick,u=e.enableLabels,f=pe(),l=T.useMemo(function(){return{onMouseEnter:i?i(t):void 0,onMouseMove:a?a(t):void 0,onMouseLeave:o?o(t):void 0,onClick:s?s(t):void 0}},[t,i,a,o,s]);return $.jsxs(le.g,Ce({"data-testid":"cell."+t.id,style:{cursor:"pointer"},opacity:r.opacity},l,{transform:jn([r.x,r.y],function(c,d){return"translate("+c+", "+d+")"}),children:[$.jsx(le.circle,{r:jn([r.width,r.height],function(c,d){return Math.min(c,d)/2}),fill:r.color,fillOpacity:r.opacity,strokeWidth:n,stroke:r.borderColor}),u&&$.jsx(le.text,{dominantBaseline:"central",textAnchor:"middle",fill:r.labelTextColor,style:Ce({},f.labels.text,{fill:void 0}),children:t.label})]}))}),o9=function(e){return{x:e.x,y:e.y,width:e.width,height:e.height,color:e.color,opacity:0,borderColor:e.borderColor,labelTextColor:e.labelTextColor,scale:0}},ka=function(e){return{x:e.x,y:e.y,width:e.width,height:e.height,color:e.color,opacity:e.opacity,borderColor:e.borderColor,labelTextColor:e.labelTextColor,scale:1}},s9=function(e){return{x:e.x,y:e.y,width:e.width,height:e.height,color:e.color,opacity:0,borderColor:e.borderColor,labelTextColor:e.labelTextColor,scale:0}},u9=function(e){var t,n=e.cells,r=e.cellComponent,i=e.borderRadius,a=e.borderWidth,o=e.isInteractive,s=e.setActiveCell,u=e.onMouseEnter,f=e.onMouseMove,l=e.onMouseLeave,c=e.onClick,d=e.tooltip,g=e.enableLabels,b=qe(),p=b.animate,y=b.config,v=ss(n,{keys:function(E){return E.id},initial:ka,from:o9,enter:ka,update:ka,leave:s9,config:y,immediate:!p}),O=g_(),_=O.showTooltipFromEvent,k=O.hideTooltip,A=T.useMemo(function(){if(o)return function(E){return function(L){_(T.createElement(d,{cell:E}),L),s(E),u==null||u(E,L)}}},[o,_,d,s,u]),C=T.useMemo(function(){if(o)return function(E){return function(L){_(T.createElement(d,{cell:E}),L),f==null||f(E,L)}}},[o,_,d,f]),F=T.useMemo(function(){if(o)return function(E){return function(L){k(),s(null),l==null||l(E,L)}}},[o,k,s,l]),Y=T.useMemo(function(){if(o)return function(E){return function(L){c==null||c(E,L)}}},[o,c]);return t=r==="rect"?i9:r==="circle"?a9:r,$.jsx($.Fragment,{children:v(function(E,L){return T.createElement(t,{cell:L,borderRadius:i,borderWidth:a,animatedProps:E,enableLabels:g,onMouseEnter:A,onMouseMove:C,onMouseLeave:F,onClick:Y})})})},f9=function(e){var t=e.cells,n=e.annotations,r=r9(t,n);return $.jsx($.Fragment,{children:r.map(function(i,a){return $.jsx(V4,Ce({},i),a)})})},c9=["isInteractive","animate","motionConfig","theme","renderWrapper"],l9=function(e){var t=e.data,n=e.layers,r=n===void 0?te.layers:n,i=e.valueFormat,a=e.width,o=e.height,s=e.margin,u=e.forceSquare,f=u===void 0?te.forceSquare:u,l=e.xInnerPadding,c=l===void 0?te.xInnerPadding:l,d=e.xOuterPadding,g=d===void 0?te.xOuterPadding:d,b=e.yInnerPadding,p=b===void 0?te.yInnerPadding:b,y=e.yOuterPadding,v=y===void 0?te.yOuterPadding:y,O=e.sizeVariation,_=O===void 0?te.sizeVariation:O,k=e.cellComponent,A=k===void 0?te.cellComponent:k,C=e.opacity,F=C===void 0?te.opacity:C,Y=e.activeOpacity,E=Y===void 0?te.activeOpacity:Y,L=e.inactiveOpacity,w=L===void 0?te.inactiveOpacity:L,q=e.borderRadius,D=q===void 0?te.borderRadius:q,U=e.borderWidth,N=U===void 0?te.borderWidth:U,W=e.borderColor,j=W===void 0?te.borderColor:W,z=e.enableGridX,R=z===void 0?te.enableGridX:z,K=e.enableGridY,he=K===void 0?te.enableGridY:K,J=e.axisTop,ge=J===void 0?te.axisTop:J,ee=e.axisRight,ae=ee===void 0?te.axisRight:ee,ce=e.axisBottom,oe=ce===void 0?te.axisBottom:ce,me=e.axisLeft,m=me===void 0?te.axisLeft:me,M=e.enableLabels,S=M===void 0?te.enableLabels:M,h=e.label,V=h===void 0?te.label:h,I=e.labelTextColor,B=I===void 0?te.labelTextColor:I,X=e.colors,fe=X===void 0?te.colors:X,Ie=e.emptyColor,E0=Ie===void 0?te.emptyColor:Ie,Cs=e.legends,Y0=Cs===void 0?te.legends:Cs,ks=e.annotations,Ss=ks===void 0?te.annotations:ks,Os=e.isInteractive,L0=Os===void 0?te.isInteractive:Os,W0=e.onMouseEnter,j0=e.onMouseMove,q0=e.onMouseLeave,z0=e.onClick,As=e.hoverTarget,H0=As===void 0?te.hoverTarget:As,Us=e.tooltip,V0=Us===void 0?te.tooltip:Us,B0=e.role,G0=e.ariaLabel,X0=e.ariaLabelledBy,Q0=e.ariaDescribedBy,an=r7(a,o,s),rr=an.margin,Z0=an.innerWidth,J0=an.innerHeight,K0=an.outerWidth,eh=an.outerHeight,ze=n9({data:t,valueFormat:i,width:Z0,height:J0,forceSquare:f,xInnerPadding:c,xOuterPadding:g,yInnerPadding:p,yOuterPadding:v,sizeVariation:_,colors:fe,emptyColor:E0,opacity:F,activeOpacity:E,inactiveOpacity:w,borderColor:j,label:V,labelTextColor:B,hoverTarget:H0}),Xi=ze.width,Qi=ze.height,Ps=ze.offsetX,Ds=ze.offsetY,Fs=ze.xScale,Rs=ze.yScale,Zi=ze.cells,Is=ze.colorScale,th=ze.activeCell,Ns=ze.setActiveCell,Ji=T.useMemo(function(){return Ce({},rr,{top:rr.top+Ds,left:rr.left+Ps})},[rr,Ps,Ds]),bt={grid:null,axes:null,cells:null,legends:null,annotations:null};r.includes("grid")&&(bt.grid=$.jsx(a4,{width:Xi,height:Qi,xScale:R?Fs:null,yScale:he?Rs:null},"grid")),r.includes("axes")&&(bt.axes=$.jsx(r4,{xScale:Fs,yScale:Rs,width:Xi,height:Qi,top:ge,right:ae,bottom:oe,left:m},"axes")),r.includes("cells")&&(bt.cells=$.jsx(T.Fragment,{children:$.jsx(u9,{cells:Zi,cellComponent:A,borderRadius:D,borderWidth:N,isInteractive:L0,setActiveCell:Ns,onMouseEnter:W0,onMouseMove:j0,onMouseLeave:q0,onClick:z0,tooltip:V0,enableLabels:S})},"cells")),r.includes("legends")&&Is!==null&&(bt.legends=$.jsx(T.Fragment,{children:Y0.map(function(on,Ki){return T.createElement($4,Ce({},on,{key:Ki,containerWidth:Xi,containerHeight:Qi,scale:Is}))})},"legends")),r.includes("annotations")&&Ss.length>0&&(bt.annotations=$.jsx(f9,{cells:Zi,annotations:Ss},"annotations"));var nh={cells:Zi,activeCell:th,setActiveCell:Ns};return $.jsx(Qd,{width:K0,height:eh,margin:Object.assign({},Ji,{top:Ji.top,left:Ji.left}),role:B0,ariaLabel:G0,ariaLabelledBy:X0,ariaDescribedBy:Q0,children:r.map(function(on,Ki){var Es;return typeof on=="function"?$.jsx(T.Fragment,{children:T.createElement(on,nh)},Ki):(Es=bt==null?void 0:bt[on])!=null?Es:null})})},d9=function(e){var t=e.isInteractive,n=t===void 0?te.isInteractive:t,r=e.animate,i=r===void 0?te.animate:r,a=e.motionConfig,o=a===void 0?te.motionConfig:a,s=e.theme,u=e.renderWrapper,f=B4(e,c9);return $.jsx(Vd,{animate:i,isInteractive:n,motionConfig:o,renderWrapper:u,theme:s,children:$.jsx(l9,Ce({isInteractive:n},f))})},L9=function(e){return $.jsx(Bd,{children:function(t){var n=t.width,r=t.height;return $.jsx(d9,Ce({width:n,height:r},e))}})};export{Gs as $,$o as A,F9 as B,fl as C,Pp as D,cl as E,Up as F,vl as G,Xy as H,_l as I,Zy as J,E9 as K,Qy as L,R9 as M,kp as N,C1 as O,Dp as P,I9 as Q,N9 as R,k9 as S,vp as T,ni as U,xo as V,Jr as W,yb as X,zc as Y,S9 as Z,D1 as _,Ih as a,b9 as a0,O9 as a1,Sn as a2,A9 as a3,U9 as a4,H as a5,y9 as a6,N1 as a7,bo as a8,H1 as a9,V1 as aa,z1 as ab,v9 as ac,_9 as ad,eg as ae,tg as af,ng as ag,rg as ah,ig as ai,og as aj,ag as ak,cv as al,Xc as am,Vc as an,ub as ao,bm as ap,p9 as aq,L9 as ar,U1 as b,x9 as c,w9 as d,js as e,M9 as f,$9 as g,T9 as h,Zr as i,$ as j,br as k,C9 as l,P9 as m,Mr as n,Ia as o,Jy as p,xl as q,D9 as r,E1 as s,o1 as t,Y9 as u,Ky as v,_p as w,au as x,si as y,Cp as z};
