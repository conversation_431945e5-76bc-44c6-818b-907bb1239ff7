import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as o}from"./vendor-851db8c1.js";import{p as ge,M as We,T as Ye,u as Je,q as Xe,A as Ze,r as Ve,v as et,x as fe,y as he,z as tt,C as pe,E as q,H as xe,J as st,K as ye}from"./index-08a5dc5b.js";import{u as rt}from"./react-hook-form-687afde5.js";const at=o.memo(({message:h,isCurrentUser:L})=>{if(!L)return null;const v=h.unread===0||h.unread===void 0;return t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsxs("div",{className:"relative flex items-center",children:[t.jsx(ge,{className:`h-3 w-3 transition-colors duration-200 ${v?"text-blue-500":"text-gray-400"}`,style:{transform:"translateX(3px)",filter:v?"drop-shadow(0 0 2px rgba(59, 130, 246, 0.3))":"none"}}),t.jsx(ge,{className:`h-3 w-3 transition-colors duration-200 ${v?"text-blue-500":"text-gray-400"}`,style:{transform:"translateX(-1px)",filter:v?"drop-shadow(0 0 2px rgba(59, 130, 246, 0.3))":"none"}})]}),v&&t.jsx("span",{className:"animate-pulse text-xs font-medium text-blue-500",children:"Seen"})]})}),ct=o.memo(()=>{const[h,L]=o.useState(!1),[v,ne]=o.useState(null),[le,H]=o.useState(null),[we,be]=o.useState([]),[je,ve]=o.useState([]),[Ne,M]=o.useState([]),[Se,F]=o.useState(!1),[z,te]=o.useState(""),[G,g]=o.useState([]),[K,Q]=o.useState(!1),[k,W]=o.useState(!0),[Ce,_e]=o.useState(!1),[Y,E]=o.useState(!1),[b,N]=o.useState(!1),[r,se]=o.useState(null),[I,re]=o.useState(!1),[i,P]=o.useState(null),[J,O]=o.useState(null),m=o.useMemo(()=>new We,[]),Te=o.useMemo(()=>new Ye,[]),[C,w]=o.useState({categories:!1,subcategories:!1,faqs:!1,ticket:!1,chat:!1}),{club:c}=Je(),y=localStorage.getItem("user"),{register:ke,handleSubmit:Ee,reset:Ie}=rt(),[$,Re]=o.useState([]),[_,X]=o.useState(!0),[x,ae]=o.useState(null),T=o.useRef(null),{state:D,markMessagesAsRead:R}=Xe(),{state:ie}=o.useContext(Ze),A=D.unreadMessages,p=o.useRef(null),U=o.useRef(!1),S=o.useRef(null),Ae=()=>{p.current&&!U.current&&(p.current.scrollTop=p.current.scrollHeight)},B=()=>{U.current||setTimeout(()=>{Ae()},100)},Be=()=>{if(p.current){const{scrollTop:e,scrollHeight:s,clientHeight:a}=p.current;e+a>=s-10?(U.current=!1,S.current&&(clearTimeout(S.current),S.current=null)):(U.current=!0,S.current&&clearTimeout(S.current),S.current=setTimeout(()=>{U.current=!1},3e3))}};o.useEffect(()=>{Fe(),Oe()},[c]),o.useEffect(()=>{var e;if(console.log("🔍 fetchExistingRooms useEffect triggered",{clubSupportEnabled:c==null?void 0:c.supportchat_enabled,roomsLength:(e=D.rooms)==null?void 0:e.length,rooms:D.rooms,isAuthenticated:ie.isAuthenticated,authUser:ie.user}),(c==null?void 0:c.supportchat_enabled)!=1){console.log("❌ Support chat not enabled, skipping fetchExistingRooms");return}console.log("✅ Support chat enabled, calling fetchExistingRooms"),$e()},[D.rooms,c==null?void 0:c.supportchat_enabled]),o.useEffect(()=>{const e=setInterval(()=>{$!=null&&$.setting_value&&ce($)},6e5);return()=>clearInterval(e)},[$]);const Fe=async()=>{w(e=>({...e,categories:!0}));try{m.setTable("faq_category");const e=await m.callRestAPI({},"GETALL");if(e.list){const s=e.list.filter(n=>n.club_id===0),a=e.list.filter(n=>n.club_id===(c==null?void 0:c.id));be([...s,...a])}}catch(e){console.error("Error loading FAQ categories:",e)}finally{w(e=>({...e,categories:!1}))}},Pe=async e=>{try{const s=await m.callRawAPI("/v3/api/lambda/realtime/online",{room_id:e});console.log("Realtime response:",s)}catch(s){console.error("Error fetching realtime:",s)}};o.useEffect(()=>{i&&Pe(i)},[i]);const ce=e=>{if(console.log("checkBusinessHours called with:",e),!(e!=null&&e.setting_value)){console.log("No working hours data, setting business hours to false"),W(!1);return}try{const s=JSON.parse(e.setting_value),n=new Date().toTimeString().slice(0,8);console.log("Working hours array:",s),console.log("Current time:",n);const l=s.some(f=>{const u=f.from,d=f.until;if(console.log(`Checking time slot: ${u} - ${d}`),d<u){const j=n>=u||n<=d;return console.log(`Midnight span check: ${j}`),j}else{const j=n>=u&&n<=d;return console.log(`Normal time check: ${j}`),j}});console.log("Final business hours result:",l),W(!0)}catch(s){console.error("Error parsing working hours:",s),W(!1)}},Oe=async()=>{try{const e=await Te.getOne("setting",5,{});console.log("result",e),e.model&&(Re(e.model),ce(e.model))}catch(e){console.error("Error fetching working hours:",e),W(!1)}},ue=async e=>{try{m.setTable("user");const s=await m.callRestAPI({id:e},"GET");s&&s.model&&(se({first_name:s.model.first_name,last_name:s.model.last_name,photo:s.model.photo,id:s.model.id}),console.log("Support member fetched:",s.model))}catch(s){console.error("Error fetching support member:",s)}},$e=async()=>{console.log("� SupportChatBot: fetchExistingRooms function called!");try{const e=D.rooms;if(console.log("chatState rooms",e),e&&e.length>0){const a=e.sort((n,l)=>new Date(l.update_at)-new Date(n.update_at)).find(n=>n.resolved==0);a?(ae(a),X(!0),P(a.id),O(a.chat_id||a.id),N(!0),E(!0),F(!0),a.admin_id&&a.admin_id!==0?(console.log("Room already has a support agent assigned:",a.admin_id),N(!0),T.current=a.admin_id,await ue(a.admin_id)):(T.current=null,se(null)),await oe(a)):(console.log("No unresolved rooms found, showing FAQs/categories only"),X(!1),P(null),O(null),N(!1),E(!1),F(!1),T.current=null)}else console.log("No rooms found, resetting states"),X(!1),ae(null),P(null),O(null),N(!1),E(!1),F(!1),T.current=null}catch(e){console.error("Error fetching existing rooms:",e)}},oe=async e=>{try{const s=await Z(e.id);if(!s||s.length===0){console.log("No messages found for room"),g([]);return}console.log("Found messages for room:",s.length);const a=s.map(n=>{const l=n.chat;if(!l||!l.message)return console.warn("Invalid message data:",n),null;const f=l.user_id.toString(),u=y.toString(),d=f===u;return{id:n.id,type:d?"user":"support",content:l.message,timestamp:new Date(l.timestamp||n.update_at||n.create_at),user_id:l.user_id,unread:n.unread}}).filter(n=>n!==null).sort((n,l)=>new Date(n.timestamp)-new Date(l.timestamp));if(console.log("Formatted messages:",a.length),a.length>0){const l=[{type:"system",content:"You have been reconnected to your previous conversation with support.",timestamp:new Date},...a];console.log("Setting chat history with messages:",l),g(l)}else console.log("No formatted messages, setting empty chat history"),g([]);e.admin_id&&e.admin_id!==0?(console.log("Room already has a support agent assigned:",e.admin_id),N(!0),T.current=e.admin_id,await ue(e.admin_id)):(T.current=null,se(null)),B(),h&&await R(e.id)}catch(s){console.error("Error loading existing messages:",s),g([])}},Z=async e=>{try{let s=new Date().toISOString().split("T")[0];const a=await m.callRawAPI("/v3/api/lambda/realtime/chat",{room_id:e,date:s},"POST");return a&&a.model?a.model:[]}catch(s){return console.error("Error getting messages from API:",s),[]}},De=async e=>{w(s=>({...s,subcategories:!0}));try{m.setTable("faq_subcategory");const s=await m.callRestAPI({filter:[`category_id,eq,${e}`]},"GETALL");s.list&&ve(s.list)}catch(s){console.error("Error loading FAQ subcategories:",s)}finally{w(s=>({...s,subcategories:!1}))}},Ue=async e=>{w(s=>({...s,faqs:!0}));try{m.setTable("faq");const s=await m.callRestAPI({filter:[`subcategory_id,eq,${e}`],join:["faq_subcategory|subcategory_id"]},"GETALL");s.list&&M(s.list)}catch(s){console.error("Error loading FAQs:",s)}finally{w(s=>({...s,faqs:!1}))}},qe=e=>{ne(e),H(null),M([]),De(e.id),g(s=>[...s,{type:"user",content:`I'll go with ${e.name}.`},{type:"bot",content:`Great! Here are some common questions related to ${e.name}:`}])},Le=e=>{H(e),Ue(e.id),g(s=>[...s,{type:"user",content:e.name},{type:"bot",content:"Here's the information you're looking for:"}])},He=async e=>{w(s=>({...s,ticket:!0}));try{m.setTable("ticket"),await m.callRestAPI({resolved:0,club_id:c==null?void 0:c.id,user_id:y,request:e.message},"POST"),_e(!0),g(s=>[...s,{type:"bot",content:t.jsxs("div",{className:"flex items-start gap-2",children:[t.jsx(st,{className:"mt-1 text-green-500"}),t.jsx("span",{children:"Your ticket has been created successfully. We'll get back to you via email shortly."})]})}]),Q(!1),Ie()}catch(s){console.error("Error creating ticket:",s)}finally{w(s=>({...s,ticket:!1}))}},Me=()=>{if(_&&x&&x.resolved===0){E(!0),F(!0),N(!0);return}k?(E(!0),F(!0),g(e=>[...e,{type:"bot",content:"I see! This sounds like something our support team can help with. Please type your message below and we'll connect you with a live support agent."}])):(Q(!0),g(e=>[...e,{type:"bot",content:t.jsxs("div",{className:"flex items-start gap-2",children:[t.jsx(ye,{className:"mt-1 text-orange-500"}),t.jsx("span",{children:"Our support team is currently offline. Please create a ticket and we'll get back to you via email as soon as possible."})]})}]))};async function ze(){try{const e=await m.callRawAPI("/v3/api/custom/courtmatchup/user/realtime/room",{user_id:y,other_user_id:1},"POST");return m.setTable("room"),await m.callRestAPI({id:e.room_id,admin_id:0,other_user_id:0},"PUT"),e}catch(e){console.log(e)}}const de=async()=>{if(z.length<1)return;if(!k){g(s=>[...s,{type:"system",content:t.jsxs("div",{className:"flex items-start gap-2",children:[t.jsx(ye,{className:"mt-1 text-orange-500"}),t.jsx("span",{children:"Our support team is currently offline. Please create a ticket for assistance."})]}),timestamp:new Date}]),te(""),E(!1),Q(!0);return}const e=z;g(s=>[...s,{type:"user",content:e,timestamp:new Date}]),te(""),B();try{w(l=>({...l,chat:!0}));let s=i,a=J;if(console.log("Current room state:",{roomId:i,chatId:J,hasOpenRoom:_,currentExistingRoom:x==null?void 0:x.id}),_&&x&&x.resolved===0)s?console.log("Using current room:",s):(console.log("Using existing unresolved room:",x.id),s=x.id,a=x.chat_id,P(s),O(a),N(!0));else if(!s||!a){console.log("Creating new room - no existing unresolved room found");const l=await ze();s=l.room_id,a=l.chat_id,P(s),O(a),X(!0),ae({id:s,chat_id:a,resolved:0,admin_id:0}),T.current=null;try{const f=await Z(s);f&&f.length>0?(console.log("🔥 SupportChatBot: Loading existing messages for new room"),await oe({id:s,chat_id:a})):(console.log("🔥 SupportChatBot: No existing messages for new room"),b||(g(u=>[...u,{type:"system",content:"You are now connected to our support team. Please wait while we assign an agent to assist you.",timestamp:new Date}]),N(!0)))}catch(f){console.error("Error loading messages for new room:",f)}}else console.log("Using current room:",s);let n=new Date().toISOString().split("T")[0];await m.postMessage({room_id:s,chat_id:a,user_id:y,message:e,date:n}),setTimeout(async()=>{try{const l=await Z(s);if(l&&l.length>0){const f=l.map(u=>{const d=u.chat;if(!d||!d.message)return null;const j=d.user_id.toString(),Ke=y.toString(),Qe=j===Ke;return{id:u.id,type:Qe?"user":"support",content:d.message,timestamp:new Date(d.timestamp||u.update_at||u.create_at),user_id:d.user_id,unread:u.unread}}).filter(u=>u!==null).sort((u,d)=>new Date(u.timestamp)-new Date(d.timestamp));g(f),B()}}catch(l){console.error("Error refreshing messages after send:",l)}},500)}catch(s){console.error(s),g(a=>[...a,{type:"system",content:"There was an error sending your message. Please try again.",timestamp:new Date}])}finally{w(s=>({...s,chat:!1}))}},Ge=()=>{ne(null),H(null),M([]),g(e=>[...e,{type:"user",content:"Let's try a different category."},{type:"bot",content:"Please choose a category below to get started:"}])};o.useEffect(()=>{B(),h&&i&&G.length>0&&G.some(s=>s.type==="support"&&s.user_id!==y&&s.unread===1)&&R(i)},[G,h,i,y]),o.useEffect(()=>{h&&i&&A>0&&R(i)},[h,i,A]),o.useEffect(()=>{h&&p.current&&B()},[h,p.current]);const{subscribe:me}=Ve(),V=o.useRef(null);o.useEffect(()=>(i&&J&&y&&(c==null?void 0:c.supportchat_enabled)===1&&(console.log("🚀 Subscribing to optimized polling for room:",i),V.current=me("message",async e=>{if(console.log("📨 Received message update from optimized polling",e),e.user_id!==y){const s=await Z(i);if(s&&s.length>0){const a=s.map(n=>{const l=n.chat;if(!l||!l.message)return console.warn("Invalid message data:",n),null;const f=l.user_id.toString(),u=y.toString(),d=f===u;return{id:n.id,type:d?"user":"support",content:l.message,timestamp:new Date(l.timestamp||n.update_at||n.create_at),user_id:l.user_id}}).filter(n=>n!==null).sort((n,l)=>new Date(n.timestamp)-new Date(l.timestamp));g(n=>{const l=new Set(n.map(u=>u.id)),f=a.filter(u=>!l.has(u.id));if(f.length>0){const u=[...n,...f];return B(),u.sort((d,j)=>new Date(d.timestamp)-new Date(j.timestamp))}return n}),h&&await R(i)}}r!=null&&r.id&&await ee(i,r.id)})),()=>{V.current&&(console.log("🛑 Unsubscribing from optimized polling"),V.current(),V.current=null)}),[i,J,y,c==null?void 0:c.supportchat_enabled,me,h,R]),o.useEffect(()=>()=>{S.current&&clearTimeout(S.current)},[]);async function ee(e,s){try{const a=await m.callRawAPI(`/v3/api/lambda/realtime/online?room_id=${e}&online_user_id=${s}`,{},"GET");return console.log("Online status response:",a),a&&typeof a.message=="boolean"?(re(a.message),console.log(`Support member ${s} is ${a.message?"online":"offline"}`)):(re(!1),console.log("Unexpected online status response format, defaulting to offline")),a}catch(a){return console.error("Error fetching online status:",a),re(!1),null}}return o.useEffect(()=>{i&&(r!=null&&r.id)&&ee(i,r==null?void 0:r.id)},[i,r==null?void 0:r.id]),o.useEffect(()=>{let e=null;return i&&(r!=null&&r.id)&&b&&(console.log("🔄 Starting online status polling for support member:",r.id),e=setInterval(()=>{ee(i,r.id)},3e4),ee(i,r.id)),()=>{e&&(console.log("🛑 Stopping online status polling"),clearInterval(e))}},[i,r==null?void 0:r.id,b]),console.log("supportMember",r),console.log("supportMemberOnlineStatus",I),console.log("isConnectedToSupport",b),(c==null?void 0:c.supportchat_enabled)!=1,console.log("is open",!h),t.jsx(t.Fragment,{children:((c==null?void 0:c.supportchat_enabled)==1||!0)&&t.jsxs("div",{className:"fixed bottom-16 left-4 z-[9999] max-w-[220px] space-y-2",children:[h?t.jsx("button",{onClick:()=>L(!1),className:"relative flex h-12 w-12 items-center justify-center rounded-full bg-primaryBlue shadow-lg transition-all hover:scale-105 hover:bg-primaryBlue/80",children:t.jsx(et,{className:"text-xl text-white"})}):t.jsx("button",{onClick:async()=>{if(L(!0),console.log("roomid and currentexiting room",i,x),i&&x)try{await oe(x)}catch(e){console.error("Error loading messages:",e)}i&&A>0&&R(i),setTimeout(()=>{p.current&&(p.current.scrollTop=p.current.scrollHeight)},200)},className:"relative w-full rounded-xl bg-gray-100 p-2 shadow-lg transition-colors hover:bg-gray-200",children:t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("div",{className:"flex h-8 w-8 items-center justify-center rounded-full bg-white shadow-sm",children:t.jsx("div",{className:"relative",children:t.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[t.jsxs("g",{"clip-path":"url(#clip0_40001166_6809)",children:[t.jsx("path",{d:"M13.125 6.4585C13.125 6.11332 12.8452 5.8335 12.5 5.8335C12.1548 5.8335 11.875 6.11332 11.875 6.4585C11.875 8.48 11.4284 9.79288 10.6106 10.6107C9.7927 11.4286 8.47982 11.8752 6.45831 11.8752C6.11313 11.8752 5.83331 12.155 5.83331 12.5002C5.83331 12.8453 6.11313 13.1252 6.45831 13.1252C8.47982 13.1252 9.7927 13.5717 10.6106 14.3896C11.4284 15.2074 11.875 16.5203 11.875 18.5418C11.875 18.887 12.1548 19.1668 12.5 19.1668C12.8452 19.1668 13.125 18.887 13.125 18.5418C13.125 16.5203 13.5716 15.2074 14.3894 14.3896C15.2073 13.5717 16.5201 13.1252 18.5416 13.1252C18.8868 13.1252 19.1666 12.8453 19.1666 12.5002C19.1666 12.155 18.8868 11.8752 18.5416 11.8752C16.5201 11.8752 15.2073 11.4286 14.3894 10.6107C13.5716 9.79288 13.125 8.48 13.125 6.4585Z",fill:"#253EA7"}),t.jsx("path",{d:"M6.04165 1.4585C6.04165 1.11332 5.76182 0.833496 5.41665 0.833496C5.07147 0.833496 4.79165 1.11332 4.79165 1.4585C4.79165 2.75662 4.50422 3.54867 4.02635 4.02654C3.54849 4.5044 2.75644 4.79183 1.45831 4.79183C1.11313 4.79183 0.833313 5.07165 0.833313 5.41683C0.833313 5.76201 1.11313 6.04183 1.45831 6.04183C2.75644 6.04183 3.54849 6.32926 4.02635 6.80712C4.50422 7.28499 4.79165 8.07703 4.79165 9.37516C4.79165 9.72034 5.07147 10.0002 5.41665 10.0002C5.76182 10.0002 6.04165 9.72034 6.04165 9.37516C6.04165 8.07703 6.32907 7.28499 6.80694 6.80712C7.2848 6.32926 8.07685 6.04183 9.37498 6.04183C9.72016 6.04183 9.99998 5.76201 9.99998 5.41683C9.99998 5.07165 9.72016 4.79183 9.37498 4.79183C8.07685 4.79183 7.2848 4.5044 6.80694 4.02654C6.32907 3.54867 6.04165 2.75662 6.04165 1.4585Z",fill:"#253EA7"})]}),t.jsx("defs",{children:t.jsx("clipPath",{id:"clip0_40001166_6809",children:t.jsx("rect",{width:"20",height:"20",fill:"white"})})})]})})}),t.jsxs("div",{className:"flex-1 text-left",children:[t.jsx("p",{className:"text-lg font-medium text-black",children:"Need help!"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Chat with our assistant."})]}),A>0&&t.jsx("div",{className:"absolute -right-1 -top-1 flex h-6 w-6 items-center justify-center rounded-full bg-red-600 text-xs font-bold text-white",children:A>9?"9+":A})]})}),t.jsx(t.Fragment,{children:h&&t.jsxs("div",{className:"fixed bottom-48 left-2 flex max-h-[500px] w-[350px] flex-col overflow-hidden rounded-xl bg-white shadow-lg",children:[t.jsx("div",{className:"rounded-t-2xl bg-primaryBlue p-2",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("div",{className:"flex items-center gap-2",children:b?t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"relative",children:[r!=null&&r.photo?t.jsx("img",{className:"h-8 w-8 rounded-full object-cover",src:r.photo,alt:"Support member"}):t.jsx(fe,{className:"text-2xl text-white"}),t.jsx("div",{className:`absolute -bottom-1 -right-1 h-3 w-3 rounded-full ring-2 ring-white ${I?"bg-green-400":"bg-gray-400"}`})]}),t.jsxs("div",{children:[t.jsx("p",{className:"text-lg font-medium text-white",children:r!=null&&r.first_name&&(r!=null&&r.last_name)?`${r.first_name} ${r.last_name}`:"Support Team"}),t.jsx("p",{className:"text-xs text-white opacity-80",children:I?"Online":"Offline"})]})]}):t.jsxs(t.Fragment,{children:[t.jsx(he,{className:"text-2xl text-white"}),t.jsxs("div",{children:[t.jsx("p",{className:"text-lg font-medium text-white",children:"AceBot"}),t.jsx("p",{className:"text-xs text-white opacity-80",children:k?"Support Available":"Support Offline"})]})]})}),t.jsx("div",{className:"flex items-center gap-2",children:b?t.jsx("div",{className:`rounded-full px-2 py-1 text-xs font-medium ${I?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:I?"Live Support":"Support Offline"}):t.jsx("div",{className:`rounded-full px-2 py-1 text-xs font-medium ${k?"bg-green-100 text-green-800":"bg-orange-100 text-orange-800"}`,children:k?"Available":"Offline"})})]})}),t.jsx("div",{ref:p,className:"min-h-0 flex-1 overflow-y-auto p-4",onLoad:()=>{p.current&&(p.current.scrollTop=p.current.scrollHeight)},onScroll:Be,children:t.jsxs("div",{className:"space-y-4",children:[!_&&t.jsxs("div",{className:"mb-6 flex items-start gap-2",children:[t.jsx("div",{className:"rounded-full bg-yellow-50 p-2",children:t.jsx(tt,{className:"text-yellow-500"})}),t.jsx("div",{className:"max-w-[80%] rounded-lg bg-gray-50 p-3",children:t.jsxs("p",{className:"text-gray-800",children:["Hi there! 👋 How can I help you today?",t.jsx("br",{}),"Please choose a category below to get started:"]})})]}),t.jsxs("div",{className:"mb-6 flex items-start gap-2",children:[t.jsx("div",{className:"rounded-full bg-blue-50 p-2",children:t.jsx(pe,{className:"text-blue-500"})}),t.jsx("div",{className:"max-w-[80%] rounded-lg bg-blue-50 p-3"})]}),G.map((e,s)=>t.jsxs("div",{className:`flex items-start gap-2 ${e.type==="user"?"justify-end":""}`,children:[e.type==="bot"&&t.jsx("div",{className:"rounded-full bg-yellow-50 p-2",children:t.jsx(he,{className:"text-primaryBlue"})}),e.type==="support"&&t.jsxs("div",{className:"relative rounded-full bg-green-50 p-2",children:[r!=null&&r.photo?t.jsx("img",{className:"h-6 w-6 rounded-full object-cover",src:r.photo,alt:"Support member"}):t.jsx(fe,{className:"text-green-600"}),(r==null?void 0:r.photo)&&t.jsx("div",{className:`absolute -bottom-0 -right-0 h-2 w-2 rounded-full ring-1 ring-white ${I?"bg-green-400":"bg-gray-400"}`})]}),e.type==="system"&&t.jsx("div",{className:"rounded-full bg-gray-100 p-2",children:t.jsx(pe,{className:"text-gray-600"})}),t.jsxs("div",{className:`max-w-[80%] rounded-lg p-3 ${e.type==="user"?"bg-primaryBlue text-white":e.type==="support"?"bg-green-100 text-green-900":e.type==="system"?"border border-gray-200 bg-gray-100 text-gray-800":"bg-gray-50"}`,children:[t.jsx("p",{className:"whitespace-pre-wrap",children:e.content}),t.jsxs("div",{className:`mt-1 flex items-center ${e.type==="user"?"justify-between":"justify-start"}`,children:[e.timestamp&&t.jsx("p",{className:"text-xs opacity-70",children:new Date(e.timestamp).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),e.type==="user"&&t.jsx(at,{message:e,isCurrentUser:!0})]})]})]},s)),!_&&!v&&!K&&!Y&&t.jsx("div",{className:"flex flex-col items-end space-y-4",children:C.categories?t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx(q,{className:"h-6 w-6 animate-spin text-primaryBlue"})}):t.jsx(t.Fragment,{children:t.jsxs("div",{className:"w-full",children:[t.jsx("p",{className:"mb-2 text-right font-medium text-gray-700",children:"General"}),t.jsx("div",{className:"flex flex-col items-end space-y-2",children:we.filter(e=>e.club_id===0).map((e,s)=>t.jsxs("button",{onClick:()=>qe(e),className:"flex w-fit items-center gap-2 rounded-lg bg-blue-50 px-4 py-2 text-left text-sm text-blue-900 transition-colors hover:bg-blue-100",children:[t.jsxs("span",{children:[s+1,"️⃣"]}),e.name]},e.id))})]})})}),!_&&v&&!le&&!K&&!Y&&t.jsxs("div",{className:"flex flex-col items-end space-y-2",children:[t.jsxs("button",{onClick:Ge,className:"mb-2 flex w-fit items-end justify-end gap-2 rounded-lg bg-gray-50 px-4 py-2 text-left text-sm text-gray-700 transition-colors hover:bg-gray-100",children:[t.jsx(xe,{className:"text-gray-500"}),"Back to Categories"]}),C.subcategories?t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx(q,{className:"h-6 w-6 animate-spin text-primaryBlue"})}):je.map(e=>t.jsx("button",{onClick:()=>Le(e),className:"w-fit rounded-lg bg-blue-50 px-4 py-2 text-left text-sm text-blue-900 transition-colors hover:bg-blue-100",children:e.name},e.id))]}),!_&&le&&!K&&!Y&&t.jsxs("div",{className:"flex flex-col items-end space-y-4",children:[t.jsxs("button",{onClick:()=>{H(null),M([])},className:"mb-2 flex w-fit items-center gap-2 rounded-lg bg-gray-50 px-4 py-2 text-left text-sm text-gray-700 transition-colors hover:bg-gray-100",children:[t.jsx(xe,{className:"text-gray-500"}),"Back to Questions"]}),C.faqs?t.jsx("div",{className:"flex items-center justify-center py-8",children:t.jsx(q,{className:"h-6 w-6 animate-spin text-primaryBlue"})}):Ne.map(e=>t.jsxs("div",{className:"w-fit rounded-lg bg-gray-50 p-4 text-sm text-gray-900",children:[t.jsx("p",{className:"mb-2 font-medium",children:e.faq_subcategory.name}),t.jsx("p",{className:"mb-4 whitespace-pre-line",children:e.answer}),t.jsxs("div",{className:"mt-4 space-y-4",children:[t.jsx("div",{className:"text-center font-medium text-gray-700",children:"Problem still not resolved?"}),k?t.jsx("button",{onClick:Me,className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Chat with Support"}):t.jsx("button",{onClick:()=>Q(!0),className:"w-full rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700",children:"Create Ticket"})]})]},e.id))]}),K&&!Ce&&t.jsxs("form",{onSubmit:Ee(He),className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Please describe your issue"}),t.jsx("textarea",{...ke("message",{required:!0}),rows:4,className:"mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-primaryBlue focus:outline-none focus:ring-1 focus:ring-primaryBlue",placeholder:"Please provide details about your issue..."})]}),t.jsx("button",{type:"submit",disabled:C.ticket,className:"flex w-full items-center justify-center rounded-lg bg-primaryBlue px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 disabled:opacity-50",children:C.ticket?t.jsx(q,{className:"h-4 w-4 animate-spin"}):"Submit Ticket"})]})]})}),(Y||Se||b)&&t.jsxs("div",{className:"flex-shrink-0 border-t border-gray-200 bg-white p-3 shadow-md",children:[b&&t.jsx("div",{className:"mb-2 flex items-center justify-between",children:t.jsxs("div",{className:"flex items-center gap-1 rounded-full bg-green-100 px-3 py-1 text-xs text-green-800",children:[t.jsx("div",{className:"h-2 w-2 rounded-full bg-green-500"}),t.jsxs("span",{children:["Connected to"," ",r!=null&&r.first_name&&(r!=null&&r.last_name)?`${r.first_name} ${r.last_name}`:"Support Team"]})]})}),t.jsxs("form",{onSubmit:e=>{e.preventDefault(),de()},className:"flex gap-2",children:[t.jsx("input",{type:"text",value:z,onChange:e=>te(e.target.value),placeholder:b?"Type your message to support...":"Type your message...",className:"flex-1 rounded-lg border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-primaryBlue",onKeyDown:e=>{e.key==="Enter"&&!e.shiftKey&&(e.preventDefault(),de())}}),t.jsx("button",{type:"submit",disabled:C.chat||z.trim()==="",className:"flex items-center justify-center rounded-lg bg-primaryBlue px-4 py-2 text-white hover:bg-primaryBlue/80 disabled:opacity-50",children:C.chat?t.jsx(q,{className:"h-4 w-4 animate-spin"}):"Send"})]})]})]})})]})})});export{ct as S};
