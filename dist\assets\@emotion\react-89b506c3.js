import{R as M,r as c}from"../vendor-851db8c1.js";import{c as j}from"./cache-9a5b99cd.js";import{g as L,r as D,i as W}from"./utils-8a8f62c5.js";import{s as O}from"./serialize-460cad7f.js";function I(){return I=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},I.apply(null,arguments)}var A={exports:{}},t={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var a=typeof Symbol=="function"&&Symbol.for,x=a?Symbol.for("react.element"):60103,_=a?Symbol.for("react.portal"):60106,y=a?Symbol.for("react.fragment"):60107,p=a?Symbol.for("react.strict_mode"):60108,v=a?Symbol.for("react.profiler"):60114,d=a?Symbol.for("react.provider"):60109,S=a?Symbol.for("react.context"):60110,P=a?Symbol.for("react.async_mode"):60111,g=a?Symbol.for("react.concurrent_mode"):60111,E=a?Symbol.for("react.forward_ref"):60112,b=a?Symbol.for("react.suspense"):60113,k=a?Symbol.for("react.suspense_list"):60120,$=a?Symbol.for("react.memo"):60115,C=a?Symbol.for("react.lazy"):60116,Y=a?Symbol.for("react.block"):60121,q=a?Symbol.for("react.fundamental"):60117,H=a?Symbol.for("react.responder"):60118,U=a?Symbol.for("react.scope"):60119;function i(e){if(typeof e=="object"&&e!==null){var r=e.$$typeof;switch(r){case x:switch(e=e.type,e){case P:case g:case y:case v:case p:case b:return e;default:switch(e=e&&e.$$typeof,e){case S:case E:case C:case $:case d:return e;default:return r}}case _:return r}}}function F(e){return i(e)===g}t.AsyncMode=P;t.ConcurrentMode=g;t.ContextConsumer=S;t.ContextProvider=d;t.Element=x;t.ForwardRef=E;t.Fragment=y;t.Lazy=C;t.Memo=$;t.Portal=_;t.Profiler=v;t.StrictMode=p;t.Suspense=b;t.isAsyncMode=function(e){return F(e)||i(e)===P};t.isConcurrentMode=F;t.isContextConsumer=function(e){return i(e)===S};t.isContextProvider=function(e){return i(e)===d};t.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===x};t.isForwardRef=function(e){return i(e)===E};t.isFragment=function(e){return i(e)===y};t.isLazy=function(e){return i(e)===C};t.isMemo=function(e){return i(e)===$};t.isPortal=function(e){return i(e)===_};t.isProfiler=function(e){return i(e)===v};t.isStrictMode=function(e){return i(e)===p};t.isSuspense=function(e){return i(e)===b};t.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===y||e===g||e===v||e===p||e===b||e===k||typeof e=="object"&&e!==null&&(e.$$typeof===C||e.$$typeof===$||e.$$typeof===d||e.$$typeof===S||e.$$typeof===E||e.$$typeof===q||e.$$typeof===H||e.$$typeof===U||e.$$typeof===Y)};t.typeOf=i;A.exports=t;var V=A.exports,N=V,B={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},G={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},R={};R[N.ForwardRef]=B;R[N.Memo]=G;var J=function(r){return r()},K=M["useInsertionEffect"]?M["useInsertionEffect"]:!1,Q=K||J,X=!1,z=c.createContext(typeof HTMLElement<"u"?j({key:"css"}):null);z.Provider;var Z=function(r){return c.forwardRef(function(n,o){var s=c.useContext(z);return r(n,s,o)})},ee=c.createContext({}),w={}.hasOwnProperty,h="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",re=function(r,n){var o={};for(var s in n)w.call(n,s)&&(o[s]=n[s]);return o[h]=r,o},te=function(r){var n=r.cache,o=r.serialized,s=r.isStringTag;return D(n,o,s),Q(function(){return W(n,o,s)}),null},ne=Z(function(e,r,n){var o=e.css;typeof o=="string"&&r.registered[o]!==void 0&&(o=r.registered[o]);var s=e[h],l=[o],f="";typeof e.className=="string"?f=L(r.registered,l,e.className):e.className!=null&&(f=e.className+" ");var T=O(l,void 0,c.useContext(ee));f+=r.key+"-"+T.name;var m={};for(var u in e)w.call(e,u)&&u!=="css"&&u!==h&&!X&&(m[u]=e[u]);return m.className=f,n&&(m.ref=n),c.createElement(c.Fragment,null,c.createElement(te,{cache:r,serialized:T,isStringTag:typeof s=="string"}),c.createElement(s,m))}),oe=ne,le=function(r,n){var o=arguments;if(n==null||!w.call(n,"css"))return c.createElement.apply(void 0,o);var s=o.length,l=new Array(s);l[0]=oe,l[1]=re(r,n);for(var f=2;f<s;f++)l[f]=o[f];return c.createElement.apply(null,l)};function ae(){for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return O(r)}var ue=function(){var r=ae.apply(void 0,arguments),n="animation-"+r.name;return{name:n,styles:"@keyframes "+n+"{"+r.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}};export{I as _,ae as c,le as j,ue as k};
