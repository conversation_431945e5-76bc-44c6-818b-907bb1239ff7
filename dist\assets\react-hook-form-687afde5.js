import{b as F}from"./vendor-851db8c1.js";var he=e=>e.type==="checkbox",ue=e=>e instanceof Date,W=e=>e==null;const fr=e=>typeof e=="object";var T=e=>!W(e)&&!Array.isArray(e)&&fr(e)&&!ue(e),dr=e=>T(e)&&e.target?he(e.target)?e.target.checked:e.target.value:e,Ur=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,yr=(e,t)=>e.has(Ur(t)),Rr=e=>{const t=e.constructor&&e.constructor.prototype;return T(t)&&t.hasOwnProperty("isPrototypeOf")},He=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function M(e){let t;const r=Array.isArray(e);if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(He&&(e instanceof Blob||e instanceof FileList))&&(r||T(e)))if(t=r?[]:{},!r&&!Rr(e))t=e;else for(const i in e)e.hasOwnProperty(i)&&(t[i]=M(e[i]));else return e;return t}var ve=e=>Array.isArray(e)?e.filter(Boolean):[],E=e=>e===void 0,c=(e,t,r)=>{if(!t||!T(e))return r;const i=ve(t.split(/[,[\].]+?/)).reduce((u,n)=>W(u)?u:u[n],e);return E(i)||i===e?E(e[t])?r:e[t]:i},G=e=>typeof e=="boolean",$e=e=>/^\w*$/.test(e),_r=e=>ve(e.replace(/["|']|\]/g,"").split(/\.|\[/)),k=(e,t,r)=>{let i=-1;const u=$e(t)?[t]:_r(t),n=u.length,y=n-1;for(;++i<n;){const g=u[i];let m=r;if(i!==y){const O=e[g];m=T(O)||Array.isArray(O)?O:isNaN(+u[i+1])?{}:[]}if(g==="__proto__")return;e[g]=m,e=e[g]}return e};const Fe={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},Y={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},te={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},Lr=F.createContext(null),pe=()=>F.useContext(Lr);var gr=(e,t,r,i=!0)=>{const u={defaultValues:t._defaultValues};for(const n in e)Object.defineProperty(u,n,{get:()=>{const y=n;return t._proxyFormState[y]!==Y.all&&(t._proxyFormState[y]=!i||Y.all),r&&(r[y]=!0),e[y]}});return u},j=e=>T(e)&&!Object.keys(e).length,hr=(e,t,r,i)=>{r(e);const{name:u,...n}=e;return j(n)||Object.keys(n).length>=Object.keys(t).length||Object.keys(n).find(y=>t[y]===(!i||Y.all))},q=e=>Array.isArray(e)?e:[e],vr=(e,t,r)=>!e||!t||e===t||q(e).some(i=>i&&(r?i===t:i.startsWith(t)||t.startsWith(i)));function Se(e){const t=F.useRef(e);t.current=e,F.useEffect(()=>{const r=!e.disabled&&t.current.subject&&t.current.subject.subscribe({next:t.current.next});return()=>{r&&r.unsubscribe()}},[e.disabled])}function Mr(e){const t=pe(),{control:r=t.control,disabled:i,name:u,exact:n}=e||{},[y,g]=F.useState(r._formState),m=F.useRef(!0),O=F.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),A=F.useRef(u);return A.current=u,Se({disabled:i,next:b=>m.current&&vr(A.current,b.name,n)&&hr(b,O.current,r._updateFormState)&&g({...r._formState,...b}),subject:r._subjects.state}),F.useEffect(()=>(m.current=!0,O.current.isValid&&r._updateValid(!0),()=>{m.current=!1}),[r]),gr(y,r,O.current,!1)}var re=e=>typeof e=="string",br=(e,t,r,i,u)=>re(e)?(i&&t.watch.add(e),c(r,e,u)):Array.isArray(e)?e.map(n=>(i&&t.watch.add(n),c(r,n))):(i&&(t.watchAll=!0),r);function Nr(e){const t=pe(),{control:r=t.control,name:i,defaultValue:u,disabled:n,exact:y}=e||{},g=F.useRef(i);g.current=i,Se({disabled:n,subject:r._subjects.values,next:A=>{vr(g.current,A.name,y)&&O(M(br(g.current,r._names,A.values||r._formValues,!1,u)))}});const[m,O]=F.useState(r._getWatch(i,u));return F.useEffect(()=>r._removeUnmounted()),m}function Br(e){const t=pe(),{name:r,disabled:i,control:u=t.control,shouldUnregister:n}=e,y=yr(u._names.array,r),g=Nr({control:u,name:r,defaultValue:c(u._formValues,r,c(u._defaultValues,r,e.defaultValue)),exact:!0}),m=Mr({control:u,name:r,exact:!0}),O=F.useRef(u.register(r,{...e.rules,value:g,...G(e.disabled)?{disabled:e.disabled}:{}}));return F.useEffect(()=>{const A=u._options.shouldUnregister||n,b=(D,H)=>{const R=c(u._fields,D);R&&R._f&&(R._f.mount=H)};if(b(r,!0),A){const D=M(c(u._options.defaultValues,r));k(u._defaultValues,r,D),E(c(u._formValues,r))&&k(u._formValues,r,D)}return()=>{(y?A&&!u._state.action:A)?u.unregister(r):b(r,!1)}},[r,u,y,n]),F.useEffect(()=>{c(u._fields,r)&&u._updateDisabledField({disabled:i,fields:u._fields,name:r,value:c(u._fields,r)._f.value})},[i,r,u]),{field:{name:r,value:g,...G(i)||m.disabled?{disabled:m.disabled||i}:{},onChange:F.useCallback(A=>O.current.onChange({target:{value:dr(A),name:r},type:Fe.CHANGE}),[r]),onBlur:F.useCallback(()=>O.current.onBlur({target:{value:c(u._formValues,r),name:r},type:Fe.BLUR}),[r,u]),ref:F.useCallback(A=>{const b=c(u._fields,r);b&&A&&(b._f.ref={focus:()=>A.focus(),select:()=>A.select(),setCustomValidity:D=>A.setCustomValidity(D),reportValidity:()=>A.reportValidity()})},[u._fields,r])},formState:m,fieldState:Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!c(m.errors,r)},isDirty:{enumerable:!0,get:()=>!!c(m.dirtyFields,r)},isTouched:{enumerable:!0,get:()=>!!c(m.touchedFields,r)},isValidating:{enumerable:!0,get:()=>!!c(m.validatingFields,r)},error:{enumerable:!0,get:()=>c(m.errors,r)}})}}const rt=e=>e.render(Br(e));var Ir=(e,t,r,i,u)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[i]:u||!0}}:{},se=()=>{const e=typeof performance>"u"?Date.now():performance.now()*1e3;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,t=>{const r=(Math.random()*16+e)%16|0;return(t=="x"?r:r&3|8).toString(16)})},Ce=(e,t,r={})=>r.shouldFocus||E(r.shouldFocus)?r.focusName||`${e}.${E(r.focusIndex)?t:r.focusIndex}.`:"",ge=e=>({isOnSubmit:!e||e===Y.onSubmit,isOnBlur:e===Y.onBlur,isOnChange:e===Y.onChange,isOnAll:e===Y.all,isOnTouch:e===Y.onTouched}),je=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(i=>e.startsWith(i)&&/^\.\w+/.test(e.slice(i.length))));const le=(e,t,r,i)=>{for(const u of r||Object.keys(e)){const n=c(e,u);if(n){const{_f:y,...g}=n;if(y){if(y.refs&&y.refs[0]&&t(y.refs[0],u)&&!i)return!0;if(y.ref&&t(y.ref,y.name)&&!i)return!0;if(le(g,t))break}else if(T(g)&&le(g,t))break}}};var Ar=(e,t,r)=>{const i=q(c(e,r));return k(i,"root",t[r]),k(e,r,i),e},Ke=e=>e.type==="file",ee=e=>typeof e=="function",me=e=>{if(!He)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Ae=e=>re(e),Ge=e=>e.type==="radio",Ve=e=>e instanceof RegExp;const ir={value:!1,isValid:!1},ar={value:!0,isValid:!0};var Fr=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!E(e[0].attributes.value)?E(e[0].value)||e[0].value===""?ar:{value:e[0].value,isValid:!0}:ar:ir}return ir};const ur={isValid:!1,value:null};var mr=e=>Array.isArray(e)?e.reduce((t,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:t,ur):ur;function nr(e,t,r="validate"){if(Ae(e)||Array.isArray(e)&&e.every(Ae)||G(e)&&!e)return{type:r,message:Ae(e)?e:"",ref:t}}var ne=e=>T(e)&&!Ve(e)?e:{value:e,message:""},We=async(e,t,r,i,u)=>{const{ref:n,refs:y,required:g,maxLength:m,minLength:O,min:A,max:b,pattern:D,validate:H,name:R,valueAsNumber:oe,mount:z,disabled:Z}=e._f,S=c(t,R);if(!z||Z)return{};const J=y?y[0]:n,Q=p=>{i&&J.reportValidity&&(J.setCustomValidity(G(p)?"":p||""),J.reportValidity())},_={},h=Ge(n),x=he(n),C=h||x,$=(oe||Ke(n))&&E(n.value)&&E(S)||me(n)&&n.value===""||S===""||Array.isArray(S)&&!S.length,P=Ir.bind(null,R,r,_),be=(p,w,L,I=te.maxLength,X=te.minLength)=>{const K=p?w:L;_[R]={type:p?I:X,message:K,ref:n,...P(p?I:X,K)}};if(u?!Array.isArray(S)||!S.length:g&&(!C&&($||W(S))||G(S)&&!S||x&&!Fr(y).isValid||h&&!mr(y).isValid)){const{value:p,message:w}=Ae(g)?{value:!!g,message:g}:ne(g);if(p&&(_[R]={type:te.required,message:w,ref:J,...P(te.required,w)},!r))return Q(w),_}if(!$&&(!W(A)||!W(b))){let p,w;const L=ne(b),I=ne(A);if(!W(S)&&!isNaN(S)){const X=n.valueAsNumber||S&&+S;W(L.value)||(p=X>L.value),W(I.value)||(w=X<I.value)}else{const X=n.valueAsDate||new Date(S),K=de=>new Date(new Date().toDateString()+" "+de),ce=n.type=="time",fe=n.type=="week";re(L.value)&&S&&(p=ce?K(S)>K(L.value):fe?S>L.value:X>new Date(L.value)),re(I.value)&&S&&(w=ce?K(S)<K(I.value):fe?S<I.value:X<new Date(I.value))}if((p||w)&&(be(!!p,L.message,I.message,te.max,te.min),!r))return Q(_[R].message),_}if((m||O)&&!$&&(re(S)||u&&Array.isArray(S))){const p=ne(m),w=ne(O),L=!W(p.value)&&S.length>+p.value,I=!W(w.value)&&S.length<+w.value;if((L||I)&&(be(L,p.message,w.message),!r))return Q(_[R].message),_}if(D&&!$&&re(S)){const{value:p,message:w}=ne(D);if(Ve(p)&&!S.match(p)&&(_[R]={type:te.pattern,message:w,ref:n,...P(te.pattern,w)},!r))return Q(w),_}if(H){if(ee(H)){const p=await H(S,t),w=nr(p,J);if(w&&(_[R]={...w,...P(te.validate,w.message)},!r))return Q(w.message),_}else if(T(H)){let p={};for(const w in H){if(!j(p)&&!r)break;const L=nr(await H[w](S,t),J,w);L&&(p={...L,...P(w,L.message)},Q(L.message),r&&(_[R]=p))}if(!j(p)&&(_[R]={ref:J,...p},!r))return _}}return Q(!0),_},Oe=(e,t)=>[...e,...q(t)],Te=e=>Array.isArray(e)?e.map(()=>{}):void 0;function Ue(e,t,r){return[...e.slice(0,t),...q(r),...e.slice(t)]}var Re=(e,t,r)=>Array.isArray(e)?(E(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],Le=(e,t)=>[...q(t),...q(e)];function Pr(e,t){let r=0;const i=[...e];for(const u of t)i.splice(u-r,1),r++;return ve(i).length?i:[]}var Me=(e,t)=>E(t)?[]:Pr(e,q(t).sort((r,i)=>r-i)),Ne=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]};function jr(e,t){const r=t.slice(0,-1).length;let i=0;for(;i<r;)e=E(e)?i++:e[t[i++]];return e}function Wr(e){for(const t in e)if(e.hasOwnProperty(t)&&!E(e[t]))return!1;return!0}function U(e,t){const r=Array.isArray(t)?t:$e(t)?[t]:_r(t),i=r.length===1?e:jr(e,r),u=r.length-1,n=r[u];return i&&delete i[n],u!==0&&(T(i)&&j(i)||Array.isArray(i)&&Wr(i))&&U(e,r.slice(0,-1)),e}var lr=(e,t,r)=>(e[t]=r,e);function tt(e){const t=pe(),{control:r=t.control,name:i,keyName:u="id",shouldUnregister:n}=e,[y,g]=F.useState(r._getFieldArray(i)),m=F.useRef(r._getFieldArray(i).map(se)),O=F.useRef(y),A=F.useRef(i),b=F.useRef(!1);A.current=i,O.current=y,r._names.array.add(i),e.rules&&r.register(i,e.rules),Se({next:({values:_,name:h})=>{if(h===A.current||!h){const x=c(_,A.current);Array.isArray(x)&&(g(x),m.current=x.map(se))}},subject:r._subjects.array});const D=F.useCallback(_=>{b.current=!0,r._updateFieldArray(i,_)},[r,i]),H=(_,h)=>{const x=q(M(_)),C=Oe(r._getFieldArray(i),x);r._names.focus=Ce(i,C.length-1,h),m.current=Oe(m.current,x.map(se)),D(C),g(C),r._updateFieldArray(i,C,Oe,{argA:Te(_)})},R=(_,h)=>{const x=q(M(_)),C=Le(r._getFieldArray(i),x);r._names.focus=Ce(i,0,h),m.current=Le(m.current,x.map(se)),D(C),g(C),r._updateFieldArray(i,C,Le,{argA:Te(_)})},oe=_=>{const h=Me(r._getFieldArray(i),_);m.current=Me(m.current,_),D(h),g(h),r._updateFieldArray(i,h,Me,{argA:_})},z=(_,h,x)=>{const C=q(M(h)),$=Ue(r._getFieldArray(i),_,C);r._names.focus=Ce(i,_,x),m.current=Ue(m.current,_,C.map(se)),D($),g($),r._updateFieldArray(i,$,Ue,{argA:_,argB:Te(h)})},Z=(_,h)=>{const x=r._getFieldArray(i);Ne(x,_,h),Ne(m.current,_,h),D(x),g(x),r._updateFieldArray(i,x,Ne,{argA:_,argB:h},!1)},S=(_,h)=>{const x=r._getFieldArray(i);Re(x,_,h),Re(m.current,_,h),D(x),g(x),r._updateFieldArray(i,x,Re,{argA:_,argB:h},!1)},J=(_,h)=>{const x=M(h),C=lr(r._getFieldArray(i),_,x);m.current=[...C].map(($,P)=>!$||P===_?se():m.current[P]),D(C),g([...C]),r._updateFieldArray(i,C,lr,{argA:_,argB:x},!0,!1)},Q=_=>{const h=q(M(_));m.current=h.map(se),D([...h]),g([...h]),r._updateFieldArray(i,[...h],x=>x,{},!0,!1)};return F.useEffect(()=>{if(r._state.action=!1,je(i,r._names)&&r._subjects.state.next({...r._formState}),b.current&&(!ge(r._options.mode).isOnSubmit||r._formState.isSubmitted))if(r._options.resolver)r._executeSchema([i]).then(_=>{const h=c(_.errors,i),x=c(r._formState.errors,i);(x?!h&&x.type||h&&(x.type!==h.type||x.message!==h.message):h&&h.type)&&(h?k(r._formState.errors,i,h):U(r._formState.errors,i),r._subjects.state.next({errors:r._formState.errors}))});else{const _=c(r._fields,i);_&&_._f&&!(ge(r._options.reValidateMode).isOnSubmit&&ge(r._options.mode).isOnSubmit)&&We(_,r._formValues,r._options.criteriaMode===Y.all,r._options.shouldUseNativeValidation,!0).then(h=>!j(h)&&r._subjects.state.next({errors:Ar(r._formState.errors,h,i)}))}r._subjects.values.next({name:i,values:{...r._formValues}}),r._names.focus&&le(r._fields,(_,h)=>{if(r._names.focus&&h.startsWith(r._names.focus)&&_.focus)return _.focus(),1}),r._names.focus="",r._updateValid(),b.current=!1},[y,i,r]),F.useEffect(()=>(!c(r._formValues,i)&&r._updateFieldArray(i),()=>{(r._options.shouldUnregister||n)&&r.unregister(i)}),[i,r,u,n]),{swap:F.useCallback(Z,[D,i,r]),move:F.useCallback(S,[D,i,r]),prepend:F.useCallback(R,[D,i,r]),append:F.useCallback(H,[D,i,r]),remove:F.useCallback(oe,[D,i,r]),insert:F.useCallback(z,[D,i,r]),update:F.useCallback(J,[D,i,r]),replace:F.useCallback(Q,[D,i,r]),fields:F.useMemo(()=>y.map((_,h)=>({..._,[u]:m.current[h]||se()})),[y,u])}}var Be=()=>{let e=[];return{get observers(){return e},next:u=>{for(const n of e)n.next&&n.next(u)},subscribe:u=>(e.push(u),{unsubscribe:()=>{e=e.filter(n=>n!==u)}}),unsubscribe:()=>{e=[]}}},qe=e=>W(e)||!fr(e);function ie(e,t){if(qe(e)||qe(t))return e===t;if(ue(e)&&ue(t))return e.getTime()===t.getTime();const r=Object.keys(e),i=Object.keys(t);if(r.length!==i.length)return!1;for(const u of r){const n=e[u];if(!i.includes(u))return!1;if(u!=="ref"){const y=t[u];if(ue(n)&&ue(y)||T(n)&&T(y)||Array.isArray(n)&&Array.isArray(y)?!ie(n,y):n!==y)return!1}}return!0}var Vr=e=>e.type==="select-multiple",qr=e=>Ge(e)||he(e),Ie=e=>me(e)&&e.isConnected,xr=e=>{for(const t in e)if(ee(e[t]))return!0;return!1};function xe(e,t={}){const r=Array.isArray(e);if(T(e)||r)for(const i in e)Array.isArray(e[i])||T(e[i])&&!xr(e[i])?(t[i]=Array.isArray(e[i])?[]:{},xe(e[i],t[i])):W(e[i])||(t[i]=!0);return t}function pr(e,t,r){const i=Array.isArray(e);if(T(e)||i)for(const u in e)Array.isArray(e[u])||T(e[u])&&!xr(e[u])?E(t)||qe(r[u])?r[u]=Array.isArray(e[u])?xe(e[u],[]):{...xe(e[u])}:pr(e[u],W(t)?{}:t[u],r[u]):r[u]=!ie(e[u],t[u]);return r}var ye=(e,t)=>pr(e,t,xe(t)),Sr=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:i})=>E(e)?e:t?e===""?NaN:e&&+e:r&&re(e)?new Date(e):i?i(e):e;function Pe(e){const t=e.ref;if(!(e.refs?e.refs.every(r=>r.disabled):t.disabled))return Ke(t)?t.files:Ge(t)?mr(e.refs).value:Vr(t)?[...t.selectedOptions].map(({value:r})=>r):he(t)?Fr(e.refs).value:Sr(E(t.value)?e.ref.value:t.value,e)}var Hr=(e,t,r,i)=>{const u={};for(const n of e){const y=c(t,n);y&&k(u,n,y._f)}return{criteriaMode:r,names:[...e],fields:u,shouldUseNativeValidation:i}},_e=e=>E(e)?e:Ve(e)?e.source:T(e)?Ve(e.value)?e.value.source:e.value:e;const or="AsyncFunction";var $r=e=>(!e||!e.validate)&&!!(ee(e.validate)&&e.validate.constructor.name===or||T(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===or)),Kr=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate);function cr(e,t,r){const i=c(e,r);if(i||$e(r))return{error:i,name:r};const u=r.split(".");for(;u.length;){const n=u.join("."),y=c(t,n),g=c(e,n);if(y&&!Array.isArray(y)&&r!==n)return{name:r};if(g&&g.type)return{name:n,error:g};u.pop()}return{name:r}}var Gr=(e,t,r,i,u)=>u.isOnAll?!1:!r&&u.isOnTouch?!(t||e):(r?i.isOnBlur:u.isOnBlur)?!e:(r?i.isOnChange:u.isOnChange)?e:!0,Yr=(e,t)=>!ve(c(e,t)).length&&U(e,t);const zr={mode:Y.onSubmit,reValidateMode:Y.onChange,shouldFocusError:!0};function Jr(e={}){let t={...zr,...e},r={submitCount:0,isDirty:!1,isLoading:ee(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1},i={},u=T(t.defaultValues)||T(t.values)?M(t.defaultValues||t.values)||{}:{},n=t.shouldUnregister?{}:M(u),y={action:!1,mount:!1,watch:!1},g={mount:new Set,unMount:new Set,array:new Set,watch:new Set},m,O=0;const A={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},b={values:Be(),array:Be(),state:Be()},D=ge(t.mode),H=ge(t.reValidateMode),R=t.criteriaMode===Y.all,oe=s=>a=>{clearTimeout(O),O=setTimeout(s,a)},z=async s=>{if(!t.disabled&&(A.isValid||s)){const a=t.resolver?j((await C()).errors):await P(i,!0);a!==r.isValid&&b.state.next({isValid:a})}},Z=(s,a)=>{!t.disabled&&(A.isValidating||A.validatingFields)&&((s||Array.from(g.mount)).forEach(l=>{l&&(a?k(r.validatingFields,l,a):U(r.validatingFields,l))}),b.state.next({validatingFields:r.validatingFields,isValidating:!j(r.validatingFields)}))},S=(s,a=[],l,d,f=!0,o=!0)=>{if(d&&l&&!t.disabled){if(y.action=!0,o&&Array.isArray(c(i,s))){const v=l(c(i,s),d.argA,d.argB);f&&k(i,s,v)}if(o&&Array.isArray(c(r.errors,s))){const v=l(c(r.errors,s),d.argA,d.argB);f&&k(r.errors,s,v),Yr(r.errors,s)}if(A.touchedFields&&o&&Array.isArray(c(r.touchedFields,s))){const v=l(c(r.touchedFields,s),d.argA,d.argB);f&&k(r.touchedFields,s,v)}A.dirtyFields&&(r.dirtyFields=ye(u,n)),b.state.next({name:s,isDirty:p(s,a),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else k(n,s,a)},J=(s,a)=>{k(r.errors,s,a),b.state.next({errors:r.errors})},Q=s=>{r.errors=s,b.state.next({errors:r.errors,isValid:!1})},_=(s,a,l,d)=>{const f=c(i,s);if(f){const o=c(n,s,E(l)?c(u,s):l);E(o)||d&&d.defaultChecked||a?k(n,s,a?o:Pe(f._f)):I(s,o),y.mount&&z()}},h=(s,a,l,d,f)=>{let o=!1,v=!1;const V={name:s};if(!t.disabled){const N=!!(c(i,s)&&c(i,s)._f&&c(i,s)._f.disabled);if(!l||d){A.isDirty&&(v=r.isDirty,r.isDirty=V.isDirty=p(),o=v!==V.isDirty);const B=N||ie(c(u,s),a);v=!!(!N&&c(r.dirtyFields,s)),B||N?U(r.dirtyFields,s):k(r.dirtyFields,s,!0),V.dirtyFields=r.dirtyFields,o=o||A.dirtyFields&&v!==!B}if(l){const B=c(r.touchedFields,s);B||(k(r.touchedFields,s,l),V.touchedFields=r.touchedFields,o=o||A.touchedFields&&B!==l)}o&&f&&b.state.next(V)}return o?V:{}},x=(s,a,l,d)=>{const f=c(r.errors,s),o=A.isValid&&G(a)&&r.isValid!==a;if(e.delayError&&l?(m=oe(()=>J(s,l)),m(e.delayError)):(clearTimeout(O),m=null,l?k(r.errors,s,l):U(r.errors,s)),(l?!ie(f,l):f)||!j(d)||o){const v={...d,...o&&G(a)?{isValid:a}:{},errors:r.errors,name:s};r={...r,...v},b.state.next(v)}},C=async s=>{Z(s,!0);const a=await t.resolver(n,t.context,Hr(s||g.mount,i,t.criteriaMode,t.shouldUseNativeValidation));return Z(s),a},$=async s=>{const{errors:a}=await C(s);if(s)for(const l of s){const d=c(a,l);d?k(r.errors,l,d):U(r.errors,l)}else r.errors=a;return a},P=async(s,a,l={valid:!0})=>{for(const d in s){const f=s[d];if(f){const{_f:o,...v}=f;if(o){const V=g.array.has(o.name),N=f._f&&$r(f._f);N&&A.validatingFields&&Z([d],!0);const B=await We(f,n,R,t.shouldUseNativeValidation&&!a,V);if(N&&A.validatingFields&&Z([d]),B[o.name]&&(l.valid=!1,a))break;!a&&(c(B,o.name)?V?Ar(r.errors,B,o.name):k(r.errors,o.name,B[o.name]):U(r.errors,o.name))}!j(v)&&await P(v,a,l)}}return l.valid},be=()=>{for(const s of g.unMount){const a=c(i,s);a&&(a._f.refs?a._f.refs.every(l=>!Ie(l)):!Ie(a._f.ref))&&we(s)}g.unMount=new Set},p=(s,a)=>!t.disabled&&(s&&a&&k(n,s,a),!ie(Ye(),u)),w=(s,a,l)=>br(s,g,{...y.mount?n:E(a)?u:re(s)?{[s]:a}:a},l,a),L=s=>ve(c(y.mount?n:u,s,e.shouldUnregister?c(u,s,[]):[])),I=(s,a,l={})=>{const d=c(i,s);let f=a;if(d){const o=d._f;o&&(!o.disabled&&k(n,s,Sr(a,o)),f=me(o.ref)&&W(a)?"":a,Vr(o.ref)?[...o.ref.options].forEach(v=>v.selected=f.includes(v.value)):o.refs?he(o.ref)?o.refs.length>1?o.refs.forEach(v=>(!v.defaultChecked||!v.disabled)&&(v.checked=Array.isArray(f)?!!f.find(V=>V===v.value):f===v.value)):o.refs[0]&&(o.refs[0].checked=!!f):o.refs.forEach(v=>v.checked=v.value===f):Ke(o.ref)?o.ref.value="":(o.ref.value=f,o.ref.type||b.values.next({name:s,values:{...n}})))}(l.shouldDirty||l.shouldTouch)&&h(s,f,l.shouldTouch,l.shouldDirty,!0),l.shouldValidate&&de(s)},X=(s,a,l)=>{for(const d in a){const f=a[d],o=`${s}.${d}`,v=c(i,o);(g.array.has(s)||T(f)||v&&!v._f)&&!ue(f)?X(o,f,l):I(o,f,l)}},K=(s,a,l={})=>{const d=c(i,s),f=g.array.has(s),o=M(a);k(n,s,o),f?(b.array.next({name:s,values:{...n}}),(A.isDirty||A.dirtyFields)&&l.shouldDirty&&b.state.next({name:s,dirtyFields:ye(u,n),isDirty:p(s,o)})):d&&!d._f&&!W(o)?X(s,o,l):I(s,o,l),je(s,g)&&b.state.next({...r}),b.values.next({name:y.mount?s:void 0,values:{...n}})},ce=async s=>{y.mount=!0;const a=s.target;let l=a.name,d=!0;const f=c(i,l),o=()=>a.type?Pe(f._f):dr(s),v=V=>{d=Number.isNaN(V)||ue(V)&&isNaN(V.getTime())||ie(V,c(n,l,V))};if(f){let V,N;const B=o(),ae=s.type===Fe.BLUR||s.type===Fe.FOCUS_OUT,Cr=!Kr(f._f)&&!t.resolver&&!c(r.errors,l)&&!f._f.deps||Gr(ae,c(r.touchedFields,l),r.isSubmitted,H,D),ke=je(l,g,ae);k(n,l,B),ae?(f._f.onBlur&&f._f.onBlur(s),m&&m(0)):f._f.onChange&&f._f.onChange(s);const Ee=h(l,B,ae,!1),Or=!j(Ee)||ke;if(!ae&&b.values.next({name:l,type:s.type,values:{...n}}),Cr)return A.isValid&&(e.mode==="onBlur"?ae&&z():z()),Or&&b.state.next({name:l,...ke?{}:Ee});if(!ae&&ke&&b.state.next({...r}),t.resolver){const{errors:tr}=await C([l]);if(v(B),d){const Tr=cr(r.errors,i,l),sr=cr(tr,i,Tr.name||l);V=sr.error,l=sr.name,N=j(tr)}}else Z([l],!0),V=(await We(f,n,R,t.shouldUseNativeValidation))[l],Z([l]),v(B),d&&(V?N=!1:A.isValid&&(N=await P(i,!0)));d&&(f._f.deps&&de(f._f.deps),x(l,N,V,Ee))}},fe=(s,a)=>{if(c(r.errors,a)&&s.focus)return s.focus(),1},de=async(s,a={})=>{let l,d;const f=q(s);if(t.resolver){const o=await $(E(s)?s:f);l=j(o),d=s?!f.some(v=>c(o,v)):l}else s?(d=(await Promise.all(f.map(async o=>{const v=c(i,o);return await P(v&&v._f?{[o]:v}:v)}))).every(Boolean),!(!d&&!r.isValid)&&z()):d=l=await P(i);return b.state.next({...!re(s)||A.isValid&&l!==r.isValid?{}:{name:s},...t.resolver||!s?{isValid:l}:{},errors:r.errors}),a.shouldFocus&&!d&&le(i,fe,s?f:g.mount),d},Ye=s=>{const a={...y.mount?n:u};return E(s)?a:re(s)?c(a,s):s.map(l=>c(a,l))},ze=(s,a)=>({invalid:!!c((a||r).errors,s),isDirty:!!c((a||r).dirtyFields,s),error:c((a||r).errors,s),isValidating:!!c(r.validatingFields,s),isTouched:!!c((a||r).touchedFields,s)}),wr=s=>{s&&q(s).forEach(a=>U(r.errors,a)),b.state.next({errors:s?r.errors:{}})},Je=(s,a,l)=>{const d=(c(i,s,{_f:{}})._f||{}).ref,f=c(r.errors,s)||{},{ref:o,message:v,type:V,...N}=f;k(r.errors,s,{...N,...a,ref:d}),b.state.next({name:s,errors:r.errors,isValid:!1}),l&&l.shouldFocus&&d&&d.focus&&d.focus()},Dr=(s,a)=>ee(s)?b.values.subscribe({next:l=>s(w(void 0,a),l)}):w(s,a,!0),we=(s,a={})=>{for(const l of s?q(s):g.mount)g.mount.delete(l),g.array.delete(l),a.keepValue||(U(i,l),U(n,l)),!a.keepError&&U(r.errors,l),!a.keepDirty&&U(r.dirtyFields,l),!a.keepTouched&&U(r.touchedFields,l),!a.keepIsValidating&&U(r.validatingFields,l),!t.shouldUnregister&&!a.keepDefaultValue&&U(u,l);b.values.next({values:{...n}}),b.state.next({...r,...a.keepDirty?{isDirty:p()}:{}}),!a.keepIsValid&&z()},Qe=({disabled:s,name:a,field:l,fields:d,value:f})=>{if(G(s)&&y.mount||s){const o=s?void 0:E(f)?Pe(l?l._f:c(d,a)._f):f;k(n,a,o),h(a,o,!1,!1,!0)}},De=(s,a={})=>{let l=c(i,s);const d=G(a.disabled)||G(t.disabled);return k(i,s,{...l||{},_f:{...l&&l._f?l._f:{ref:{name:s}},name:s,mount:!0,...a}}),g.mount.add(s),l?Qe({field:l,disabled:G(a.disabled)?a.disabled:t.disabled,name:s,value:a.value}):_(s,!0,a.value),{...d?{disabled:a.disabled||t.disabled}:{},...t.progressive?{required:!!a.required,min:_e(a.min),max:_e(a.max),minLength:_e(a.minLength),maxLength:_e(a.maxLength),pattern:_e(a.pattern)}:{},name:s,onChange:ce,onBlur:ce,ref:f=>{if(f){De(s,a),l=c(i,s);const o=E(f.value)&&f.querySelectorAll&&f.querySelectorAll("input,select,textarea")[0]||f,v=qr(o),V=l._f.refs||[];if(v?V.find(N=>N===o):o===l._f.ref)return;k(i,s,{_f:{...l._f,...v?{refs:[...V.filter(Ie),o,...Array.isArray(c(u,s))?[{}]:[]],ref:{type:o.type,name:s}}:{ref:o}}}),_(s,!1,void 0,o)}else l=c(i,s,{}),l._f&&(l._f.mount=!1),(t.shouldUnregister||a.shouldUnregister)&&!(yr(g.array,s)&&y.action)&&g.unMount.add(s)}}},Xe=()=>t.shouldFocusError&&le(i,fe,g.mount),kr=s=>{G(s)&&(b.state.next({disabled:s}),le(i,(a,l)=>{const d=c(i,l);d&&(a.disabled=d._f.disabled||s,Array.isArray(d._f.refs)&&d._f.refs.forEach(f=>{f.disabled=d._f.disabled||s}))},0,!1))},Ze=(s,a)=>async l=>{let d;if(l&&(l.preventDefault&&l.preventDefault(),l.persist&&l.persist()),t.disabled){a&&await a({...r.errors},l);return}let f=M(n);if(b.state.next({isSubmitting:!0}),t.resolver){const{errors:o,values:v}=await C();r.errors=o,f=v}else await P(i);if(U(r.errors,"root"),j(r.errors)){b.state.next({errors:{}});try{await s(f,l)}catch(o){d=o}}else a&&await a({...r.errors},l),Xe(),setTimeout(Xe);if(b.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:j(r.errors)&&!d,submitCount:r.submitCount+1,errors:r.errors}),d)throw d},Er=(s,a={})=>{c(i,s)&&(E(a.defaultValue)?K(s,M(c(u,s))):(K(s,a.defaultValue),k(u,s,M(a.defaultValue))),a.keepTouched||U(r.touchedFields,s),a.keepDirty||(U(r.dirtyFields,s),r.isDirty=a.defaultValue?p(s,M(c(u,s))):p()),a.keepError||(U(r.errors,s),A.isValid&&z()),b.state.next({...r}))},er=(s,a={})=>{const l=s?M(s):u,d=M(l),f=j(s),o=f?u:d;if(a.keepDefaultValues||(u=l),!a.keepValues){if(a.keepDirtyValues){const v=new Set([...g.mount,...Object.keys(ye(u,n))]);for(const V of Array.from(v))c(r.dirtyFields,V)?k(o,V,c(n,V)):K(V,c(o,V))}else{if(He&&E(s))for(const v of g.mount){const V=c(i,v);if(V&&V._f){const N=Array.isArray(V._f.refs)?V._f.refs[0]:V._f.ref;if(me(N)){const B=N.closest("form");if(B){B.reset();break}}}}i={}}n=e.shouldUnregister?a.keepDefaultValues?M(u):{}:M(o),b.array.next({values:{...o}}),b.values.next({values:{...o}})}g={mount:a.keepDirtyValues?g.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},y.mount=!A.isValid||!!a.keepIsValid||!!a.keepDirtyValues,y.watch=!!e.shouldUnregister,b.state.next({submitCount:a.keepSubmitCount?r.submitCount:0,isDirty:f?!1:a.keepDirty?r.isDirty:!!(a.keepDefaultValues&&!ie(s,u)),isSubmitted:a.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:f?{}:a.keepDirtyValues?a.keepDefaultValues&&n?ye(u,n):r.dirtyFields:a.keepDefaultValues&&s?ye(u,s):a.keepDirty?r.dirtyFields:{},touchedFields:a.keepTouched?r.touchedFields:{},errors:a.keepErrors?r.errors:{},isSubmitSuccessful:a.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1})},rr=(s,a)=>er(ee(s)?s(n):s,a);return{control:{register:De,unregister:we,getFieldState:ze,handleSubmit:Ze,setError:Je,_executeSchema:C,_getWatch:w,_getDirty:p,_updateValid:z,_removeUnmounted:be,_updateFieldArray:S,_updateDisabledField:Qe,_getFieldArray:L,_reset:er,_resetDefaultValues:()=>ee(t.defaultValues)&&t.defaultValues().then(s=>{rr(s,t.resetOptions),b.state.next({isLoading:!1})}),_updateFormState:s=>{r={...r,...s}},_disableForm:kr,_subjects:b,_proxyFormState:A,_setErrors:Q,get _fields(){return i},get _formValues(){return n},get _state(){return y},set _state(s){y=s},get _defaultValues(){return u},get _names(){return g},set _names(s){g=s},get _formState(){return r},set _formState(s){r=s},get _options(){return t},set _options(s){t={...t,...s}}},trigger:de,register:De,handleSubmit:Ze,watch:Dr,setValue:K,getValues:Ye,reset:rr,resetField:Er,clearErrors:wr,unregister:we,setError:Je,setFocus:(s,a={})=>{const l=c(i,s),d=l&&l._f;if(d){const f=d.refs?d.refs[0]:d.ref;f.focus&&(f.focus(),a.shouldSelect&&ee(f.select)&&f.select())}},getFieldState:ze}}function st(e={}){const t=F.useRef(),r=F.useRef(),[i,u]=F.useState({isDirty:!1,isValidating:!1,isLoading:ee(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:ee(e.defaultValues)?void 0:e.defaultValues});t.current||(t.current={...Jr(e),formState:i});const n=t.current.control;return n._options=e,Se({subject:n._subjects.state,next:y=>{hr(y,n._proxyFormState,n._updateFormState,!0)&&u({...n._formState})}}),F.useEffect(()=>n._disableForm(e.disabled),[n,e.disabled]),F.useEffect(()=>{if(n._proxyFormState.isDirty){const y=n._getDirty();y!==i.isDirty&&n._subjects.state.next({isDirty:y})}},[n,i.isDirty]),F.useEffect(()=>{e.values&&!ie(e.values,r.current)?(n._reset(e.values,n._options.resetOptions),r.current=e.values,u(y=>({...y}))):n._resetDefaultValues()},[e.values,n]),F.useEffect(()=>{e.errors&&n._setErrors(e.errors)},[e.errors,n]),F.useEffect(()=>{n._state.mount||(n._updateValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()}),F.useEffect(()=>{e.shouldUnregister&&n._subjects.values.next({values:n._getWatch()})},[e.shouldUnregister,n]),t.current.formState=gr(i,n),t.current}export{rt as C,Ir as a,tt as b,c as g,k as s,st as u};
