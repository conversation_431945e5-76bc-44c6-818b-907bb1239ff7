import{i as g,t as u,g as D}from"./dashboard-4a19149e.js";import{y as a}from"../@fullcalendar/core-8ccc1ac4.js";import{U as d}from"./core-0760343f.js";const c={strings:{dropHereOr:"Drop here or %{browse}",browse:"browse"}},m={version:"3.1.1"},f={inputName:"files[]",width:"100%",height:"100%"};let v=class l extends d{constructor(s,i){super(s,{...f,...i}),this.isDragDropSupported=g(),this.addFiles=t=>{const r=t.map(e=>({source:this.id,name:e.name,type:e.type,data:e,meta:{relativePath:e.relativePath||null}}));try{this.uppy.addFiles(r)}catch(e){this.uppy.log(e)}},this.onInputChange=t=>{const r=u(t.target.files);r.length>0&&(this.uppy.log("[DragDrop] Files selected through input"),this.addFiles(r)),t.target.value=null},this.handleDragOver=t=>{var r,e;t.preventDefault(),t.stopPropagation();const{types:p}=t.dataTransfer,o=p.some(h=>h==="Files"),{allowNewUpload:n}=this.uppy.getState();if(!o||!n){t.dataTransfer.dropEffect="none",clearTimeout(this.removeDragOverClassTimeout);return}t.dataTransfer.dropEffect="copy",clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!0}),(r=(e=this.opts).onDragOver)==null||r.call(e,t)},this.handleDragLeave=t=>{var r,e;t.preventDefault(),t.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.removeDragOverClassTimeout=setTimeout(()=>{this.setPluginState({isDraggingOver:!1})},50),(r=(e=this.opts).onDragLeave)==null||r.call(e,t)},this.handleDrop=async t=>{var r,e;t.preventDefault(),t.stopPropagation(),clearTimeout(this.removeDragOverClassTimeout),this.setPluginState({isDraggingOver:!1});const p=n=>{this.uppy.log(n,"error")},o=await D(t.dataTransfer,{logDropError:p});o.length>0&&(this.uppy.log("[DragDrop] Files dropped"),this.addFiles(o)),(r=(e=this.opts).onDrop)==null||r.call(e,t)},this.type="acquirer",this.id=this.opts.id||"DragDrop",this.title="Drag & Drop",this.defaultLocale=c,this.i18nInit()}renderHiddenFileInput(){const{restrictions:s}=this.uppy.opts;return a("input",{className:"uppy-DragDrop-input",type:"file",hidden:!0,ref:i=>{this.fileInputRef=i},name:this.opts.inputName,multiple:s.maxNumberOfFiles!==1,accept:s.allowedFileTypes,onChange:this.onInputChange})}static renderArrowSvg(){return a("svg",{"aria-hidden":"true",focusable:"false",className:"uppy-c-icon uppy-DragDrop-arrow",width:"16",height:"16",viewBox:"0 0 16 16"},a("path",{d:"M11 10V0H5v10H2l6 6 6-6h-3zm0 0",fillRule:"evenodd"}))}renderLabel(){return a("div",{className:"uppy-DragDrop-label"},this.i18nArray("dropHereOr",{browse:a("span",{className:"uppy-DragDrop-browse"},this.i18n("browse"))}))}renderNote(){return a("span",{className:"uppy-DragDrop-note"},this.opts.note)}render(){const s=`uppy-u-reset
      uppy-DragDrop-container
      ${this.isDragDropSupported?"uppy-DragDrop--isDragDropSupported":""}
      ${this.getPluginState().isDraggingOver?"uppy-DragDrop--isDraggingOver":""}
    `,i={width:this.opts.width,height:this.opts.height};return a("button",{type:"button",className:s,style:i,onClick:()=>this.fileInputRef.click(),onDragOver:this.handleDragOver,onDragLeave:this.handleDragLeave,onDrop:this.handleDrop},this.renderHiddenFileInput(),a("div",{className:"uppy-DragDrop-inner"},l.renderArrowSvg(),this.renderLabel(),this.renderNote()))}install(){const{target:s}=this.opts;this.setPluginState({isDraggingOver:!1}),s&&this.mount(s,this)}uninstall(){this.unmount()}};v.VERSION=m.version;export{v as D};
