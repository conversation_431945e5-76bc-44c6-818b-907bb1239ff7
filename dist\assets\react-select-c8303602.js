import{_ as St,i as be,a as Ft}from"./@mantine/core-8cbffb6d.js";import{r as f,a as Ot}from"./vendor-851db8c1.js";import{k as Dt,j as E,_ as S,c as Ge}from"./@emotion/react-89b506c3.js";import{m as yt}from"./@uppy/dashboard-4a19149e.js";function Z(n){"@babel/helpers - typeof";return Z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Z(n)}function It(n,t){if(Z(n)!="object"||!n)return n;var i=n[Symbol.toPrimitive];if(i!==void 0){var r=i.call(n,t||"default");if(Z(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(n)}function Ye(n){var t=It(n,"string");return Z(t)=="symbol"?t:t+""}function ee(n,t,i){return(t=Ye(t))in n?Object.defineProperty(n,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[t]=i,n}function Pe(n,t){var i=Object.keys(n);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(n);t&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),i.push.apply(i,r)}return i}function C(n){for(var t=1;t<arguments.length;t++){var i=arguments[t]!=null?arguments[t]:{};t%2?Pe(Object(i),!0).forEach(function(r){ee(n,r,i[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(i)):Pe(Object(i)).forEach(function(r){Object.defineProperty(n,r,Object.getOwnPropertyDescriptor(i,r))})}return n}function At(n){if(Array.isArray(n))return n}function Pt(n,t){var i=n==null?null:typeof Symbol<"u"&&n[Symbol.iterator]||n["@@iterator"];if(i!=null){var r,e,o,a,l=[],u=!0,s=!1;try{if(o=(i=i.call(n)).next,t===0){if(Object(i)!==i)return;u=!1}else for(;!(u=(r=o.call(i)).done)&&(l.push(r.value),l.length!==t);u=!0);}catch(c){s=!0,e=c}finally{try{if(!u&&i.return!=null&&(a=i.return(),Object(a)!==a))return}finally{if(s)throw e}}return l}}function Ee(n,t){(t==null||t>n.length)&&(t=n.length);for(var i=0,r=Array(t);i<t;i++)r[i]=n[i];return r}function qe(n,t){if(n){if(typeof n=="string")return Ee(n,t);var i={}.toString.call(n).slice(8,-1);return i==="Object"&&n.constructor&&(i=n.constructor.name),i==="Map"||i==="Set"?Array.from(n):i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?Ee(n,t):void 0}}function Vt(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function W(n,t){return At(n)||Pt(n,t)||qe(n,t)||Vt()}function G(n,t){if(n==null)return{};var i,r,e=St(n,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(n);for(r=0;r<o.length;r++)i=o[r],t.includes(i)||{}.propertyIsEnumerable.call(n,i)&&(e[i]=n[i])}return e}var Mt=["defaultInputValue","defaultMenuIsOpen","defaultValue","inputValue","menuIsOpen","onChange","onInputChange","onMenuClose","onMenuOpen","value"];function xt(n){var t=n.defaultInputValue,i=t===void 0?"":t,r=n.defaultMenuIsOpen,e=r===void 0?!1:r,o=n.defaultValue,a=o===void 0?null:o,l=n.inputValue,u=n.menuIsOpen,s=n.onChange,c=n.onInputChange,d=n.onMenuClose,v=n.onMenuOpen,m=n.value,b=G(n,Mt),g=f.useState(l!==void 0?l:i),p=W(g,2),h=p[0],y=p[1],O=f.useState(u!==void 0?u:e),D=W(O,2),I=D[0],A=D[1],P=f.useState(m!==void 0?m:a),F=W(P,2),V=F[0],R=F[1],B=f.useCallback(function(H,Y){typeof s=="function"&&s(H,Y),R(H)},[s]),T=f.useCallback(function(H,Y){var q;typeof c=="function"&&(q=c(H,Y)),y(q!==void 0?q:H)},[c]),U=f.useCallback(function(){typeof v=="function"&&v(),A(!0)},[v]),j=f.useCallback(function(){typeof d=="function"&&d(),A(!1)},[d]),L=l!==void 0?l:h,x=u!==void 0?u:I,_=m!==void 0?m:V;return C(C({},b),{},{inputValue:L,menuIsOpen:x,onChange:B,onInputChange:T,onMenuClose:j,onMenuOpen:U,value:_})}function wt(n,t){if(!(n instanceof t))throw new TypeError("Cannot call a class as a function")}function Ve(n,t){for(var i=0;i<t.length;i++){var r=t[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,Ye(r.key),r)}}function Lt(n,t,i){return t&&Ve(n.prototype,t),i&&Ve(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n}function Ce(n,t){return Ce=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(i,r){return i.__proto__=r,i},Ce(n,t)}function Rt(n,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&Ce(n,t)}function ae(n){return ae=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ae(n)}function Ke(){try{var n=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ke=function(){return!!n})()}function Tt(n){if(n===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n}function Bt(n,t){if(t&&(Z(t)=="object"||typeof t=="function"))return t;if(t!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Tt(n)}function kt(n){var t=Ke();return function(){var i,r=ae(n);if(t){var e=ae(this).constructor;i=Reflect.construct(r,arguments,e)}else i=r.apply(this,arguments);return Bt(this,i)}}function Ht(n){if(Array.isArray(n))return Ee(n)}function $t(n){if(typeof Symbol<"u"&&n[Symbol.iterator]!=null||n["@@iterator"]!=null)return Array.from(n)}function _t(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Fe(n){return Ht(n)||$t(n)||qe(n)||_t()}function Ut(n,t){return t||(t=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(t)}}))}var jt=["className","clearValue","cx","getStyles","getClassNames","getValue","hasValue","isMulti","isRtl","options","selectOption","selectProps","setValue","theme"],se=function(){};function Nt(n,t){return t?t[0]==="-"?n+t:n+"__"+t:n}function zt(n,t){for(var i=arguments.length,r=new Array(i>2?i-2:0),e=2;e<i;e++)r[e-2]=arguments[e];var o=[].concat(r);if(t&&n)for(var a in t)t.hasOwnProperty(a)&&t[a]&&o.push("".concat(Nt(n,a)));return o.filter(function(l){return l}).map(function(l){return String(l).trim()}).join(" ")}var Me=function(t){return Qt(t)?t.filter(Boolean):Z(t)==="object"&&t!==null?[t]:[]},Xe=function(t){t.className,t.clearValue,t.cx,t.getStyles,t.getClassNames,t.getValue,t.hasValue,t.isMulti,t.isRtl,t.options,t.selectOption,t.selectProps,t.setValue,t.theme;var i=G(t,jt);return C({},i)},M=function(t,i,r){var e=t.cx,o=t.getStyles,a=t.getClassNames,l=t.className;return{css:o(i,t),className:e(r??{},a(i,t),l)}};function de(n){return[document.documentElement,document.body,window].indexOf(n)>-1}function Wt(n){return de(n)?window.innerHeight:n.clientHeight}function Ze(n){return de(n)?window.pageYOffset:n.scrollTop}function le(n,t){if(de(n)){window.scrollTo(0,t);return}n.scrollTop=t}function Gt(n){var t=getComputedStyle(n),i=t.position==="absolute",r=/(auto|scroll)/;if(t.position==="fixed")return document.documentElement;for(var e=n;e=e.parentElement;)if(t=getComputedStyle(e),!(i&&t.position==="static")&&r.test(t.overflow+t.overflowY+t.overflowX))return e;return document.documentElement}function Yt(n,t,i,r){return i*((n=n/r-1)*n*n+1)+t}function ie(n,t){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:200,r=arguments.length>3&&arguments[3]!==void 0?arguments[3]:se,e=Ze(n),o=t-e,a=10,l=0;function u(){l+=a;var s=Yt(l,e,o,i);le(n,s),l<i?window.requestAnimationFrame(u):r(n)}u()}function xe(n,t){var i=n.getBoundingClientRect(),r=t.getBoundingClientRect(),e=t.offsetHeight/3;r.bottom+e>i.bottom?le(n,Math.min(t.offsetTop+t.clientHeight-n.offsetHeight+e,n.scrollHeight)):r.top-e<i.top&&le(n,Math.max(t.offsetTop-e,0))}function qt(n){var t=n.getBoundingClientRect();return{bottom:t.bottom,height:t.height,left:t.left,right:t.right,top:t.top,width:t.width}}function we(){try{return document.createEvent("TouchEvent"),!0}catch{return!1}}function Kt(){try{return/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)}catch{return!1}}var Je=!1,Xt={get passive(){return Je=!0}},re=typeof window<"u"?window:{};re.addEventListener&&re.removeEventListener&&(re.addEventListener("p",se,Xt),re.removeEventListener("p",se,!1));var Zt=Je;function Jt(n){return n!=null}function Qt(n){return Array.isArray(n)}function ue(n,t,i){return n?t:i}var en=function(t){for(var i=arguments.length,r=new Array(i>1?i-1:0),e=1;e<i;e++)r[e-1]=arguments[e];var o=Object.entries(t).filter(function(a){var l=W(a,1),u=l[0];return!r.includes(u)});return o.reduce(function(a,l){var u=W(l,2),s=u[0],c=u[1];return a[s]=c,a},{})},tn=["children","innerProps"],nn=["children","innerProps"];function rn(n){var t=n.maxHeight,i=n.menuEl,r=n.minHeight,e=n.placement,o=n.shouldScroll,a=n.isFixedPosition,l=n.controlHeight,u=Gt(i),s={placement:"bottom",maxHeight:t};if(!i||!i.offsetParent)return s;var c=u.getBoundingClientRect(),d=c.height,v=i.getBoundingClientRect(),m=v.bottom,b=v.height,g=v.top,p=i.offsetParent.getBoundingClientRect(),h=p.top,y=a?window.innerHeight:Wt(u),O=Ze(u),D=parseInt(getComputedStyle(i).marginBottom,10),I=parseInt(getComputedStyle(i).marginTop,10),A=h-I,P=y-g,F=A+O,V=d-O-g,R=m-y+O+D,B=O+g-I,T=160;switch(e){case"auto":case"bottom":if(P>=b)return{placement:"bottom",maxHeight:t};if(V>=b&&!a)return o&&ie(u,R,T),{placement:"bottom",maxHeight:t};if(!a&&V>=r||a&&P>=r){o&&ie(u,R,T);var U=a?P-D:V-D;return{placement:"bottom",maxHeight:U}}if(e==="auto"||a){var j=t,L=a?A:F;return L>=r&&(j=Math.min(L-D-l,t)),{placement:"top",maxHeight:j}}if(e==="bottom")return o&&le(u,R),{placement:"bottom",maxHeight:t};break;case"top":if(A>=b)return{placement:"top",maxHeight:t};if(F>=b&&!a)return o&&ie(u,B,T),{placement:"top",maxHeight:t};if(!a&&F>=r||a&&A>=r){var x=t;return(!a&&F>=r||a&&A>=r)&&(x=a?A-I:F-I),o&&ie(u,B,T),{placement:"top",maxHeight:x}}return{placement:"bottom",maxHeight:t};default:throw new Error('Invalid placement provided "'.concat(e,'".'))}return s}function un(n){var t={bottom:"top",top:"bottom"};return n?t[n]:"bottom"}var Qe=function(t){return t==="auto"?"bottom":t},on=function(t,i){var r,e=t.placement,o=t.theme,a=o.borderRadius,l=o.spacing,u=o.colors;return C((r={label:"menu"},ee(r,un(e),"100%"),ee(r,"position","absolute"),ee(r,"width","100%"),ee(r,"zIndex",1),r),i?{}:{backgroundColor:u.neutral0,borderRadius:a,boxShadow:"0 0 0 1px hsla(0, 0%, 0%, 0.1), 0 4px 11px hsla(0, 0%, 0%, 0.1)",marginBottom:l.menuGutter,marginTop:l.menuGutter})},et=f.createContext(null),an=function(t){var i=t.children,r=t.minMenuHeight,e=t.maxMenuHeight,o=t.menuPlacement,a=t.menuPosition,l=t.menuShouldScrollIntoView,u=t.theme,s=f.useContext(et)||{},c=s.setPortalPlacement,d=f.useRef(null),v=f.useState(e),m=W(v,2),b=m[0],g=m[1],p=f.useState(null),h=W(p,2),y=h[0],O=h[1],D=u.spacing.controlHeight;return be(function(){var I=d.current;if(I){var A=a==="fixed",P=l&&!A,F=rn({maxHeight:e,menuEl:I,minHeight:r,placement:o,shouldScroll:P,isFixedPosition:A,controlHeight:D});g(F.maxHeight),O(F.placement),c==null||c(F.placement)}},[e,o,a,l,r,c,D]),i({ref:d,placerProps:C(C({},t),{},{placement:y||Qe(o),maxHeight:b})})},sn=function(t){var i=t.children,r=t.innerRef,e=t.innerProps;return E("div",S({},M(t,"menu",{menu:!0}),{ref:r},e),i)},ln=sn,cn=function(t,i){var r=t.maxHeight,e=t.theme.spacing.baseUnit;return C({maxHeight:r,overflowY:"auto",position:"relative",WebkitOverflowScrolling:"touch"},i?{}:{paddingBottom:e,paddingTop:e})},dn=function(t){var i=t.children,r=t.innerProps,e=t.innerRef,o=t.isMulti;return E("div",S({},M(t,"menuList",{"menu-list":!0,"menu-list--is-multi":o}),{ref:e},r),i)},tt=function(t,i){var r=t.theme,e=r.spacing.baseUnit,o=r.colors;return C({textAlign:"center"},i?{}:{color:o.neutral40,padding:"".concat(e*2,"px ").concat(e*3,"px")})},pn=tt,fn=tt,vn=function(t){var i=t.children,r=i===void 0?"No options":i,e=t.innerProps,o=G(t,tn);return E("div",S({},M(C(C({},o),{},{children:r,innerProps:e}),"noOptionsMessage",{"menu-notice":!0,"menu-notice--no-options":!0}),e),r)},mn=function(t){var i=t.children,r=i===void 0?"Loading...":i,e=t.innerProps,o=G(t,nn);return E("div",S({},M(C(C({},o),{},{children:r,innerProps:e}),"loadingMessage",{"menu-notice":!0,"menu-notice--loading":!0}),e),r)},hn=function(t){var i=t.rect,r=t.offset,e=t.position;return{left:i.left,position:e,top:r,width:i.width,zIndex:1}},gn=function(t){var i=t.appendTo,r=t.children,e=t.controlElement,o=t.innerProps,a=t.menuPlacement,l=t.menuPosition,u=f.useRef(null),s=f.useRef(null),c=f.useState(Qe(a)),d=W(c,2),v=d[0],m=d[1],b=f.useMemo(function(){return{setPortalPlacement:m}},[]),g=f.useState(null),p=W(g,2),h=p[0],y=p[1],O=f.useCallback(function(){if(e){var P=qt(e),F=l==="fixed"?0:window.pageYOffset,V=P[v]+F;(V!==(h==null?void 0:h.offset)||P.left!==(h==null?void 0:h.rect.left)||P.width!==(h==null?void 0:h.rect.width))&&y({offset:V,rect:P})}},[e,l,v,h==null?void 0:h.offset,h==null?void 0:h.rect.left,h==null?void 0:h.rect.width]);be(function(){O()},[O]);var D=f.useCallback(function(){typeof s.current=="function"&&(s.current(),s.current=null),e&&u.current&&(s.current=Ft(e,u.current,O,{elementResize:"ResizeObserver"in window}))},[e,O]);be(function(){D()},[D]);var I=f.useCallback(function(P){u.current=P,D()},[D]);if(!i&&l!=="fixed"||!h)return null;var A=E("div",S({ref:I},M(C(C({},t),{},{offset:h.offset,position:l,rect:h.rect}),"menuPortal",{"menu-portal":!0}),o),r);return E(et.Provider,{value:b},i?Ot.createPortal(A,i):A)},bn=function(t){var i=t.isDisabled,r=t.isRtl;return{label:"container",direction:r?"rtl":void 0,pointerEvents:i?"none":void 0,position:"relative"}},En=function(t){var i=t.children,r=t.innerProps,e=t.isDisabled,o=t.isRtl;return E("div",S({},M(t,"container",{"--is-disabled":e,"--is-rtl":o}),r),i)},Cn=function(t,i){var r=t.theme.spacing,e=t.isMulti,o=t.hasValue,a=t.selectProps.controlShouldRenderValue;return C({alignItems:"center",display:e&&o&&a?"flex":"grid",flex:1,flexWrap:"wrap",WebkitOverflowScrolling:"touch",position:"relative",overflow:"hidden"},i?{}:{padding:"".concat(r.baseUnit/2,"px ").concat(r.baseUnit*2,"px")})},Sn=function(t){var i=t.children,r=t.innerProps,e=t.isMulti,o=t.hasValue;return E("div",S({},M(t,"valueContainer",{"value-container":!0,"value-container--is-multi":e,"value-container--has-value":o}),r),i)},Fn=function(){return{alignItems:"center",alignSelf:"stretch",display:"flex",flexShrink:0}},On=function(t){var i=t.children,r=t.innerProps;return E("div",S({},M(t,"indicatorsContainer",{indicators:!0}),r),i)},Le,Dn=["size"],yn=["innerProps","isRtl","size"],In={name:"8mmkcg",styles:"display:inline-block;fill:currentColor;line-height:1;stroke:currentColor;stroke-width:0"},nt=function(t){var i=t.size,r=G(t,Dn);return E("svg",S({height:i,width:i,viewBox:"0 0 20 20","aria-hidden":"true",focusable:"false",css:In},r))},Oe=function(t){return E(nt,S({size:20},t),E("path",{d:"M14.348 14.849c-0.469 0.469-1.229 0.469-1.697 0l-2.651-3.030-2.651 3.029c-0.469 0.469-1.229 0.469-1.697 0-0.469-0.469-0.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-0.469-0.469-0.469-1.228 0-1.697s1.228-0.469 1.697 0l2.652 3.031 2.651-3.031c0.469-0.469 1.228-0.469 1.697 0s0.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c0.469 0.469 0.469 1.229 0 1.698z"}))},it=function(t){return E(nt,S({size:20},t),E("path",{d:"M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"}))},rt=function(t,i){var r=t.isFocused,e=t.theme,o=e.spacing.baseUnit,a=e.colors;return C({label:"indicatorContainer",display:"flex",transition:"color 150ms"},i?{}:{color:r?a.neutral60:a.neutral20,padding:o*2,":hover":{color:r?a.neutral80:a.neutral40}})},An=rt,Pn=function(t){var i=t.children,r=t.innerProps;return E("div",S({},M(t,"dropdownIndicator",{indicator:!0,"dropdown-indicator":!0}),r),i||E(it,null))},Vn=rt,Mn=function(t){var i=t.children,r=t.innerProps;return E("div",S({},M(t,"clearIndicator",{indicator:!0,"clear-indicator":!0}),r),i||E(Oe,null))},xn=function(t,i){var r=t.isDisabled,e=t.theme,o=e.spacing.baseUnit,a=e.colors;return C({label:"indicatorSeparator",alignSelf:"stretch",width:1},i?{}:{backgroundColor:r?a.neutral10:a.neutral20,marginBottom:o*2,marginTop:o*2})},wn=function(t){var i=t.innerProps;return E("span",S({},i,M(t,"indicatorSeparator",{"indicator-separator":!0})))},Ln=Dt(Le||(Le=Ut([`
  0%, 80%, 100% { opacity: 0; }
  40% { opacity: 1; }
`]))),Rn=function(t,i){var r=t.isFocused,e=t.size,o=t.theme,a=o.colors,l=o.spacing.baseUnit;return C({label:"loadingIndicator",display:"flex",transition:"color 150ms",alignSelf:"center",fontSize:e,lineHeight:1,marginRight:e,textAlign:"center",verticalAlign:"middle"},i?{}:{color:r?a.neutral60:a.neutral20,padding:l*2})},pe=function(t){var i=t.delay,r=t.offset;return E("span",{css:Ge({animation:"".concat(Ln," 1s ease-in-out ").concat(i,"ms infinite;"),backgroundColor:"currentColor",borderRadius:"1em",display:"inline-block",marginLeft:r?"1em":void 0,height:"1em",verticalAlign:"top",width:"1em"},"","")})},Tn=function(t){var i=t.innerProps,r=t.isRtl,e=t.size,o=e===void 0?4:e,a=G(t,yn);return E("div",S({},M(C(C({},a),{},{innerProps:i,isRtl:r,size:o}),"loadingIndicator",{indicator:!0,"loading-indicator":!0}),i),E(pe,{delay:0,offset:r}),E(pe,{delay:160,offset:!0}),E(pe,{delay:320,offset:!r}))},Bn=function(t,i){var r=t.isDisabled,e=t.isFocused,o=t.theme,a=o.colors,l=o.borderRadius,u=o.spacing;return C({label:"control",alignItems:"center",cursor:"default",display:"flex",flexWrap:"wrap",justifyContent:"space-between",minHeight:u.controlHeight,outline:"0 !important",position:"relative",transition:"all 100ms"},i?{}:{backgroundColor:r?a.neutral5:a.neutral0,borderColor:r?a.neutral10:e?a.primary:a.neutral20,borderRadius:l,borderStyle:"solid",borderWidth:1,boxShadow:e?"0 0 0 1px ".concat(a.primary):void 0,"&:hover":{borderColor:e?a.primary:a.neutral30}})},kn=function(t){var i=t.children,r=t.isDisabled,e=t.isFocused,o=t.innerRef,a=t.innerProps,l=t.menuIsOpen;return E("div",S({ref:o},M(t,"control",{control:!0,"control--is-disabled":r,"control--is-focused":e,"control--menu-is-open":l}),a,{"aria-disabled":r||void 0}),i)},Hn=kn,$n=["data"],_n=function(t,i){var r=t.theme.spacing;return i?{}:{paddingBottom:r.baseUnit*2,paddingTop:r.baseUnit*2}},Un=function(t){var i=t.children,r=t.cx,e=t.getStyles,o=t.getClassNames,a=t.Heading,l=t.headingProps,u=t.innerProps,s=t.label,c=t.theme,d=t.selectProps;return E("div",S({},M(t,"group",{group:!0}),u),E(a,S({},l,{selectProps:d,theme:c,getStyles:e,getClassNames:o,cx:r}),s),E("div",null,i))},jn=function(t,i){var r=t.theme,e=r.colors,o=r.spacing;return C({label:"group",cursor:"default",display:"block"},i?{}:{color:e.neutral40,fontSize:"75%",fontWeight:500,marginBottom:"0.25em",paddingLeft:o.baseUnit*3,paddingRight:o.baseUnit*3,textTransform:"uppercase"})},Nn=function(t){var i=Xe(t);i.data;var r=G(i,$n);return E("div",S({},M(t,"groupHeading",{"group-heading":!0}),r))},zn=Un,Wn=["innerRef","isDisabled","isHidden","inputClassName"],Gn=function(t,i){var r=t.isDisabled,e=t.value,o=t.theme,a=o.spacing,l=o.colors;return C(C({visibility:r?"hidden":"visible",transform:e?"translateZ(0)":""},Yn),i?{}:{margin:a.baseUnit/2,paddingBottom:a.baseUnit/2,paddingTop:a.baseUnit/2,color:l.neutral80})},ut={gridArea:"1 / 2",font:"inherit",minWidth:"2px",border:0,margin:0,outline:0,padding:0},Yn={flex:"1 1 auto",display:"inline-grid",gridArea:"1 / 1 / 2 / 3",gridTemplateColumns:"0 min-content","&:after":C({content:'attr(data-value) " "',visibility:"hidden",whiteSpace:"pre"},ut)},qn=function(t){return C({label:"input",color:"inherit",background:0,opacity:t?0:1,width:"100%"},ut)},Kn=function(t){var i=t.cx,r=t.value,e=Xe(t),o=e.innerRef,a=e.isDisabled,l=e.isHidden,u=e.inputClassName,s=G(e,Wn);return E("div",S({},M(t,"input",{"input-container":!0}),{"data-value":r||""}),E("input",S({className:i({input:!0},u),ref:o,style:qn(l),disabled:a},s)))},Xn=Kn,Zn=function(t,i){var r=t.theme,e=r.spacing,o=r.borderRadius,a=r.colors;return C({label:"multiValue",display:"flex",minWidth:0},i?{}:{backgroundColor:a.neutral10,borderRadius:o/2,margin:e.baseUnit/2})},Jn=function(t,i){var r=t.theme,e=r.borderRadius,o=r.colors,a=t.cropWithEllipsis;return C({overflow:"hidden",textOverflow:a||a===void 0?"ellipsis":void 0,whiteSpace:"nowrap"},i?{}:{borderRadius:e/2,color:o.neutral80,fontSize:"85%",padding:3,paddingLeft:6})},Qn=function(t,i){var r=t.theme,e=r.spacing,o=r.borderRadius,a=r.colors,l=t.isFocused;return C({alignItems:"center",display:"flex"},i?{}:{borderRadius:o/2,backgroundColor:l?a.dangerLight:void 0,paddingLeft:e.baseUnit,paddingRight:e.baseUnit,":hover":{backgroundColor:a.dangerLight,color:a.danger}})},ot=function(t){var i=t.children,r=t.innerProps;return E("div",r,i)},ei=ot,ti=ot;function ni(n){var t=n.children,i=n.innerProps;return E("div",S({role:"button"},i),t||E(Oe,{size:14}))}var ii=function(t){var i=t.children,r=t.components,e=t.data,o=t.innerProps,a=t.isDisabled,l=t.removeProps,u=t.selectProps,s=r.Container,c=r.Label,d=r.Remove;return E(s,{data:e,innerProps:C(C({},M(t,"multiValue",{"multi-value":!0,"multi-value--is-disabled":a})),o),selectProps:u},E(c,{data:e,innerProps:C({},M(t,"multiValueLabel",{"multi-value__label":!0})),selectProps:u},i),E(d,{data:e,innerProps:C(C({},M(t,"multiValueRemove",{"multi-value__remove":!0})),{},{"aria-label":"Remove ".concat(i||"option")},l),selectProps:u}))},ri=ii,ui=function(t,i){var r=t.isDisabled,e=t.isFocused,o=t.isSelected,a=t.theme,l=a.spacing,u=a.colors;return C({label:"option",cursor:"default",display:"block",fontSize:"inherit",width:"100%",userSelect:"none",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)"},i?{}:{backgroundColor:o?u.primary:e?u.primary25:"transparent",color:r?u.neutral20:o?u.neutral0:"inherit",padding:"".concat(l.baseUnit*2,"px ").concat(l.baseUnit*3,"px"),":active":{backgroundColor:r?void 0:o?u.primary:u.primary50}})},oi=function(t){var i=t.children,r=t.isDisabled,e=t.isFocused,o=t.isSelected,a=t.innerRef,l=t.innerProps;return E("div",S({},M(t,"option",{option:!0,"option--is-disabled":r,"option--is-focused":e,"option--is-selected":o}),{ref:a,"aria-disabled":r},l),i)},ai=oi,si=function(t,i){var r=t.theme,e=r.spacing,o=r.colors;return C({label:"placeholder",gridArea:"1 / 1 / 2 / 3"},i?{}:{color:o.neutral50,marginLeft:e.baseUnit/2,marginRight:e.baseUnit/2})},li=function(t){var i=t.children,r=t.innerProps;return E("div",S({},M(t,"placeholder",{placeholder:!0}),r),i)},ci=li,di=function(t,i){var r=t.isDisabled,e=t.theme,o=e.spacing,a=e.colors;return C({label:"singleValue",gridArea:"1 / 1 / 2 / 3",maxWidth:"100%",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},i?{}:{color:r?a.neutral40:a.neutral80,marginLeft:o.baseUnit/2,marginRight:o.baseUnit/2})},pi=function(t){var i=t.children,r=t.isDisabled,e=t.innerProps;return E("div",S({},M(t,"singleValue",{"single-value":!0,"single-value--is-disabled":r}),e),i)},fi=pi,vi={ClearIndicator:Mn,Control:Hn,DropdownIndicator:Pn,DownChevron:it,CrossIcon:Oe,Group:zn,GroupHeading:Nn,IndicatorsContainer:On,IndicatorSeparator:wn,Input:Xn,LoadingIndicator:Tn,Menu:ln,MenuList:dn,MenuPortal:gn,LoadingMessage:mn,NoOptionsMessage:vn,MultiValue:ri,MultiValueContainer:ei,MultiValueLabel:ti,MultiValueRemove:ni,Option:ai,Placeholder:ci,SelectContainer:En,SingleValue:fi,ValueContainer:Sn},mi=function(t){return C(C({},vi),t.components)},hi={name:"7pg0cj-a11yText",styles:"label:a11yText;z-index:9999;border:0;clip:rect(1px, 1px, 1px, 1px);height:1px;width:1px;position:absolute;overflow:hidden;padding:0;white-space:nowrap"},gi=function(t){return E("span",S({css:hi},t))},Re=gi,bi={guidance:function(t){var i=t.isSearchable,r=t.isMulti,e=t.tabSelectsValue,o=t.context,a=t.isInitialFocus;switch(o){case"menu":return"Use Up and Down to choose options, press Enter to select the currently focused option, press Escape to exit the menu".concat(e?", press Tab to select the option and exit the menu":"",".");case"input":return a?"".concat(t["aria-label"]||"Select"," is focused ").concat(i?",type to refine list":"",", press Down to open the menu, ").concat(r?" press left to focus selected values":""):"";case"value":return"Use left and right to toggle between focused values, press Backspace to remove the currently focused value";default:return""}},onChange:function(t){var i=t.action,r=t.label,e=r===void 0?"":r,o=t.labels,a=t.isDisabled;switch(i){case"deselect-option":case"pop-value":case"remove-value":return"option ".concat(e,", deselected.");case"clear":return"All selected options have been cleared.";case"initial-input-focus":return"option".concat(o.length>1?"s":""," ").concat(o.join(","),", selected.");case"select-option":return a?"option ".concat(e," is disabled. Select another option."):"option ".concat(e,", selected.");default:return""}},onFocus:function(t){var i=t.context,r=t.focused,e=t.options,o=t.label,a=o===void 0?"":o,l=t.selectValue,u=t.isDisabled,s=t.isSelected,c=t.isAppleDevice,d=function(g,p){return g&&g.length?"".concat(g.indexOf(p)+1," of ").concat(g.length):""};if(i==="value"&&l)return"value ".concat(a," focused, ").concat(d(l,r),".");if(i==="menu"&&c){var v=u?" disabled":"",m="".concat(s?" selected":"").concat(v);return"".concat(a).concat(m,", ").concat(d(e,r),".")}return""},onFilter:function(t){var i=t.inputValue,r=t.resultsMessage;return"".concat(r).concat(i?" for search term "+i:"",".")}},Ei=function(t){var i=t.ariaSelection,r=t.focusedOption,e=t.focusedValue,o=t.focusableOptions,a=t.isFocused,l=t.selectValue,u=t.selectProps,s=t.id,c=t.isAppleDevice,d=u.ariaLiveMessages,v=u.getOptionLabel,m=u.inputValue,b=u.isMulti,g=u.isOptionDisabled,p=u.isSearchable,h=u.menuIsOpen,y=u.options,O=u.screenReaderStatus,D=u.tabSelectsValue,I=u.isLoading,A=u["aria-label"],P=u["aria-live"],F=f.useMemo(function(){return C(C({},bi),d||{})},[d]),V=f.useMemo(function(){var L="";if(i&&F.onChange){var x=i.option,_=i.options,H=i.removedValue,Y=i.removedValues,q=i.value,te=function(z){return Array.isArray(z)?null:z},w=H||x||te(q),k=w?v(w):"",N=_||Y||void 0,K=N?N.map(v):[],$=C({isDisabled:w&&g(w,l),label:k,labels:K},i);L=F.onChange($)}return L},[i,F,g,l,v]),R=f.useMemo(function(){var L="",x=r||e,_=!!(r&&l&&l.includes(r));if(x&&F.onFocus){var H={focused:x,label:v(x),isDisabled:g(x,l),isSelected:_,options:o,context:x===r?"menu":"value",selectValue:l,isAppleDevice:c};L=F.onFocus(H)}return L},[r,e,v,g,F,o,l,c]),B=f.useMemo(function(){var L="";if(h&&y.length&&!I&&F.onFilter){var x=O({count:o.length});L=F.onFilter({inputValue:m,resultsMessage:x})}return L},[o,m,h,F,y,O,I]),T=(i==null?void 0:i.action)==="initial-input-focus",U=f.useMemo(function(){var L="";if(F.guidance){var x=e?"value":h?"menu":"input";L=F.guidance({"aria-label":A,context:x,isDisabled:r&&g(r,l),isMulti:b,isSearchable:p,tabSelectsValue:D,isInitialFocus:T})}return L},[A,r,e,b,g,p,h,F,l,D,T]),j=E(f.Fragment,null,E("span",{id:"aria-selection"},V),E("span",{id:"aria-focused"},R),E("span",{id:"aria-results"},B),E("span",{id:"aria-guidance"},U));return E(f.Fragment,null,E(Re,{id:s},T&&j),E(Re,{"aria-live":P,"aria-atomic":"false","aria-relevant":"additions text",role:"log"},a&&!T&&j))},Ci=Ei,Se=[{base:"A",letters:"AⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ"},{base:"AA",letters:"Ꜳ"},{base:"AE",letters:"ÆǼǢ"},{base:"AO",letters:"Ꜵ"},{base:"AU",letters:"Ꜷ"},{base:"AV",letters:"ꜸꜺ"},{base:"AY",letters:"Ꜽ"},{base:"B",letters:"BⒷＢḂḄḆɃƂƁ"},{base:"C",letters:"CⒸＣĆĈĊČÇḈƇȻꜾ"},{base:"D",letters:"DⒹＤḊĎḌḐḒḎĐƋƊƉꝹ"},{base:"DZ",letters:"ǱǄ"},{base:"Dz",letters:"ǲǅ"},{base:"E",letters:"EⒺＥÈÉÊỀẾỄỂẼĒḔḖĔĖËẺĚȄȆẸỆȨḜĘḘḚƐƎ"},{base:"F",letters:"FⒻＦḞƑꝻ"},{base:"G",letters:"GⒼＧǴĜḠĞĠǦĢǤƓꞠꝽꝾ"},{base:"H",letters:"HⒽＨĤḢḦȞḤḨḪĦⱧⱵꞍ"},{base:"I",letters:"IⒾＩÌÍÎĨĪĬİÏḮỈǏȈȊỊĮḬƗ"},{base:"J",letters:"JⒿＪĴɈ"},{base:"K",letters:"KⓀＫḰǨḲĶḴƘⱩꝀꝂꝄꞢ"},{base:"L",letters:"LⓁＬĿĹĽḶḸĻḼḺŁȽⱢⱠꝈꝆꞀ"},{base:"LJ",letters:"Ǉ"},{base:"Lj",letters:"ǈ"},{base:"M",letters:"MⓂＭḾṀṂⱮƜ"},{base:"N",letters:"NⓃＮǸŃÑṄŇṆŅṊṈȠƝꞐꞤ"},{base:"NJ",letters:"Ǌ"},{base:"Nj",letters:"ǋ"},{base:"O",letters:"OⓄＯÒÓÔỒỐỖỔÕṌȬṎŌṐṒŎȮȰÖȪỎŐǑȌȎƠỜỚỠỞỢỌỘǪǬØǾƆƟꝊꝌ"},{base:"OI",letters:"Ƣ"},{base:"OO",letters:"Ꝏ"},{base:"OU",letters:"Ȣ"},{base:"P",letters:"PⓅＰṔṖƤⱣꝐꝒꝔ"},{base:"Q",letters:"QⓆＱꝖꝘɊ"},{base:"R",letters:"RⓇＲŔṘŘȐȒṚṜŖṞɌⱤꝚꞦꞂ"},{base:"S",letters:"SⓈＳẞŚṤŜṠŠṦṢṨȘŞⱾꞨꞄ"},{base:"T",letters:"TⓉＴṪŤṬȚŢṰṮŦƬƮȾꞆ"},{base:"TZ",letters:"Ꜩ"},{base:"U",letters:"UⓊＵÙÚÛŨṸŪṺŬÜǛǗǕǙỦŮŰǓȔȖƯỪỨỮỬỰỤṲŲṶṴɄ"},{base:"V",letters:"VⓋＶṼṾƲꝞɅ"},{base:"VY",letters:"Ꝡ"},{base:"W",letters:"WⓌＷẀẂŴẆẄẈⱲ"},{base:"X",letters:"XⓍＸẊẌ"},{base:"Y",letters:"YⓎＹỲÝŶỸȲẎŸỶỴƳɎỾ"},{base:"Z",letters:"ZⓏＺŹẐŻŽẒẔƵȤⱿⱫꝢ"},{base:"a",letters:"aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐ"},{base:"aa",letters:"ꜳ"},{base:"ae",letters:"æǽǣ"},{base:"ao",letters:"ꜵ"},{base:"au",letters:"ꜷ"},{base:"av",letters:"ꜹꜻ"},{base:"ay",letters:"ꜽ"},{base:"b",letters:"bⓑｂḃḅḇƀƃɓ"},{base:"c",letters:"cⓒｃćĉċčçḉƈȼꜿↄ"},{base:"d",letters:"dⓓｄḋďḍḑḓḏđƌɖɗꝺ"},{base:"dz",letters:"ǳǆ"},{base:"e",letters:"eⓔｅèéêềếễểẽēḕḗĕėëẻěȅȇẹệȩḝęḙḛɇɛǝ"},{base:"f",letters:"fⓕｆḟƒꝼ"},{base:"g",letters:"gⓖｇǵĝḡğġǧģǥɠꞡᵹꝿ"},{base:"h",letters:"hⓗｈĥḣḧȟḥḩḫẖħⱨⱶɥ"},{base:"hv",letters:"ƕ"},{base:"i",letters:"iⓘｉìíîĩīĭïḯỉǐȉȋịįḭɨı"},{base:"j",letters:"jⓙｊĵǰɉ"},{base:"k",letters:"kⓚｋḱǩḳķḵƙⱪꝁꝃꝅꞣ"},{base:"l",letters:"lⓛｌŀĺľḷḹļḽḻſłƚɫⱡꝉꞁꝇ"},{base:"lj",letters:"ǉ"},{base:"m",letters:"mⓜｍḿṁṃɱɯ"},{base:"n",letters:"nⓝｎǹńñṅňṇņṋṉƞɲŉꞑꞥ"},{base:"nj",letters:"ǌ"},{base:"o",letters:"oⓞｏòóôồốỗổõṍȭṏōṑṓŏȯȱöȫỏőǒȍȏơờớỡởợọộǫǭøǿɔꝋꝍɵ"},{base:"oi",letters:"ƣ"},{base:"ou",letters:"ȣ"},{base:"oo",letters:"ꝏ"},{base:"p",letters:"pⓟｐṕṗƥᵽꝑꝓꝕ"},{base:"q",letters:"qⓠｑɋꝗꝙ"},{base:"r",letters:"rⓡｒŕṙřȑȓṛṝŗṟɍɽꝛꞧꞃ"},{base:"s",letters:"sⓢｓßśṥŝṡšṧṣṩșşȿꞩꞅẛ"},{base:"t",letters:"tⓣｔṫẗťṭțţṱṯŧƭʈⱦꞇ"},{base:"tz",letters:"ꜩ"},{base:"u",letters:"uⓤｕùúûũṹūṻŭüǜǘǖǚủůűǔȕȗưừứữửựụṳųṷṵʉ"},{base:"v",letters:"vⓥｖṽṿʋꝟʌ"},{base:"vy",letters:"ꝡ"},{base:"w",letters:"wⓦｗẁẃŵẇẅẘẉⱳ"},{base:"x",letters:"xⓧｘẋẍ"},{base:"y",letters:"yⓨｙỳýŷỹȳẏÿỷẙỵƴɏỿ"},{base:"z",letters:"zⓩｚźẑżžẓẕƶȥɀⱬꝣ"}],Si=new RegExp("["+Se.map(function(n){return n.letters}).join("")+"]","g"),at={};for(var fe=0;fe<Se.length;fe++)for(var ve=Se[fe],me=0;me<ve.letters.length;me++)at[ve.letters[me]]=ve.base;var st=function(t){return t.replace(Si,function(i){return at[i]})},Fi=yt(st),Te=function(t){return t.replace(/^\s+|\s+$/g,"")},Oi=function(t){return"".concat(t.label," ").concat(t.value)},Di=function(t){return function(i,r){if(i.data.__isNew__)return!0;var e=C({ignoreCase:!0,ignoreAccents:!0,stringify:Oi,trim:!0,matchFrom:"any"},t),o=e.ignoreCase,a=e.ignoreAccents,l=e.stringify,u=e.trim,s=e.matchFrom,c=u?Te(r):r,d=u?Te(l(i)):l(i);return o&&(c=c.toLowerCase(),d=d.toLowerCase()),a&&(c=Fi(c),d=st(d)),s==="start"?d.substr(0,c.length)===c:d.indexOf(c)>-1}},yi=["innerRef"];function Ii(n){var t=n.innerRef,i=G(n,yi),r=en(i,"onExited","in","enter","exit","appear");return E("input",S({ref:t},r,{css:Ge({label:"dummyInput",background:0,border:0,caretColor:"transparent",fontSize:"inherit",gridArea:"1 / 1 / 2 / 3",outline:0,padding:0,width:1,color:"transparent",left:-100,opacity:0,position:"relative",transform:"scale(.01)"},"","")}))}var Ai=function(t){t.cancelable&&t.preventDefault(),t.stopPropagation()};function Pi(n){var t=n.isEnabled,i=n.onBottomArrive,r=n.onBottomLeave,e=n.onTopArrive,o=n.onTopLeave,a=f.useRef(!1),l=f.useRef(!1),u=f.useRef(0),s=f.useRef(null),c=f.useCallback(function(p,h){if(s.current!==null){var y=s.current,O=y.scrollTop,D=y.scrollHeight,I=y.clientHeight,A=s.current,P=h>0,F=D-I-O,V=!1;F>h&&a.current&&(r&&r(p),a.current=!1),P&&l.current&&(o&&o(p),l.current=!1),P&&h>F?(i&&!a.current&&i(p),A.scrollTop=D,V=!0,a.current=!0):!P&&-h>O&&(e&&!l.current&&e(p),A.scrollTop=0,V=!0,l.current=!0),V&&Ai(p)}},[i,r,e,o]),d=f.useCallback(function(p){c(p,p.deltaY)},[c]),v=f.useCallback(function(p){u.current=p.changedTouches[0].clientY},[]),m=f.useCallback(function(p){var h=u.current-p.changedTouches[0].clientY;c(p,h)},[c]),b=f.useCallback(function(p){if(p){var h=Zt?{passive:!1}:!1;p.addEventListener("wheel",d,h),p.addEventListener("touchstart",v,h),p.addEventListener("touchmove",m,h)}},[m,v,d]),g=f.useCallback(function(p){p&&(p.removeEventListener("wheel",d,!1),p.removeEventListener("touchstart",v,!1),p.removeEventListener("touchmove",m,!1))},[m,v,d]);return f.useEffect(function(){if(t){var p=s.current;return b(p),function(){g(p)}}},[t,b,g]),function(p){s.current=p}}var Be=["boxSizing","height","overflow","paddingRight","position"],ke={boxSizing:"border-box",overflow:"hidden",position:"relative",height:"100%"};function He(n){n.cancelable&&n.preventDefault()}function $e(n){n.stopPropagation()}function _e(){var n=this.scrollTop,t=this.scrollHeight,i=n+this.offsetHeight;n===0?this.scrollTop=1:i===t&&(this.scrollTop=n-1)}function Ue(){return"ontouchstart"in window||navigator.maxTouchPoints}var je=!!(typeof window<"u"&&window.document&&window.document.createElement),Q=0,J={capture:!1,passive:!1};function Vi(n){var t=n.isEnabled,i=n.accountForScrollbars,r=i===void 0?!0:i,e=f.useRef({}),o=f.useRef(null),a=f.useCallback(function(u){if(je){var s=document.body,c=s&&s.style;if(r&&Be.forEach(function(b){var g=c&&c[b];e.current[b]=g}),r&&Q<1){var d=parseInt(e.current.paddingRight,10)||0,v=document.body?document.body.clientWidth:0,m=window.innerWidth-v+d||0;Object.keys(ke).forEach(function(b){var g=ke[b];c&&(c[b]=g)}),c&&(c.paddingRight="".concat(m,"px"))}s&&Ue()&&(s.addEventListener("touchmove",He,J),u&&(u.addEventListener("touchstart",_e,J),u.addEventListener("touchmove",$e,J))),Q+=1}},[r]),l=f.useCallback(function(u){if(je){var s=document.body,c=s&&s.style;Q=Math.max(Q-1,0),r&&Q<1&&Be.forEach(function(d){var v=e.current[d];c&&(c[d]=v)}),s&&Ue()&&(s.removeEventListener("touchmove",He,J),u&&(u.removeEventListener("touchstart",_e,J),u.removeEventListener("touchmove",$e,J)))}},[r]);return f.useEffect(function(){if(t){var u=o.current;return a(u),function(){l(u)}}},[t,a,l]),function(u){o.current=u}}var Mi=function(t){var i=t.target;return i.ownerDocument.activeElement&&i.ownerDocument.activeElement.blur()},xi={name:"1kfdb0e",styles:"position:fixed;left:0;bottom:0;right:0;top:0"};function wi(n){var t=n.children,i=n.lockEnabled,r=n.captureEnabled,e=r===void 0?!0:r,o=n.onBottomArrive,a=n.onBottomLeave,l=n.onTopArrive,u=n.onTopLeave,s=Pi({isEnabled:e,onBottomArrive:o,onBottomLeave:a,onTopArrive:l,onTopLeave:u}),c=Vi({isEnabled:i}),d=function(m){s(m),c(m)};return E(f.Fragment,null,i&&E("div",{onClick:Mi,css:xi}),t(d))}var Li={name:"1a0ro4n-requiredInput",styles:"label:requiredInput;opacity:0;pointer-events:none;position:absolute;bottom:0;left:0;right:0;width:100%"},Ri=function(t){var i=t.name,r=t.onFocus;return E("input",{required:!0,name:i,tabIndex:-1,"aria-hidden":"true",onFocus:r,css:Li,value:"",onChange:function(){}})},Ti=Ri;function De(n){var t;return typeof window<"u"&&window.navigator!=null?n.test(((t=window.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||window.navigator.platform):!1}function Bi(){return De(/^iPhone/i)}function lt(){return De(/^Mac/i)}function ki(){return De(/^iPad/i)||lt()&&navigator.maxTouchPoints>1}function Hi(){return Bi()||ki()}function $i(){return lt()||Hi()}var _i=function(t){return t.label},Ui=function(t){return t.label},ji=function(t){return t.value},Ni=function(t){return!!t.isDisabled},zi={clearIndicator:Vn,container:bn,control:Bn,dropdownIndicator:An,group:_n,groupHeading:jn,indicatorsContainer:Fn,indicatorSeparator:xn,input:Gn,loadingIndicator:Rn,loadingMessage:fn,menu:on,menuList:cn,menuPortal:hn,multiValue:Zn,multiValueLabel:Jn,multiValueRemove:Qn,noOptionsMessage:pn,option:ui,placeholder:si,singleValue:di,valueContainer:Cn},Wi={primary:"#2684FF",primary75:"#4C9AFF",primary50:"#B2D4FF",primary25:"#DEEBFF",danger:"#DE350B",dangerLight:"#FFBDAD",neutral0:"hsl(0, 0%, 100%)",neutral5:"hsl(0, 0%, 95%)",neutral10:"hsl(0, 0%, 90%)",neutral20:"hsl(0, 0%, 80%)",neutral30:"hsl(0, 0%, 70%)",neutral40:"hsl(0, 0%, 60%)",neutral50:"hsl(0, 0%, 50%)",neutral60:"hsl(0, 0%, 40%)",neutral70:"hsl(0, 0%, 30%)",neutral80:"hsl(0, 0%, 20%)",neutral90:"hsl(0, 0%, 10%)"},Gi=4,ct=4,Yi=38,qi=ct*2,Ki={baseUnit:ct,controlHeight:Yi,menuGutter:qi},he={borderRadius:Gi,colors:Wi,spacing:Ki},Xi={"aria-live":"polite",backspaceRemovesValue:!0,blurInputOnSelect:we(),captureMenuScroll:!we(),classNames:{},closeMenuOnSelect:!0,closeMenuOnScroll:!1,components:{},controlShouldRenderValue:!0,escapeClearsValue:!1,filterOption:Di(),formatGroupLabel:_i,getOptionLabel:Ui,getOptionValue:ji,isDisabled:!1,isLoading:!1,isMulti:!1,isRtl:!1,isSearchable:!0,isOptionDisabled:Ni,loadingMessage:function(){return"Loading..."},maxMenuHeight:300,minMenuHeight:140,menuIsOpen:!1,menuPlacement:"bottom",menuPosition:"absolute",menuShouldBlockScroll:!1,menuShouldScrollIntoView:!Kt(),noOptionsMessage:function(){return"No options"},openMenuOnFocus:!1,openMenuOnClick:!0,options:[],pageSize:5,placeholder:"Select...",screenReaderStatus:function(t){var i=t.count;return"".concat(i," result").concat(i!==1?"s":""," available")},styles:{},tabIndex:0,tabSelectsValue:!0,unstyled:!1};function Ne(n,t,i,r){var e=ft(n,t,i),o=vt(n,t,i),a=pt(n,t),l=ce(n,t);return{type:"option",data:t,isDisabled:e,isSelected:o,label:a,value:l,index:r}}function oe(n,t){return n.options.map(function(i,r){if("options"in i){var e=i.options.map(function(a,l){return Ne(n,a,t,l)}).filter(function(a){return We(n,a)});return e.length>0?{type:"group",data:i,options:e,index:r}:void 0}var o=Ne(n,i,t,r);return We(n,o)?o:void 0}).filter(Jt)}function dt(n){return n.reduce(function(t,i){return i.type==="group"?t.push.apply(t,Fe(i.options.map(function(r){return r.data}))):t.push(i.data),t},[])}function ze(n,t){return n.reduce(function(i,r){return r.type==="group"?i.push.apply(i,Fe(r.options.map(function(e){return{data:e.data,id:"".concat(t,"-").concat(r.index,"-").concat(e.index)}}))):i.push({data:r.data,id:"".concat(t,"-").concat(r.index)}),i},[])}function Zi(n,t){return dt(oe(n,t))}function We(n,t){var i=n.inputValue,r=i===void 0?"":i,e=t.data,o=t.isSelected,a=t.label,l=t.value;return(!ht(n)||!o)&&mt(n,{label:a,value:l,data:e},r)}function Ji(n,t){var i=n.focusedValue,r=n.selectValue,e=r.indexOf(i);if(e>-1){var o=t.indexOf(i);if(o>-1)return i;if(e<t.length)return t[e]}return null}function Qi(n,t){var i=n.focusedOption;return i&&t.indexOf(i)>-1?i:t[0]}var ge=function(t,i){var r,e=(r=t.find(function(o){return o.data===i}))===null||r===void 0?void 0:r.id;return e||null},pt=function(t,i){return t.getOptionLabel(i)},ce=function(t,i){return t.getOptionValue(i)};function ft(n,t,i){return typeof n.isOptionDisabled=="function"?n.isOptionDisabled(t,i):!1}function vt(n,t,i){if(i.indexOf(t)>-1)return!0;if(typeof n.isOptionSelected=="function")return n.isOptionSelected(t,i);var r=ce(n,t);return i.some(function(e){return ce(n,e)===r})}function mt(n,t,i){return n.filterOption?n.filterOption(t,i):!0}var ht=function(t){var i=t.hideSelectedOptions,r=t.isMulti;return i===void 0?r:i},er=1,gt=function(n){Rt(i,n);var t=kt(i);function i(r){var e;if(wt(this,i),e=t.call(this,r),e.state={ariaSelection:null,focusedOption:null,focusedOptionId:null,focusableOptionsWithIds:[],focusedValue:null,inputIsHidden:!1,isFocused:!1,selectValue:[],clearFocusValueOnUpdate:!1,prevWasFocused:!1,inputIsHiddenAfterUpdate:void 0,prevProps:void 0,instancePrefix:""},e.blockOptionHover=!1,e.isComposing=!1,e.commonProps=void 0,e.initialTouchX=0,e.initialTouchY=0,e.openAfterFocus=!1,e.scrollToFocusedOptionOnUpdate=!1,e.userIsDragging=void 0,e.isAppleDevice=$i(),e.controlRef=null,e.getControlRef=function(u){e.controlRef=u},e.focusedOptionRef=null,e.getFocusedOptionRef=function(u){e.focusedOptionRef=u},e.menuListRef=null,e.getMenuListRef=function(u){e.menuListRef=u},e.inputRef=null,e.getInputRef=function(u){e.inputRef=u},e.focus=e.focusInput,e.blur=e.blurInput,e.onChange=function(u,s){var c=e.props,d=c.onChange,v=c.name;s.name=v,e.ariaOnChange(u,s),d(u,s)},e.setValue=function(u,s,c){var d=e.props,v=d.closeMenuOnSelect,m=d.isMulti,b=d.inputValue;e.onInputChange("",{action:"set-value",prevInputValue:b}),v&&(e.setState({inputIsHiddenAfterUpdate:!m}),e.onMenuClose()),e.setState({clearFocusValueOnUpdate:!0}),e.onChange(u,{action:s,option:c})},e.selectOption=function(u){var s=e.props,c=s.blurInputOnSelect,d=s.isMulti,v=s.name,m=e.state.selectValue,b=d&&e.isOptionSelected(u,m),g=e.isOptionDisabled(u,m);if(b){var p=e.getOptionValue(u);e.setValue(m.filter(function(h){return e.getOptionValue(h)!==p}),"deselect-option",u)}else if(!g)d?e.setValue([].concat(Fe(m),[u]),"select-option",u):e.setValue(u,"select-option");else{e.ariaOnChange(u,{action:"select-option",option:u,name:v});return}c&&e.blurInput()},e.removeValue=function(u){var s=e.props.isMulti,c=e.state.selectValue,d=e.getOptionValue(u),v=c.filter(function(b){return e.getOptionValue(b)!==d}),m=ue(s,v,v[0]||null);e.onChange(m,{action:"remove-value",removedValue:u}),e.focusInput()},e.clearValue=function(){var u=e.state.selectValue;e.onChange(ue(e.props.isMulti,[],null),{action:"clear",removedValues:u})},e.popValue=function(){var u=e.props.isMulti,s=e.state.selectValue,c=s[s.length-1],d=s.slice(0,s.length-1),v=ue(u,d,d[0]||null);c&&e.onChange(v,{action:"pop-value",removedValue:c})},e.getFocusedOptionId=function(u){return ge(e.state.focusableOptionsWithIds,u)},e.getFocusableOptionsWithIds=function(){return ze(oe(e.props,e.state.selectValue),e.getElementId("option"))},e.getValue=function(){return e.state.selectValue},e.cx=function(){for(var u=arguments.length,s=new Array(u),c=0;c<u;c++)s[c]=arguments[c];return zt.apply(void 0,[e.props.classNamePrefix].concat(s))},e.getOptionLabel=function(u){return pt(e.props,u)},e.getOptionValue=function(u){return ce(e.props,u)},e.getStyles=function(u,s){var c=e.props.unstyled,d=zi[u](s,c);d.boxSizing="border-box";var v=e.props.styles[u];return v?v(d,s):d},e.getClassNames=function(u,s){var c,d;return(c=(d=e.props.classNames)[u])===null||c===void 0?void 0:c.call(d,s)},e.getElementId=function(u){return"".concat(e.state.instancePrefix,"-").concat(u)},e.getComponents=function(){return mi(e.props)},e.buildCategorizedOptions=function(){return oe(e.props,e.state.selectValue)},e.getCategorizedOptions=function(){return e.props.menuIsOpen?e.buildCategorizedOptions():[]},e.buildFocusableOptions=function(){return dt(e.buildCategorizedOptions())},e.getFocusableOptions=function(){return e.props.menuIsOpen?e.buildFocusableOptions():[]},e.ariaOnChange=function(u,s){e.setState({ariaSelection:C({value:u},s)})},e.onMenuMouseDown=function(u){u.button===0&&(u.stopPropagation(),u.preventDefault(),e.focusInput())},e.onMenuMouseMove=function(u){e.blockOptionHover=!1},e.onControlMouseDown=function(u){if(!u.defaultPrevented){var s=e.props.openMenuOnClick;e.state.isFocused?e.props.menuIsOpen?u.target.tagName!=="INPUT"&&u.target.tagName!=="TEXTAREA"&&e.onMenuClose():s&&e.openMenu("first"):(s&&(e.openAfterFocus=!0),e.focusInput()),u.target.tagName!=="INPUT"&&u.target.tagName!=="TEXTAREA"&&u.preventDefault()}},e.onDropdownIndicatorMouseDown=function(u){if(!(u&&u.type==="mousedown"&&u.button!==0)&&!e.props.isDisabled){var s=e.props,c=s.isMulti,d=s.menuIsOpen;e.focusInput(),d?(e.setState({inputIsHiddenAfterUpdate:!c}),e.onMenuClose()):e.openMenu("first"),u.preventDefault()}},e.onClearIndicatorMouseDown=function(u){u&&u.type==="mousedown"&&u.button!==0||(e.clearValue(),u.preventDefault(),e.openAfterFocus=!1,u.type==="touchend"?e.focusInput():setTimeout(function(){return e.focusInput()}))},e.onScroll=function(u){typeof e.props.closeMenuOnScroll=="boolean"?u.target instanceof HTMLElement&&de(u.target)&&e.props.onMenuClose():typeof e.props.closeMenuOnScroll=="function"&&e.props.closeMenuOnScroll(u)&&e.props.onMenuClose()},e.onCompositionStart=function(){e.isComposing=!0},e.onCompositionEnd=function(){e.isComposing=!1},e.onTouchStart=function(u){var s=u.touches,c=s&&s.item(0);c&&(e.initialTouchX=c.clientX,e.initialTouchY=c.clientY,e.userIsDragging=!1)},e.onTouchMove=function(u){var s=u.touches,c=s&&s.item(0);if(c){var d=Math.abs(c.clientX-e.initialTouchX),v=Math.abs(c.clientY-e.initialTouchY),m=5;e.userIsDragging=d>m||v>m}},e.onTouchEnd=function(u){e.userIsDragging||(e.controlRef&&!e.controlRef.contains(u.target)&&e.menuListRef&&!e.menuListRef.contains(u.target)&&e.blurInput(),e.initialTouchX=0,e.initialTouchY=0)},e.onControlTouchEnd=function(u){e.userIsDragging||e.onControlMouseDown(u)},e.onClearIndicatorTouchEnd=function(u){e.userIsDragging||e.onClearIndicatorMouseDown(u)},e.onDropdownIndicatorTouchEnd=function(u){e.userIsDragging||e.onDropdownIndicatorMouseDown(u)},e.handleInputChange=function(u){var s=e.props.inputValue,c=u.currentTarget.value;e.setState({inputIsHiddenAfterUpdate:!1}),e.onInputChange(c,{action:"input-change",prevInputValue:s}),e.props.menuIsOpen||e.onMenuOpen()},e.onInputFocus=function(u){e.props.onFocus&&e.props.onFocus(u),e.setState({inputIsHiddenAfterUpdate:!1,isFocused:!0}),(e.openAfterFocus||e.props.openMenuOnFocus)&&e.openMenu("first"),e.openAfterFocus=!1},e.onInputBlur=function(u){var s=e.props.inputValue;if(e.menuListRef&&e.menuListRef.contains(document.activeElement)){e.inputRef.focus();return}e.props.onBlur&&e.props.onBlur(u),e.onInputChange("",{action:"input-blur",prevInputValue:s}),e.onMenuClose(),e.setState({focusedValue:null,isFocused:!1})},e.onOptionHover=function(u){if(!(e.blockOptionHover||e.state.focusedOption===u)){var s=e.getFocusableOptions(),c=s.indexOf(u);e.setState({focusedOption:u,focusedOptionId:c>-1?e.getFocusedOptionId(u):null})}},e.shouldHideSelectedOptions=function(){return ht(e.props)},e.onValueInputFocus=function(u){u.preventDefault(),u.stopPropagation(),e.focus()},e.onKeyDown=function(u){var s=e.props,c=s.isMulti,d=s.backspaceRemovesValue,v=s.escapeClearsValue,m=s.inputValue,b=s.isClearable,g=s.isDisabled,p=s.menuIsOpen,h=s.onKeyDown,y=s.tabSelectsValue,O=s.openMenuOnFocus,D=e.state,I=D.focusedOption,A=D.focusedValue,P=D.selectValue;if(!g&&!(typeof h=="function"&&(h(u),u.defaultPrevented))){switch(e.blockOptionHover=!0,u.key){case"ArrowLeft":if(!c||m)return;e.focusValue("previous");break;case"ArrowRight":if(!c||m)return;e.focusValue("next");break;case"Delete":case"Backspace":if(m)return;if(A)e.removeValue(A);else{if(!d)return;c?e.popValue():b&&e.clearValue()}break;case"Tab":if(e.isComposing||u.shiftKey||!p||!y||!I||O&&e.isOptionSelected(I,P))return;e.selectOption(I);break;case"Enter":if(u.keyCode===229)break;if(p){if(!I||e.isComposing)return;e.selectOption(I);break}return;case"Escape":p?(e.setState({inputIsHiddenAfterUpdate:!1}),e.onInputChange("",{action:"menu-close",prevInputValue:m}),e.onMenuClose()):b&&v&&e.clearValue();break;case" ":if(m)return;if(!p){e.openMenu("first");break}if(!I)return;e.selectOption(I);break;case"ArrowUp":p?e.focusOption("up"):e.openMenu("last");break;case"ArrowDown":p?e.focusOption("down"):e.openMenu("first");break;case"PageUp":if(!p)return;e.focusOption("pageup");break;case"PageDown":if(!p)return;e.focusOption("pagedown");break;case"Home":if(!p)return;e.focusOption("first");break;case"End":if(!p)return;e.focusOption("last");break;default:return}u.preventDefault()}},e.state.instancePrefix="react-select-"+(e.props.instanceId||++er),e.state.selectValue=Me(r.value),r.menuIsOpen&&e.state.selectValue.length){var o=e.getFocusableOptionsWithIds(),a=e.buildFocusableOptions(),l=a.indexOf(e.state.selectValue[0]);e.state.focusableOptionsWithIds=o,e.state.focusedOption=a[l],e.state.focusedOptionId=ge(o,a[l])}return e}return Lt(i,[{key:"componentDidMount",value:function(){this.startListeningComposition(),this.startListeningToTouch(),this.props.closeMenuOnScroll&&document&&document.addEventListener&&document.addEventListener("scroll",this.onScroll,!0),this.props.autoFocus&&this.focusInput(),this.props.menuIsOpen&&this.state.focusedOption&&this.menuListRef&&this.focusedOptionRef&&xe(this.menuListRef,this.focusedOptionRef)}},{key:"componentDidUpdate",value:function(e){var o=this.props,a=o.isDisabled,l=o.menuIsOpen,u=this.state.isFocused;(u&&!a&&e.isDisabled||u&&l&&!e.menuIsOpen)&&this.focusInput(),u&&a&&!e.isDisabled?this.setState({isFocused:!1},this.onMenuClose):!u&&!a&&e.isDisabled&&this.inputRef===document.activeElement&&this.setState({isFocused:!0}),this.menuListRef&&this.focusedOptionRef&&this.scrollToFocusedOptionOnUpdate&&(xe(this.menuListRef,this.focusedOptionRef),this.scrollToFocusedOptionOnUpdate=!1)}},{key:"componentWillUnmount",value:function(){this.stopListeningComposition(),this.stopListeningToTouch(),document.removeEventListener("scroll",this.onScroll,!0)}},{key:"onMenuOpen",value:function(){this.props.onMenuOpen()}},{key:"onMenuClose",value:function(){this.onInputChange("",{action:"menu-close",prevInputValue:this.props.inputValue}),this.props.onMenuClose()}},{key:"onInputChange",value:function(e,o){this.props.onInputChange(e,o)}},{key:"focusInput",value:function(){this.inputRef&&this.inputRef.focus()}},{key:"blurInput",value:function(){this.inputRef&&this.inputRef.blur()}},{key:"openMenu",value:function(e){var o=this,a=this.state,l=a.selectValue,u=a.isFocused,s=this.buildFocusableOptions(),c=e==="first"?0:s.length-1;if(!this.props.isMulti){var d=s.indexOf(l[0]);d>-1&&(c=d)}this.scrollToFocusedOptionOnUpdate=!(u&&this.menuListRef),this.setState({inputIsHiddenAfterUpdate:!1,focusedValue:null,focusedOption:s[c],focusedOptionId:this.getFocusedOptionId(s[c])},function(){return o.onMenuOpen()})}},{key:"focusValue",value:function(e){var o=this.state,a=o.selectValue,l=o.focusedValue;if(this.props.isMulti){this.setState({focusedOption:null});var u=a.indexOf(l);l||(u=-1);var s=a.length-1,c=-1;if(a.length){switch(e){case"previous":u===0?c=0:u===-1?c=s:c=u-1;break;case"next":u>-1&&u<s&&(c=u+1);break}this.setState({inputIsHidden:c!==-1,focusedValue:a[c]})}}}},{key:"focusOption",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"first",o=this.props.pageSize,a=this.state.focusedOption,l=this.getFocusableOptions();if(l.length){var u=0,s=l.indexOf(a);a||(s=-1),e==="up"?u=s>0?s-1:l.length-1:e==="down"?u=(s+1)%l.length:e==="pageup"?(u=s-o,u<0&&(u=0)):e==="pagedown"?(u=s+o,u>l.length-1&&(u=l.length-1)):e==="last"&&(u=l.length-1),this.scrollToFocusedOptionOnUpdate=!0,this.setState({focusedOption:l[u],focusedValue:null,focusedOptionId:this.getFocusedOptionId(l[u])})}}},{key:"getTheme",value:function(){return this.props.theme?typeof this.props.theme=="function"?this.props.theme(he):C(C({},he),this.props.theme):he}},{key:"getCommonProps",value:function(){var e=this.clearValue,o=this.cx,a=this.getStyles,l=this.getClassNames,u=this.getValue,s=this.selectOption,c=this.setValue,d=this.props,v=d.isMulti,m=d.isRtl,b=d.options,g=this.hasValue();return{clearValue:e,cx:o,getStyles:a,getClassNames:l,getValue:u,hasValue:g,isMulti:v,isRtl:m,options:b,selectOption:s,selectProps:d,setValue:c,theme:this.getTheme()}}},{key:"hasValue",value:function(){var e=this.state.selectValue;return e.length>0}},{key:"hasOptions",value:function(){return!!this.getFocusableOptions().length}},{key:"isClearable",value:function(){var e=this.props,o=e.isClearable,a=e.isMulti;return o===void 0?a:o}},{key:"isOptionDisabled",value:function(e,o){return ft(this.props,e,o)}},{key:"isOptionSelected",value:function(e,o){return vt(this.props,e,o)}},{key:"filterOption",value:function(e,o){return mt(this.props,e,o)}},{key:"formatOptionLabel",value:function(e,o){if(typeof this.props.formatOptionLabel=="function"){var a=this.props.inputValue,l=this.state.selectValue;return this.props.formatOptionLabel(e,{context:o,inputValue:a,selectValue:l})}else return this.getOptionLabel(e)}},{key:"formatGroupLabel",value:function(e){return this.props.formatGroupLabel(e)}},{key:"startListeningComposition",value:function(){document&&document.addEventListener&&(document.addEventListener("compositionstart",this.onCompositionStart,!1),document.addEventListener("compositionend",this.onCompositionEnd,!1))}},{key:"stopListeningComposition",value:function(){document&&document.removeEventListener&&(document.removeEventListener("compositionstart",this.onCompositionStart),document.removeEventListener("compositionend",this.onCompositionEnd))}},{key:"startListeningToTouch",value:function(){document&&document.addEventListener&&(document.addEventListener("touchstart",this.onTouchStart,!1),document.addEventListener("touchmove",this.onTouchMove,!1),document.addEventListener("touchend",this.onTouchEnd,!1))}},{key:"stopListeningToTouch",value:function(){document&&document.removeEventListener&&(document.removeEventListener("touchstart",this.onTouchStart),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"renderInput",value:function(){var e=this.props,o=e.isDisabled,a=e.isSearchable,l=e.inputId,u=e.inputValue,s=e.tabIndex,c=e.form,d=e.menuIsOpen,v=e.required,m=this.getComponents(),b=m.Input,g=this.state,p=g.inputIsHidden,h=g.ariaSelection,y=this.commonProps,O=l||this.getElementId("input"),D=C(C(C({"aria-autocomplete":"list","aria-expanded":d,"aria-haspopup":!0,"aria-errormessage":this.props["aria-errormessage"],"aria-invalid":this.props["aria-invalid"],"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-required":v,role:"combobox","aria-activedescendant":this.isAppleDevice?void 0:this.state.focusedOptionId||""},d&&{"aria-controls":this.getElementId("listbox")}),!a&&{"aria-readonly":!0}),this.hasValue()?(h==null?void 0:h.action)==="initial-input-focus"&&{"aria-describedby":this.getElementId("live-region")}:{"aria-describedby":this.getElementId("placeholder")});return a?f.createElement(b,S({},y,{autoCapitalize:"none",autoComplete:"off",autoCorrect:"off",id:O,innerRef:this.getInputRef,isDisabled:o,isHidden:p,onBlur:this.onInputBlur,onChange:this.handleInputChange,onFocus:this.onInputFocus,spellCheck:"false",tabIndex:s,form:c,type:"text",value:u},D)):f.createElement(Ii,S({id:O,innerRef:this.getInputRef,onBlur:this.onInputBlur,onChange:se,onFocus:this.onInputFocus,disabled:o,tabIndex:s,inputMode:"none",form:c,value:""},D))}},{key:"renderPlaceholderOrValue",value:function(){var e=this,o=this.getComponents(),a=o.MultiValue,l=o.MultiValueContainer,u=o.MultiValueLabel,s=o.MultiValueRemove,c=o.SingleValue,d=o.Placeholder,v=this.commonProps,m=this.props,b=m.controlShouldRenderValue,g=m.isDisabled,p=m.isMulti,h=m.inputValue,y=m.placeholder,O=this.state,D=O.selectValue,I=O.focusedValue,A=O.isFocused;if(!this.hasValue()||!b)return h?null:f.createElement(d,S({},v,{key:"placeholder",isDisabled:g,isFocused:A,innerProps:{id:this.getElementId("placeholder")}}),y);if(p)return D.map(function(F,V){var R=F===I,B="".concat(e.getOptionLabel(F),"-").concat(e.getOptionValue(F));return f.createElement(a,S({},v,{components:{Container:l,Label:u,Remove:s},isFocused:R,isDisabled:g,key:B,index:V,removeProps:{onClick:function(){return e.removeValue(F)},onTouchEnd:function(){return e.removeValue(F)},onMouseDown:function(U){U.preventDefault()}},data:F}),e.formatOptionLabel(F,"value"))});if(h)return null;var P=D[0];return f.createElement(c,S({},v,{data:P,isDisabled:g}),this.formatOptionLabel(P,"value"))}},{key:"renderClearIndicator",value:function(){var e=this.getComponents(),o=e.ClearIndicator,a=this.commonProps,l=this.props,u=l.isDisabled,s=l.isLoading,c=this.state.isFocused;if(!this.isClearable()||!o||u||!this.hasValue()||s)return null;var d={onMouseDown:this.onClearIndicatorMouseDown,onTouchEnd:this.onClearIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(o,S({},a,{innerProps:d,isFocused:c}))}},{key:"renderLoadingIndicator",value:function(){var e=this.getComponents(),o=e.LoadingIndicator,a=this.commonProps,l=this.props,u=l.isDisabled,s=l.isLoading,c=this.state.isFocused;if(!o||!s)return null;var d={"aria-hidden":"true"};return f.createElement(o,S({},a,{innerProps:d,isDisabled:u,isFocused:c}))}},{key:"renderIndicatorSeparator",value:function(){var e=this.getComponents(),o=e.DropdownIndicator,a=e.IndicatorSeparator;if(!o||!a)return null;var l=this.commonProps,u=this.props.isDisabled,s=this.state.isFocused;return f.createElement(a,S({},l,{isDisabled:u,isFocused:s}))}},{key:"renderDropdownIndicator",value:function(){var e=this.getComponents(),o=e.DropdownIndicator;if(!o)return null;var a=this.commonProps,l=this.props.isDisabled,u=this.state.isFocused,s={onMouseDown:this.onDropdownIndicatorMouseDown,onTouchEnd:this.onDropdownIndicatorTouchEnd,"aria-hidden":"true"};return f.createElement(o,S({},a,{innerProps:s,isDisabled:l,isFocused:u}))}},{key:"renderMenu",value:function(){var e=this,o=this.getComponents(),a=o.Group,l=o.GroupHeading,u=o.Menu,s=o.MenuList,c=o.MenuPortal,d=o.LoadingMessage,v=o.NoOptionsMessage,m=o.Option,b=this.commonProps,g=this.state.focusedOption,p=this.props,h=p.captureMenuScroll,y=p.inputValue,O=p.isLoading,D=p.loadingMessage,I=p.minMenuHeight,A=p.maxMenuHeight,P=p.menuIsOpen,F=p.menuPlacement,V=p.menuPosition,R=p.menuPortalTarget,B=p.menuShouldBlockScroll,T=p.menuShouldScrollIntoView,U=p.noOptionsMessage,j=p.onMenuScrollToTop,L=p.onMenuScrollToBottom;if(!P)return null;var x=function(k,N){var K=k.type,$=k.data,X=k.isDisabled,z=k.isSelected,ne=k.label,bt=k.value,ye=g===$,Ie=X?void 0:function(){return e.onOptionHover($)},Et=X?void 0:function(){return e.selectOption($)},Ae="".concat(e.getElementId("option"),"-").concat(N),Ct={id:Ae,onClick:Et,onMouseMove:Ie,onMouseOver:Ie,tabIndex:-1,role:"option","aria-selected":e.isAppleDevice?void 0:z};return f.createElement(m,S({},b,{innerProps:Ct,data:$,isDisabled:X,isSelected:z,key:Ae,label:ne,type:K,value:bt,isFocused:ye,innerRef:ye?e.getFocusedOptionRef:void 0}),e.formatOptionLabel(k.data,"menu"))},_;if(this.hasOptions())_=this.getCategorizedOptions().map(function(w){if(w.type==="group"){var k=w.data,N=w.options,K=w.index,$="".concat(e.getElementId("group"),"-").concat(K),X="".concat($,"-heading");return f.createElement(a,S({},b,{key:$,data:k,options:N,Heading:l,headingProps:{id:X,data:w.data},label:e.formatGroupLabel(w.data)}),w.options.map(function(z){return x(z,"".concat(K,"-").concat(z.index))}))}else if(w.type==="option")return x(w,"".concat(w.index))});else if(O){var H=D({inputValue:y});if(H===null)return null;_=f.createElement(d,b,H)}else{var Y=U({inputValue:y});if(Y===null)return null;_=f.createElement(v,b,Y)}var q={minMenuHeight:I,maxMenuHeight:A,menuPlacement:F,menuPosition:V,menuShouldScrollIntoView:T},te=f.createElement(an,S({},b,q),function(w){var k=w.ref,N=w.placerProps,K=N.placement,$=N.maxHeight;return f.createElement(u,S({},b,q,{innerRef:k,innerProps:{onMouseDown:e.onMenuMouseDown,onMouseMove:e.onMenuMouseMove},isLoading:O,placement:K}),f.createElement(wi,{captureEnabled:h,onTopArrive:j,onBottomArrive:L,lockEnabled:B},function(X){return f.createElement(s,S({},b,{innerRef:function(ne){e.getMenuListRef(ne),X(ne)},innerProps:{role:"listbox","aria-multiselectable":b.isMulti,id:e.getElementId("listbox")},isLoading:O,maxHeight:$,focusedOption:g}),_)}))});return R||V==="fixed"?f.createElement(c,S({},b,{appendTo:R,controlElement:this.controlRef,menuPlacement:F,menuPosition:V}),te):te}},{key:"renderFormField",value:function(){var e=this,o=this.props,a=o.delimiter,l=o.isDisabled,u=o.isMulti,s=o.name,c=o.required,d=this.state.selectValue;if(c&&!this.hasValue()&&!l)return f.createElement(Ti,{name:s,onFocus:this.onValueInputFocus});if(!(!s||l))if(u)if(a){var v=d.map(function(g){return e.getOptionValue(g)}).join(a);return f.createElement("input",{name:s,type:"hidden",value:v})}else{var m=d.length>0?d.map(function(g,p){return f.createElement("input",{key:"i-".concat(p),name:s,type:"hidden",value:e.getOptionValue(g)})}):f.createElement("input",{name:s,type:"hidden",value:""});return f.createElement("div",null,m)}else{var b=d[0]?this.getOptionValue(d[0]):"";return f.createElement("input",{name:s,type:"hidden",value:b})}}},{key:"renderLiveRegion",value:function(){var e=this.commonProps,o=this.state,a=o.ariaSelection,l=o.focusedOption,u=o.focusedValue,s=o.isFocused,c=o.selectValue,d=this.getFocusableOptions();return f.createElement(Ci,S({},e,{id:this.getElementId("live-region"),ariaSelection:a,focusedOption:l,focusedValue:u,isFocused:s,selectValue:c,focusableOptions:d,isAppleDevice:this.isAppleDevice}))}},{key:"render",value:function(){var e=this.getComponents(),o=e.Control,a=e.IndicatorsContainer,l=e.SelectContainer,u=e.ValueContainer,s=this.props,c=s.className,d=s.id,v=s.isDisabled,m=s.menuIsOpen,b=this.state.isFocused,g=this.commonProps=this.getCommonProps();return f.createElement(l,S({},g,{className:c,innerProps:{id:d,onKeyDown:this.onKeyDown},isDisabled:v,isFocused:b}),this.renderLiveRegion(),f.createElement(o,S({},g,{innerRef:this.getControlRef,innerProps:{onMouseDown:this.onControlMouseDown,onTouchEnd:this.onControlTouchEnd},isDisabled:v,isFocused:b,menuIsOpen:m}),f.createElement(u,S({},g,{isDisabled:v}),this.renderPlaceholderOrValue(),this.renderInput()),f.createElement(a,S({},g,{isDisabled:v}),this.renderClearIndicator(),this.renderLoadingIndicator(),this.renderIndicatorSeparator(),this.renderDropdownIndicator())),this.renderMenu(),this.renderFormField())}}],[{key:"getDerivedStateFromProps",value:function(e,o){var a=o.prevProps,l=o.clearFocusValueOnUpdate,u=o.inputIsHiddenAfterUpdate,s=o.ariaSelection,c=o.isFocused,d=o.prevWasFocused,v=o.instancePrefix,m=e.options,b=e.value,g=e.menuIsOpen,p=e.inputValue,h=e.isMulti,y=Me(b),O={};if(a&&(b!==a.value||m!==a.options||g!==a.menuIsOpen||p!==a.inputValue)){var D=g?Zi(e,y):[],I=g?ze(oe(e,y),"".concat(v,"-option")):[],A=l?Ji(o,y):null,P=Qi(o,D),F=ge(I,P);O={selectValue:y,focusedOption:P,focusedOptionId:F,focusableOptionsWithIds:I,focusedValue:A,clearFocusValueOnUpdate:!1}}var V=u!=null&&e!==a?{inputIsHidden:u,inputIsHiddenAfterUpdate:void 0}:{},R=s,B=c&&d;return c&&!B&&(R={value:ue(h,y,y[0]||null),options:y,action:"initial-input-focus"},B=!d),(s==null?void 0:s.action)==="initial-input-focus"&&(R=null),C(C(C({},O),V),{},{prevProps:e,ariaSelection:R,prevWasFocused:B})}}]),i}(f.Component);gt.defaultProps=Xi;var tr=f.forwardRef(function(n,t){var i=xt(n);return f.createElement(gt,S({ref:t},i))}),or=tr;export{or as S};
