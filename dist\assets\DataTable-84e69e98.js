import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import{P as s}from"./@fortawesome/react-fontawesome-13437837.js";const b=({columns:p,data:d,loading:n,renderCustomCell:l,tableClassName:o="w-full min-w-full table-auto border-separate border-spacing-y-1 sm:border-spacing-y-2",rowClassName:g="cursor-pointer bg-gray-100 hover:bg-gray-50 transition-colors duration-150",cellClassName:c="px-3 py-3 sm:px-4 sm:py-4 lg:px-6",headerClassName:x="whitespace-nowrap px-3 py-3 sm:px-4 sm:py-4 lg:px-6 text-left text-sm font-medium text-gray-500",emptyMessage:h="No data available",loadingMessage:j="Loading...",onClick:m,responsive:y=!0})=>e.jsxs("div",{className:"w-full overflow-x-auto",children:[e.jsx("div",{className:`min-w-full ${y?"min-w-[800px]":""}`,children:e.jsxs("table",{className:o,children:[e.jsx("thead",{className:"!border-none",children:e.jsx("tr",{children:p.map((a,t)=>a.accessor===""?e.jsx("th",{scope:"col",className:`${x} ${a.width||""}`},t):e.jsx("th",{scope:"col",className:`${x} ${a.width||""}`,children:a.header},t))})}),e.jsx("tbody",{children:d.map((a,t)=>e.jsx("tr",{className:g,onClick:()=>m&&m(a),children:p.map((r,i)=>l&&l[r.accessor]?e.jsx("td",{className:`${c} ${i===0?"rounded-l-xl":""} ${i===p.length-1?"!rounded-r-xl":""} ${r.width||""}`,children:l[r.accessor](a)},`${t}-${i}`):e.jsx("td",{className:`${c} ${i===0?"rounded-l-xl":""} ${i===p.length-1?"!rounded-r-xl":""} ${r.width||""}`,children:e.jsx("div",{className:"flex items-center",children:e.jsx("span",{className:"break-words text-sm font-medium text-gray-900",children:r.mappingExist&&r.mappings?r.mappings[a[r.accessor]]:a[r.accessor]||"--"})})},`${t}-${i}`))},t))})]})}),n&&e.jsx("div",{className:"px-3 py-4 sm:px-6",children:e.jsx("p",{className:"text-sm text-gray-500",children:j})}),!n&&d.length===0&&e.jsx("div",{className:"w-full px-3 py-8 text-center sm:px-6",children:e.jsx("p",{className:"text-sm text-gray-500",children:h})})]});b.propTypes={columns:s.arrayOf(s.shape({header:s.string.isRequired,accessor:s.string.isRequired,mappingExist:s.bool,mappings:s.object,width:s.string})).isRequired,data:s.array.isRequired,loading:s.bool,renderCustomCell:s.objectOf(s.func),tableClassName:s.string,rowClassName:s.string,cellClassName:s.string,headerClassName:s.string,emptyMessage:s.string,loadingMessage:s.string,onClick:s.func,responsive:s.bool};export{b as D};
