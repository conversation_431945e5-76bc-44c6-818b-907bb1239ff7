import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as o,b as nl}from"./vendor-851db8c1.js";import{u as pl,e as xl,U as X,a0 as fl,az as hl,a7 as bl,aA as gl,M as yl,T as wl,k as kl}from"./index-08a5dc5b.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const Y=new yl,jl=new wl;function tt(){var D,T,$;const[Z,v]=o.useState([]),[f,y]=o.useState(null),[ll,w]=nl.useState(!1),[tl,k]=o.useState(!1),[s,j]=o.useState(null),[m,h]=o.useState(null),[p,x]=o.useState(null),[r,_]=o.useState(!1),[N,S]=o.useState(!1),[el,sl]=o.useState([]),[C,E]=o.useState(""),{club:c,sports:u,courts:a}=pl();async function i({filters:t}){w(!0);try{console.log("filters",t);const b=(await jl.getList("reservation",{filter:[`courtmatchup_reservation.club_id,cs,${c==null?void 0:c.id}`,...t],join:["booking|booking_id","user|user_id","buddy|buddy_id","clubs|club_id"]})).list.map(l=>{var L,A,B,F,M,O,z,I,R,U,J,K,Q,G,H,P,W,q,V;return{id:(L=l==null?void 0:l.booking)==null?void 0:L.id,court_ids:(A=l==null?void 0:l.booking)!=null&&A.court_ids?JSON.parse((B=l==null?void 0:l.booking)==null?void 0:B.court_ids):(F=l==null?void 0:l.booking)==null?void 0:F.court_id,startTime:(M=l==null?void 0:l.booking)==null?void 0:M.start_time,endTime:(O=l==null?void 0:l.booking)==null?void 0:O.end_time,date:(z=l==null?void 0:l.booking)==null?void 0:z.date,reservation_type:(I=l==null?void 0:l.booking)==null?void 0:I.reservation_type,title:(R=kl.find(g=>{var n;return g.value==((n=l==null?void 0:l.booking)==null?void 0:n.reservation_type)}))==null?void 0:R.label,status:(U=l==null?void 0:l.booking)==null?void 0:U.status,price:(J=l==null?void 0:l.booking)==null?void 0:J.price,duration:(K=l==null?void 0:l.booking)==null?void 0:K.duration,players:(Q=l==null?void 0:l.booking)!=null&&Q.player_ids?JSON.parse((G=l==null?void 0:l.booking)==null?void 0:G.player_ids):[],sport:u.find(g=>{var n;return g.id==((n=l==null?void 0:l.booking)==null?void 0:n.sport_id)}),user_id:(H=l==null?void 0:l.booking)==null?void 0:H.user_id,booking:l==null?void 0:l.booking,user:l==null?void 0:l.user,buddy:l==null?void 0:l.buddy,club:l==null?void 0:l.clubs,type:(P=l==null?void 0:l.booking)==null?void 0:P.type,sub_type:(W=l==null?void 0:l.booking)==null?void 0:W.subtype,court_fee:(q=l==null?void 0:l.booking)==null?void 0:q.court_fee,sport_id:(V=l==null?void 0:l.booking)==null?void 0:V.sport_id}});v(b)}catch(d){console.error("Error fetching data:",d)}finally{w(!1)}}const cl=t=>{j(t),h(null),x(null);const d=`courtmatchup_booking.sport_id,cs,${t==null?void 0:t.id}`;i({filters:[d]})},dl=t=>{h(t),x(null),_(!1);const d=[`courtmatchup_booking.sport_id,cs,${s==null?void 0:s.id}`,`courtmatchup_booking.type,cs,${t}`];i({filters:d})},ol=t=>{x(t),S(!1);const d=[`courtmatchup_booking.sport_id,cs,${s==null?void 0:s.id}`,`courtmatchup_booking.type,cs,${m}`,`courtmatchup_booking.subtype,cs,${t}`];i({filters:d})},ul=async()=>{try{Y.setTable("user");const t=await Y.callRestAPI({},"GETALL");sl(t.list),console.log("users",t)}catch(t){console.log(t)}};o.useEffect(()=>{c!=null&&c.id&&(i({filters:[]}),ul())},[c==null?void 0:c.id]);const il=()=>{k(!0)},ml=t=>{y(t)},al=()=>{j(null),h(null),x(null),E(""),i({filters:[]})};return e.jsxs("div",{className:"h-full rounded-lg bg-white p-4",children:[e.jsx("div",{className:"flex w-full flex-col",children:e.jsxs("div",{className:"mb-6 flex flex-col space-y-4",children:[e.jsx("div",{className:"w-full",children:e.jsxs("div",{className:"relative flex items-center",children:[e.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:e.jsx(xl,{className:"h-5 w-5 text-gray-500"})}),e.jsx("input",{type:"text",value:C,onChange:t=>{E(t.target.value);const d=s?[`courtmatchup_booking.sport_id,cs,${s==null?void 0:s.id}`]:[],b=t.target.value?[`courtmatchup_user.first_name,cs,${t.target.value}`]:[];i({filters:[...d,...b]})},className:"block w-full rounded-md border border-gray-200 py-2 pl-10 pr-4 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search players by name..."})]})}),e.jsxs("div",{className:"flex flex-col space-y-4",children:[(u==null?void 0:u.length)>0&&e.jsx("div",{className:"w-full",children:e.jsx("div",{className:"flex flex-wrap gap-2 p-2",children:u==null?void 0:u.map((t,d)=>e.jsx("button",{className:`flex-shrink-0 whitespace-nowrap rounded-lg border px-4 py-2 text-sm font-medium shadow-sm transition-all duration-200 ${(s==null?void 0:s.id)===(t==null?void 0:t.id)?"border-primaryBlue bg-primaryBlue text-white shadow-md":"border-gray-200 bg-white text-gray-700 hover:border-gray-300 hover:bg-gray-50 hover:shadow-md"}`,onClick:()=>cl(t),children:t==null?void 0:t.name},d))})}),e.jsxs("div",{className:"flex flex-col space-y-3 sm:flex-row sm:space-x-3 sm:space-y-0",children:[s&&e.jsxs("div",{className:"relative w-full sm:w-auto sm:min-w-[160px]",children:[e.jsxs("button",{onClick:()=>_(!r),className:"flex w-full items-center justify-between space-x-2 rounded-lg border bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:[e.jsx("span",{className:"truncate",children:m||"Select Type"}),e.jsx(X,{className:"h-4 w-4 flex-shrink-0"})]}),r&&e.jsx("div",{className:"absolute left-0 z-20 mt-1 w-full min-w-[200px] rounded-lg border bg-white shadow-lg",children:(D=s==null?void 0:s.sport_types)==null?void 0:D.map((t,d)=>e.jsx("button",{className:"block w-full px-4 py-2 text-left text-sm hover:bg-gray-100",onClick:()=>dl(t.type),children:t.type},d))})]}),m&&e.jsxs("div",{className:"relative w-full sm:w-auto sm:min-w-[160px]",children:[e.jsxs("button",{onClick:()=>S(!N),className:"flex w-full items-center justify-between space-x-2 rounded-lg border bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50",children:[e.jsx("span",{className:"truncate",children:p||"Select Subtype"}),e.jsx(X,{className:"h-4 w-4 flex-shrink-0"})]}),N&&e.jsx("div",{className:"absolute left-0 z-20 mt-1 w-full min-w-[200px] rounded-lg border bg-white shadow-lg",children:($=(T=s==null?void 0:s.sport_types)==null?void 0:T.find(t=>t.type===m))==null?void 0:$.subtype.map((t,d)=>e.jsx("button",{className:"block w-full px-4 py-2 text-left text-sm hover:bg-gray-100",onClick:()=>ol(t),children:t},d))})]})]}),e.jsxs("div",{className:"flex flex-col space-y-2 sm:flex-row sm:flex-wrap sm:gap-2 sm:space-y-0",children:[(C||s||m||p)&&e.jsxs("button",{onClick:al,className:"flex w-full items-center justify-center space-x-2 rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 sm:w-auto",children:[e.jsx(fl,{className:"h-4 w-4"}),e.jsx("span",{children:"Clear filters"})]}),e.jsxs("button",{className:"flex w-full items-center justify-center space-x-2 rounded-lg border bg-white px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 sm:w-auto",children:[e.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),e.jsx("span",{children:"History"})]}),e.jsxs("button",{onClick:il,className:"hover:bg-navy-800 flex w-full items-center justify-center space-x-2 rounded-lg bg-navy-700 px-3 py-2 text-sm font-medium text-white sm:w-auto",children:[e.jsx("span",{children:"+"}),e.jsx("span",{children:"Add event"})]})]})]})]})}),e.jsx(hl,{sports:u,events:Z,loading:ll,onDateChange:i,onEventClick:ml,courts:(a==null?void 0:a.filter(t=>!(s&&t.sport_id!==s.id||m&&t.type!==m||p&&t.sub_type!==p)))||[],fetchData:i}),f&&e.jsx(bl,{isOpen:!!f,onClose:()=>y(null),event:f,courts:a||[],users:el,clubId:c==null?void 0:c.id,sports:u,fetchData:i,club:c}),(c==null?void 0:c.id)&&e.jsx(gl,{setShowEventModal:k,showEventModal:tl,fetchData:i,club:c,sports:u,courts:a})]})}export{tt as default};
