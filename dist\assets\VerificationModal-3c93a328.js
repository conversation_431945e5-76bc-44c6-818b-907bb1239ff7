import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import{P as s}from"./@fortawesome/react-fontawesome-13437837.js";const n=({email:o,isOpen:i,onClose:t,title:r,description:l})=>i?e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsx("div",{className:"w-[450px] rounded-2xl bg-white p-6",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:"flex h-10 w-10 items-center justify-center rounded-lg bg-[#EBF1FF]",children:e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12C21 16.9706 16.9706 21 12 21ZM11.1 11.1V16.5H12.9V11.1H11.1ZM11.1 7.5V9.3H12.9V7.5H11.1Z",fill:"#375DFB"})})}),e.jsxs("div",{className:"flex flex-1 flex-col gap-2",children:[e.jsx("h2",{className:"text-xl font-medium text-gray-900",children:r}),e.jsx("p",{className:"text-sm text-gray-600",children:l})]}),e.jsx("button",{onClick:t,className:"rounded-lg p-2 text-gray-400 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M13 7L7 13M13 13L7 7M19.25 10C19.25 15.1086 15.1086 19.25 10 19.25C4.89137 19.25 0.75 15.1086 0.75 10C0.75 4.89137 4.89137 0.75 10 0.75C15.1086 0.75 19.25 4.89137 19.25 10Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})})})]})})}):null;n.propTypes={email:s.string,isOpen:s.bool.isRequired,onClose:s.func.isRequired,title:s.string.isRequired,description:s.string.isRequired};export{n as V};
