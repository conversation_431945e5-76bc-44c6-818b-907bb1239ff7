import{j as se}from"../@nivo/heatmap-ba1ecfff.js";import{r as i,a as ge}from"../vendor-851db8c1.js";function Ce(t){"@babel/helpers - typeof";return Ce=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ce(t)}function ns(t,e){if(Ce(t)!="object"||!t)return t;var n=t[Symbol.toPrimitive];if(n!==void 0){var s=n.call(t,e||"default");if(Ce(s)!="object")return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ss(t){var e=ns(t,"string");return Ce(e)=="symbol"?e:e+""}function f(t,e,n){return(e=ss(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Zn(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Ge,Et;function is(){if(Et)return Ge;Et=1;var t=function(n,s,r,o,a,l,u,c){if(!n){var p;if(s===void 0)p=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var h=[r,o,a,l,u,c],g=0;p=new Error(s.replace(/%s/g,function(){return h[g++]})),p.name="Invariant Violation"}throw p.framesToPop=1,p}};return Ge=t,Ge}var rs=is(),ne=Zn(rs),A=i.createContext(null);function os(){ne(!!i.useContext,"useGoogleMap is React hook and requires React version 16.8+");var t=i.useContext(A);return ne(!!t,"useGoogleMap needs a GoogleMap available up in the tree"),t}function as(t,e,n){return Object.keys(t).reduce(function(r,o){return e(r,t[o],o)},n)}function ls(t,e){Object.keys(t).forEach(n=>e(t[n],n))}function us(t,e,n,s){var r={},o=(a,l)=>{var u=n[l];u!==e[l]&&(r[l]=u,a(s,u))};return ls(t,o),r}function ps(t,e,n){var s=as(n,function(o,a,l){return typeof t[l]=="function"&&o.push(google.maps.event.addListener(e,a,t[l])),o},[]);return s}function ds(t){google.maps.event.removeListener(t)}function Z(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];t.forEach(ds)}function _(t){var{updaterMap:e,eventMap:n,prevProps:s,nextProps:r,instance:o}=t,a=ps(r,o,n);return us(e,s,r,o),a}var wt={onDblClick:"dblclick",onDragEnd:"dragend",onDragStart:"dragstart",onMapTypeIdChanged:"maptypeid_changed",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseDown:"mousedown",onMouseUp:"mouseup",onRightClick:"rightclick",onTilesLoaded:"tilesloaded",onBoundsChanged:"bounds_changed",onCenterChanged:"center_changed",onClick:"click",onDrag:"drag",onHeadingChanged:"heading_changed",onIdle:"idle",onProjectionChanged:"projection_changed",onResize:"resize",onTiltChanged:"tilt_changed",onZoomChanged:"zoom_changed"},xt={extraMapTypes(t,e){e.forEach(function(s,r){t.mapTypes.set(String(r),s)})},center(t,e){t.setCenter(e)},clickableIcons(t,e){t.setClickableIcons(e)},heading(t,e){t.setHeading(e)},mapTypeId(t,e){t.setMapTypeId(e)},options(t,e){t.setOptions(e)},streetView(t,e){t.setStreetView(e)},tilt(t,e){t.setTilt(e)},zoom(t,e){t.setZoom(e)}};function cs(t){var{children:e,options:n,id:s,mapContainerStyle:r,mapContainerClassName:o,center:a,onClick:l,onDblClick:u,onDrag:c,onDragEnd:p,onDragStart:h,onMouseMove:g,onMouseOut:v,onMouseOver:d,onMouseDown:x,onMouseUp:M,onRightClick:S,onCenterChanged:E,onLoad:P,onUnmount:b}=t,[m,C]=i.useState(null),w=i.useRef(null),[D,y]=i.useState(null),[k,O]=i.useState(null),[U,V]=i.useState(null),[T,R]=i.useState(null),[W,q]=i.useState(null),[N,G]=i.useState(null),[$,K]=i.useState(null),[J,j]=i.useState(null),[Y,L]=i.useState(null),[ee,X]=i.useState(null),[z,te]=i.useState(null),[I,F]=i.useState(null);return i.useEffect(()=>{n&&m!==null&&m.setOptions(n)},[m,n]),i.useEffect(()=>{m!==null&&typeof a<"u"&&m.setCenter(a)},[m,a]),i.useEffect(()=>{m&&u&&(k!==null&&google.maps.event.removeListener(k),O(google.maps.event.addListener(m,"dblclick",u)))},[u]),i.useEffect(()=>{m&&p&&(U!==null&&google.maps.event.removeListener(U),V(google.maps.event.addListener(m,"dragend",p)))},[p]),i.useEffect(()=>{m&&h&&(T!==null&&google.maps.event.removeListener(T),R(google.maps.event.addListener(m,"dragstart",h)))},[h]),i.useEffect(()=>{m&&x&&(W!==null&&google.maps.event.removeListener(W),q(google.maps.event.addListener(m,"mousedown",x)))},[x]),i.useEffect(()=>{m&&g&&(N!==null&&google.maps.event.removeListener(N),G(google.maps.event.addListener(m,"mousemove",g)))},[g]),i.useEffect(()=>{m&&v&&($!==null&&google.maps.event.removeListener($),K(google.maps.event.addListener(m,"mouseout",v)))},[v]),i.useEffect(()=>{m&&d&&(J!==null&&google.maps.event.removeListener(J),j(google.maps.event.addListener(m,"mouseover",d)))},[d]),i.useEffect(()=>{m&&M&&(Y!==null&&google.maps.event.removeListener(Y),L(google.maps.event.addListener(m,"mouseup",M)))},[M]),i.useEffect(()=>{m&&S&&(ee!==null&&google.maps.event.removeListener(ee),X(google.maps.event.addListener(m,"rightclick",S)))},[S]),i.useEffect(()=>{m&&l&&(z!==null&&google.maps.event.removeListener(z),te(google.maps.event.addListener(m,"click",l)))},[l]),i.useEffect(()=>{m&&c&&(I!==null&&google.maps.event.removeListener(I),F(google.maps.event.addListener(m,"drag",c)))},[c]),i.useEffect(()=>{m&&E&&(D!==null&&google.maps.event.removeListener(D),y(google.maps.event.addListener(m,"center_changed",E)))},[l]),i.useEffect(()=>{var oe=w.current===null?null:new google.maps.Map(w.current,n);return C(oe),oe!==null&&P&&P(oe),()=>{oe!==null&&b&&b(oe)}},[]),se.jsx("div",{id:s,ref:w,style:r,className:o,children:se.jsx(A.Provider,{value:m,children:m!==null?e:null})})}i.memo(cs);class Hi extends i.PureComponent{constructor(){super(...arguments),f(this,"state",{map:null}),f(this,"registeredEvents",[]),f(this,"mapRef",null),f(this,"getInstance",()=>this.mapRef===null?null:new google.maps.Map(this.mapRef,this.props.options)),f(this,"panTo",e=>{var n=this.getInstance();n&&n.panTo(e)}),f(this,"setMapCallback",()=>{this.state.map!==null&&this.props.onLoad&&this.props.onLoad(this.state.map)}),f(this,"getRef",e=>{this.mapRef=e})}componentDidMount(){var e=this.getInstance();this.registeredEvents=_({updaterMap:xt,eventMap:wt,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{map:e}},this.setMapCallback)}componentDidUpdate(e){this.state.map!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:xt,eventMap:wt,prevProps:e,nextProps:this.props,instance:this.state.map}))}componentWillUnmount(){this.state.map!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.map),Z(this.registeredEvents))}render(){return se.jsx("div",{id:this.props.id,ref:this.getRef,style:this.props.mapContainerStyle,className:this.props.mapContainerClassName,children:se.jsx(A.Provider,{value:this.state.map,children:this.state.map!==null?this.props.children:null})})}}function St(t,e,n,s,r,o,a){try{var l=t[o](a),u=l.value}catch(c){return void n(c)}l.done?e(u):Promise.resolve(u).then(s,r)}function Vn(t){return function(){var e=this,n=arguments;return new Promise(function(s,r){var o=t.apply(e,n);function a(u){St(o,s,r,a,l,"next",u)}function l(u){St(o,s,r,a,l,"throw",u)}a(void 0)})}}function Wn(t){var{googleMapsApiKey:e,googleMapsClientId:n,version:s="weekly",language:r,region:o,libraries:a,channel:l,mapIds:u,authReferrerPolicy:c}=t,p=[];return ne(e&&n||!(e&&n),"You need to specify either googleMapsApiKey or googleMapsClientId for @react-google-maps/api load script to work. You cannot use both at the same time."),e?p.push("key=".concat(e)):n&&p.push("client=".concat(n)),s&&p.push("v=".concat(s)),r&&p.push("language=".concat(r)),o&&p.push("region=".concat(o)),a&&a.length&&p.push("libraries=".concat(a.sort().join(","))),l&&p.push("channel=".concat(l)),u&&u.length&&p.push("map_ids=".concat(u.join(","))),c&&p.push("auth_referrer_policy=".concat(c)),p.push("loading=async"),p.push("callback=initMap"),"https://maps.googleapis.com/maps/api/js?".concat(p.join("&"))}var de=typeof document<"u";function Nn(t){var{url:e,id:n,nonce:s}=t;return de?new Promise(function(o,a){var l=document.getElementById(n),u=window;if(l){var c=l.getAttribute("data-state");if(l.src===e&&c!=="error"){if(c==="ready")return o(n);var p=u.initMap,h=l.onerror;u.initMap=function(){p&&p(),o(n)},l.onerror=function(v){h&&h(v),a(v)};return}else l.remove()}var g=document.createElement("script");g.type="text/javascript",g.src=e,g.id=n,g.async=!0,g.nonce=s||"",g.onerror=function(d){g.setAttribute("data-state","error"),a(d)},u.initMap=function(){g.setAttribute("data-state","ready"),o(n)},document.head.appendChild(g)}).catch(r=>{throw console.error("injectScript error: ",r),r}):Promise.reject(new Error("document is undefined"))}function kt(t){var e=t.href;return e&&(e.indexOf("https://fonts.googleapis.com/css?family=Roboto")===0||e.indexOf("https://fonts.googleapis.com/css?family=Google+Sans+Text")===0)?!0:t.tagName.toLowerCase()==="style"&&t.styleSheet&&t.styleSheet.cssText&&t.styleSheet.cssText.replace(`\r
`,"").indexOf(".gm-style")===0?(t.styleSheet.cssText="",!0):t.tagName.toLowerCase()==="style"&&t.innerHTML&&t.innerHTML.replace(`\r
`,"").indexOf(".gm-style")===0?(t.innerHTML="",!0):t.tagName.toLowerCase()==="style"&&!t.styleSheet&&!t.innerHTML}function $n(){var t=document.getElementsByTagName("head")[0];if(t){var e=t.insertBefore.bind(t);t.insertBefore=function(r,o){return kt(r)||Reflect.apply(e,t,[r,o]),r};var n=t.appendChild.bind(t);t.appendChild=function(r){return kt(r)||Reflect.apply(n,t,[r]),r}}}var me=!1;function Fn(){return se.jsx("div",{children:"Loading..."})}var Qe={id:"script-loader",version:"weekly"};class gs extends i.PureComponent{constructor(){super(...arguments),f(this,"check",i.createRef()),f(this,"state",{loaded:!1}),f(this,"cleanupCallback",()=>{delete window.google.maps,this.injectScript()}),f(this,"isCleaningUp",Vn(function*(){function e(n){if(!me)n();else if(de)var s=window.setInterval(function(){me||(window.clearInterval(s),n())},1)}return new Promise(e)})),f(this,"cleanup",()=>{me=!0;var e=document.getElementById(this.props.id);e&&e.parentNode&&e.parentNode.removeChild(e),Array.prototype.slice.call(document.getElementsByTagName("script")).filter(function(s){return typeof s.src=="string"&&s.src.includes("maps.googleapis")}).forEach(function(s){s.parentNode&&s.parentNode.removeChild(s)}),Array.prototype.slice.call(document.getElementsByTagName("link")).filter(function(s){return s.href==="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Google+Sans"}).forEach(function(s){s.parentNode&&s.parentNode.removeChild(s)}),Array.prototype.slice.call(document.getElementsByTagName("style")).filter(function(s){return s.innerText!==void 0&&s.innerText.length>0&&s.innerText.includes(".gm-")}).forEach(function(s){s.parentNode&&s.parentNode.removeChild(s)})}),f(this,"injectScript",()=>{this.props.preventGoogleFontsLoading&&$n(),ne(!!this.props.id,'LoadScript requires "id" prop to be a string: %s',this.props.id);var e={id:this.props.id,nonce:this.props.nonce,url:Wn(this.props)};Nn(e).then(()=>{this.props.onLoad&&this.props.onLoad(),this.setState(function(){return{loaded:!0}})}).catch(n=>{this.props.onError&&this.props.onError(n),console.error(`
          There has been an Error with loading Google Maps API script, please check that you provided correct google API key (`.concat(this.props.googleMapsApiKey||"-",") or Client ID (").concat(this.props.googleMapsClientId||"-",`) to <LoadScript />
          Otherwise it is a Network issue.
        `))})})}componentDidMount(){if(de){if(window.google&&window.google.maps&&!me){console.error("google api is already presented");return}this.isCleaningUp().then(this.injectScript).catch(function(n){console.error("Error at injecting script after cleaning up: ",n)})}}componentDidUpdate(e){this.props.libraries!==e.libraries&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),de&&e.language!==this.props.language&&(this.cleanup(),this.setState(function(){return{loaded:!1}},this.cleanupCallback))}componentWillUnmount(){if(de){this.cleanup();var e=()=>{this.check.current||(delete window.google,me=!1)};window.setTimeout(e,1),this.props.onUnmount&&this.props.onUnmount()}}render(){return se.jsxs(se.Fragment,{children:[se.jsx("div",{ref:this.check}),this.state.loaded?this.props.children:this.props.loadingElement||se.jsx(Fn,{})]})}}f(gs,"defaultProps",Qe);function hs(t,e){if(t==null)return{};var n={};for(var s in t)if({}.hasOwnProperty.call(t,s)){if(e.includes(s))continue;n[s]=t[s]}return n}function tt(t,e){if(t==null)return{};var n,s,r=hs(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(s=0;s<o.length;s++)n=o[s],e.includes(n)||{}.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}var Ot;function fs(t){var{id:e=Qe.id,version:n=Qe.version,nonce:s,googleMapsApiKey:r,googleMapsClientId:o,language:a,region:l,libraries:u,preventGoogleFontsLoading:c,channel:p,mapIds:h,authReferrerPolicy:g}=t,v=i.useRef(!1),[d,x]=i.useState(!1),[M,S]=i.useState(void 0);i.useEffect(function(){return v.current=!0,()=>{v.current=!1}},[]),i.useEffect(function(){de&&c&&$n()},[c]),i.useEffect(function(){d&&ne(!!window.google,"useLoadScript was marked as loaded, but window.google is not present. Something went wrong.")},[d]);var E=Wn({version:n,googleMapsApiKey:r,googleMapsClientId:o,language:a,region:l,libraries:u,channel:p,mapIds:h,authReferrerPolicy:g});i.useEffect(function(){if(!de)return;function m(){v.current&&(x(!0),Ot=E)}if(window.google&&window.google.maps&&Ot===E){m();return}Nn({id:e,url:E,nonce:s}).then(m).catch(function(w){v.current&&S(w),console.warn(`
        There has been an Error with loading Google Maps API script, please check that you provided correct google API key (`.concat(r||"-",") or Client ID (").concat(o||"-",`)
        Otherwise it is a Network issue.
      `)),console.error(w)})},[e,E,s]);var P=i.useRef();return i.useEffect(function(){P.current&&u!==P.current&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),P.current=u},[u]),{isLoaded:d,loadError:M,url:E}}var vs=["loadingElement","onLoad","onError","onUnmount","children"],ms=se.jsx(Fn,{});function Ls(t){var{loadingElement:e,onLoad:n,onError:s,onUnmount:r,children:o}=t,a=tt(t,vs),{isLoaded:l,loadError:u}=fs(a);return i.useEffect(function(){l&&typeof n=="function"&&n()},[l,n]),i.useEffect(function(){u&&typeof s=="function"&&s(u)},[u,s]),i.useEffect(function(){return()=>{r&&r()}},[r]),l?o:e||ms}i.memo(Ls);var Pt;(function(t){t[t.INITIALIZED=0]="INITIALIZED",t[t.LOADING=1]="LOADING",t[t.SUCCESS=2]="SUCCESS",t[t.FAILURE=3]="FAILURE"})(Pt||(Pt={}));function Dt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function ze(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Dt(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Dt(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var It={},jt={options(t,e){t.setOptions(e)}};function ys(t){var{options:e,onLoad:n,onUnmount:s}=t,r=i.useContext(A),[o,a]=i.useState(null);return i.useEffect(()=>{o!==null&&o.setMap(r)},[r]),i.useEffect(()=>{e&&o!==null&&o.setOptions(e)},[o,e]),i.useEffect(()=>{var l=new google.maps.TrafficLayer(ze(ze({},e),{},{map:r}));return a(l),n&&n(l),()=>{o!==null&&(s&&s(o),o.setMap(null))}},[]),null}i.memo(ys);class bs extends i.PureComponent{constructor(){super(...arguments),f(this,"state",{trafficLayer:null}),f(this,"setTrafficLayerCallback",()=>{this.state.trafficLayer!==null&&this.props.onLoad&&this.props.onLoad(this.state.trafficLayer)}),f(this,"registeredEvents",[])}componentDidMount(){var e=new google.maps.TrafficLayer(ze(ze({},this.props.options),{},{map:this.context}));this.registeredEvents=_({updaterMap:jt,eventMap:It,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{trafficLayer:e}},this.setTrafficLayerCallback)}componentDidUpdate(e){this.state.trafficLayer!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:jt,eventMap:It,prevProps:e,nextProps:this.props,instance:this.state.trafficLayer}))}componentWillUnmount(){this.state.trafficLayer!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.trafficLayer),Z(this.registeredEvents),this.state.trafficLayer.setMap(null))}render(){return null}}f(bs,"contextType",A);function Cs(t){var{onLoad:e,onUnmount:n}=t,s=i.useContext(A),[r,o]=i.useState(null);return i.useEffect(()=>{r!==null&&r.setMap(s)},[s]),i.useEffect(()=>{var a=new google.maps.BicyclingLayer;return o(a),a.setMap(s),e&&e(a),()=>{a!==null&&(n&&n(a),a.setMap(null))}},[]),null}i.memo(Cs);class Ms extends i.PureComponent{constructor(){super(...arguments),f(this,"state",{bicyclingLayer:null}),f(this,"setBicyclingLayerCallback",()=>{this.state.bicyclingLayer!==null&&(this.state.bicyclingLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.bicyclingLayer))})}componentDidMount(){var e=new google.maps.BicyclingLayer;this.setState(()=>({bicyclingLayer:e}),this.setBicyclingLayerCallback)}componentWillUnmount(){this.state.bicyclingLayer!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.bicyclingLayer),this.state.bicyclingLayer.setMap(null))}render(){return null}}f(Ms,"contextType",A);function Es(t){var{onLoad:e,onUnmount:n}=t,s=i.useContext(A),[r,o]=i.useState(null);return i.useEffect(()=>{r!==null&&r.setMap(s)},[s]),i.useEffect(()=>{var a=new google.maps.TransitLayer;return o(a),a.setMap(s),e&&e(a),()=>{r!==null&&(n&&n(r),r.setMap(null))}},[]),null}i.memo(Es);class ws extends i.PureComponent{constructor(){super(...arguments),f(this,"state",{transitLayer:null}),f(this,"setTransitLayerCallback",()=>{this.state.transitLayer!==null&&(this.state.transitLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.transitLayer))})}componentDidMount(){var e=new google.maps.TransitLayer;this.setState(function(){return{transitLayer:e}},this.setTransitLayerCallback)}componentWillUnmount(){this.state.transitLayer!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.transitLayer),this.state.transitLayer.setMap(null))}render(){return null}}f(ws,"contextType",A);function Tt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function _e(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Tt(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Tt(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var Rt={onCircleComplete:"circlecomplete",onMarkerComplete:"markercomplete",onOverlayComplete:"overlaycomplete",onPolygonComplete:"polygoncomplete",onPolylineComplete:"polylinecomplete",onRectangleComplete:"rectanglecomplete"},Bt={drawingMode(t,e){t.setDrawingMode(e)},options(t,e){t.setOptions(e)}};function xs(t){var{options:e,drawingMode:n,onCircleComplete:s,onMarkerComplete:r,onOverlayComplete:o,onPolygonComplete:a,onPolylineComplete:l,onRectangleComplete:u,onLoad:c,onUnmount:p}=t,h=i.useContext(A),[g,v]=i.useState(null),[d,x]=i.useState(null),[M,S]=i.useState(null),[E,P]=i.useState(null),[b,m]=i.useState(null),[C,w]=i.useState(null),[D,y]=i.useState(null);return i.useEffect(()=>{g!==null&&g.setMap(h)},[h]),i.useEffect(()=>{e&&g!==null&&g.setOptions(e)},[g,e]),i.useEffect(()=>{g!==null&&g.setDrawingMode(n??null)},[g,n]),i.useEffect(()=>{g&&s&&(d!==null&&google.maps.event.removeListener(d),x(google.maps.event.addListener(g,"circlecomplete",s)))},[g,s]),i.useEffect(()=>{g&&r&&(M!==null&&google.maps.event.removeListener(M),S(google.maps.event.addListener(g,"markercomplete",r)))},[g,r]),i.useEffect(()=>{g&&o&&(E!==null&&google.maps.event.removeListener(E),P(google.maps.event.addListener(g,"overlaycomplete",o)))},[g,o]),i.useEffect(()=>{g&&a&&(b!==null&&google.maps.event.removeListener(b),m(google.maps.event.addListener(g,"polygoncomplete",a)))},[g,a]),i.useEffect(()=>{g&&l&&(C!==null&&google.maps.event.removeListener(C),w(google.maps.event.addListener(g,"polylinecomplete",l)))},[g,l]),i.useEffect(()=>{g&&u&&(D!==null&&google.maps.event.removeListener(D),y(google.maps.event.addListener(g,"rectanglecomplete",u)))},[g,u]),i.useEffect(()=>{ne(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing);var k=new google.maps.drawing.DrawingManager(_e(_e({},e),{},{map:h}));return n&&k.setDrawingMode(n),s&&x(google.maps.event.addListener(k,"circlecomplete",s)),r&&S(google.maps.event.addListener(k,"markercomplete",r)),o&&P(google.maps.event.addListener(k,"overlaycomplete",o)),a&&m(google.maps.event.addListener(k,"polygoncomplete",a)),l&&w(google.maps.event.addListener(k,"polylinecomplete",l)),u&&y(google.maps.event.addListener(k,"rectanglecomplete",u)),v(k),c&&c(k),()=>{g!==null&&(d&&google.maps.event.removeListener(d),M&&google.maps.event.removeListener(M),E&&google.maps.event.removeListener(E),b&&google.maps.event.removeListener(b),C&&google.maps.event.removeListener(C),D&&google.maps.event.removeListener(D),p&&p(g),g.setMap(null))}},[]),null}i.memo(xs);class Ss extends i.PureComponent{constructor(e){super(e),f(this,"registeredEvents",[]),f(this,"state",{drawingManager:null}),f(this,"setDrawingManagerCallback",()=>{this.state.drawingManager!==null&&this.props.onLoad&&this.props.onLoad(this.state.drawingManager)}),ne(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing)}componentDidMount(){var e=new google.maps.drawing.DrawingManager(_e(_e({},this.props.options),{},{map:this.context}));this.registeredEvents=_({updaterMap:Bt,eventMap:Rt,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{drawingManager:e}},this.setDrawingManagerCallback)}componentDidUpdate(e){this.state.drawingManager!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:Bt,eventMap:Rt,prevProps:e,nextProps:this.props,instance:this.state.drawingManager}))}componentWillUnmount(){this.state.drawingManager!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.drawingManager),Z(this.registeredEvents),this.state.drawingManager.setMap(null))}render(){return null}}f(Ss,"contextType",A);function Ut(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function ce(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Ut(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Ut(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var At={onAnimationChanged:"animation_changed",onClick:"click",onClickableChanged:"clickable_changed",onCursorChanged:"cursor_changed",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDraggableChanged:"draggable_changed",onDragStart:"dragstart",onFlatChanged:"flat_changed",onIconChanged:"icon_changed",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onPositionChanged:"position_changed",onRightClick:"rightclick",onShapeChanged:"shape_changed",onTitleChanged:"title_changed",onVisibleChanged:"visible_changed",onZindexChanged:"zindex_changed"},zt={animation(t,e){t.setAnimation(e)},clickable(t,e){t.setClickable(e)},cursor(t,e){t.setCursor(e)},draggable(t,e){t.setDraggable(e)},icon(t,e){t.setIcon(e)},label(t,e){t.setLabel(e)},map(t,e){t.setMap(e)},opacity(t,e){t.setOpacity(e)},options(t,e){t.setOptions(e)},position(t,e){t.setPosition(e)},shape(t,e){t.setShape(e)},title(t,e){t.setTitle(e)},visible(t,e){t.setVisible(e)},zIndex(t,e){t.setZIndex(e)}},Ze={};function ks(t){var{position:e,options:n,clusterer:s,noClustererRedraw:r,children:o,draggable:a,visible:l,animation:u,clickable:c,cursor:p,icon:h,label:g,opacity:v,shape:d,title:x,zIndex:M,onClick:S,onDblClick:E,onDrag:P,onDragEnd:b,onDragStart:m,onMouseOut:C,onMouseOver:w,onMouseUp:D,onMouseDown:y,onRightClick:k,onClickableChanged:O,onCursorChanged:U,onAnimationChanged:V,onDraggableChanged:T,onFlatChanged:R,onIconChanged:W,onPositionChanged:q,onShapeChanged:N,onTitleChanged:G,onVisibleChanged:$,onZindexChanged:K,onLoad:J,onUnmount:j}=t,Y=i.useContext(A),[L,ee]=i.useState(null),[X,z]=i.useState(null),[te,I]=i.useState(null),[F,oe]=i.useState(null),[ae,fe]=i.useState(null),[Q,it]=i.useState(null),[Me,rt]=i.useState(null),[Ee,ot]=i.useState(null),[we,at]=i.useState(null),[xe,lt]=i.useState(null),[ut,pt]=i.useState(null),[Se,dt]=i.useState(null),[ke,ct]=i.useState(null),[Oe,gt]=i.useState(null),[Pe,ht]=i.useState(null),[De,ft]=i.useState(null),[Ie,vt]=i.useState(null),[je,mt]=i.useState(null),[Lt,yt]=i.useState(null),[Te,bt]=i.useState(null),[Re,Ct]=i.useState(null),[Be,Mt]=i.useState(null);i.useEffect(()=>{L!==null&&L.setMap(Y)},[Y]),i.useEffect(()=>{typeof n<"u"&&L!==null&&L.setOptions(n)},[L,n]),i.useEffect(()=>{typeof a<"u"&&L!==null&&L.setDraggable(a)},[L,a]),i.useEffect(()=>{e&&L!==null&&L.setPosition(e)},[L,e]),i.useEffect(()=>{typeof l<"u"&&L!==null&&L.setVisible(l)},[L,l]),i.useEffect(()=>{L==null||L.setAnimation(u)},[L,u]),i.useEffect(()=>{L&&c!==void 0&&L.setClickable(c)},[L,c]),i.useEffect(()=>{L&&p!==void 0&&L.setCursor(p)},[L,p]),i.useEffect(()=>{L&&h!==void 0&&L.setIcon(h)},[L,h]),i.useEffect(()=>{L&&g!==void 0&&L.setLabel(g)},[L,g]),i.useEffect(()=>{L&&v!==void 0&&L.setOpacity(v)},[L,v]),i.useEffect(()=>{L&&d!==void 0&&L.setShape(d)},[L,d]),i.useEffect(()=>{L&&x!==void 0&&L.setTitle(x)},[L,x]),i.useEffect(()=>{L&&M!==void 0&&L.setZIndex(M)},[L,M]),i.useEffect(()=>{L&&E&&(X!==null&&google.maps.event.removeListener(X),z(google.maps.event.addListener(L,"dblclick",E)))},[E]),i.useEffect(()=>{L&&b&&(te!==null&&google.maps.event.removeListener(te),I(google.maps.event.addListener(L,"dragend",b)))},[b]),i.useEffect(()=>{L&&m&&(F!==null&&google.maps.event.removeListener(F),oe(google.maps.event.addListener(L,"dragstart",m)))},[m]),i.useEffect(()=>{L&&y&&(ae!==null&&google.maps.event.removeListener(ae),fe(google.maps.event.addListener(L,"mousedown",y)))},[y]),i.useEffect(()=>{L&&C&&(Q!==null&&google.maps.event.removeListener(Q),it(google.maps.event.addListener(L,"mouseout",C)))},[C]),i.useEffect(()=>{L&&w&&(Me!==null&&google.maps.event.removeListener(Me),rt(google.maps.event.addListener(L,"mouseover",w)))},[w]),i.useEffect(()=>{L&&D&&(Ee!==null&&google.maps.event.removeListener(Ee),ot(google.maps.event.addListener(L,"mouseup",D)))},[D]),i.useEffect(()=>{L&&k&&(we!==null&&google.maps.event.removeListener(we),at(google.maps.event.addListener(L,"rightclick",k)))},[k]),i.useEffect(()=>{L&&S&&(xe!==null&&google.maps.event.removeListener(xe),lt(google.maps.event.addListener(L,"click",S)))},[S]),i.useEffect(()=>{L&&P&&(ut!==null&&google.maps.event.removeListener(ut),pt(google.maps.event.addListener(L,"drag",P)))},[P]),i.useEffect(()=>{L&&O&&(Se!==null&&google.maps.event.removeListener(Se),dt(google.maps.event.addListener(L,"clickable_changed",O)))},[O]),i.useEffect(()=>{L&&U&&(ke!==null&&google.maps.event.removeListener(ke),ct(google.maps.event.addListener(L,"cursor_changed",U)))},[U]),i.useEffect(()=>{L&&V&&(Oe!==null&&google.maps.event.removeListener(Oe),gt(google.maps.event.addListener(L,"animation_changed",V)))},[V]),i.useEffect(()=>{L&&T&&(Pe!==null&&google.maps.event.removeListener(Pe),ht(google.maps.event.addListener(L,"draggable_changed",T)))},[T]),i.useEffect(()=>{L&&R&&(De!==null&&google.maps.event.removeListener(De),ft(google.maps.event.addListener(L,"flat_changed",R)))},[R]),i.useEffect(()=>{L&&W&&(Ie!==null&&google.maps.event.removeListener(Ie),vt(google.maps.event.addListener(L,"icon_changed",W)))},[W]),i.useEffect(()=>{L&&q&&(je!==null&&google.maps.event.removeListener(je),mt(google.maps.event.addListener(L,"position_changed",q)))},[q]),i.useEffect(()=>{L&&N&&(Lt!==null&&google.maps.event.removeListener(Lt),yt(google.maps.event.addListener(L,"shape_changed",N)))},[N]),i.useEffect(()=>{L&&G&&(Te!==null&&google.maps.event.removeListener(Te),bt(google.maps.event.addListener(L,"title_changed",G)))},[G]),i.useEffect(()=>{L&&$&&(Re!==null&&google.maps.event.removeListener(Re),Ct(google.maps.event.addListener(L,"visible_changed",$)))},[$]),i.useEffect(()=>{L&&K&&(Be!==null&&google.maps.event.removeListener(Be),Mt(google.maps.event.addListener(L,"zindex_changed",K)))},[K]),i.useEffect(()=>{var ve=ce(ce(ce({},n||Ze),s?Ze:{map:Y}),{},{position:e}),B=new google.maps.Marker(ve);return s?s.addMarker(B,!!r):B.setMap(Y),e&&B.setPosition(e),typeof l<"u"&&B.setVisible(l),typeof a<"u"&&B.setDraggable(a),typeof c<"u"&&B.setClickable(c),typeof p=="string"&&B.setCursor(p),h&&B.setIcon(h),typeof g<"u"&&B.setLabel(g),typeof v<"u"&&B.setOpacity(v),d&&B.setShape(d),typeof x=="string"&&B.setTitle(x),typeof M=="number"&&B.setZIndex(M),E&&z(google.maps.event.addListener(B,"dblclick",E)),b&&I(google.maps.event.addListener(B,"dragend",b)),m&&oe(google.maps.event.addListener(B,"dragstart",m)),y&&fe(google.maps.event.addListener(B,"mousedown",y)),C&&it(google.maps.event.addListener(B,"mouseout",C)),w&&rt(google.maps.event.addListener(B,"mouseover",w)),D&&ot(google.maps.event.addListener(B,"mouseup",D)),k&&at(google.maps.event.addListener(B,"rightclick",k)),S&&lt(google.maps.event.addListener(B,"click",S)),P&&pt(google.maps.event.addListener(B,"drag",P)),O&&dt(google.maps.event.addListener(B,"clickable_changed",O)),U&&ct(google.maps.event.addListener(B,"cursor_changed",U)),V&&gt(google.maps.event.addListener(B,"animation_changed",V)),T&&ht(google.maps.event.addListener(B,"draggable_changed",T)),R&&ft(google.maps.event.addListener(B,"flat_changed",R)),W&&vt(google.maps.event.addListener(B,"icon_changed",W)),q&&mt(google.maps.event.addListener(B,"position_changed",q)),N&&yt(google.maps.event.addListener(B,"shape_changed",N)),G&&bt(google.maps.event.addListener(B,"title_changed",G)),$&&Ct(google.maps.event.addListener(B,"visible_changed",$)),K&&Mt(google.maps.event.addListener(B,"zindex_changed",K)),ee(B),J&&J(B),()=>{X!==null&&google.maps.event.removeListener(X),te!==null&&google.maps.event.removeListener(te),F!==null&&google.maps.event.removeListener(F),ae!==null&&google.maps.event.removeListener(ae),Q!==null&&google.maps.event.removeListener(Q),Me!==null&&google.maps.event.removeListener(Me),Ee!==null&&google.maps.event.removeListener(Ee),we!==null&&google.maps.event.removeListener(we),xe!==null&&google.maps.event.removeListener(xe),Se!==null&&google.maps.event.removeListener(Se),ke!==null&&google.maps.event.removeListener(ke),Oe!==null&&google.maps.event.removeListener(Oe),Pe!==null&&google.maps.event.removeListener(Pe),De!==null&&google.maps.event.removeListener(De),Ie!==null&&google.maps.event.removeListener(Ie),je!==null&&google.maps.event.removeListener(je),Te!==null&&google.maps.event.removeListener(Te),Re!==null&&google.maps.event.removeListener(Re),Be!==null&&google.maps.event.removeListener(Be),j&&j(B),s?s.removeMarker(B,!!r):B&&B.setMap(null)}},[]);var ts=i.useMemo(()=>o?i.Children.map(o,ve=>{if(!i.isValidElement(ve))return ve;var B=ve;return i.cloneElement(B,{anchor:L})}):null,[o,L]);return se.jsx(se.Fragment,{children:ts})||null}i.memo(ks);class Os extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[])}componentDidMount(){var e=this;return Vn(function*(){var n=ce(ce(ce({},e.props.options||Ze),e.props.clusterer?Ze:{map:e.context}),{},{position:e.props.position});e.marker=new google.maps.Marker(n),e.props.clusterer?e.props.clusterer.addMarker(e.marker,!!e.props.noClustererRedraw):e.marker.setMap(e.context),e.registeredEvents=_({updaterMap:zt,eventMap:At,prevProps:{},nextProps:e.props,instance:e.marker}),e.props.onLoad&&e.props.onLoad(e.marker)})()}componentDidUpdate(e){this.marker&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:zt,eventMap:At,prevProps:e,nextProps:this.props,instance:this.marker}))}componentWillUnmount(){this.marker&&(this.props.onUnmount&&this.props.onUnmount(this.marker),Z(this.registeredEvents),this.props.clusterer?this.props.clusterer.removeMarker(this.marker,!!this.props.noClustererRedraw):this.marker&&this.marker.setMap(null))}render(){var e=this.props.children?i.Children.map(this.props.children,n=>{if(!i.isValidElement(n))return n;var s=n;return i.cloneElement(s,{anchor:this.marker})}):null;return e||null}}f(Os,"contextType",A);var Ps=function(){function t(e,n){e.getClusterer().extend(t,google.maps.OverlayView),this.cluster=e,this.clusterClassName=this.cluster.getClusterer().getClusterClass(),this.className=this.clusterClassName,this.styles=n,this.center=void 0,this.div=null,this.sums=null,this.visible=!1,this.boundsChangedListener=null,this.url="",this.height=0,this.width=0,this.anchorText=[0,0],this.anchorIcon=[0,0],this.textColor="black",this.textSize=11,this.textDecoration="none",this.fontWeight="bold",this.fontStyle="normal",this.fontFamily="Arial,sans-serif",this.backgroundPosition="0 0",this.cMouseDownInCluster=null,this.cDraggingMapByCluster=null,this.timeOut=null,this.setMap(e.getMap()),this.onBoundsChanged=this.onBoundsChanged.bind(this),this.onMouseDown=this.onMouseDown.bind(this),this.onClick=this.onClick.bind(this),this.onMouseOver=this.onMouseOver.bind(this),this.onMouseOut=this.onMouseOut.bind(this),this.onAdd=this.onAdd.bind(this),this.onRemove=this.onRemove.bind(this),this.draw=this.draw.bind(this),this.hide=this.hide.bind(this),this.show=this.show.bind(this),this.useStyle=this.useStyle.bind(this),this.setCenter=this.setCenter.bind(this),this.getPosFromLatLng=this.getPosFromLatLng.bind(this)}return t.prototype.onBoundsChanged=function(){this.cDraggingMapByCluster=this.cMouseDownInCluster},t.prototype.onMouseDown=function(){this.cMouseDownInCluster=!0,this.cDraggingMapByCluster=!1},t.prototype.onClick=function(e){if(this.cMouseDownInCluster=!1,!this.cDraggingMapByCluster){var n=this.cluster.getClusterer();if(google.maps.event.trigger(n,"click",this.cluster),google.maps.event.trigger(n,"clusterclick",this.cluster),n.getZoomOnClick()){var s=n.getMaxZoom(),r=this.cluster.getBounds(),o=n.getMap();o!==null&&"fitBounds"in o&&o.fitBounds(r),this.timeOut=window.setTimeout(function(){var a=n.getMap();if(a!==null){"fitBounds"in a&&a.fitBounds(r);var l=a.getZoom()||0;s!==null&&l>s&&a.setZoom(s+1)}},100)}e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}},t.prototype.onMouseOver=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseover",this.cluster)},t.prototype.onMouseOut=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseout",this.cluster)},t.prototype.onAdd=function(){var e;this.div=document.createElement("div"),this.div.className=this.className,this.visible&&this.show(),(e=this.getPanes())===null||e===void 0||e.overlayMouseTarget.appendChild(this.div);var n=this.getMap();n!==null&&(this.boundsChangedListener=google.maps.event.addListener(n,"bounds_changed",this.onBoundsChanged),this.div.addEventListener("mousedown",this.onMouseDown),this.div.addEventListener("click",this.onClick),this.div.addEventListener("mouseover",this.onMouseOver),this.div.addEventListener("mouseout",this.onMouseOut))},t.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.hide(),this.boundsChangedListener!==null&&google.maps.event.removeListener(this.boundsChangedListener),this.div.removeEventListener("mousedown",this.onMouseDown),this.div.removeEventListener("click",this.onClick),this.div.removeEventListener("mouseover",this.onMouseOver),this.div.removeEventListener("mouseout",this.onMouseOut),this.div.parentNode.removeChild(this.div),this.timeOut!==null&&(window.clearTimeout(this.timeOut),this.timeOut=null),this.div=null)},t.prototype.draw=function(){if(this.visible&&this.div!==null&&this.center){var e=this.getPosFromLatLng(this.center);this.div.style.top=e!==null?"".concat(e.y,"px"):"0",this.div.style.left=e!==null?"".concat(e.x,"px"):"0"}},t.prototype.hide=function(){this.div&&(this.div.style.display="none"),this.visible=!1},t.prototype.show=function(){var e,n,s,r,o,a;if(this.div&&this.center){var l=this.sums===null||typeof this.sums.title>"u"||this.sums.title===""?this.cluster.getClusterer().getTitle():this.sums.title,u=this.backgroundPosition.split(" "),c=parseInt(((e=u[0])===null||e===void 0?void 0:e.replace(/^\s+|\s+$/g,""))||"0",10),p=parseInt(((n=u[1])===null||n===void 0?void 0:n.replace(/^\s+|\s+$/g,""))||"0",10),h=this.getPosFromLatLng(this.center);this.div.className=this.className,this.div.setAttribute("style","cursor: pointer; position: absolute; top: ".concat(h!==null?"".concat(h.y,"px"):"0","; left: ").concat(h!==null?"".concat(h.x,"px"):"0","; width: ").concat(this.width,"px; height: ").concat(this.height,"px; "));var g=document.createElement("img");g.alt=l,g.src=this.url,g.width=this.width,g.height=this.height,g.setAttribute("style","position: absolute; top: ".concat(p,"px; left: ").concat(c,"px")),this.cluster.getClusterer().enableRetinaIcons||(g.style.clip="rect(-".concat(p,"px, -").concat(c+this.width,"px, -").concat(p+this.height,", -").concat(c,")"));var v=document.createElement("div");v.setAttribute("style","position: absolute; top: ".concat(this.anchorText[0],"px; left: ").concat(this.anchorText[1],"px; color: ").concat(this.textColor,"; font-size: ").concat(this.textSize,"px; font-family: ").concat(this.fontFamily,"; font-weight: ").concat(this.fontWeight,"; fontStyle: ").concat(this.fontStyle,"; text-decoration: ").concat(this.textDecoration,"; text-align: center; width: ").concat(this.width,"px; line-height: ").concat(this.height,"px")),!((s=this.sums)===null||s===void 0)&&s.text&&(v.innerText="".concat((r=this.sums)===null||r===void 0?void 0:r.text)),!((o=this.sums)===null||o===void 0)&&o.html&&(v.innerHTML="".concat((a=this.sums)===null||a===void 0?void 0:a.html)),this.div.innerHTML="",this.div.appendChild(g),this.div.appendChild(v),this.div.title=l,this.div.style.display=""}this.visible=!0},t.prototype.useStyle=function(e){this.sums=e;var n=this.cluster.getClusterer().getStyles(),s=n[Math.min(n.length-1,Math.max(0,e.index-1))];s&&(this.url=s.url,this.height=s.height,this.width=s.width,s.className&&(this.className="".concat(this.clusterClassName," ").concat(s.className)),this.anchorText=s.anchorText||[0,0],this.anchorIcon=s.anchorIcon||[this.height/2,this.width/2],this.textColor=s.textColor||"black",this.textSize=s.textSize||11,this.textDecoration=s.textDecoration||"none",this.fontWeight=s.fontWeight||"bold",this.fontStyle=s.fontStyle||"normal",this.fontFamily=s.fontFamily||"Arial,sans-serif",this.backgroundPosition=s.backgroundPosition||"0 0")},t.prototype.setCenter=function(e){this.center=e},t.prototype.getPosFromLatLng=function(e){var n=this.getProjection().fromLatLngToDivPixel(e);return n!==null&&(n.x-=this.anchorIcon[1],n.y-=this.anchorIcon[0]),n},t}(),Ds=function(){function t(e){this.markerClusterer=e,this.map=this.markerClusterer.getMap(),this.gridSize=this.markerClusterer.getGridSize(),this.minClusterSize=this.markerClusterer.getMinimumClusterSize(),this.averageCenter=this.markerClusterer.getAverageCenter(),this.markers=[],this.center=void 0,this.bounds=null,this.clusterIcon=new Ps(this,this.markerClusterer.getStyles()),this.getSize=this.getSize.bind(this),this.getMarkers=this.getMarkers.bind(this),this.getCenter=this.getCenter.bind(this),this.getMap=this.getMap.bind(this),this.getClusterer=this.getClusterer.bind(this),this.getBounds=this.getBounds.bind(this),this.remove=this.remove.bind(this),this.addMarker=this.addMarker.bind(this),this.isMarkerInClusterBounds=this.isMarkerInClusterBounds.bind(this),this.calculateBounds=this.calculateBounds.bind(this),this.updateIcon=this.updateIcon.bind(this),this.isMarkerAlreadyAdded=this.isMarkerAlreadyAdded.bind(this)}return t.prototype.getSize=function(){return this.markers.length},t.prototype.getMarkers=function(){return this.markers},t.prototype.getCenter=function(){return this.center},t.prototype.getMap=function(){return this.map},t.prototype.getClusterer=function(){return this.markerClusterer},t.prototype.getBounds=function(){for(var e=new google.maps.LatLngBounds(this.center,this.center),n=this.getMarkers(),s=0,r=n;s<r.length;s++){var o=r[s],a=o.getPosition();a&&e.extend(a)}return e},t.prototype.remove=function(){this.clusterIcon.setMap(null),this.markers=[],delete this.markers},t.prototype.addMarker=function(e){var n;if(this.isMarkerAlreadyAdded(e))return!1;if(this.center){if(this.averageCenter){var s=e.getPosition();if(s){var r=this.markers.length+1;this.center=new google.maps.LatLng((this.center.lat()*(r-1)+s.lat())/r,(this.center.lng()*(r-1)+s.lng())/r),this.calculateBounds()}}}else{var s=e.getPosition();s&&(this.center=s,this.calculateBounds())}e.isAdded=!0,this.markers.push(e);var o=this.markers.length,a=this.markerClusterer.getMaxZoom(),l=(n=this.map)===null||n===void 0?void 0:n.getZoom();if(a!==null&&typeof l<"u"&&l>a)e.getMap()!==this.map&&e.setMap(this.map);else if(o<this.minClusterSize)e.getMap()!==this.map&&e.setMap(this.map);else if(o===this.minClusterSize)for(var u=0,c=this.markers;u<c.length;u++){var p=c[u];p.setMap(null)}else e.setMap(null);return!0},t.prototype.isMarkerInClusterBounds=function(e){if(this.bounds!==null){var n=e.getPosition();if(n)return this.bounds.contains(n)}return!1},t.prototype.calculateBounds=function(){this.bounds=this.markerClusterer.getExtendedBounds(new google.maps.LatLngBounds(this.center,this.center))},t.prototype.updateIcon=function(){var e,n=this.markers.length,s=this.markerClusterer.getMaxZoom(),r=(e=this.map)===null||e===void 0?void 0:e.getZoom();if(s!==null&&typeof r<"u"&&r>s){this.clusterIcon.hide();return}if(n<this.minClusterSize){this.clusterIcon.hide();return}this.center&&this.clusterIcon.setCenter(this.center),this.clusterIcon.useStyle(this.markerClusterer.getCalculator()(this.markers,this.markerClusterer.getStyles().length)),this.clusterIcon.show()},t.prototype.isMarkerAlreadyAdded=function(e){if(this.markers.includes)return this.markers.includes(e);for(var n=0;n<this.markers.length;n++)if(e===this.markers[n])return!0;return!1},t}();function Is(t,e){var n=t.length,s=n.toString().length,r=Math.min(s,e);return{text:n.toString(),index:r,title:""}}var js=2e3,Ts=500,Rs="https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m",Bs="png",Us=[53,56,66,78,90],As="cluster",Hn=function(){function t(e,n,s){n===void 0&&(n=[]),s===void 0&&(s={}),this.getMinimumClusterSize=this.getMinimumClusterSize.bind(this),this.setMinimumClusterSize=this.setMinimumClusterSize.bind(this),this.getEnableRetinaIcons=this.getEnableRetinaIcons.bind(this),this.setEnableRetinaIcons=this.setEnableRetinaIcons.bind(this),this.addToClosestCluster=this.addToClosestCluster.bind(this),this.getImageExtension=this.getImageExtension.bind(this),this.setImageExtension=this.setImageExtension.bind(this),this.getExtendedBounds=this.getExtendedBounds.bind(this),this.getAverageCenter=this.getAverageCenter.bind(this),this.setAverageCenter=this.setAverageCenter.bind(this),this.getTotalClusters=this.getTotalClusters.bind(this),this.fitMapToMarkers=this.fitMapToMarkers.bind(this),this.getIgnoreHidden=this.getIgnoreHidden.bind(this),this.setIgnoreHidden=this.setIgnoreHidden.bind(this),this.getClusterClass=this.getClusterClass.bind(this),this.setClusterClass=this.setClusterClass.bind(this),this.getTotalMarkers=this.getTotalMarkers.bind(this),this.getZoomOnClick=this.getZoomOnClick.bind(this),this.setZoomOnClick=this.setZoomOnClick.bind(this),this.getBatchSizeIE=this.getBatchSizeIE.bind(this),this.setBatchSizeIE=this.setBatchSizeIE.bind(this),this.createClusters=this.createClusters.bind(this),this.onZoomChanged=this.onZoomChanged.bind(this),this.getImageSizes=this.getImageSizes.bind(this),this.setImageSizes=this.setImageSizes.bind(this),this.getCalculator=this.getCalculator.bind(this),this.setCalculator=this.setCalculator.bind(this),this.removeMarkers=this.removeMarkers.bind(this),this.resetViewport=this.resetViewport.bind(this),this.getImagePath=this.getImagePath.bind(this),this.setImagePath=this.setImagePath.bind(this),this.pushMarkerTo=this.pushMarkerTo.bind(this),this.removeMarker=this.removeMarker.bind(this),this.clearMarkers=this.clearMarkers.bind(this),this.setupStyles=this.setupStyles.bind(this),this.getGridSize=this.getGridSize.bind(this),this.setGridSize=this.setGridSize.bind(this),this.getClusters=this.getClusters.bind(this),this.getMaxZoom=this.getMaxZoom.bind(this),this.setMaxZoom=this.setMaxZoom.bind(this),this.getMarkers=this.getMarkers.bind(this),this.addMarkers=this.addMarkers.bind(this),this.getStyles=this.getStyles.bind(this),this.setStyles=this.setStyles.bind(this),this.addMarker=this.addMarker.bind(this),this.onRemove=this.onRemove.bind(this),this.getTitle=this.getTitle.bind(this),this.setTitle=this.setTitle.bind(this),this.repaint=this.repaint.bind(this),this.onIdle=this.onIdle.bind(this),this.redraw=this.redraw.bind(this),this.onAdd=this.onAdd.bind(this),this.draw=this.draw.bind(this),this.extend=this.extend.bind(this),this.extend(t,google.maps.OverlayView),this.markers=[],this.clusters=[],this.listeners=[],this.activeMap=null,this.ready=!1,this.gridSize=s.gridSize||60,this.minClusterSize=s.minimumClusterSize||2,this.maxZoom=s.maxZoom||null,this.styles=s.styles||[],this.title=s.title||"",this.zoomOnClick=!0,s.zoomOnClick!==void 0&&(this.zoomOnClick=s.zoomOnClick),this.averageCenter=!1,s.averageCenter!==void 0&&(this.averageCenter=s.averageCenter),this.ignoreHidden=!1,s.ignoreHidden!==void 0&&(this.ignoreHidden=s.ignoreHidden),this.enableRetinaIcons=!1,s.enableRetinaIcons!==void 0&&(this.enableRetinaIcons=s.enableRetinaIcons),this.imagePath=s.imagePath||Rs,this.imageExtension=s.imageExtension||Bs,this.imageSizes=s.imageSizes||Us,this.calculator=s.calculator||Is,this.batchSize=s.batchSize||js,this.batchSizeIE=s.batchSizeIE||Ts,this.clusterClass=s.clusterClass||As,navigator.userAgent.toLowerCase().indexOf("msie")!==-1&&(this.batchSize=this.batchSizeIE),this.timerRefStatic=null,this.setupStyles(),this.addMarkers(n,!0),this.setMap(e)}return t.prototype.onZoomChanged=function(){var e,n;this.resetViewport(!1),(((e=this.getMap())===null||e===void 0?void 0:e.getZoom())===(this.get("minZoom")||0)||((n=this.getMap())===null||n===void 0?void 0:n.getZoom())===this.get("maxZoom"))&&google.maps.event.trigger(this,"idle")},t.prototype.onIdle=function(){this.redraw()},t.prototype.onAdd=function(){var e=this.getMap();this.activeMap=e,this.ready=!0,this.repaint(),e!==null&&(this.listeners=[google.maps.event.addListener(e,"zoom_changed",this.onZoomChanged),google.maps.event.addListener(e,"idle",this.onIdle)])},t.prototype.onRemove=function(){for(var e=0,n=this.markers;e<n.length;e++){var s=n[e];s.getMap()!==this.activeMap&&s.setMap(this.activeMap)}for(var r=0,o=this.clusters;r<o.length;r++){var a=o[r];a.remove()}this.clusters=[];for(var l=0,u=this.listeners;l<u.length;l++){var c=u[l];google.maps.event.removeListener(c)}this.listeners=[],this.activeMap=null,this.ready=!1},t.prototype.draw=function(){},t.prototype.getMap=function(){return null},t.prototype.getPanes=function(){return null},t.prototype.getProjection=function(){return{fromContainerPixelToLatLng:function(){return null},fromDivPixelToLatLng:function(){return null},fromLatLngToContainerPixel:function(){return null},fromLatLngToDivPixel:function(){return null},getVisibleRegion:function(){return null},getWorldWidth:function(){return 0}}},t.prototype.setMap=function(){},t.prototype.addListener=function(){return{remove:function(){}}},t.prototype.bindTo=function(){},t.prototype.get=function(){},t.prototype.notify=function(){},t.prototype.set=function(){},t.prototype.setValues=function(){},t.prototype.unbind=function(){},t.prototype.unbindAll=function(){},t.prototype.setupStyles=function(){if(!(this.styles.length>0))for(var e=0;e<this.imageSizes.length;e++)this.styles.push({url:"".concat(this.imagePath+(e+1),".").concat(this.imageExtension),height:this.imageSizes[e]||0,width:this.imageSizes[e]||0})},t.prototype.fitMapToMarkers=function(){for(var e=this.getMarkers(),n=new google.maps.LatLngBounds,s=0,r=e;s<r.length;s++){var o=r[s],a=o.getPosition();a&&n.extend(a)}var l=this.getMap();l!==null&&"fitBounds"in l&&l.fitBounds(n)},t.prototype.getGridSize=function(){return this.gridSize},t.prototype.setGridSize=function(e){this.gridSize=e},t.prototype.getMinimumClusterSize=function(){return this.minClusterSize},t.prototype.setMinimumClusterSize=function(e){this.minClusterSize=e},t.prototype.getMaxZoom=function(){return this.maxZoom},t.prototype.setMaxZoom=function(e){this.maxZoom=e},t.prototype.getStyles=function(){return this.styles},t.prototype.setStyles=function(e){this.styles=e},t.prototype.getTitle=function(){return this.title},t.prototype.setTitle=function(e){this.title=e},t.prototype.getZoomOnClick=function(){return this.zoomOnClick},t.prototype.setZoomOnClick=function(e){this.zoomOnClick=e},t.prototype.getAverageCenter=function(){return this.averageCenter},t.prototype.setAverageCenter=function(e){this.averageCenter=e},t.prototype.getIgnoreHidden=function(){return this.ignoreHidden},t.prototype.setIgnoreHidden=function(e){this.ignoreHidden=e},t.prototype.getEnableRetinaIcons=function(){return this.enableRetinaIcons},t.prototype.setEnableRetinaIcons=function(e){this.enableRetinaIcons=e},t.prototype.getImageExtension=function(){return this.imageExtension},t.prototype.setImageExtension=function(e){this.imageExtension=e},t.prototype.getImagePath=function(){return this.imagePath},t.prototype.setImagePath=function(e){this.imagePath=e},t.prototype.getImageSizes=function(){return this.imageSizes},t.prototype.setImageSizes=function(e){this.imageSizes=e},t.prototype.getCalculator=function(){return this.calculator},t.prototype.setCalculator=function(e){this.calculator=e},t.prototype.getBatchSizeIE=function(){return this.batchSizeIE},t.prototype.setBatchSizeIE=function(e){this.batchSizeIE=e},t.prototype.getClusterClass=function(){return this.clusterClass},t.prototype.setClusterClass=function(e){this.clusterClass=e},t.prototype.getMarkers=function(){return this.markers},t.prototype.getTotalMarkers=function(){return this.markers.length},t.prototype.getClusters=function(){return this.clusters},t.prototype.getTotalClusters=function(){return this.clusters.length},t.prototype.addMarker=function(e,n){this.pushMarkerTo(e),n||this.redraw()},t.prototype.addMarkers=function(e,n){for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){var r=e[s];r&&this.pushMarkerTo(r)}n||this.redraw()},t.prototype.pushMarkerTo=function(e){var n=this;e.getDraggable()&&google.maps.event.addListener(e,"dragend",function(){n.ready&&(e.isAdded=!1,n.repaint())}),e.isAdded=!1,this.markers.push(e)},t.prototype.removeMarker_=function(e){var n=-1;if(this.markers.indexOf)n=this.markers.indexOf(e);else for(var s=0;s<this.markers.length;s++)if(e===this.markers[s]){n=s;break}return n===-1?!1:(e.setMap(null),this.markers.splice(n,1),!0)},t.prototype.removeMarker=function(e,n){var s=this.removeMarker_(e);return!n&&s&&this.repaint(),s},t.prototype.removeMarkers=function(e,n){for(var s=!1,r=0,o=e;r<o.length;r++){var a=o[r];s=s||this.removeMarker_(a)}return!n&&s&&this.repaint(),s},t.prototype.clearMarkers=function(){this.resetViewport(!0),this.markers=[]},t.prototype.repaint=function(){var e=this.clusters.slice();this.clusters=[],this.resetViewport(!1),this.redraw(),setTimeout(function(){for(var s=0,r=e;s<r.length;s++){var o=r[s];o.remove()}},0)},t.prototype.getExtendedBounds=function(e){var n=this.getProjection(),s=n.fromLatLngToDivPixel(new google.maps.LatLng(e.getNorthEast().lat(),e.getNorthEast().lng()));s!==null&&(s.x+=this.gridSize,s.y-=this.gridSize);var r=n.fromLatLngToDivPixel(new google.maps.LatLng(e.getSouthWest().lat(),e.getSouthWest().lng()));if(r!==null&&(r.x-=this.gridSize,r.y+=this.gridSize),s!==null){var o=n.fromDivPixelToLatLng(s);o!==null&&e.extend(o)}if(r!==null){var a=n.fromDivPixelToLatLng(r);a!==null&&e.extend(a)}return e},t.prototype.redraw=function(){this.createClusters(0)},t.prototype.resetViewport=function(e){for(var n=0,s=this.clusters;n<s.length;n++){var r=s[n];r.remove()}this.clusters=[];for(var o=0,a=this.markers;o<a.length;o++){var l=a[o];l.isAdded=!1,e&&l.setMap(null)}},t.prototype.distanceBetweenPoints=function(e,n){var s=6371,r=(n.lat()-e.lat())*Math.PI/180,o=(n.lng()-e.lng())*Math.PI/180,a=Math.sin(r/2)*Math.sin(r/2)+Math.cos(e.lat()*Math.PI/180)*Math.cos(n.lat()*Math.PI/180)*Math.sin(o/2)*Math.sin(o/2);return s*(2*Math.atan2(Math.sqrt(a),Math.sqrt(1-a)))},t.prototype.isMarkerInBounds=function(e,n){var s=e.getPosition();return s?n.contains(s):!1},t.prototype.addToClosestCluster=function(e){for(var n,s=4e4,r=null,o=0,a=this.clusters;o<a.length;o++){var l=a[o];n=l;var u=n.getCenter(),c=e.getPosition();if(u&&c){var p=this.distanceBetweenPoints(u,c);p<s&&(s=p,r=n)}}r&&r.isMarkerInClusterBounds(e)?r.addMarker(e):(n=new Ds(this),n.addMarker(e),this.clusters.push(n))},t.prototype.createClusters=function(e){var n=this;if(this.ready){e===0&&(google.maps.event.trigger(this,"clusteringbegin",this),this.timerRefStatic!==null&&(window.clearTimeout(this.timerRefStatic),delete this.timerRefStatic));for(var s=this.getMap(),r=s!==null&&("getBounds"in s)?s.getBounds():null,o=(s==null?void 0:s.getZoom())||0,a=o>3?new google.maps.LatLngBounds(r==null?void 0:r.getSouthWest(),r==null?void 0:r.getNorthEast()):new google.maps.LatLngBounds(new google.maps.LatLng(85.02070771743472,-178.48388434375),new google.maps.LatLng(-85.08136444384544,178.00048865625)),l=this.getExtendedBounds(a),u=Math.min(e+this.batchSize,this.markers.length),c=e;c<u;c++){var p=this.markers[c];p&&!p.isAdded&&this.isMarkerInBounds(p,l)&&(!this.ignoreHidden||this.ignoreHidden&&p.getVisible())&&this.addToClosestCluster(p)}if(u<this.markers.length)this.timerRefStatic=window.setTimeout(function(){n.createClusters(u)},0);else{this.timerRefStatic=null,google.maps.event.trigger(this,"clusteringend",this);for(var h=0,g=this.clusters;h<g.length;h++){var v=g[h];v.updateIcon()}}}},t.prototype.extend=function(e,n){return(function(r){for(var o in r.prototype){var a=o;this.prototype[a]=r.prototype[a]}return this}).apply(e,[n])},t}();function _t(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function zs(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?_t(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):_t(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var re={onClick:"click",onClusteringBegin:"clusteringbegin",onClusteringEnd:"clusteringend",onMouseOut:"mouseout",onMouseOver:"mouseover"},H={averageCenter(t,e){t.setAverageCenter(e)},batchSizeIE(t,e){t.setBatchSizeIE(e)},calculator(t,e){t.setCalculator(e)},clusterClass(t,e){t.setClusterClass(e)},enableRetinaIcons(t,e){t.setEnableRetinaIcons(e)},gridSize(t,e){t.setGridSize(e)},ignoreHidden(t,e){t.setIgnoreHidden(e)},imageExtension(t,e){t.setImageExtension(e)},imagePath(t,e){t.setImagePath(e)},imageSizes(t,e){t.setImageSizes(e)},maxZoom(t,e){t.setMaxZoom(e)},minimumClusterSize(t,e){t.setMinimumClusterSize(e)},styles(t,e){t.setStyles(e)},title(t,e){t.setTitle(e)},zoomOnClick(t,e){t.setZoomOnClick(e)}},_s={};function Zs(t){var{children:e,options:n,averageCenter:s,batchSizeIE:r,calculator:o,clusterClass:a,enableRetinaIcons:l,gridSize:u,ignoreHidden:c,imageExtension:p,imagePath:h,imageSizes:g,maxZoom:v,minimumClusterSize:d,styles:x,title:M,zoomOnClick:S,onClick:E,onClusteringBegin:P,onClusteringEnd:b,onMouseOver:m,onMouseOut:C,onLoad:w,onUnmount:D}=t,[y,k]=i.useState(null),O=i.useContext(A),[U,V]=i.useState(null),[T,R]=i.useState(null),[W,q]=i.useState(null),[N,G]=i.useState(null),[$,K]=i.useState(null);return i.useEffect(()=>{y&&C&&(N!==null&&google.maps.event.removeListener(N),G(google.maps.event.addListener(y,re.onMouseOut,C)))},[C]),i.useEffect(()=>{y&&m&&($!==null&&google.maps.event.removeListener($),K(google.maps.event.addListener(y,re.onMouseOver,m)))},[m]),i.useEffect(()=>{y&&E&&(U!==null&&google.maps.event.removeListener(U),V(google.maps.event.addListener(y,re.onClick,E)))},[E]),i.useEffect(()=>{y&&P&&(T!==null&&google.maps.event.removeListener(T),R(google.maps.event.addListener(y,re.onClusteringBegin,P)))},[P]),i.useEffect(()=>{y&&b&&(W!==null&&google.maps.event.removeListener(W),R(google.maps.event.addListener(y,re.onClusteringEnd,b)))},[b]),i.useEffect(()=>{typeof s<"u"&&y!==null&&H.averageCenter(y,s)},[y,s]),i.useEffect(()=>{typeof r<"u"&&y!==null&&H.batchSizeIE(y,r)},[y,r]),i.useEffect(()=>{typeof o<"u"&&y!==null&&H.calculator(y,o)},[y,o]),i.useEffect(()=>{typeof a<"u"&&y!==null&&H.clusterClass(y,a)},[y,a]),i.useEffect(()=>{typeof l<"u"&&y!==null&&H.enableRetinaIcons(y,l)},[y,l]),i.useEffect(()=>{typeof u<"u"&&y!==null&&H.gridSize(y,u)},[y,u]),i.useEffect(()=>{typeof c<"u"&&y!==null&&H.ignoreHidden(y,c)},[y,c]),i.useEffect(()=>{typeof p<"u"&&y!==null&&H.imageExtension(y,p)},[y,p]),i.useEffect(()=>{typeof h<"u"&&y!==null&&H.imagePath(y,h)},[y,h]),i.useEffect(()=>{typeof g<"u"&&y!==null&&H.imageSizes(y,g)},[y,g]),i.useEffect(()=>{typeof v<"u"&&y!==null&&H.maxZoom(y,v)},[y,v]),i.useEffect(()=>{typeof d<"u"&&y!==null&&H.minimumClusterSize(y,d)},[y,d]),i.useEffect(()=>{typeof x<"u"&&y!==null&&H.styles(y,x)},[y,x]),i.useEffect(()=>{typeof M<"u"&&y!==null&&H.title(y,M)},[y,M]),i.useEffect(()=>{typeof S<"u"&&y!==null&&H.zoomOnClick(y,S)},[y,S]),i.useEffect(()=>{if(O){var J=zs({},n||_s),j=new Hn(O,[],J);return s&&H.averageCenter(j,s),r&&H.batchSizeIE(j,r),o&&H.calculator(j,o),a&&H.clusterClass(j,a),l&&H.enableRetinaIcons(j,l),u&&H.gridSize(j,u),c&&H.ignoreHidden(j,c),p&&H.imageExtension(j,p),h&&H.imagePath(j,h),g&&H.imageSizes(j,g),v&&H.maxZoom(j,v),d&&H.minimumClusterSize(j,d),x&&H.styles(j,x),M&&H.title(j,M),S&&H.zoomOnClick(j,S),C&&G(google.maps.event.addListener(j,re.onMouseOut,C)),m&&K(google.maps.event.addListener(j,re.onMouseOver,m)),E&&V(google.maps.event.addListener(j,re.onClick,E)),P&&R(google.maps.event.addListener(j,re.onClusteringBegin,P)),b&&q(google.maps.event.addListener(j,re.onClusteringEnd,b)),k(j),w&&w(j),()=>{N!==null&&google.maps.event.removeListener(N),$!==null&&google.maps.event.removeListener($),U!==null&&google.maps.event.removeListener(U),T!==null&&google.maps.event.removeListener(T),W!==null&&google.maps.event.removeListener(W),D&&D(j)}}},[]),y!==null&&e(y)||null}i.memo(Zs);class Vs extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"state",{markerClusterer:null}),f(this,"setClustererCallback",()=>{this.state.markerClusterer!==null&&this.props.onLoad&&this.props.onLoad(this.state.markerClusterer)})}componentDidMount(){if(this.context){var e=new Hn(this.context,[],this.props.options);this.registeredEvents=_({updaterMap:H,eventMap:re,prevProps:{},nextProps:this.props,instance:e}),this.setState(()=>({markerClusterer:e}),this.setClustererCallback)}}componentDidUpdate(e){this.state.markerClusterer&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:H,eventMap:re,prevProps:e,nextProps:this.props,instance:this.state.markerClusterer}))}componentWillUnmount(){this.state.markerClusterer!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.markerClusterer),Z(this.registeredEvents),this.state.markerClusterer.setMap(null))}render(){return this.state.markerClusterer!==null?this.props.children(this.state.markerClusterer):null}}f(Vs,"contextType",A);function Zt(t){t.cancelBubble=!0,t.stopPropagation&&t.stopPropagation()}var Gn=function(){function t(e){e===void 0&&(e={}),this.getCloseClickHandler=this.getCloseClickHandler.bind(this),this.closeClickHandler=this.closeClickHandler.bind(this),this.createInfoBoxDiv=this.createInfoBoxDiv.bind(this),this.addClickHandler=this.addClickHandler.bind(this),this.getCloseBoxImg=this.getCloseBoxImg.bind(this),this.getBoxWidths=this.getBoxWidths.bind(this),this.setBoxStyle=this.setBoxStyle.bind(this),this.setPosition=this.setPosition.bind(this),this.getPosition=this.getPosition.bind(this),this.setOptions=this.setOptions.bind(this),this.setContent=this.setContent.bind(this),this.setVisible=this.setVisible.bind(this),this.getContent=this.getContent.bind(this),this.getVisible=this.getVisible.bind(this),this.setZIndex=this.setZIndex.bind(this),this.getZIndex=this.getZIndex.bind(this),this.onRemove=this.onRemove.bind(this),this.panBox=this.panBox.bind(this),this.extend=this.extend.bind(this),this.close=this.close.bind(this),this.draw=this.draw.bind(this),this.show=this.show.bind(this),this.hide=this.hide.bind(this),this.open=this.open.bind(this),this.extend(t,google.maps.OverlayView),this.content=e.content||"",this.disableAutoPan=e.disableAutoPan||!1,this.maxWidth=e.maxWidth||0,this.pixelOffset=e.pixelOffset||new google.maps.Size(0,0),this.position=e.position||new google.maps.LatLng(0,0),this.zIndex=e.zIndex||null,this.boxClass=e.boxClass||"infoBox",this.boxStyle=e.boxStyle||{},this.closeBoxMargin=e.closeBoxMargin||"2px",this.closeBoxURL=e.closeBoxURL||"http://www.google.com/intl/en_us/mapfiles/close.gif",e.closeBoxURL===""&&(this.closeBoxURL=""),this.infoBoxClearance=e.infoBoxClearance||new google.maps.Size(1,1),typeof e.visible>"u"&&(typeof e.isHidden>"u"?e.visible=!0:e.visible=!e.isHidden),this.isHidden=!e.visible,this.alignBottom=e.alignBottom||!1,this.pane=e.pane||"floatPane",this.enableEventPropagation=e.enableEventPropagation||!1,this.div=null,this.closeListener=null,this.moveListener=null,this.mapListener=null,this.contextListener=null,this.eventListeners=null,this.fixedWidthSet=null}return t.prototype.createInfoBoxDiv=function(){var e=this,n=function(p){p.returnValue=!1,p.preventDefault&&p.preventDefault(),e.enableEventPropagation||Zt(p)};if(!this.div){this.div=document.createElement("div"),this.setBoxStyle(),typeof this.content=="string"?this.div.innerHTML=this.getCloseBoxImg()+this.content:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(this.content));var s=this.getPanes();if(s!==null&&s[this.pane].appendChild(this.div),this.addClickHandler(),this.div.style.width)this.fixedWidthSet=!0;else if(this.maxWidth!==0&&this.div.offsetWidth>this.maxWidth)this.div.style.width=this.maxWidth+"px",this.fixedWidthSet=!0;else{var r=this.getBoxWidths();this.div.style.width=this.div.offsetWidth-r.left-r.right+"px",this.fixedWidthSet=!1}if(this.panBox(this.disableAutoPan),!this.enableEventPropagation){this.eventListeners=[];for(var o=["mousedown","mouseover","mouseout","mouseup","click","dblclick","touchstart","touchend","touchmove"],a=0,l=o;a<l.length;a++){var u=l[a];this.eventListeners.push(google.maps.event.addListener(this.div,u,Zt))}this.eventListeners.push(google.maps.event.addListener(this.div,"mouseover",function(){e.div&&(e.div.style.cursor="default")}))}this.contextListener=google.maps.event.addListener(this.div,"contextmenu",n),google.maps.event.trigger(this,"domready")}},t.prototype.getCloseBoxImg=function(){var e="";return this.closeBoxURL!==""&&(e='<img alt=""',e+=' aria-hidden="true"',e+=" src='"+this.closeBoxURL+"'",e+=" align=right",e+=" style='",e+=" position: relative;",e+=" cursor: pointer;",e+=" margin: "+this.closeBoxMargin+";",e+="'>"),e},t.prototype.addClickHandler=function(){this.closeListener=this.div&&this.div.firstChild&&this.closeBoxURL!==""?google.maps.event.addListener(this.div.firstChild,"click",this.getCloseClickHandler()):null},t.prototype.closeClickHandler=function(e){e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation(),google.maps.event.trigger(this,"closeclick"),this.close()},t.prototype.getCloseClickHandler=function(){return this.closeClickHandler},t.prototype.panBox=function(e){if(this.div&&!e){var n=this.getMap();if(n instanceof google.maps.Map){var s=0,r=0,o=n.getBounds();o&&!o.contains(this.position)&&n.setCenter(this.position);var a=n.getDiv(),l=a.offsetWidth,u=a.offsetHeight,c=this.pixelOffset.width,p=this.pixelOffset.height,h=this.div.offsetWidth,g=this.div.offsetHeight,v=this.infoBoxClearance.width,d=this.infoBoxClearance.height,x=this.getProjection(),M=x.fromLatLngToContainerPixel(this.position);M!==null&&(M.x<-c+v?s=M.x+c-v:M.x+h+c+v>l&&(s=M.x+h+c+v-l),this.alignBottom?M.y<-p+d+g?r=M.y+p-d-g:M.y+p+d>u&&(r=M.y+p+d-u):M.y<-p+d?r=M.y+p-d:M.y+g+p+d>u&&(r=M.y+g+p+d-u)),s===0&&r===0||n.panBy(s,r)}}},t.prototype.setBoxStyle=function(){if(this.div){this.div.className=this.boxClass,this.div.style.cssText="";var e=this.boxStyle;for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(this.div.style[n]=e[n]);if(this.div.style.webkitTransform="translateZ(0)",typeof this.div.style.opacity<"u"&&this.div.style.opacity!==""){var s=parseFloat(this.div.style.opacity||"");this.div.style.msFilter='"progid:DXImageTransform.Microsoft.Alpha(Opacity='+s*100+')"',this.div.style.filter="alpha(opacity="+s*100+")"}this.div.style.position="absolute",this.div.style.visibility="hidden",this.zIndex!==null&&(this.div.style.zIndex=this.zIndex+""),this.div.style.overflow||(this.div.style.overflow="auto")}},t.prototype.getBoxWidths=function(){var e={top:0,bottom:0,left:0,right:0};if(!this.div)return e;if(document.defaultView){var n=this.div.ownerDocument,s=n&&n.defaultView?n.defaultView.getComputedStyle(this.div,""):null;s&&(e.top=parseInt(s.borderTopWidth||"",10)||0,e.bottom=parseInt(s.borderBottomWidth||"",10)||0,e.left=parseInt(s.borderLeftWidth||"",10)||0,e.right=parseInt(s.borderRightWidth||"",10)||0)}else if(document.documentElement.currentStyle){var r=this.div.currentStyle;r&&(e.top=parseInt(r.borderTopWidth||"",10)||0,e.bottom=parseInt(r.borderBottomWidth||"",10)||0,e.left=parseInt(r.borderLeftWidth||"",10)||0,e.right=parseInt(r.borderRightWidth||"",10)||0)}return e},t.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.div.parentNode.removeChild(this.div),this.div=null)},t.prototype.draw=function(){if(this.createInfoBoxDiv(),this.div){var e=this.getProjection(),n=e.fromLatLngToDivPixel(this.position);n!==null&&(this.div.style.left=n.x+this.pixelOffset.width+"px",this.alignBottom?this.div.style.bottom=-(n.y+this.pixelOffset.height)+"px":this.div.style.top=n.y+this.pixelOffset.height+"px"),this.isHidden?this.div.style.visibility="hidden":this.div.style.visibility="visible"}},t.prototype.setOptions=function(e){e===void 0&&(e={}),typeof e.boxClass<"u"&&(this.boxClass=e.boxClass,this.setBoxStyle()),typeof e.boxStyle<"u"&&(this.boxStyle=e.boxStyle,this.setBoxStyle()),typeof e.content<"u"&&this.setContent(e.content),typeof e.disableAutoPan<"u"&&(this.disableAutoPan=e.disableAutoPan),typeof e.maxWidth<"u"&&(this.maxWidth=e.maxWidth),typeof e.pixelOffset<"u"&&(this.pixelOffset=e.pixelOffset),typeof e.alignBottom<"u"&&(this.alignBottom=e.alignBottom),typeof e.position<"u"&&this.setPosition(e.position),typeof e.zIndex<"u"&&this.setZIndex(e.zIndex),typeof e.closeBoxMargin<"u"&&(this.closeBoxMargin=e.closeBoxMargin),typeof e.closeBoxURL<"u"&&(this.closeBoxURL=e.closeBoxURL),typeof e.infoBoxClearance<"u"&&(this.infoBoxClearance=e.infoBoxClearance),typeof e.isHidden<"u"&&(this.isHidden=e.isHidden),typeof e.visible<"u"&&(this.isHidden=!e.visible),typeof e.enableEventPropagation<"u"&&(this.enableEventPropagation=e.enableEventPropagation),this.div&&this.draw()},t.prototype.setContent=function(e){this.content=e,this.div&&(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.fixedWidthSet||(this.div.style.width=""),typeof e=="string"?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e)),this.fixedWidthSet||(this.div.style.width=this.div.offsetWidth+"px",typeof e=="string"?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e))),this.addClickHandler()),google.maps.event.trigger(this,"content_changed")},t.prototype.setPosition=function(e){this.position=e,this.div&&this.draw(),google.maps.event.trigger(this,"position_changed")},t.prototype.setVisible=function(e){this.isHidden=!e,this.div&&(this.div.style.visibility=this.isHidden?"hidden":"visible")},t.prototype.setZIndex=function(e){this.zIndex=e,this.div&&(this.div.style.zIndex=e+""),google.maps.event.trigger(this,"zindex_changed")},t.prototype.getContent=function(){return this.content},t.prototype.getPosition=function(){return this.position},t.prototype.getZIndex=function(){return this.zIndex},t.prototype.getVisible=function(){var e=this.getMap();return typeof e>"u"||e===null?!1:!this.isHidden},t.prototype.show=function(){this.isHidden=!1,this.div&&(this.div.style.visibility="visible")},t.prototype.hide=function(){this.isHidden=!0,this.div&&(this.div.style.visibility="hidden")},t.prototype.open=function(e,n){var s=this;n&&(this.position=n.getPosition(),this.moveListener=google.maps.event.addListener(n,"position_changed",function(){var r=n.getPosition();s.setPosition(r)}),this.mapListener=google.maps.event.addListener(n,"map_changed",function(){s.setMap(n.map)})),this.setMap(e),this.div&&this.panBox()},t.prototype.close=function(){if(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.eventListeners){for(var e=0,n=this.eventListeners;e<n.length;e++){var s=n[e];google.maps.event.removeListener(s)}this.eventListeners=null}this.moveListener&&(google.maps.event.removeListener(this.moveListener),this.moveListener=null),this.mapListener&&(google.maps.event.removeListener(this.mapListener),this.mapListener=null),this.contextListener&&(google.maps.event.removeListener(this.contextListener),this.contextListener=null),this.setMap(null)},t.prototype.extend=function(e,n){return(function(r){for(var o in r.prototype)Object.prototype.hasOwnProperty.call(this,o)||(this.prototype[o]=r.prototype[o]);return this}).apply(e,[n])},t}(),Ws=["position"],Ns=["position"];function Vt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function Ve(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Vt(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Vt(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var Wt={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},Nt={options(t,e){t.setOptions(e)},position(t,e){e instanceof google.maps.LatLng?t.setPosition(e):t.setPosition(new google.maps.LatLng(e.lat,e.lng))},visible(t,e){t.setVisible(e)},zIndex(t,e){t.setZIndex(e)}},$s={};function Fs(t){var{children:e,anchor:n,options:s,position:r,zIndex:o,onCloseClick:a,onDomReady:l,onContentChanged:u,onPositionChanged:c,onZindexChanged:p,onLoad:h,onUnmount:g}=t,v=i.useContext(A),[d,x]=i.useState(null),[M,S]=i.useState(null),[E,P]=i.useState(null),[b,m]=i.useState(null),[C,w]=i.useState(null),[D,y]=i.useState(null),k=i.useRef(null);return i.useEffect(()=>{v&&d!==null&&(d.close(),n?d.open(v,n):d.getPosition()&&d.open(v))},[v,d,n]),i.useEffect(()=>{s&&d!==null&&d.setOptions(s)},[d,s]),i.useEffect(()=>{if(r&&d!==null){var O=r instanceof google.maps.LatLng?r:new google.maps.LatLng(r.lat,r.lng);d.setPosition(O)}},[r]),i.useEffect(()=>{typeof o=="number"&&d!==null&&d.setZIndex(o)},[o]),i.useEffect(()=>{d&&a&&(M!==null&&google.maps.event.removeListener(M),S(google.maps.event.addListener(d,"closeclick",a)))},[a]),i.useEffect(()=>{d&&l&&(E!==null&&google.maps.event.removeListener(E),P(google.maps.event.addListener(d,"domready",l)))},[l]),i.useEffect(()=>{d&&u&&(b!==null&&google.maps.event.removeListener(b),m(google.maps.event.addListener(d,"content_changed",u)))},[u]),i.useEffect(()=>{d&&c&&(C!==null&&google.maps.event.removeListener(C),w(google.maps.event.addListener(d,"position_changed",c)))},[c]),i.useEffect(()=>{d&&p&&(D!==null&&google.maps.event.removeListener(D),y(google.maps.event.addListener(d,"zindex_changed",p)))},[p]),i.useEffect(()=>{if(v){var O=s||$s,{position:U}=O,V=tt(O,Ws),T;U&&!(U instanceof google.maps.LatLng)&&(T=new google.maps.LatLng(U.lat,U.lng));var R=new Gn(Ve(Ve({},V),T?{position:T}:{}));k.current=document.createElement("div"),x(R),a&&S(google.maps.event.addListener(R,"closeclick",a)),l&&P(google.maps.event.addListener(R,"domready",l)),u&&m(google.maps.event.addListener(R,"content_changed",u)),c&&w(google.maps.event.addListener(R,"position_changed",c)),p&&y(google.maps.event.addListener(R,"zindex_changed",p)),R.setContent(k.current),n?R.open(v,n):R.getPosition()?R.open(v):ne(!1,"You must provide either an anchor or a position prop for <InfoBox>."),h&&h(R)}return()=>{d!==null&&(M&&google.maps.event.removeListener(M),b&&google.maps.event.removeListener(b),E&&google.maps.event.removeListener(E),C&&google.maps.event.removeListener(C),D&&google.maps.event.removeListener(D),g&&g(d),d.close())}},[]),k.current?ge.createPortal(i.Children.only(e),k.current):null}i.memo(Fs);class Hs extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"containerElement",null),f(this,"state",{infoBox:null}),f(this,"open",(e,n)=>{n?this.context!==null&&e.open(this.context,n):e.getPosition()?this.context!==null&&e.open(this.context):ne(!1,"You must provide either an anchor or a position prop for <InfoBox>.")}),f(this,"setInfoBoxCallback",()=>{this.state.infoBox!==null&&this.containerElement!==null&&(this.state.infoBox.setContent(this.containerElement),this.open(this.state.infoBox,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoBox))})}componentDidMount(){var e=this.props.options||{},{position:n}=e,s=tt(e,Ns),r;n&&!(n instanceof google.maps.LatLng)&&(r=new google.maps.LatLng(n.lat,n.lng));var o=new Gn(Ve(Ve({},s),r?{position:r}:{}));this.containerElement=document.createElement("div"),this.registeredEvents=_({updaterMap:Nt,eventMap:Wt,prevProps:{},nextProps:this.props,instance:o}),this.setState({infoBox:o},this.setInfoBoxCallback)}componentDidUpdate(e){var{infoBox:n}=this.state;n!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:Nt,eventMap:Wt,prevProps:e,nextProps:this.props,instance:n}))}componentWillUnmount(){var{onUnmount:e}=this.props,{infoBox:n}=this.state;n!==null&&(e&&e(n),Z(this.registeredEvents),n.close())}render(){return this.containerElement?ge.createPortal(i.Children.only(this.props.children),this.containerElement):null}}f(Hs,"contextType",A);var Ke,$t;function Gs(){return $t||($t=1,Ke=function t(e,n){if(e===n)return!0;if(e&&n&&typeof e=="object"&&typeof n=="object"){if(e.constructor!==n.constructor)return!1;var s,r,o;if(Array.isArray(e)){if(s=e.length,s!=n.length)return!1;for(r=s;r--!==0;)if(!t(e[r],n[r]))return!1;return!0}if(e.constructor===RegExp)return e.source===n.source&&e.flags===n.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===n.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===n.toString();if(o=Object.keys(e),s=o.length,s!==Object.keys(n).length)return!1;for(r=s;r--!==0;)if(!Object.prototype.hasOwnProperty.call(n,o[r]))return!1;for(r=s;r--!==0;){var a=o[r];if(!t(e[a],n[a]))return!1}return!0}return e!==e&&n!==n}),Ke}var Ks=Gs(),Ft=Zn(Ks),Ht=[Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],qe=1,Le=8;class nt{static from(e){if(!(e instanceof ArrayBuffer))throw new Error("Data must be an instance of ArrayBuffer.");var[n,s]=new Uint8Array(e,0,2);if(n!==219)throw new Error("Data does not appear to be in a KDBush format.");var r=s>>4;if(r!==qe)throw new Error("Got v".concat(r," data when expected v").concat(qe,"."));var o=Ht[s&15];if(!o)throw new Error("Unrecognized array type.");var[a]=new Uint16Array(e,2,1),[l]=new Uint32Array(e,4,1);return new nt(l,a,o,e)}constructor(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:64,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Float64Array,r=arguments.length>3?arguments[3]:void 0;if(isNaN(e)||e<0)throw new Error("Unpexpected numItems value: ".concat(e,"."));this.numItems=+e,this.nodeSize=Math.min(Math.max(+n,2),65535),this.ArrayType=s,this.IndexArrayType=e<65536?Uint16Array:Uint32Array;var o=Ht.indexOf(this.ArrayType),a=e*2*this.ArrayType.BYTES_PER_ELEMENT,l=e*this.IndexArrayType.BYTES_PER_ELEMENT,u=(8-l%8)%8;if(o<0)throw new Error("Unexpected typed array class: ".concat(s,"."));r&&r instanceof ArrayBuffer?(this.data=r,this.ids=new this.IndexArrayType(this.data,Le,e),this.coords=new this.ArrayType(this.data,Le+l+u,e*2),this._pos=e*2,this._finished=!0):(this.data=new ArrayBuffer(Le+a+l+u),this.ids=new this.IndexArrayType(this.data,Le,e),this.coords=new this.ArrayType(this.data,Le+l+u,e*2),this._pos=0,this._finished=!1,new Uint8Array(this.data,0,2).set([219,(qe<<4)+o]),new Uint16Array(this.data,2,1)[0]=n,new Uint32Array(this.data,4,1)[0]=e)}add(e,n){var s=this._pos>>1;return this.ids[s]=s,this.coords[this._pos++]=e,this.coords[this._pos++]=n,s}finish(){var e=this._pos>>1;if(e!==this.numItems)throw new Error("Added ".concat(e," items when expected ").concat(this.numItems,"."));return Xe(this.ids,this.coords,this.nodeSize,0,this.numItems-1,0),this._finished=!0,this}range(e,n,s,r){if(!this._finished)throw new Error("Data not yet indexed - call index.finish().");for(var{ids:o,coords:a,nodeSize:l}=this,u=[0,o.length-1,0],c=[];u.length;){var p=u.pop()||0,h=u.pop()||0,g=u.pop()||0;if(h-g<=l){for(var v=g;v<=h;v++){var d=a[2*v],x=a[2*v+1];d>=e&&d<=s&&x>=n&&x<=r&&c.push(o[v])}continue}var M=g+h>>1,S=a[2*M],E=a[2*M+1];S>=e&&S<=s&&E>=n&&E<=r&&c.push(o[M]),(p===0?e<=S:n<=E)&&(u.push(g),u.push(M-1),u.push(1-p)),(p===0?s>=S:r>=E)&&(u.push(M+1),u.push(h),u.push(1-p))}return c}within(e,n,s){if(!this._finished)throw new Error("Data not yet indexed - call index.finish().");for(var{ids:r,coords:o,nodeSize:a}=this,l=[0,r.length-1,0],u=[],c=s*s;l.length;){var p=l.pop()||0,h=l.pop()||0,g=l.pop()||0;if(h-g<=a){for(var v=g;v<=h;v++)Gt(o[2*v],o[2*v+1],e,n)<=c&&u.push(r[v]);continue}var d=g+h>>1,x=o[2*d],M=o[2*d+1];Gt(x,M,e,n)<=c&&u.push(r[d]),(p===0?e-s<=x:n-s<=M)&&(l.push(g),l.push(d-1),l.push(1-p)),(p===0?e+s>=x:n+s>=M)&&(l.push(d+1),l.push(h),l.push(1-p))}return u}}function Xe(t,e,n,s,r,o){if(!(r-s<=n)){var a=s+r>>1;Kn(t,e,a,s,r,o),Xe(t,e,n,s,a-1,1-o),Xe(t,e,n,a+1,r,1-o)}}function Kn(t,e,n,s,r,o){for(;r>s;){if(r-s>600){var a=r-s+1,l=n-s+1,u=Math.log(a),c=.5*Math.exp(2*u/3),p=.5*Math.sqrt(u*c*(a-c)/a)*(l-a/2<0?-1:1),h=Math.max(s,Math.floor(n-l*c/a+p)),g=Math.min(r,Math.floor(n+(a-l)*c/a+p));Kn(t,e,n,h,g,o)}var v=e[2*n+o],d=s,x=r;for(ye(t,e,s,n),e[2*r+o]>v&&ye(t,e,s,r);d<x;){for(ye(t,e,d,x),d++,x--;e[2*d+o]<v;)d++;for(;e[2*x+o]>v;)x--}e[2*s+o]===v?ye(t,e,s,x):(x++,ye(t,e,x,r)),x<=n&&(s=x+1),n<=x&&(r=x-1)}}function ye(t,e,n,s){Je(t,n,s),Je(e,2*n,2*s),Je(e,2*n+1,2*s+1)}function Je(t,e,n){var s=t[e];t[e]=t[n],t[n]=s}function Gt(t,e,n,s){var r=t-n,o=e-s;return r*r+o*o}var qs={minZoom:0,maxZoom:16,minPoints:2,radius:40,extent:512,nodeSize:64,log:!1,generateId:!1,reduce:null,map:t=>t},Kt=Math.fround||(t=>e=>(t[0]=+e,t[0]))(new Float32Array(1)),pe=2,ue=3,Ye=4,le=5,qn=6;class Js{constructor(e){this.options=Object.assign(Object.create(qs),e),this.trees=new Array(this.options.maxZoom+1),this.stride=this.options.reduce?7:6,this.clusterProps=[]}load(e){var{log:n,minZoom:s,maxZoom:r}=this.options;n&&console.time("total time");var o="prepare ".concat(e.length," points");n&&console.time(o),this.points=e;for(var a=[],l=0;l<e.length;l++){var u=e[l];if(u.geometry){var[c,p]=u.geometry.coordinates,h=Kt(Ue(c)),g=Kt(Ae(p));a.push(h,g,1/0,l,-1,1),this.options.reduce&&a.push(0)}}var v=this.trees[r+1]=this._createTree(a);n&&console.timeEnd(o);for(var d=r;d>=s;d--){var x=+Date.now();v=this.trees[d]=this._createTree(this._cluster(v,d)),n&&console.log("z%d: %d clusters in %dms",d,v.numItems,+Date.now()-x)}return n&&console.timeEnd("total time"),this}getClusters(e,n){var s=((e[0]+180)%360+360)%360-180,r=Math.max(-90,Math.min(90,e[1])),o=e[2]===180?180:((e[2]+180)%360+360)%360-180,a=Math.max(-90,Math.min(90,e[3]));if(e[2]-e[0]>=360)s=-180,o=180;else if(s>o){var l=this.getClusters([s,r,180,a],n),u=this.getClusters([-180,r,o,a],n);return l.concat(u)}var c=this.trees[this._limitZoom(n)],p=c.range(Ue(s),Ae(a),Ue(o),Ae(r)),h=c.data,g=[];for(var v of p){var d=this.stride*v;g.push(h[d+le]>1?qt(h,d,this.clusterProps):this.points[h[d+ue]])}return g}getChildren(e){var n=this._getOriginId(e),s=this._getOriginZoom(e),r="No cluster with the specified id.",o=this.trees[s];if(!o)throw new Error(r);var a=o.data;if(n*this.stride>=a.length)throw new Error(r);var l=this.options.radius/(this.options.extent*Math.pow(2,s-1)),u=a[n*this.stride],c=a[n*this.stride+1],p=o.within(u,c,l),h=[];for(var g of p){var v=g*this.stride;a[v+Ye]===e&&h.push(a[v+le]>1?qt(a,v,this.clusterProps):this.points[a[v+ue]])}if(h.length===0)throw new Error(r);return h}getLeaves(e,n,s){n=n||10,s=s||0;var r=[];return this._appendLeaves(r,e,n,s,0),r}getTile(e,n,s){var r=this.trees[this._limitZoom(e)],o=Math.pow(2,e),{extent:a,radius:l}=this.options,u=l/a,c=(s-u)/o,p=(s+1+u)/o,h={features:[]};return this._addTileFeatures(r.range((n-u)/o,c,(n+1+u)/o,p),r.data,n,s,o,h),n===0&&this._addTileFeatures(r.range(1-u/o,c,1,p),r.data,o,s,o,h),n===o-1&&this._addTileFeatures(r.range(0,c,u/o,p),r.data,-1,s,o,h),h.features.length?h:null}getClusterExpansionZoom(e){for(var n=this._getOriginZoom(e)-1;n<=this.options.maxZoom;){var s=this.getChildren(e);if(n++,s.length!==1)break;e=s[0].properties.cluster_id}return n}_appendLeaves(e,n,s,r,o){var a=this.getChildren(n);for(var l of a){var u=l.properties;if(u&&u.cluster?o+u.point_count<=r?o+=u.point_count:o=this._appendLeaves(e,u.cluster_id,s,r,o):o<r?o++:e.push(l),e.length===s)break}return o}_createTree(e){for(var n=new nt(e.length/this.stride|0,this.options.nodeSize,Float32Array),s=0;s<e.length;s+=this.stride)n.add(e[s],e[s+1]);return n.finish(),n.data=e,n}_addTileFeatures(e,n,s,r,o,a){for(var l of e){var u=l*this.stride,c=n[u+le]>1,p=void 0,h=void 0,g=void 0;if(c)p=Jn(n,u,this.clusterProps),h=n[u],g=n[u+1];else{var v=this.points[n[u+ue]];p=v.properties;var[d,x]=v.geometry.coordinates;h=Ue(d),g=Ae(x)}var M={type:1,geometry:[[Math.round(this.options.extent*(h*o-s)),Math.round(this.options.extent*(g*o-r))]],tags:p},S=void 0;c||this.options.generateId?S=n[u+ue]:S=this.points[n[u+ue]].id,S!==void 0&&(M.id=S),a.features.push(M)}}_limitZoom(e){return Math.max(this.options.minZoom,Math.min(Math.floor(+e),this.options.maxZoom+1))}_cluster(e,n){for(var{radius:s,extent:r,reduce:o,minPoints:a}=this.options,l=s/(r*Math.pow(2,n)),u=e.data,c=[],p=this.stride,h=0;h<u.length;h+=p)if(!(u[h+pe]<=n)){u[h+pe]=n;var g=u[h],v=u[h+1],d=e.within(u[h],u[h+1],l),x=u[h+le],M=x;for(var S of d){var E=S*p;u[E+pe]>n&&(M+=u[E+le])}if(M>x&&M>=a){var P=g*x,b=v*x,m=void 0,C=-1,w=((h/p|0)<<5)+(n+1)+this.points.length;for(var D of d){var y=D*p;if(!(u[y+pe]<=n)){u[y+pe]=n;var k=u[y+le];P+=u[y]*k,b+=u[y+1]*k,u[y+Ye]=w,o&&(m||(m=this._map(u,h,!0),C=this.clusterProps.length,this.clusterProps.push(m)),o(m,this._map(u,y)))}}u[h+Ye]=w,c.push(P/M,b/M,1/0,w,-1,M),o&&c.push(C)}else{for(var O=0;O<p;O++)c.push(u[h+O]);if(M>1)for(var U of d){var V=U*p;if(!(u[V+pe]<=n)){u[V+pe]=n;for(var T=0;T<p;T++)c.push(u[V+T])}}}}return c}_getOriginId(e){return e-this.points.length>>5}_getOriginZoom(e){return(e-this.points.length)%32}_map(e,n,s){if(e[n+le]>1){var r=this.clusterProps[e[n+qn]];return s?Object.assign({},r):r}var o=this.points[e[n+ue]].properties,a=this.options.map(o);return s&&a===o?Object.assign({},a):a}}function qt(t,e,n){return{type:"Feature",id:t[e+ue],properties:Jn(t,e,n),geometry:{type:"Point",coordinates:[Ys(t[e]),Qs(t[e+1])]}}}function Jn(t,e,n){var s=t[e+le],r=s>=1e4?"".concat(Math.round(s/1e3),"k"):s>=1e3?"".concat(Math.round(s/100)/10,"k"):s,o=t[e+qn],a=o===-1?{}:Object.assign({},n[o]);return Object.assign(a,{cluster:!0,cluster_id:t[e+ue],point_count:s,point_count_abbreviated:r})}function Ue(t){return t/360+.5}function Ae(t){var e=Math.sin(t*Math.PI/180),n=.5-.25*Math.log((1+e)/(1-e))/Math.PI;return n<0?0:n>1?1:n}function Ys(t){return(t-.5)*360}function Qs(t){var e=(180-t*360)*Math.PI/180;return 360*Math.atan(Math.exp(e))/Math.PI-90}/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */function Xs(t,e){var n={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(n[s]=t[s]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,s=Object.getOwnPropertySymbols(t);r<s.length;r++)e.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(t,s[r])&&(n[s[r]]=t[s[r]]);return n}class ie{static isAdvancedMarkerAvailable(e){return google.maps.marker&&e.getMapCapabilities().isAdvancedMarkersAvailable===!0}static isAdvancedMarker(e){return google.maps.marker&&e instanceof google.maps.marker.AdvancedMarkerElement}static setMap(e,n){this.isAdvancedMarker(e)?e.map=n:e.setMap(n)}static getPosition(e){if(this.isAdvancedMarker(e)){if(e.position){if(e.position instanceof google.maps.LatLng)return e.position;if(e.position.lat&&e.position.lng)return new google.maps.LatLng(e.position.lat,e.position.lng)}return new google.maps.LatLng(null)}return e.getPosition()}static getVisible(e){return this.isAdvancedMarker(e)?!0:e.getVisible()}}class et{constructor(e){var{markers:n,position:s}=e;this.markers=n,s&&(s instanceof google.maps.LatLng?this._position=s:this._position=new google.maps.LatLng(s))}get bounds(){if(!(this.markers.length===0&&!this._position)){var e=new google.maps.LatLngBounds(this._position,this._position);for(var n of this.markers)e.extend(ie.getPosition(n));return e}}get position(){return this._position||this.bounds.getCenter()}get count(){return this.markers.filter(e=>ie.getVisible(e)).length}push(e){this.markers.push(e)}delete(){this.marker&&(ie.setMap(this.marker,null),this.marker=void 0),this.markers.length=0}}class ei{constructor(e){var{maxZoom:n=16}=e;this.maxZoom=n}noop(e){var{markers:n}=e;return ti(n)}}var ti=t=>{var e=t.map(n=>new et({position:ie.getPosition(n),markers:[n]}));return e};class ni extends ei{constructor(e){var{maxZoom:n,radius:s=60}=e,r=Xs(e,["maxZoom","radius"]);super({maxZoom:n}),this.state={zoom:-1},this.superCluster=new Js(Object.assign({maxZoom:this.maxZoom,radius:s},r))}calculate(e){var n=!1,s={zoom:e.map.getZoom()};if(!Ft(e.markers,this.markers)){n=!0,this.markers=[...e.markers];var r=this.markers.map(o=>{var a=ie.getPosition(o),l=[a.lng(),a.lat()];return{type:"Feature",geometry:{type:"Point",coordinates:l},properties:{marker:o}}});this.superCluster.load(r)}return n||(this.state.zoom<=this.maxZoom||s.zoom<=this.maxZoom)&&(n=!Ft(this.state,s)),this.state=s,n&&(this.clusters=this.cluster(e)),{clusters:this.clusters,changed:n}}cluster(e){var{map:n}=e;return this.superCluster.getClusters([-180,-90,180,90],Math.round(n.getZoom())).map(s=>this.transformCluster(s))}transformCluster(e){var{geometry:{coordinates:[n,s]},properties:r}=e;if(r.cluster)return new et({markers:this.superCluster.getLeaves(r.cluster_id,1/0).map(a=>a.properties.marker),position:{lat:s,lng:n}});var o=r.marker;return new et({markers:[o],position:ie.getPosition(o)})}}class si{constructor(e,n){this.markers={sum:e.length};var s=n.map(o=>o.count),r=s.reduce((o,a)=>o+a,0);this.clusters={count:n.length,markers:{mean:r/n.length,sum:r,min:Math.min(...s),max:Math.max(...s)}}}}class ii{render(e,n,s){var{count:r,position:o}=e,a=r>Math.max(10,n.clusters.markers.mean)?"#ff0000":"#0000ff",l='<svg fill="'.concat(a,`" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240" width="50" height="50">
<circle cx="120" cy="120" opacity=".6" r="70" />
<circle cx="120" cy="120" opacity=".3" r="90" />
<circle cx="120" cy="120" opacity=".2" r="110" />
<text x="50%" y="50%" style="fill:#fff" text-anchor="middle" font-size="50" dominant-baseline="middle" font-family="roboto,arial,sans-serif">`).concat(r,`</text>
</svg>`),u="Cluster of ".concat(r," markers"),c=Number(google.maps.Marker.MAX_ZINDEX)+r;if(ie.isAdvancedMarkerAvailable(s)){var p=new DOMParser,h=p.parseFromString(l,"image/svg+xml").documentElement;h.setAttribute("transform","translate(0 25)");var g={map:s,position:o,zIndex:c,title:u,content:h};return new google.maps.marker.AdvancedMarkerElement(g)}var v={position:o,zIndex:c,title:u,icon:{url:"data:image/svg+xml;base64,".concat(btoa(l)),anchor:new google.maps.Point(25,25)}};return new google.maps.Marker(v)}}function ri(t,e){for(var n in e.prototype)t.prototype[n]=e.prototype[n]}class st{constructor(){ri(st,google.maps.OverlayView)}}var be;(function(t){t.CLUSTERING_BEGIN="clusteringbegin",t.CLUSTERING_END="clusteringend",t.CLUSTER_CLICK="click"})(be||(be={}));var oi=(t,e,n)=>{n.fitBounds(e.bounds)};class ai extends st{constructor(e){var{map:n,markers:s=[],algorithmOptions:r={},algorithm:o=new ni(r),renderer:a=new ii,onClusterClick:l=oi}=e;super(),this.markers=[...s],this.clusters=[],this.algorithm=o,this.renderer=a,this.onClusterClick=l,n&&this.setMap(n)}addMarker(e,n){this.markers.includes(e)||(this.markers.push(e),n||this.render())}addMarkers(e,n){e.forEach(s=>{this.addMarker(s,!0)}),n||this.render()}removeMarker(e,n){var s=this.markers.indexOf(e);return s===-1?!1:(ie.setMap(e,null),this.markers.splice(s,1),n||this.render(),!0)}removeMarkers(e,n){var s=!1;return e.forEach(r=>{s=this.removeMarker(r,!0)||s}),s&&!n&&this.render(),s}clearMarkers(e){this.markers.length=0,e||this.render()}render(){var e=this.getMap();if(e instanceof google.maps.Map&&e.getProjection()){google.maps.event.trigger(this,be.CLUSTERING_BEGIN,this);var{clusters:n,changed:s}=this.algorithm.calculate({markers:this.markers,map:e,mapCanvasProjection:this.getProjection()});if(s||s==null){var r=new Set;for(var o of n)o.markers.length==1&&r.add(o.markers[0]);var a=[];for(var l of this.clusters)l.marker!=null&&(l.markers.length==1?r.has(l.marker)||ie.setMap(l.marker,null):a.push(l.marker));this.clusters=n,this.renderClusters(),requestAnimationFrame(()=>a.forEach(u=>ie.setMap(u,null)))}google.maps.event.trigger(this,be.CLUSTERING_END,this)}}onAdd(){this.idleListener=this.getMap().addListener("idle",this.render.bind(this)),this.render()}onRemove(){google.maps.event.removeListener(this.idleListener),this.reset()}reset(){this.markers.forEach(e=>ie.setMap(e,null)),this.clusters.forEach(e=>e.delete()),this.clusters=[]}renderClusters(){var e=new si(this.markers,this.clusters),n=this.getMap();this.clusters.forEach(s=>{s.markers.length===1?s.marker=s.markers[0]:(s.marker=this.renderer.render(s,e,n),s.markers.forEach(r=>ie.setMap(r,null)),this.onClusterClick&&s.marker.addListener("click",r=>{google.maps.event.trigger(this,be.CLUSTER_CLICK,s),this.onClusterClick(r,s,n)})),ie.setMap(s.marker,n)})}}function Jt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function Yt(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Jt(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Jt(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}function li(t){var e=os(),[n,s]=i.useState(null);return i.useEffect(()=>{if(e&&n===null){var r=new ai(Yt(Yt({},t),{},{map:e}));s(r)}},[e]),n}function ui(t){var{children:e,options:n}=t,s=li(n);return s!==null?e(s):null}i.memo(ui);var Qt={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},Xt={options(t,e){t.setOptions(e)},position(t,e){t.setPosition(e)},zIndex(t,e){t.setZIndex(e)}};function pi(t){var{children:e,anchor:n,options:s,position:r,zIndex:o,onCloseClick:a,onDomReady:l,onContentChanged:u,onPositionChanged:c,onZindexChanged:p,onLoad:h,onUnmount:g}=t,v=i.useContext(A),[d,x]=i.useState(null),[M,S]=i.useState(null),[E,P]=i.useState(null),[b,m]=i.useState(null),[C,w]=i.useState(null),[D,y]=i.useState(null),k=i.useRef(null);return i.useEffect(()=>{d!==null&&(d.close(),n?d.open(v,n):d.getPosition()&&d.open(v))},[v,d,n]),i.useEffect(()=>{s&&d!==null&&d.setOptions(s)},[d,s]),i.useEffect(()=>{r&&d!==null&&d.setPosition(r)},[r]),i.useEffect(()=>{typeof o=="number"&&d!==null&&d.setZIndex(o)},[o]),i.useEffect(()=>{d&&a&&(M!==null&&google.maps.event.removeListener(M),S(google.maps.event.addListener(d,"closeclick",a)))},[a]),i.useEffect(()=>{d&&l&&(E!==null&&google.maps.event.removeListener(E),P(google.maps.event.addListener(d,"domready",l)))},[l]),i.useEffect(()=>{d&&u&&(b!==null&&google.maps.event.removeListener(b),m(google.maps.event.addListener(d,"content_changed",u)))},[u]),i.useEffect(()=>{d&&c&&(C!==null&&google.maps.event.removeListener(C),w(google.maps.event.addListener(d,"position_changed",c)))},[c]),i.useEffect(()=>{d&&p&&(D!==null&&google.maps.event.removeListener(D),y(google.maps.event.addListener(d,"zindex_changed",p)))},[p]),i.useEffect(()=>{var O=new google.maps.InfoWindow(s);return x(O),k.current=document.createElement("div"),a&&S(google.maps.event.addListener(O,"closeclick",a)),l&&P(google.maps.event.addListener(O,"domready",l)),u&&m(google.maps.event.addListener(O,"content_changed",u)),c&&w(google.maps.event.addListener(O,"position_changed",c)),p&&y(google.maps.event.addListener(O,"zindex_changed",p)),O.setContent(k.current),r&&O.setPosition(r),o&&O.setZIndex(o),n?O.open(v,n):O.getPosition()?O.open(v):ne(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>."),h&&h(O),()=>{M&&google.maps.event.removeListener(M),b&&google.maps.event.removeListener(b),E&&google.maps.event.removeListener(E),C&&google.maps.event.removeListener(C),D&&google.maps.event.removeListener(D),g&&g(O),O.close()}},[]),k.current?ge.createPortal(i.Children.only(e),k.current):null}i.memo(pi);class di extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"containerElement",null),f(this,"state",{infoWindow:null}),f(this,"open",(e,n)=>{n?e.open(this.context,n):e.getPosition()?e.open(this.context):ne(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>.")}),f(this,"setInfoWindowCallback",()=>{this.state.infoWindow!==null&&this.containerElement!==null&&(this.state.infoWindow.setContent(this.containerElement),this.open(this.state.infoWindow,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoWindow))})}componentDidMount(){var e=new google.maps.InfoWindow(this.props.options);this.containerElement=document.createElement("div"),this.registeredEvents=_({updaterMap:Xt,eventMap:Qt,prevProps:{},nextProps:this.props,instance:e}),this.setState(()=>({infoWindow:e}),this.setInfoWindowCallback)}componentDidUpdate(e){this.state.infoWindow!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:Xt,eventMap:Qt,prevProps:e,nextProps:this.props,instance:this.state.infoWindow}))}componentWillUnmount(){this.state.infoWindow!==null&&(Z(this.registeredEvents),this.props.onUnmount&&this.props.onUnmount(this.state.infoWindow),this.state.infoWindow.close())}render(){return this.containerElement?ge.createPortal(i.Children.only(this.props.children),this.containerElement):null}}f(di,"contextType",A);function en(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function We(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?en(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):en(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var tn={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},nn={draggable(t,e){t.setDraggable(e)},editable(t,e){t.setEditable(e)},map(t,e){t.setMap(e)},options(t,e){t.setOptions(e)},path(t,e){t.setPath(e)},visible(t,e){t.setVisible(e)}},ci={};function gi(t){var{options:e,draggable:n,editable:s,visible:r,path:o,onDblClick:a,onDragEnd:l,onDragStart:u,onMouseDown:c,onMouseMove:p,onMouseOut:h,onMouseOver:g,onMouseUp:v,onRightClick:d,onClick:x,onDrag:M,onLoad:S,onUnmount:E}=t,P=i.useContext(A),[b,m]=i.useState(null),[C,w]=i.useState(null),[D,y]=i.useState(null),[k,O]=i.useState(null),[U,V]=i.useState(null),[T,R]=i.useState(null),[W,q]=i.useState(null),[N,G]=i.useState(null),[$,K]=i.useState(null),[J,j]=i.useState(null),[Y,L]=i.useState(null),[ee,X]=i.useState(null);return i.useEffect(()=>{b!==null&&b.setMap(P)},[P]),i.useEffect(()=>{typeof e<"u"&&b!==null&&b.setOptions(e)},[b,e]),i.useEffect(()=>{typeof n<"u"&&b!==null&&b.setDraggable(n)},[b,n]),i.useEffect(()=>{typeof s<"u"&&b!==null&&b.setEditable(s)},[b,s]),i.useEffect(()=>{typeof r<"u"&&b!==null&&b.setVisible(r)},[b,r]),i.useEffect(()=>{typeof o<"u"&&b!==null&&b.setPath(o)},[b,o]),i.useEffect(()=>{b&&a&&(C!==null&&google.maps.event.removeListener(C),w(google.maps.event.addListener(b,"dblclick",a)))},[a]),i.useEffect(()=>{b&&l&&(D!==null&&google.maps.event.removeListener(D),y(google.maps.event.addListener(b,"dragend",l)))},[l]),i.useEffect(()=>{b&&u&&(k!==null&&google.maps.event.removeListener(k),O(google.maps.event.addListener(b,"dragstart",u)))},[u]),i.useEffect(()=>{b&&c&&(U!==null&&google.maps.event.removeListener(U),V(google.maps.event.addListener(b,"mousedown",c)))},[c]),i.useEffect(()=>{b&&p&&(T!==null&&google.maps.event.removeListener(T),R(google.maps.event.addListener(b,"mousemove",p)))},[p]),i.useEffect(()=>{b&&h&&(W!==null&&google.maps.event.removeListener(W),q(google.maps.event.addListener(b,"mouseout",h)))},[h]),i.useEffect(()=>{b&&g&&(N!==null&&google.maps.event.removeListener(N),G(google.maps.event.addListener(b,"mouseover",g)))},[g]),i.useEffect(()=>{b&&v&&($!==null&&google.maps.event.removeListener($),K(google.maps.event.addListener(b,"mouseup",v)))},[v]),i.useEffect(()=>{b&&d&&(J!==null&&google.maps.event.removeListener(J),j(google.maps.event.addListener(b,"rightclick",d)))},[d]),i.useEffect(()=>{b&&x&&(Y!==null&&google.maps.event.removeListener(Y),L(google.maps.event.addListener(b,"click",x)))},[x]),i.useEffect(()=>{b&&M&&(ee!==null&&google.maps.event.removeListener(ee),X(google.maps.event.addListener(b,"drag",M)))},[M]),i.useEffect(()=>{var z=new google.maps.Polyline(We(We({},e||ci),{},{map:P}));return o&&z.setPath(o),typeof r<"u"&&z.setVisible(r),typeof s<"u"&&z.setEditable(s),typeof n<"u"&&z.setDraggable(n),a&&w(google.maps.event.addListener(z,"dblclick",a)),l&&y(google.maps.event.addListener(z,"dragend",l)),u&&O(google.maps.event.addListener(z,"dragstart",u)),c&&V(google.maps.event.addListener(z,"mousedown",c)),p&&R(google.maps.event.addListener(z,"mousemove",p)),h&&q(google.maps.event.addListener(z,"mouseout",h)),g&&G(google.maps.event.addListener(z,"mouseover",g)),v&&K(google.maps.event.addListener(z,"mouseup",v)),d&&j(google.maps.event.addListener(z,"rightclick",d)),x&&L(google.maps.event.addListener(z,"click",x)),M&&X(google.maps.event.addListener(z,"drag",M)),m(z),S&&S(z),()=>{C!==null&&google.maps.event.removeListener(C),D!==null&&google.maps.event.removeListener(D),k!==null&&google.maps.event.removeListener(k),U!==null&&google.maps.event.removeListener(U),T!==null&&google.maps.event.removeListener(T),W!==null&&google.maps.event.removeListener(W),N!==null&&google.maps.event.removeListener(N),$!==null&&google.maps.event.removeListener($),J!==null&&google.maps.event.removeListener(J),Y!==null&&google.maps.event.removeListener(Y),E&&E(z),z.setMap(null)}},[]),null}i.memo(gi);class hi extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"state",{polyline:null}),f(this,"setPolylineCallback",()=>{this.state.polyline!==null&&this.props.onLoad&&this.props.onLoad(this.state.polyline)})}componentDidMount(){var e=new google.maps.Polyline(We(We({},this.props.options),{},{map:this.context}));this.registeredEvents=_({updaterMap:nn,eventMap:tn,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{polyline:e}},this.setPolylineCallback)}componentDidUpdate(e){this.state.polyline!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:nn,eventMap:tn,prevProps:e,nextProps:this.props,instance:this.state.polyline}))}componentWillUnmount(){this.state.polyline!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.polyline),Z(this.registeredEvents),this.state.polyline.setMap(null))}render(){return null}}f(hi,"contextType",A);function sn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function rn(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?sn(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):sn(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var on={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},an={draggable(t,e){t.setDraggable(e)},editable(t,e){t.setEditable(e)},map(t,e){t.setMap(e)},options(t,e){t.setOptions(e)},path(t,e){t.setPath(e)},paths(t,e){t.setPaths(e)},visible(t,e){t.setVisible(e)}};function fi(t){var{options:e,draggable:n,editable:s,visible:r,path:o,paths:a,onDblClick:l,onDragEnd:u,onDragStart:c,onMouseDown:p,onMouseMove:h,onMouseOut:g,onMouseOver:v,onMouseUp:d,onRightClick:x,onClick:M,onDrag:S,onLoad:E,onUnmount:P,onEdit:b}=t,m=i.useContext(A),[C,w]=i.useState(null),[D,y]=i.useState(null),[k,O]=i.useState(null),[U,V]=i.useState(null),[T,R]=i.useState(null),[W,q]=i.useState(null),[N,G]=i.useState(null),[$,K]=i.useState(null),[J,j]=i.useState(null),[Y,L]=i.useState(null),[ee,X]=i.useState(null),[z,te]=i.useState(null);return i.useEffect(()=>{C!==null&&C.setMap(m)},[m]),i.useEffect(()=>{typeof e<"u"&&C!==null&&C.setOptions(e)},[C,e]),i.useEffect(()=>{typeof n<"u"&&C!==null&&C.setDraggable(n)},[C,n]),i.useEffect(()=>{typeof s<"u"&&C!==null&&C.setEditable(s)},[C,s]),i.useEffect(()=>{typeof r<"u"&&C!==null&&C.setVisible(r)},[C,r]),i.useEffect(()=>{typeof o<"u"&&C!==null&&C.setPath(o)},[C,o]),i.useEffect(()=>{typeof a<"u"&&C!==null&&C.setPaths(a)},[C,a]),i.useEffect(()=>{C&&typeof l=="function"&&(D!==null&&google.maps.event.removeListener(D),y(google.maps.event.addListener(C,"dblclick",l)))},[l]),i.useEffect(()=>{C&&(google.maps.event.addListener(C.getPath(),"insert_at",()=>{b==null||b(C)}),google.maps.event.addListener(C.getPath(),"set_at",()=>{b==null||b(C)}),google.maps.event.addListener(C.getPath(),"remove_at",()=>{b==null||b(C)}))},[C,b]),i.useEffect(()=>{C&&typeof u=="function"&&(k!==null&&google.maps.event.removeListener(k),O(google.maps.event.addListener(C,"dragend",u)))},[u]),i.useEffect(()=>{C&&typeof c=="function"&&(U!==null&&google.maps.event.removeListener(U),V(google.maps.event.addListener(C,"dragstart",c)))},[c]),i.useEffect(()=>{C&&typeof p=="function"&&(T!==null&&google.maps.event.removeListener(T),R(google.maps.event.addListener(C,"mousedown",p)))},[p]),i.useEffect(()=>{C&&typeof h=="function"&&(W!==null&&google.maps.event.removeListener(W),q(google.maps.event.addListener(C,"mousemove",h)))},[h]),i.useEffect(()=>{C&&typeof g=="function"&&(N!==null&&google.maps.event.removeListener(N),G(google.maps.event.addListener(C,"mouseout",g)))},[g]),i.useEffect(()=>{C&&typeof v=="function"&&($!==null&&google.maps.event.removeListener($),K(google.maps.event.addListener(C,"mouseover",v)))},[v]),i.useEffect(()=>{C&&typeof d=="function"&&(J!==null&&google.maps.event.removeListener(J),j(google.maps.event.addListener(C,"mouseup",d)))},[d]),i.useEffect(()=>{C&&typeof x=="function"&&(Y!==null&&google.maps.event.removeListener(Y),L(google.maps.event.addListener(C,"rightclick",x)))},[x]),i.useEffect(()=>{C&&typeof M=="function"&&(ee!==null&&google.maps.event.removeListener(ee),X(google.maps.event.addListener(C,"click",M)))},[M]),i.useEffect(()=>{C&&typeof S=="function"&&(z!==null&&google.maps.event.removeListener(z),te(google.maps.event.addListener(C,"drag",S)))},[S]),i.useEffect(()=>{var I=new google.maps.Polygon(rn(rn({},e),{},{map:m}));return o&&I.setPath(o),a&&I.setPaths(a),typeof r<"u"&&I.setVisible(r),typeof s<"u"&&I.setEditable(s),typeof n<"u"&&I.setDraggable(n),l&&y(google.maps.event.addListener(I,"dblclick",l)),u&&O(google.maps.event.addListener(I,"dragend",u)),c&&V(google.maps.event.addListener(I,"dragstart",c)),p&&R(google.maps.event.addListener(I,"mousedown",p)),h&&q(google.maps.event.addListener(I,"mousemove",h)),g&&G(google.maps.event.addListener(I,"mouseout",g)),v&&K(google.maps.event.addListener(I,"mouseover",v)),d&&j(google.maps.event.addListener(I,"mouseup",d)),x&&L(google.maps.event.addListener(I,"rightclick",x)),M&&X(google.maps.event.addListener(I,"click",M)),S&&te(google.maps.event.addListener(I,"drag",S)),w(I),E&&E(I),()=>{D!==null&&google.maps.event.removeListener(D),k!==null&&google.maps.event.removeListener(k),U!==null&&google.maps.event.removeListener(U),T!==null&&google.maps.event.removeListener(T),W!==null&&google.maps.event.removeListener(W),N!==null&&google.maps.event.removeListener(N),$!==null&&google.maps.event.removeListener($),J!==null&&google.maps.event.removeListener(J),Y!==null&&google.maps.event.removeListener(Y),ee!==null&&google.maps.event.removeListener(ee),P&&P(I),I.setMap(null)}},[]),null}i.memo(fi);class vi extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[])}componentDidMount(){var e=this.props.options||{};this.polygon=new google.maps.Polygon(e),this.polygon.setMap(this.context),this.registeredEvents=_({updaterMap:an,eventMap:on,prevProps:{},nextProps:this.props,instance:this.polygon}),this.props.onLoad&&this.props.onLoad(this.polygon)}componentDidUpdate(e){this.polygon&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:an,eventMap:on,prevProps:e,nextProps:this.props,instance:this.polygon}))}componentWillUnmount(){this.polygon&&(this.props.onUnmount&&this.props.onUnmount(this.polygon),Z(this.registeredEvents),this.polygon&&this.polygon.setMap(null))}render(){return null}}f(vi,"contextType",A);function ln(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function Ne(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?ln(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ln(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var un={onBoundsChanged:"bounds_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},pn={bounds(t,e){t.setBounds(e)},draggable(t,e){t.setDraggable(e)},editable(t,e){t.setEditable(e)},map(t,e){t.setMap(e)},options(t,e){t.setOptions(e)},visible(t,e){t.setVisible(e)}};function mi(t){var{options:e,bounds:n,draggable:s,editable:r,visible:o,onDblClick:a,onDragEnd:l,onDragStart:u,onMouseDown:c,onMouseMove:p,onMouseOut:h,onMouseOver:g,onMouseUp:v,onRightClick:d,onClick:x,onDrag:M,onBoundsChanged:S,onLoad:E,onUnmount:P}=t,b=i.useContext(A),[m,C]=i.useState(null),[w,D]=i.useState(null),[y,k]=i.useState(null),[O,U]=i.useState(null),[V,T]=i.useState(null),[R,W]=i.useState(null),[q,N]=i.useState(null),[G,$]=i.useState(null),[K,J]=i.useState(null),[j,Y]=i.useState(null),[L,ee]=i.useState(null),[X,z]=i.useState(null),[te,I]=i.useState(null);return i.useEffect(()=>{m!==null&&m.setMap(b)},[b]),i.useEffect(()=>{typeof e<"u"&&m!==null&&m.setOptions(e)},[m,e]),i.useEffect(()=>{typeof s<"u"&&m!==null&&m.setDraggable(s)},[m,s]),i.useEffect(()=>{typeof r<"u"&&m!==null&&m.setEditable(r)},[m,r]),i.useEffect(()=>{typeof o<"u"&&m!==null&&m.setVisible(o)},[m,o]),i.useEffect(()=>{typeof n<"u"&&m!==null&&m.setBounds(n)},[m,n]),i.useEffect(()=>{m&&a&&(w!==null&&google.maps.event.removeListener(w),D(google.maps.event.addListener(m,"dblclick",a)))},[a]),i.useEffect(()=>{m&&l&&(y!==null&&google.maps.event.removeListener(y),k(google.maps.event.addListener(m,"dragend",l)))},[l]),i.useEffect(()=>{m&&u&&(O!==null&&google.maps.event.removeListener(O),U(google.maps.event.addListener(m,"dragstart",u)))},[u]),i.useEffect(()=>{m&&c&&(V!==null&&google.maps.event.removeListener(V),T(google.maps.event.addListener(m,"mousedown",c)))},[c]),i.useEffect(()=>{m&&p&&(R!==null&&google.maps.event.removeListener(R),W(google.maps.event.addListener(m,"mousemove",p)))},[p]),i.useEffect(()=>{m&&h&&(q!==null&&google.maps.event.removeListener(q),N(google.maps.event.addListener(m,"mouseout",h)))},[h]),i.useEffect(()=>{m&&g&&(G!==null&&google.maps.event.removeListener(G),$(google.maps.event.addListener(m,"mouseover",g)))},[g]),i.useEffect(()=>{m&&v&&(K!==null&&google.maps.event.removeListener(K),J(google.maps.event.addListener(m,"mouseup",v)))},[v]),i.useEffect(()=>{m&&d&&(j!==null&&google.maps.event.removeListener(j),Y(google.maps.event.addListener(m,"rightclick",d)))},[d]),i.useEffect(()=>{m&&x&&(L!==null&&google.maps.event.removeListener(L),ee(google.maps.event.addListener(m,"click",x)))},[x]),i.useEffect(()=>{m&&M&&(X!==null&&google.maps.event.removeListener(X),z(google.maps.event.addListener(m,"drag",M)))},[M]),i.useEffect(()=>{m&&S&&(te!==null&&google.maps.event.removeListener(te),I(google.maps.event.addListener(m,"bounds_changed",S)))},[S]),i.useEffect(()=>{var F=new google.maps.Rectangle(Ne(Ne({},e),{},{map:b}));return typeof o<"u"&&F.setVisible(o),typeof r<"u"&&F.setEditable(r),typeof s<"u"&&F.setDraggable(s),typeof n<"u"&&F.setBounds(n),a&&D(google.maps.event.addListener(F,"dblclick",a)),l&&k(google.maps.event.addListener(F,"dragend",l)),u&&U(google.maps.event.addListener(F,"dragstart",u)),c&&T(google.maps.event.addListener(F,"mousedown",c)),p&&W(google.maps.event.addListener(F,"mousemove",p)),h&&N(google.maps.event.addListener(F,"mouseout",h)),g&&$(google.maps.event.addListener(F,"mouseover",g)),v&&J(google.maps.event.addListener(F,"mouseup",v)),d&&Y(google.maps.event.addListener(F,"rightclick",d)),x&&ee(google.maps.event.addListener(F,"click",x)),M&&z(google.maps.event.addListener(F,"drag",M)),S&&I(google.maps.event.addListener(F,"bounds_changed",S)),C(F),E&&E(F),()=>{w!==null&&google.maps.event.removeListener(w),y!==null&&google.maps.event.removeListener(y),O!==null&&google.maps.event.removeListener(O),V!==null&&google.maps.event.removeListener(V),R!==null&&google.maps.event.removeListener(R),q!==null&&google.maps.event.removeListener(q),G!==null&&google.maps.event.removeListener(G),K!==null&&google.maps.event.removeListener(K),j!==null&&google.maps.event.removeListener(j),L!==null&&google.maps.event.removeListener(L),X!==null&&google.maps.event.removeListener(X),te!==null&&google.maps.event.removeListener(te),P&&P(F),F.setMap(null)}},[]),null}i.memo(mi);class Li extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"state",{rectangle:null}),f(this,"setRectangleCallback",()=>{this.state.rectangle!==null&&this.props.onLoad&&this.props.onLoad(this.state.rectangle)})}componentDidMount(){var e=new google.maps.Rectangle(Ne(Ne({},this.props.options),{},{map:this.context}));this.registeredEvents=_({updaterMap:pn,eventMap:un,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{rectangle:e}},this.setRectangleCallback)}componentDidUpdate(e){this.state.rectangle!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:pn,eventMap:un,prevProps:e,nextProps:this.props,instance:this.state.rectangle}))}componentWillUnmount(){this.state.rectangle!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.rectangle),Z(this.registeredEvents),this.state.rectangle.setMap(null))}render(){return null}}f(Li,"contextType",A);function dn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function $e(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?dn(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):dn(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var cn={onCenterChanged:"center_changed",onRadiusChanged:"radius_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},gn={center(t,e){t.setCenter(e)},draggable(t,e){t.setDraggable(e)},editable(t,e){t.setEditable(e)},map(t,e){t.setMap(e)},options(t,e){t.setOptions(e)},radius(t,e){t.setRadius(e)},visible(t,e){t.setVisible(e)}},yi={};function bi(t){var{options:e,center:n,radius:s,draggable:r,editable:o,visible:a,onDblClick:l,onDragEnd:u,onDragStart:c,onMouseDown:p,onMouseMove:h,onMouseOut:g,onMouseOver:v,onMouseUp:d,onRightClick:x,onClick:M,onDrag:S,onCenterChanged:E,onRadiusChanged:P,onLoad:b,onUnmount:m}=t,C=i.useContext(A),[w,D]=i.useState(null),[y,k]=i.useState(null),[O,U]=i.useState(null),[V,T]=i.useState(null),[R,W]=i.useState(null),[q,N]=i.useState(null),[G,$]=i.useState(null),[K,J]=i.useState(null),[j,Y]=i.useState(null),[L,ee]=i.useState(null),[X,z]=i.useState(null),[te,I]=i.useState(null),[F,oe]=i.useState(null),[ae,fe]=i.useState(null);return i.useEffect(()=>{w!==null&&w.setMap(C)},[C]),i.useEffect(()=>{typeof e<"u"&&w!==null&&w.setOptions(e)},[w,e]),i.useEffect(()=>{typeof r<"u"&&w!==null&&w.setDraggable(r)},[w,r]),i.useEffect(()=>{typeof o<"u"&&w!==null&&w.setEditable(o)},[w,o]),i.useEffect(()=>{typeof a<"u"&&w!==null&&w.setVisible(a)},[w,a]),i.useEffect(()=>{typeof s=="number"&&w!==null&&w.setRadius(s)},[w,s]),i.useEffect(()=>{typeof n<"u"&&w!==null&&w.setCenter(n)},[w,n]),i.useEffect(()=>{w&&l&&(y!==null&&google.maps.event.removeListener(y),k(google.maps.event.addListener(w,"dblclick",l)))},[l]),i.useEffect(()=>{w&&u&&(O!==null&&google.maps.event.removeListener(O),U(google.maps.event.addListener(w,"dragend",u)))},[u]),i.useEffect(()=>{w&&c&&(V!==null&&google.maps.event.removeListener(V),T(google.maps.event.addListener(w,"dragstart",c)))},[c]),i.useEffect(()=>{w&&p&&(R!==null&&google.maps.event.removeListener(R),W(google.maps.event.addListener(w,"mousedown",p)))},[p]),i.useEffect(()=>{w&&h&&(q!==null&&google.maps.event.removeListener(q),N(google.maps.event.addListener(w,"mousemove",h)))},[h]),i.useEffect(()=>{w&&g&&(G!==null&&google.maps.event.removeListener(G),$(google.maps.event.addListener(w,"mouseout",g)))},[g]),i.useEffect(()=>{w&&v&&(K!==null&&google.maps.event.removeListener(K),J(google.maps.event.addListener(w,"mouseover",v)))},[v]),i.useEffect(()=>{w&&d&&(j!==null&&google.maps.event.removeListener(j),Y(google.maps.event.addListener(w,"mouseup",d)))},[d]),i.useEffect(()=>{w&&x&&(L!==null&&google.maps.event.removeListener(L),ee(google.maps.event.addListener(w,"rightclick",x)))},[x]),i.useEffect(()=>{w&&M&&(X!==null&&google.maps.event.removeListener(X),z(google.maps.event.addListener(w,"click",M)))},[M]),i.useEffect(()=>{w&&S&&(te!==null&&google.maps.event.removeListener(te),I(google.maps.event.addListener(w,"drag",S)))},[S]),i.useEffect(()=>{w&&E&&(F!==null&&google.maps.event.removeListener(F),oe(google.maps.event.addListener(w,"center_changed",E)))},[M]),i.useEffect(()=>{w&&P&&(ae!==null&&google.maps.event.removeListener(ae),fe(google.maps.event.addListener(w,"radius_changed",P)))},[P]),i.useEffect(()=>{var Q=new google.maps.Circle($e($e({},e||yi),{},{map:C}));return typeof s=="number"&&Q.setRadius(s),typeof n<"u"&&Q.setCenter(n),typeof s=="number"&&Q.setRadius(s),typeof a<"u"&&Q.setVisible(a),typeof o<"u"&&Q.setEditable(o),typeof r<"u"&&Q.setDraggable(r),l&&k(google.maps.event.addListener(Q,"dblclick",l)),u&&U(google.maps.event.addListener(Q,"dragend",u)),c&&T(google.maps.event.addListener(Q,"dragstart",c)),p&&W(google.maps.event.addListener(Q,"mousedown",p)),h&&N(google.maps.event.addListener(Q,"mousemove",h)),g&&$(google.maps.event.addListener(Q,"mouseout",g)),v&&J(google.maps.event.addListener(Q,"mouseover",v)),d&&Y(google.maps.event.addListener(Q,"mouseup",d)),x&&ee(google.maps.event.addListener(Q,"rightclick",x)),M&&z(google.maps.event.addListener(Q,"click",M)),S&&I(google.maps.event.addListener(Q,"drag",S)),E&&oe(google.maps.event.addListener(Q,"center_changed",E)),P&&fe(google.maps.event.addListener(Q,"radius_changed",P)),D(Q),b&&b(Q),()=>{y!==null&&google.maps.event.removeListener(y),O!==null&&google.maps.event.removeListener(O),V!==null&&google.maps.event.removeListener(V),R!==null&&google.maps.event.removeListener(R),q!==null&&google.maps.event.removeListener(q),G!==null&&google.maps.event.removeListener(G),K!==null&&google.maps.event.removeListener(K),j!==null&&google.maps.event.removeListener(j),L!==null&&google.maps.event.removeListener(L),X!==null&&google.maps.event.removeListener(X),F!==null&&google.maps.event.removeListener(F),ae!==null&&google.maps.event.removeListener(ae),m&&m(Q),Q.setMap(null)}},[]),null}i.memo(bi);class Ci extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"state",{circle:null}),f(this,"setCircleCallback",()=>{this.state.circle!==null&&this.props.onLoad&&this.props.onLoad(this.state.circle)})}componentDidMount(){var e=new google.maps.Circle($e($e({},this.props.options),{},{map:this.context}));this.registeredEvents=_({updaterMap:gn,eventMap:cn,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{circle:e}},this.setCircleCallback)}componentDidUpdate(e){this.state.circle!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:gn,eventMap:cn,prevProps:e,nextProps:this.props,instance:this.state.circle}))}componentWillUnmount(){if(this.state.circle!==null){var e;this.props.onUnmount&&this.props.onUnmount(this.state.circle),Z(this.registeredEvents),(e=this.state.circle)===null||e===void 0||e.setMap(null)}}render(){return null}}f(Ci,"contextType",A);function hn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function Fe(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?hn(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):hn(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var fn={onClick:"click",onDblClick:"dblclick",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick",onAddFeature:"addfeature",onRemoveFeature:"removefeature",onRemoveProperty:"removeproperty",onSetGeometry:"setgeometry",onSetProperty:"setproperty"},vn={add(t,e){t.add(e)},addgeojson(t,e,n){t.addGeoJson(e,n)},contains(t,e){t.contains(e)},foreach(t,e){t.forEach(e)},loadgeojson(t,e,n,s){t.loadGeoJson(e,n,s)},overridestyle(t,e,n){t.overrideStyle(e,n)},remove(t,e){t.remove(e)},revertstyle(t,e){t.revertStyle(e)},controlposition(t,e){t.setControlPosition(e)},controls(t,e){t.setControls(e)},drawingmode(t,e){t.setDrawingMode(e)},map(t,e){t.setMap(e)},style(t,e){t.setStyle(e)},togeojson(t,e){t.toGeoJson(e)}};function Mi(t){var{options:e,onClick:n,onDblClick:s,onMouseDown:r,onMouseMove:o,onMouseOut:a,onMouseOver:l,onMouseUp:u,onRightClick:c,onAddFeature:p,onRemoveFeature:h,onRemoveProperty:g,onSetGeometry:v,onSetProperty:d,onLoad:x,onUnmount:M}=t,S=i.useContext(A),[E,P]=i.useState(null),[b,m]=i.useState(null),[C,w]=i.useState(null),[D,y]=i.useState(null),[k,O]=i.useState(null),[U,V]=i.useState(null),[T,R]=i.useState(null),[W,q]=i.useState(null),[N,G]=i.useState(null),[$,K]=i.useState(null),[J,j]=i.useState(null),[Y,L]=i.useState(null),[ee,X]=i.useState(null),[z,te]=i.useState(null);return i.useEffect(()=>{E!==null&&E.setMap(S)},[S]),i.useEffect(()=>{E&&s&&(b!==null&&google.maps.event.removeListener(b),m(google.maps.event.addListener(E,"dblclick",s)))},[s]),i.useEffect(()=>{E&&r&&(C!==null&&google.maps.event.removeListener(C),w(google.maps.event.addListener(E,"mousedown",r)))},[r]),i.useEffect(()=>{E&&o&&(D!==null&&google.maps.event.removeListener(D),y(google.maps.event.addListener(E,"mousemove",o)))},[o]),i.useEffect(()=>{E&&a&&(k!==null&&google.maps.event.removeListener(k),O(google.maps.event.addListener(E,"mouseout",a)))},[a]),i.useEffect(()=>{E&&l&&(U!==null&&google.maps.event.removeListener(U),V(google.maps.event.addListener(E,"mouseover",l)))},[l]),i.useEffect(()=>{E&&u&&(T!==null&&google.maps.event.removeListener(T),R(google.maps.event.addListener(E,"mouseup",u)))},[u]),i.useEffect(()=>{E&&c&&(W!==null&&google.maps.event.removeListener(W),q(google.maps.event.addListener(E,"rightclick",c)))},[c]),i.useEffect(()=>{E&&n&&(N!==null&&google.maps.event.removeListener(N),G(google.maps.event.addListener(E,"click",n)))},[n]),i.useEffect(()=>{E&&p&&($!==null&&google.maps.event.removeListener($),K(google.maps.event.addListener(E,"addfeature",p)))},[p]),i.useEffect(()=>{E&&h&&(J!==null&&google.maps.event.removeListener(J),j(google.maps.event.addListener(E,"removefeature",h)))},[h]),i.useEffect(()=>{E&&g&&(Y!==null&&google.maps.event.removeListener(Y),L(google.maps.event.addListener(E,"removeproperty",g)))},[g]),i.useEffect(()=>{E&&v&&(ee!==null&&google.maps.event.removeListener(ee),X(google.maps.event.addListener(E,"setgeometry",v)))},[v]),i.useEffect(()=>{E&&d&&(z!==null&&google.maps.event.removeListener(z),te(google.maps.event.addListener(E,"setproperty",d)))},[d]),i.useEffect(()=>{if(S!==null){var I=new google.maps.Data(Fe(Fe({},e),{},{map:S}));s&&m(google.maps.event.addListener(I,"dblclick",s)),r&&w(google.maps.event.addListener(I,"mousedown",r)),o&&y(google.maps.event.addListener(I,"mousemove",o)),a&&O(google.maps.event.addListener(I,"mouseout",a)),l&&V(google.maps.event.addListener(I,"mouseover",l)),u&&R(google.maps.event.addListener(I,"mouseup",u)),c&&q(google.maps.event.addListener(I,"rightclick",c)),n&&G(google.maps.event.addListener(I,"click",n)),p&&K(google.maps.event.addListener(I,"addfeature",p)),h&&j(google.maps.event.addListener(I,"removefeature",h)),g&&L(google.maps.event.addListener(I,"removeproperty",g)),v&&X(google.maps.event.addListener(I,"setgeometry",v)),d&&te(google.maps.event.addListener(I,"setproperty",d)),P(I),x&&x(I)}return()=>{E&&(b!==null&&google.maps.event.removeListener(b),C!==null&&google.maps.event.removeListener(C),D!==null&&google.maps.event.removeListener(D),k!==null&&google.maps.event.removeListener(k),U!==null&&google.maps.event.removeListener(U),T!==null&&google.maps.event.removeListener(T),W!==null&&google.maps.event.removeListener(W),N!==null&&google.maps.event.removeListener(N),$!==null&&google.maps.event.removeListener($),J!==null&&google.maps.event.removeListener(J),Y!==null&&google.maps.event.removeListener(Y),ee!==null&&google.maps.event.removeListener(ee),z!==null&&google.maps.event.removeListener(z),M&&M(E),E.setMap(null))}},[]),null}i.memo(Mi);class Ei extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"state",{data:null}),f(this,"setDataCallback",()=>{this.state.data!==null&&this.props.onLoad&&this.props.onLoad(this.state.data)})}componentDidMount(){if(this.context!==null){var e=new google.maps.Data(Fe(Fe({},this.props.options),{},{map:this.context}));this.registeredEvents=_({updaterMap:vn,eventMap:fn,prevProps:{},nextProps:this.props,instance:e}),this.setState(()=>({data:e}),this.setDataCallback)}}componentDidUpdate(e){this.state.data!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:vn,eventMap:fn,prevProps:e,nextProps:this.props,instance:this.state.data}))}componentWillUnmount(){this.state.data!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.data),Z(this.registeredEvents),this.state.data&&this.state.data.setMap(null))}render(){return null}}f(Ei,"contextType",A);function mn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function Ln(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?mn(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):mn(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var yn={onClick:"click",onDefaultViewportChanged:"defaultviewport_changed",onStatusChanged:"status_changed"},bn={options(t,e){t.setOptions(e)},url(t,e){t.setUrl(e)},zIndex(t,e){t.setZIndex(e)}};class wi extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"state",{kmlLayer:null}),f(this,"setKmlLayerCallback",()=>{this.state.kmlLayer!==null&&this.props.onLoad&&this.props.onLoad(this.state.kmlLayer)})}componentDidMount(){var e=new google.maps.KmlLayer(Ln(Ln({},this.props.options),{},{map:this.context}));this.registeredEvents=_({updaterMap:bn,eventMap:yn,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{kmlLayer:e}},this.setKmlLayerCallback)}componentDidUpdate(e){this.state.kmlLayer!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:bn,eventMap:yn,prevProps:e,nextProps:this.props,instance:this.state.kmlLayer}))}componentWillUnmount(){this.state.kmlLayer!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.kmlLayer),Z(this.registeredEvents),this.state.kmlLayer.setMap(null))}render(){return null}}f(wi,"contextType",A);function Yn(t,e){return typeof e=="function"?e(t.offsetWidth,t.offsetHeight):{x:0,y:0}}function xi(t,e){return new e(t.lat,t.lng)}function Si(t,e){return new e(new google.maps.LatLng(t.ne.lat,t.ne.lng),new google.maps.LatLng(t.sw.lat,t.sw.lng))}function ki(t,e,n){return t instanceof e?t:n(t,e)}function Oi(t,e,n){return t instanceof e?t:n(t,e)}function Pi(t,e,n){var s=t&&t.fromLatLngToDivPixel(n.getNorthEast()),r=t&&t.fromLatLngToDivPixel(n.getSouthWest());return s&&r?{left:"".concat(r.x+e.x,"px"),top:"".concat(s.y+e.y,"px"),width:"".concat(s.x-r.x-e.x,"px"),height:"".concat(r.y-s.y-e.y,"px")}:{left:"-9999px",top:"-9999px"}}function Di(t,e,n){var s=t&&t.fromLatLngToDivPixel(n);if(s){var{x:r,y:o}=s;return{left:"".concat(r+e.x,"px"),top:"".concat(o+e.y,"px")}}return{left:"-9999px",top:"-9999px"}}function Qn(t,e,n,s){return n!==void 0?Pi(t,e,Oi(n,google.maps.LatLngBounds,Si)):Di(t,e,ki(s,google.maps.LatLng,xi))}function Ii(t,e){return t.left===e.left&&t.top===e.top&&t.width===e.height&&t.height===e.height}function Cn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function ji(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Cn(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Cn(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}function Ti(t,e,n,s,r){class o extends google.maps.OverlayView{constructor(l,u,c,p){super(),this.container=l,this.pane=u,this.position=c,this.bounds=p}onAdd(){var l,u=(l=this.getPanes())===null||l===void 0?void 0:l[this.pane];u==null||u.appendChild(this.container)}draw(){var l=this.getProjection(),u=ji({},this.container?Yn(this.container,r):{x:0,y:0}),c=Qn(l,u,this.bounds,this.position);for(var[p,h]of Object.entries(c))this.container.style[p]=h}onRemove(){this.container.parentNode!==null&&this.container.parentNode.removeChild(this.container)}}return new o(t,e,n,s)}function Mn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function Ri(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Mn(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Mn(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}function En(t){if(!t)return"";var e=t instanceof google.maps.LatLng?t:new google.maps.LatLng(t.lat,t.lng);return e+""}function wn(t){if(!t)return"";var e=t instanceof google.maps.LatLngBounds?t:new google.maps.LatLngBounds(new google.maps.LatLng(t.south,t.east),new google.maps.LatLng(t.north,t.west));return e+""}function Bi(t){var{position:e,bounds:n,mapPaneName:s,zIndex:r,onLoad:o,onUnmount:a,getPixelPositionOffset:l,children:u}=t,c=i.useContext(A),p=i.useMemo(()=>{var g=document.createElement("div");return g.style.position="absolute",g},[]),h=i.useMemo(()=>Ti(p,s,e,n,l),[p,s,e,n]);return i.useEffect(()=>(o==null||o(h),h==null||h.setMap(c),()=>{a==null||a(h),h==null||h.setMap(null)}),[c,h]),i.useEffect(()=>{p.style.zIndex="".concat(r)},[r,p]),ge.createPortal(u,p)}i.memo(Bi);class he extends i.PureComponent{constructor(e){super(e),f(this,"state",{paneEl:null,containerStyle:{position:"absolute"}}),f(this,"updatePane",()=>{var s=this.props.mapPaneName,r=this.overlayView.getPanes();ne(!!s,"OverlayView requires props.mapPaneName but got %s",s),r?this.setState({paneEl:r[s]}):this.setState({paneEl:null})}),f(this,"onAdd",()=>{var s,r;this.updatePane(),(s=(r=this.props).onLoad)===null||s===void 0||s.call(r,this.overlayView)}),f(this,"onPositionElement",()=>{var s=this.overlayView.getProjection(),r=Ri({x:0,y:0},this.containerRef.current?Yn(this.containerRef.current,this.props.getPixelPositionOffset):{}),o=Qn(s,r,this.props.bounds,this.props.position);if(!Ii(o,{left:this.state.containerStyle.left,top:this.state.containerStyle.top,width:this.state.containerStyle.width,height:this.state.containerStyle.height})){var a,l,u,c;this.setState({containerStyle:{top:(a=o.top)!==null&&a!==void 0?a:0,left:(l=o.left)!==null&&l!==void 0?l:0,width:(u=o.width)!==null&&u!==void 0?u:0,height:(c=o.height)!==null&&c!==void 0?c:0,position:"absolute"}})}}),f(this,"draw",()=>{this.onPositionElement()}),f(this,"onRemove",()=>{var s,r;this.setState(()=>({paneEl:null})),(s=(r=this.props).onUnmount)===null||s===void 0||s.call(r,this.overlayView)}),this.containerRef=i.createRef();var n=new google.maps.OverlayView;n.onAdd=this.onAdd,n.draw=this.draw,n.onRemove=this.onRemove,this.overlayView=n}componentDidMount(){this.overlayView.setMap(this.context)}componentDidUpdate(e){var n=En(e.position),s=En(this.props.position),r=wn(e.bounds),o=wn(this.props.bounds);(n!==s||r!==o)&&this.overlayView.draw(),e.mapPaneName!==this.props.mapPaneName&&this.updatePane()}componentWillUnmount(){this.overlayView.setMap(null)}render(){var e=this.state.paneEl;return e?ge.createPortal(se.jsx("div",{ref:this.containerRef,style:this.state.containerStyle,children:i.Children.only(this.props.children)}),e):null}}f(he,"FLOAT_PANE","floatPane");f(he,"MAP_PANE","mapPane");f(he,"MARKER_LAYER","markerLayer");f(he,"OVERLAY_LAYER","overlayLayer");f(he,"OVERLAY_MOUSE_TARGET","overlayMouseTarget");f(he,"contextType",A);function Ui(){}function xn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function Sn(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?xn(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):xn(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var kn={onDblClick:"dblclick",onClick:"click"},On={opacity(t,e){t.setOpacity(e)}};function Ai(t){var{url:e,bounds:n,options:s,visible:r}=t,o=i.useContext(A),a=new google.maps.LatLngBounds(new google.maps.LatLng(n.south,n.west),new google.maps.LatLng(n.north,n.east)),l=i.useMemo(()=>new google.maps.GroundOverlay(e,a,s),[]);return i.useEffect(()=>{l!==null&&l.setMap(o)},[o]),i.useEffect(()=>{typeof e<"u"&&l!==null&&(l.set("url",e),l.setMap(o))},[l,e]),i.useEffect(()=>{typeof r<"u"&&l!==null&&l.setOpacity(r?1:0)},[l,r]),i.useEffect(()=>{var u=new google.maps.LatLngBounds(new google.maps.LatLng(n.south,n.west),new google.maps.LatLng(n.north,n.east));typeof n<"u"&&l!==null&&(l.set("bounds",u),l.setMap(o))},[l,n]),null}i.memo(Ai);class Xn extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"state",{groundOverlay:null}),f(this,"setGroundOverlayCallback",()=>{this.state.groundOverlay!==null&&this.props.onLoad&&this.props.onLoad(this.state.groundOverlay)})}componentDidMount(){ne(!!this.props.url||!!this.props.bounds,"For GroundOverlay, url and bounds are passed in to constructor and are immutable after instantiated. This is the behavior of Google Maps JavaScript API v3 ( See https://developers.google.com/maps/documentation/javascript/reference#GroundOverlay) Hence, use the corresponding two props provided by `react-google-maps-api`, url and bounds. In some cases, you'll need the GroundOverlay component to reflect the changes of url and bounds. You can leverage the React's key property to remount the component. Typically, just `key={url}` would serve your need. See https://github.com/tomchentw/react-google-maps/issues/655");var e=new google.maps.GroundOverlay(this.props.url,this.props.bounds,Sn(Sn({},this.props.options),{},{map:this.context}));this.registeredEvents=_({updaterMap:On,eventMap:kn,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{groundOverlay:e}},this.setGroundOverlayCallback)}componentDidUpdate(e){this.state.groundOverlay!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:On,eventMap:kn,prevProps:e,nextProps:this.props,instance:this.state.groundOverlay}))}componentWillUnmount(){this.state.groundOverlay&&(this.props.onUnmount&&this.props.onUnmount(this.state.groundOverlay),this.state.groundOverlay.setMap(null))}render(){return null}}f(Xn,"defaultProps",{onLoad:Ui});f(Xn,"contextType",A);function Pn(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);e&&(s=s.filter(function(r){return Object.getOwnPropertyDescriptor(t,r).enumerable})),n.push.apply(n,s)}return n}function He(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Pn(Object(n),!0).forEach(function(s){f(t,s,n[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Pn(Object(n)).forEach(function(s){Object.defineProperty(t,s,Object.getOwnPropertyDescriptor(n,s))})}return t}var Dn={},In={data(t,e){t.setData(e)},map(t,e){t.setMap(e)},options(t,e){t.setOptions(e)}};function zi(t){var{data:e,onLoad:n,onUnmount:s,options:r}=t,o=i.useContext(A),[a,l]=i.useState(null);return i.useEffect(()=>{google.maps.visualization||ne(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} in useJsApiScript? %s',google.maps.visualization)},[]),i.useEffect(()=>{ne(!!e,"data property is required in HeatmapLayer %s",e)},[e]),i.useEffect(()=>{a!==null&&a.setMap(o)},[o]),i.useEffect(()=>{r&&a!==null&&a.setOptions(r)},[a,r]),i.useEffect(()=>{var u=new google.maps.visualization.HeatmapLayer(He(He({},r),{},{data:e,map:o}));return l(u),n&&n(u),()=>{a!==null&&(s&&s(a),a.setMap(null))}},[]),null}i.memo(zi);class _i extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"state",{heatmapLayer:null}),f(this,"setHeatmapLayerCallback",()=>{this.state.heatmapLayer!==null&&this.props.onLoad&&this.props.onLoad(this.state.heatmapLayer)})}componentDidMount(){ne(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} to <LoadScript />? %s',google.maps.visualization),ne(!!this.props.data,"data property is required in HeatmapLayer %s",this.props.data);var e=new google.maps.visualization.HeatmapLayer(He(He({},this.props.options),{},{data:this.props.data,map:this.context}));this.registeredEvents=_({updaterMap:In,eventMap:Dn,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{heatmapLayer:e}},this.setHeatmapLayerCallback)}componentDidUpdate(e){Z(this.registeredEvents),this.registeredEvents=_({updaterMap:In,eventMap:Dn,prevProps:e,nextProps:this.props,instance:this.state.heatmapLayer})}componentWillUnmount(){this.state.heatmapLayer!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.heatmapLayer),Z(this.registeredEvents),this.state.heatmapLayer.setMap(null))}render(){return null}}f(_i,"contextType",A);var jn={onCloseClick:"closeclick",onPanoChanged:"pano_changed",onPositionChanged:"position_changed",onPovChanged:"pov_changed",onResize:"resize",onStatusChanged:"status_changed",onVisibleChanged:"visible_changed",onZoomChanged:"zoom_changed"},Tn={register(t,e,n){t.registerPanoProvider(e,n)},links(t,e){t.setLinks(e)},motionTracking(t,e){t.setMotionTracking(e)},options(t,e){t.setOptions(e)},pano(t,e){t.setPano(e)},position(t,e){t.setPosition(e)},pov(t,e){t.setPov(e)},visible(t,e){t.setVisible(e)},zoom(t,e){t.setZoom(e)}};class Zi extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"state",{streetViewPanorama:null}),f(this,"setStreetViewPanoramaCallback",()=>{this.state.streetViewPanorama!==null&&this.props.onLoad&&this.props.onLoad(this.state.streetViewPanorama)})}componentDidMount(){var e,n,s=(e=(n=this.context)===null||n===void 0?void 0:n.getStreetView())!==null&&e!==void 0?e:null;this.registeredEvents=_({updaterMap:Tn,eventMap:jn,prevProps:{},nextProps:this.props,instance:s}),this.setState(()=>({streetViewPanorama:s}),this.setStreetViewPanoramaCallback)}componentDidUpdate(e){this.state.streetViewPanorama!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:Tn,eventMap:jn,prevProps:e,nextProps:this.props,instance:this.state.streetViewPanorama}))}componentWillUnmount(){this.state.streetViewPanorama!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.streetViewPanorama),Z(this.registeredEvents),this.state.streetViewPanorama.setVisible(!1))}render(){return null}}f(Zi,"contextType",A);class Vi extends i.PureComponent{constructor(){super(...arguments),f(this,"state",{streetViewService:null}),f(this,"setStreetViewServiceCallback",()=>{this.state.streetViewService!==null&&this.props.onLoad&&this.props.onLoad(this.state.streetViewService)})}componentDidMount(){var e=new google.maps.StreetViewService;this.setState(function(){return{streetViewService:e}},this.setStreetViewServiceCallback)}componentWillUnmount(){this.state.streetViewService!==null&&this.props.onUnmount&&this.props.onUnmount(this.state.streetViewService)}render(){return null}}f(Vi,"contextType",A);var Rn={onDirectionsChanged:"directions_changed"},Bn={directions(t,e){t.setDirections(e)},map(t,e){t.setMap(e)},options(t,e){t.setOptions(e)},panel(t,e){t.setPanel(e)},routeIndex(t,e){t.setRouteIndex(e)}};class Wi extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"state",{directionsRenderer:null}),f(this,"setDirectionsRendererCallback",()=>{this.state.directionsRenderer!==null&&(this.state.directionsRenderer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.directionsRenderer))})}componentDidMount(){var e=new google.maps.DirectionsRenderer(this.props.options);this.registeredEvents=_({updaterMap:Bn,eventMap:Rn,prevProps:{},nextProps:this.props,instance:e}),this.setState(function(){return{directionsRenderer:e}},this.setDirectionsRendererCallback)}componentDidUpdate(e){this.state.directionsRenderer!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:Bn,eventMap:Rn,prevProps:e,nextProps:this.props,instance:this.state.directionsRenderer}))}componentWillUnmount(){this.state.directionsRenderer!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.directionsRenderer),Z(this.registeredEvents),this.state.directionsRenderer&&this.state.directionsRenderer.setMap(null))}render(){return null}}f(Wi,"contextType",A);var Un={onPlacesChanged:"places_changed"},An={bounds(t,e){t.setBounds(e)}};class Ni extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"containerElement",i.createRef()),f(this,"state",{searchBox:null}),f(this,"setSearchBoxCallback",()=>{this.state.searchBox!==null&&this.props.onLoad&&this.props.onLoad(this.state.searchBox)})}componentDidMount(){if(ne(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places),this.containerElement!==null&&this.containerElement.current!==null){var e=this.containerElement.current.querySelector("input");if(e!==null){var n=new google.maps.places.SearchBox(e,this.props.options);this.registeredEvents=_({updaterMap:An,eventMap:Un,prevProps:{},nextProps:this.props,instance:n}),this.setState(function(){return{searchBox:n}},this.setSearchBoxCallback)}}}componentDidUpdate(e){this.state.searchBox!==null&&(Z(this.registeredEvents),this.registeredEvents=_({updaterMap:An,eventMap:Un,prevProps:e,nextProps:this.props,instance:this.state.searchBox}))}componentWillUnmount(){this.state.searchBox!==null&&(this.props.onUnmount&&this.props.onUnmount(this.state.searchBox),Z(this.registeredEvents))}render(){return se.jsx("div",{ref:this.containerElement,children:i.Children.only(this.props.children)})}}f(Ni,"contextType",A);var zn={onPlaceChanged:"place_changed"},_n={bounds(t,e){t.setBounds(e)},restrictions(t,e){t.setComponentRestrictions(e)},fields(t,e){t.setFields(e)},options(t,e){t.setOptions(e)},types(t,e){t.setTypes(e)}};class es extends i.PureComponent{constructor(){super(...arguments),f(this,"registeredEvents",[]),f(this,"containerElement",i.createRef()),f(this,"state",{autocomplete:null}),f(this,"setAutocompleteCallback",()=>{this.state.autocomplete!==null&&this.props.onLoad&&this.props.onLoad(this.state.autocomplete)})}componentDidMount(){var e;ne(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places);var n=(e=this.containerElement.current)===null||e===void 0?void 0:e.querySelector("input");if(n){var s=new google.maps.places.Autocomplete(n,this.props.options);this.registeredEvents=_({updaterMap:_n,eventMap:zn,prevProps:{},nextProps:this.props,instance:s}),this.setState(()=>({autocomplete:s}),this.setAutocompleteCallback)}}componentDidUpdate(e){Z(this.registeredEvents),this.registeredEvents=_({updaterMap:_n,eventMap:zn,prevProps:e,nextProps:this.props,instance:this.state.autocomplete})}componentWillUnmount(){this.state.autocomplete!==null&&Z(this.registeredEvents)}render(){return se.jsx("div",{ref:this.containerElement,className:this.props.className,children:i.Children.only(this.props.children)})}}f(es,"defaultProps",{className:""});f(es,"contextType",A);export{Hi as G,di as I,Os as M,fs as u};
