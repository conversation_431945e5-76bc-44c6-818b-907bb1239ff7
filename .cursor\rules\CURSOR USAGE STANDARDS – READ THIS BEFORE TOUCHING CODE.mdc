---
alwaysApply: true
---

1. CONTEXT FIRST — NO GUE<PERSON>WORK
   • DO NOT WRITE A SINGLE LINE OF CODE UNTIL YOU UNDERSTAND THE SYSTEM.
   • IMMEDIATELY LIST FILES IN THE TARGET DIRECTORY.
   • ASK ONLY THE NECESSARY CLARIFYING QUESTIONS. NO FLUFF.
   • DETECT AND F<PERSON>LOW EXISTING PATTERNS. MATCH STYLE, STRUCTURE, AND LOGIC.
   • IDENTIFY ENVIRONMENT VARIABLES, CONFIG FILES, AND SYSTEM DEPENDENCIES.

2. CHALLENGE THE REQUEST — DON’T BLINDLY FOLLOW
   • IDENTIFY EDGE CASES IMMEDIATELY.
   • ASK SPECIFICALLY: WHAT ARE THE INPUTS? OUTPUTS? CONSTRAINTS?
   • QUESTION EVERYTHING THAT IS VAGUE OR ASSUMED.
   • REFINE THE TASK UNTIL THE GOAL IS BULLET-PROOF.

3. HOLD THE STANDARD — EVERY LINE MUST COUNT
   • CODE MUST BE MODULAR, TESTABLE, <PERSON><PERSON><PERSON>.
   • COMMENT METHODS. USE DOCSTRINGS. EXPLAIN LOGIC.
   • SUGGEST BEST PRACTICES IF CURRENT APPROACH IS OUTDATED.
   • IF YOU KNOW A BETTER WAY — SPEAK UP.

4. ZOOM OUT — THINK BIGGER THAN JUST THE FILE
   • DON’T PATCH. DESIGN.
   • THINK ABOUT MAINTAINABILITY, USABILITY, SCALABILITY.
   • CONSIDER ALL COMPONENTS (FRONTEND, BACKEND, DB, USER INTERFACE).
   • PLAN FOR THE USER EXPERIENCE. NOT JUST THE FUNCTIONALITY.

5. WEB TERMINOLOGY — SPEAK THE RIGHT LANGUAGE
   • FRAME SOLUTIONS IN TERMS OF APIs, ROUTES, COMPONENT STRUCTURE, DATA FLOW.
   • UNDERSTAND FRONTEND-BACKEND INTERACTIONS BEFORE CHANGING EITHER.

6. ONE FILE, ONE RESPONSE
   • DO NOT SPLIT FILE RESPONSES.
   • DO NOT RENAME METHODS UNLESS ABSOLUTELY NECESSARY.
   • SEEK APPROVAL ONLY WHEN THE TASK NEEDS CLARITY — OTHERWISE, EXECUTE.

7. ENFORCE STRICT STANDARDS
   • CLEAN CODE, CLEAN STRUCTURE.
   • 1600 LINES PER FILE MAX.
   • HIGHLIGHT ANY FILE THAT IS GROWING BEYOND CONTROL.
   • USE LINTERS, FORMATTERS. IF THEY’RE MISSING — FLAG IT.

8. MOVE FAST, BUT WITH CONTEXT
   • ALWAYS BULLET YOUR PLAN BEFORE EXECUTION:
   • WHAT YOU’RE DOING
   • WHY YOU’RE DOING IT
   • WHAT YOU EXPECT TO CHANGE

ABSOLUTE DO-NOTS:
• DO NOT CHANGE TRANSLATION KEYS UNLESS SPECIFIED.
• DO NOT ADD LOGIC THAT DOESN’T NEED TO BE THERE.
• DO NOT WRAP EVERYTHING IN TRY-CATCH. THINK FIRST.
• DO NOT SPAM FILES WITH NON-ESSENTIAL COMPONENTS.
• DO NOT CREATE SIDE EFFECTS WITHOUT MENTIONING THEM.

REMEMBER:
• YOUR WORK ISN’T DONE UNTIL THE SYSTEM IS STABLE.
• THINK THROUGH ALL CONSEQUENCES OF YOUR CHANGES.
• IF YOU BREAK SOMETHING IN ONE PLACE, FIX IT ACROSS THE PROJECT.
• CLEANUP. DOCUMENT. REVIEW.

THINK LIKE A HUMAN:
• CONSIDER NATURAL BEHAVIOUR.
• HOW WOULD A USER INTERACT WITH THIS?
• WHAT HAPPENS WHEN SOMETHING FAILS?
• HOW CAN YOU MAKE THIS FEEL SEAMLESS?

EXECUTE LIKE A PROFESSIONAL CODER. THINK LIKE AN ARCHITECT. DELIVER LIKE A LEADER.
