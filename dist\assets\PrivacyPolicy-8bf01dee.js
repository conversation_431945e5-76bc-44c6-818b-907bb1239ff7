import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import"./@craftjs/core-d3c11b68.js";import"./vendor-851db8c1.js";import"./@fortawesome/react-fontawesome-********.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const l=()=>{const o=[{title:"1. Introduction",content:"Your privacy is important to us. This Privacy Policy explains how we collect, use, and protect your personal information when you use our website and services."},{title:"2. Information We Collect",content:`We collect the following information:

• Account Information: Name, email address, phone number, and payment details.
• Booking Details: Reservation history, preferences, and payment history.
• Usage Data: Information about how you use our website, including device and browser information.`},{title:"3. How We Use Your Information",content:`We use your information to:

• Facilitate bookings, payments, and user connections.
• Improve our services and personalize your experience.
• Communicate with you about reservations, promotions, or updates.
• Ensure compliance with applicable laws and regulations.`},{title:"4. Sharing Your Information",content:`• With Other Users: If you opt to connect with other users, your email address will be shared with selected users.
• With Third Parties: We may share your information with payment processors, service providers, or as required by law.
• With Facilities or Instructors: Your booking details may be shared with the relevant facilities or instructors.`},{title:"5. Data Security",content:"We implement industry-standard measures to protect your information. However, no system is completely secure, and we cannot guarantee the absolute security of your data."},{title:"6. Your Rights",content:`You have the right to:

• Access, update, or delete your personal information.
• Opt out of promotional communications.
• Request clarification about how your data is used.`},{title:"7. Cookies and Tracking",content:"We use cookies and similar technologies to enhance your experience. You can manage your cookie preferences through your browser settings."},{title:"8. Retention of Information",content:"We retain your information for as long as necessary to provide our services and comply with legal obligations."},{title:"9. Changes to This Policy",content:"We may update this Privacy Policy from time to time. Changes will be communicated via the website, and continued use of the service constitutes acceptance of the updated policy."},{title:"10. Contact Us",content:"For questions about this Privacy Policy, please contact <NAME_EMAIL>."}];return e.jsx("div",{className:"max-w-3xl mx-auto py-12 px-4",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[e.jsx("h1",{className:"text-3xl font-bold mb-8",children:"Privacy Policy"}),o.map((t,i)=>e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-xl font-semibold mb-3",children:t.title}),e.jsx("div",{className:"text-gray-600 whitespace-pre-line leading-relaxed",children:t.content})]},i)),e.jsxs("p",{className:"text-sm text-gray-500 mt-8",children:["Last updated: ",new Date().toLocaleDateString()]})]})})};export{l as default};
