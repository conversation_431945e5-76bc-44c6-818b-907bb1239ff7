import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as O,r as a,f as ve,L as ye,k as Ee}from"./vendor-851db8c1.js";import{u as me}from"./react-hook-form-687afde5.js";import{o as pe}from"./yup-2824f222.js";import{c as xe,a as ue,e as we}from"./yup-54691517.js";import{M as ee,T as Ne,A as Se,G as Q,h as ne,f as he,t as q,O as ae,b as re,c as le,d as x,P as Te,Q as Fe,R as Me,S as $e,u as De,U as Be,V as Le,W as Ue,i as Re,j as ze,X as Oe,F as Ze,Y as Ge}from"./index-08a5dc5b.js";import"./index-02625b16.js";import{I as He}from"./ImageCropModal-266718bc.js";import{F as Ye,a as qe}from"./index.esm-51ae62c8.js";import{S as We}from"./index.esm-92169588.js";import{b as Je}from"./index.esm-c561e951.js";import{u as _e,a as Ke,C as je}from"./@stripe/react-stripe-js-64f0e61f.js";import Qe from"./PaginationBar-a8a611bb.js";import{L as Xe}from"./index.esm-3a36c7d6.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import{B as Ve}from"./BackButton-11ba52b2.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./@hookform/resolvers-67648cca.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./react-image-crop-1f5038af.js";let J=new ee,es=new Ne;const ss=()=>{const b=xe({email:ue().email().required()}).required(),{dispatch:w}=O.useContext(Se),[N,y]=a.useState("");O.useState({});const[k,E]=a.useState("");a.useState(!1);const[u,f]=a.useState(!1),[n,I]=a.useState({}),[H,A]=a.useState(!0),[W,F]=a.useState(null),[M,h]=a.useState(""),{dispatch:j}=O.useContext(Q),[v,D]=a.useState(!1),[g,z]=a.useState(null),[Z,B]=a.useState(!1),[$,S]=a.useState(null),{register:G,handleSubmit:s,setError:o,setValue:p,formState:{errors:c}}=me({resolver:pe(b)}),_=localStorage.getItem("user");async function L(){var l;A(!0);try{const i=await es.getList("profile",{filter:[`user_id,eq,${_}`],join:["user|user_id"]}),r=(l=i==null?void 0:i.list)==null?void 0:l[0];if(r){const m=r.user||{},R=r.id,t={...r,...m,profile_id:R,user_id:m.id};I(t),p("email",m==null?void 0:m.email),p("first_name",m==null?void 0:m.first_name),p("last_name",m==null?void 0:m.last_name),p("phone",m==null?void 0:m.phone),p("bio",m==null?void 0:m.bio),y(m==null?void 0:m.email),E(m==null?void 0:m.photo),p("gender",r==null?void 0:r.gender),p("address",r==null?void 0:r.address),p("city",r==null?void 0:r.city),p("state",r==null?void 0:r.state),p("zip_code",r==null?void 0:r.zip_code),p("ntrp",r==null?void 0:r.ntrp),w({type:"UPDATE_PROFILE",payload:t}),A(!1)}}catch(i){q(w,i.response.data.message?i.response.data.message:i.message)}}const C=["email","first_name","last_name","phone","bio","photo","alternative_phone","age_group","family_role","password","verify","status"],X=["gender","address","city","state","zip_code","ntrp","date_of_birth","country","house_no"];console.log("User Profile Data:",{user_id:n==null?void 0:n.user_id,profile_id:n==null?void 0:n.profile_id,defaultValues:n});const P=async(l,i)=>{try{f(!0);const r={[l]:i},m=C.includes(l),R=X.includes(l);if(m){J.setTable("user");const t=await J.callRestAPI({id:n==null?void 0:n.user_id,...r},"PUT");t.error?U(t):(await ae(J,{activity_type:re.user_management,action_type:le.UPDATE,data:{field:l,old_value:n==null?void 0:n[l],new_value:i,user_id:n==null?void 0:n.user_id,table:"user"},description:`Updated user profile field: ${l}`}),x(j,"Profile Updated",4e3),F(null),h(""),L())}else if(R){J.setTable("profile");const t=await J.callRestAPI({id:n==null?void 0:n.profile_id,...r},"PUT");t.error?U(t):(await ae(J,{activity_type:re.user_management,action_type:le.UPDATE,data:{field:l,old_value:n==null?void 0:n[l],new_value:i,user_id:n==null?void 0:n.user_id,profile_id:n==null?void 0:n.profile_id,table:"profile"},description:`Updated profile field: ${l}`}),x(j,"Profile Updated",4e3),F(null),h(""),L())}else{x(j,"Unknown field type: "+l,4e3,"error"),f(!1);return}f(!1)}catch(r){f(!1),o(l,{type:"manual",message:r!=null&&r.message&&r==null?void 0:r.message}),q(w,r!=null&&r.message&&r==null?void 0:r.message)}},U=l=>{if(l.validation){const i=Object.keys(l.validation);for(let r=0;r<i.length;r++){const m=i[r];o(m,{type:"manual",message:l.validation[m]})}}},ie=l=>{try{if(l.size>2*1024*1024){x(j,"File size exceeds 2MB limit. Please choose a smaller file.",3e3,"error");return}S(l.type);const i=new FileReader;i.onload=()=>{z(i.result),B(!0)},i.readAsDataURL(l)}catch(i){x(j,i==null?void 0:i.message,3e3,"error"),console.log(i)}},V=async l=>{try{D(!0);const i=$==="image/png",r=new File([l],`cropped_profile.${i?"png":"jpg"}`,{type:i?"image/png":"image/jpeg"});let m=new FormData;m.append("file",r);let R=await J.uploadImage(m);P("photo",R==null?void 0:R.url)}catch(i){x(j,i==null?void 0:i.message,3e3,"error"),console.log(i)}finally{D(!1)}},oe=()=>{P("photo",null),I({...n,photo:null})};return O.useEffect(()=>{L()},[]),e.jsxs("div",{className:"",children:[H||v&&e.jsx(ne,{}),e.jsx("div",{className:"",children:e.jsxs("div",{className:"",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Profile details"})}),e.jsx(He,{isOpen:Z,onClose:()=>B(!1),image:g,onCropComplete:V}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:k||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsx("div",{children:e.jsxs("div",{className:"flex flex-col items-start justify-between gap-2",children:[e.jsx("p",{className:" font-medium text-gray-700",children:"Profile Picture"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:oe,disabled:!k,className:"rounded-xl border border-red-600 px-3 py-1.5  text-red-600 disabled:opacity-50",children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-xl border border-gray-300 px-3 py-1.5  text-gray-700",children:["Change Photo",e.jsx("input",{type:"file",accept:"image/*",onChange:l=>ie(l.target.files[0]),className:"hidden"})]})]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Min 400x400px, PNG or JPEG"})]})})]}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"First Name"},{key:"last_name",label:"Last Name"},{key:"gender",label:"Gender",type:"select",options:["male","female","other"]},{key:"email",label:"Email",note:"Your email is not shared with other users."},{key:"phone",label:"Phone"},{key:"address",label:"Address"},{key:"city",label:"City"},{key:"state",label:"State"},{key:"zip_code",label:"Zip Code"},{key:"ntrp",label:"NTRP"},{key:"bio",label:"Bio",type:"textarea"}].map(l=>e.jsx("div",{children:W===l.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{className:" font-medium text-gray-700",children:l.label}),e.jsx("button",{onClick:()=>F(null),className:" text-primaryBlue underline hover:text-primaryBlue",children:"Cancel"})]}),l.type==="select"?e.jsxs("select",{value:M,onChange:i=>h(i.target.value),className:"mt-1 w-full rounded-md border border-gray-300 p-2",children:[e.jsxs("option",{value:"",children:["Select ",l.label.toLowerCase()]}),l.options.map(i=>e.jsx("option",{value:i,children:i.charAt(0).toUpperCase()+i.slice(1)},i))]}):l.type==="textarea"?e.jsx("textarea",{value:M,onChange:i=>h(i.target.value),rows:4,className:"mt-1 w-full rounded-md border border-gray-300 p-2"}):e.jsx("input",{type:"text",value:M,onChange:i=>h(i.target.value),className:"mt-1  w-full rounded-xl border border-gray-300 p-2"}),l.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:l.note}),e.jsx("div",{className:"mt-2",children:e.jsx(he,{loading:u,onClick:()=>P(l.key,M),className:"rounded-xl bg-primaryBlue px-4 py-2  font-medium text-white hover:bg-primaryBlue",children:"Save"})})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:" font-medium text-gray-500",children:l.label}),e.jsx("button",{onClick:()=>{F(l.key),h((n==null?void 0:n[l.key])||"")},className:" text-primaryBlue hover:text-indigo-800",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:(n==null?void 0:n[l.key])||"--"}),l.note&&e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:l.note})]})},l.key))})]})]})})]})};let ts=new ee;function as({getData:b,onClose:w}){const[N,y]=a.useState(!1),k=_e(),E=Ke(),{dispatch:u}=a.useContext(Q),f=xe({user:we(),token:ue()}),{register:n,setValue:I,handleSubmit:H,setError:A,formState:{errors:W}}=me({resolver:pe(f)}),F=async M=>{y(!0),k.createToken(E.getElement(je)).then(async h=>{if(console.log(h),h.error){x(u,h.error||"Something went wrong");return}const j={sourceToken:h.token.id};try{const v=await ts.createCustomerStripeCard(j);if(!v.error)x(u,"Card added successfully");else if(v.validation){const D=Object.keys(v.validation);for(let g=0;g<D.length;g++){const z=D[g];x(u,v.validation[z],3e3)}}b(),w()}catch(v){console.error(v),x(u,v.message,5e3),q(u,v.code)}finally{y(!1)}})};return e.jsx("div",{className:"space-y-6",children:e.jsx("div",{className:"mt-5",children:e.jsxs("form",{className:"",onSubmit:H(F),children:[e.jsx(je,{className:"mb-3 rounded p-4 shadow-inner",options:{hidePostalCode:!0,style:{base:{backgroundColor:"",fontSize:"14px",lineHeight:"20px"}}}}),e.jsx(he,{loading:N,type:"submit",className:"inline-block rounded-lg bg-primaryBlue px-3 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-blue-700 hover:shadow-lg focus:bg-blue-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-blue-800 active:shadow-lg",children:N?"Adding...":"Add card"})]})})})}let de=new ee;function rs(){var G;const[b,w]=a.useState("bank_cards"),[N,y]=a.useState(!1),[k,E]=a.useState(!1),[u,f]=a.useState(""),[n,I]=a.useState(10),[H,A]=a.useState(!1),[W,F]=a.useState(!1),[M,h]=a.useState(!1),[j,v]=O.useState({});_e();const D=xe({user:we(),token:ue()}),{dispatch:g}=O.useContext(Q);me({resolver:pe(D)});const z=[{id:"bank_cards",label:"Bank cards",icon:e.jsx($e,{})}],Z=async s=>{E(!0);try{console.log("Saving card:",s),await new Promise(o=>setTimeout(o,1e3)),y(!1)}catch(o){console.error("Error saving card:",o)}finally{E(!1)}};async function B(s){var o,p;try{h(!0);const{data:c,limit:_,error:L,message:C}=await de.getCustomerStripeCards(s);if(console.log(c),L&&x(g,C,5e3),!c)return;u||f(((o=c==null?void 0:c.data[0])==null?void 0:o.id)??""),v(c),I(+_),A(u&&u!==((p=c.data[0])==null?void 0:p.id)),F(c.has_more)}catch(c){console.error("ERROR",c),x(g,c.message,5e3),q(dispatch,c.code)}finally{h(!1)}}const $=async s=>{h(!0);const{error:o,message:p}=await de.setStripeCustomerDefaultCard(s);if(x(g,p),o){console.error(o);return}B({}),h(!1)},S=async s=>{h(!0);const{isDeleted:o,error:p,message:c}=await de.deleteCustomerStripeCard(s);if(x(g,c),p){console.error(p);return}B({}),h(!1)};return O.useEffect(()=>{B({})},[]),e.jsxs("div",{className:"mx-auto max-w-2xl p-4 sm:p-6",children:[e.jsx("h2",{className:"mb-6 text-2xl font-semibold",children:"Payment methods"}),M&&e.jsx(ne,{}),e.jsx("div",{className:"mb-6 flex flex-wrap gap-4 border-b sm:flex-nowrap sm:gap-0 sm:space-x-4",children:z.map(s=>e.jsxs("button",{onClick:()=>w(s.id),className:`flex items-center space-x-2 px-1 pb-2 ${b===s.id?"border-b-2 border-primaryBlue text-primaryBlue":"text-gray-500 hover:text-gray-700"}`,children:[e.jsx("span",{className:"text-lg",children:s.icon}),e.jsx("span",{children:s.label})]},s.id))}),b==="bank_cards"&&e.jsxs("div",{className:"space-y-4",children:[(G=j==null?void 0:j.data)==null?void 0:G.map(s=>e.jsxs("div",{className:"flex flex-col justify-between gap-3 rounded-xl border p-4",children:[e.jsxs("div",{className:"flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("div",{className:"text-sm text-gray-700 sm:text-base",children:s.customer.email}),e.jsx("div",{className:"flex items-center justify-end space-x-4",children:e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>$(s.id),className:`text-sm sm:text-base ${s.id===s.customer.default_source?"text-green-600":"text-blue-600"} underline`,children:s.id===s.customer.default_source?"Default":"Set Default"}),e.jsx("button",{onClick:()=>S(s.id),className:"text-sm text-gray-500 underline sm:text-base",children:"Delete"})]})})]}),e.jsxs("div",{className:"flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("div",{className:"flex space-x-2",children:[s.brand==="Visa"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#1A1F71] text-white",children:e.jsx(Je,{size:18})}),s.brand==="Mastercard"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#EB001B] text-white",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"2.11676in",height:"1.5in",viewBox:"0 0 152.407 108",children:e.jsxs("g",{children:[e.jsx("rect",{width:"152.407",height:"108",style:"fill: none"}),e.jsxs("g",{children:[e.jsx("rect",{x:"60.4117",y:"25.6968",width:"31.5",height:"56.6064",style:"fill: #ff5f00"}),e.jsx("path",{d:"M382.20839,306a35.9375,35.9375,0,0,1,13.7499-28.3032,36,36,0,1,0,0,56.6064A35.938,35.938,0,0,1,382.20839,306Z",transform:"translate(-319.79649 -252)",style:"fill: #eb001b"}),e.jsx("path",{d:"M454.20349,306a35.99867,35.99867,0,0,1-58.2452,28.3032,36.00518,36.00518,0,0,0,0-56.6064A35.99867,35.99867,0,0,1,454.20349,306Z",transform:"translate(-319.79649 -252)",style:"fill: #f79e1b"}),e.jsx("path",{d:"M450.76889,328.3077v-1.1589h.4673v-.2361h-1.1901v.2361h.4675v1.1589Zm2.3105,0v-1.3973h-.3648l-.41959.9611-.41971-.9611h-.365v1.3973h.2576v-1.054l.3935.9087h.2671l.39351-.911v1.0563Z",transform:"translate(-319.79649 -252)",style:"fill: #f79e1b"})]})]})})}),s.brand==="MasterCard"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded  text-white",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"2.11676in",height:"1.5in",viewBox:"0 0 152.407 108",children:e.jsxs("g",{children:[e.jsx("rect",{width:"152.407",height:"108",style:{fill:"none"}}),e.jsxs("g",{children:[e.jsx("rect",{x:"60.4117",y:"25.6968",width:"31.5",height:"56.6064",style:{fill:"#ff5f00"}}),e.jsx("path",{d:"M382.20839,306a35.9375,35.9375,0,0,1,13.7499-28.3032,36,36,0,1,0,0,56.6064A35.938,35.938,0,0,1,382.20839,306Z",transform:"translate(-319.79649 -252)",style:{fill:"#eb001b"}}),e.jsx("path",{d:"M454.20349,306a35.99867,35.99867,0,0,1-58.2452,28.3032,36.00518,36.00518,0,0,0,0-56.6064A35.99867,35.99867,0,0,1,454.20349,306Z",transform:"translate(-319.79649 -252)",style:{fill:"#f79e1b"}}),e.jsx("path",{d:"M450.76889,328.3077v-1.1589h.4673v-.2361h-1.1901v.2361h.4675v1.1589Zm2.3105,0v-1.3973h-.3648l-.41959.9611-.41971-.9611h-.365v1.3973h.2576v-1.054l.3935.9087h.2671l.39351-.911v1.0563Z",transform:"translate(-319.79649 -252)",style:{fill:"#f79e1b"}})]})]})})}),s.brand==="American Express"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#006FCF] text-white",children:e.jsx(We,{size:18})}),s.brand==="Discover"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#FF6000] text-sm font-bold text-white",children:"DISC"}),s.brand==="Diners Club"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#0069AA] text-white",children:e.jsx(Te,{size:18})}),s.brand==="JCB"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#0B4EA2] text-white",children:e.jsx(Fe,{size:18})}),s.brand==="UnionPay"&&e.jsx("div",{className:"flex h-6 w-9 items-center justify-center rounded bg-[#00447C] text-sm font-bold text-white",children:"UP"})]}),e.jsxs("p",{className:"text-sm text-black sm:text-base",children:[s.brand," • ",s.last4]})]}),e.jsx("div",{children:e.jsxs("p",{className:"text-sm text-gray-500",children:["Exp. ",s.exp_month,"/",s.exp_year.toString().slice(-2)]})})]})]},s.id)),e.jsxs("button",{onClick:()=>y(!0),className:"flex w-full items-center justify-center space-x-2 rounded-lg border border-blue-600 px-4 py-2 text-blue-600 hover:bg-blue-50 sm:w-auto",children:[e.jsx("span",{children:"+"}),e.jsx("span",{children:"Add card"})]})]}),e.jsx(Me,{isOpen:N,onClose:()=>y(!1),title:"Add card",primaryButtonText:"Add card",onPrimaryAction:Z,submitting:k,showFooter:!1,children:e.jsx(as,{onSubmit:Z,getData:B,onClose:()=>y(!1)})})]})}let K=new ee,ls=new Ne;const te=b=>new Date(b*1e3).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"});function ns(){const[b,w]=a.useState({}),[N,y]=a.useState([]),[k,E]=a.useState(10),[u,f]=a.useState(1),[n,I]=a.useState(1),[H,A]=a.useState(0),[W,F]=a.useState(!1),[M,h]=a.useState(!1),[j,v]=a.useState(!1),[D,g]=a.useState(!1),[z,Z]=a.useState([]),[B,$]=a.useState(!1),[S,G]=a.useState({}),[s,o]=a.useState({}),{triggerRefetch:p}=De(),c=ve(),{dispatch:_}=a.useContext(Q),{dispatch:L,state:C}=O.useContext(Se),X=a.useRef(null);if(X.current===null){const t=parseInt(localStorage.getItem("user")),d=C.originalUserId||(isNaN(t)?null:t);d&&(X.current=d)}const P=X.current,U=C.isFamilyMemberSwitch?C.user:P,ie=P;async function V(t,d,T={}){v(!0);try{console.log(C),T.user_id=U;const Y=await K.getCustomerStripeSubscriptions({page:t,limit:100,sort:"asc"},T),{list:Ce,total:Pe,limit:ke,num_pages:fe,page:ce}=Y,ge=[...Ce].sort((se,Ie)=>{const Ae=parseInt(se.createdAt);return parseInt(Ie.createdAt)-Ae}),be={};ge.forEach(se=>{se.status==="active"&&(be[se.subId]=!0)}),w(be),y(ge),E(+ke),f(+fe),I(+ce),A(+Pe),F(+ce>1),h(+ce+1<=+fe)}catch(Y){console.error(Y),q(L,Y.code)}finally{v(!1)}}async function oe(){try{const t=await K.getCustomerStripeSubscription(U);o(t.customer)}catch(t){console.error(t),q(L,t.code)}}const l=async t=>{$(!0);try{const d=await K.cancelStripeSubscription(t);if(d.error){console.error(d.message),x(_,d.message,7500,"error");return}await ae(K,{activity_type:re.user_management,action_type:le.UPDATE,data:{subscription_id:t,action:"cancelled",user_id:U},description:`Cancelled subscription ${t}`}),x(_,d.message,1e4,"success"),V(1,k),g(!1),G({})}catch(d){console.error(d),x(_,d.message,7500,"error"),q(L,d.code)}finally{$(!1)}},i=async()=>{try{if(!P||isNaN(P)){console.error("Invalid mainAccountUserId:",P);return}const t=P,T=(await ls.getList("user",{filter:[`guardian,eq,${t}`,"role,cs,user"]})).list.filter(Y=>Y.id!==P);Z(T)}catch(t){console.error("Error fetching family members:",t)}};a.useEffect(()=>{V(1)},[U]),a.useEffect(()=>{oe()},[U]),a.useEffect(()=>{i()},[]);const r=async t=>{try{if(t===P)return;const d=z.find(Y=>Y.id===t);if(!d){x(_,"Invalid family member selected",7500,"error");return}const T=await K.callRawAPI("/v3/api/custom/courtmatchup/user/groups/switch-family-member",{family_member_id:t},"POST");if(T.error){x(_,T.message||"Failed to switch user",7500,"error");return}await ae(K,{activity_type:re.user_management,action_type:le.UPDATE,data:{action:"switched_family_member",from_user_id:P,to_user_id:t,family_member_name:(d==null?void 0:d.first_name)+" "+(d==null?void 0:d.last_name)},description:`Switched to family member: ${d==null?void 0:d.first_name} ${d==null?void 0:d.last_name}`}),L({type:"SWITCH_TO_FAMILY_MEMBER",payload:{user_id:T.family_member.id,token:T.token,role:"user",first_name:T.family_member.first_name,last_name:T.family_member.last_name,original_user_id:ie,family_member:T.family_member}}),x(_,`Switched to ${T.family_member.first_name}'s account`,3e3,"success")}catch(d){console.error("Error switching user:",d),x(_,d.message||"Failed to switch user",7500,"error")}},m=async()=>{try{if(!C.isFamilyMemberSwitch)return;const t=await K.callRawAPI("/v3/api/custom/courtmatchup/user/data/switch-back",{},"GET");if(t.error){x(_,t.message||"Failed to switch back",7500,"error");return}L({type:"SWITCH_BACK_TO_MAIN",payload:{user_id:t.user_id,token:t.token,role:"user",first_name:t.first_name,last_name:t.last_name}}),x(_,"Switched back to your account",3e3,"success")}catch(t){console.error("Error switching back:",t),x(_,t.message||"Failed to switch back",7500,"error")}},R=t=>{I(t),V(t)};return e.jsxs("div",{className:"mx-auto max-w-3xl p-3 sm:p-6",children:[j&&e.jsx(ne,{}),e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Membership"}),e.jsxs("div",{className:"flex flex-col gap-2 sm:flex-row",children:[e.jsx(ye,{to:"/user/membership/buy",className:"w-full rounded-lg bg-gray-600 px-4 py-2 text-center text-white transition-colors hover:bg-gray-700 sm:w-auto",children:"Browse Plans"}),e.jsx("button",{onClick:()=>{const t=new URLSearchParams;C.isFamilyMemberSwitch&&(t.set("familyMemberId",U),C.familyMemberDetails&&t.set("familyMemberName",C.familyMemberDetails.first_name)),c(`/user/membership/buy?${t.toString()}`)},className:"w-full rounded-lg bg-blue-600 px-4 py-2 text-center text-white transition-colors hover:bg-blue-700 sm:w-auto",children:(()=>C.isFamilyMemberSwitch&&C.familyMemberDetails?`Buy plan for ${C.familyMemberDetails.first_name}`:"Buy new plan")()})]})]}),e.jsx("div",{className:"mb-4 border-b border-gray-200"}),e.jsx("div",{className:"relative mb-4 flex justify-end",children:e.jsxs("div",{className:"mb-4",children:[e.jsx("div",{className:"relative",children:e.jsxs("select",{value:U,onChange:t=>{const d=parseInt(t.target.value);d!==U&&(d===P?m():r(d))},className:"w-full appearance-none rounded-lg border border-gray-300 bg-white px-3 py-2 pr-8 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",children:[e.jsx("option",{value:P,children:"Myself"}),z.map(t=>e.jsxs("option",{value:t.id,children:[t.first_name," ",t.last_name," (",t.family_role||"Family Member",")"]},t.id))]})}),e.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"Select the user whose membership you want to manage"})]})}),!j&&N.length===0&&e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-xl bg-gray-50 px-4 py-12 text-center",children:[e.jsx("div",{className:"mb-4 rounded-full bg-blue-100 p-3",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Active Memberships"}),e.jsx("p",{className:"mb-6 text-sm text-gray-600",children:"You currently don't have any active membership plans."}),e.jsx(ye,{to:"/user/membership/buy",className:"rounded-lg bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700",children:"Browse Plans"})]}),N.map(t=>e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50",children:[e.jsxs("button",{onClick:()=>{w(d=>({...d,[t.subId]:!d[t.subId]}))},className:"flex w-full cursor-pointer flex-col gap-2 p-4 hover:bg-gray-100 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[b[t.subId]?e.jsx(Be,{size:20}):e.jsx(Le,{size:20}),e.jsxs("span",{className:"text-sm font-medium sm:text-base",children:[te(t.currentPeriodStart)," -"," ",te(t.currentPeriodEnd)]})]}),e.jsxs("div",{className:"flex items-center justify-between gap-3 pl-7 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600 sm:text-base",children:t.planName}),e.jsx("span",{className:`rounded-full px-3 py-1 text-sm capitalize ${t.status==="active"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:t.status})]})]}),b[t.subId]&&e.jsx("div",{className:"p-4",children:e.jsxs("div",{className:"space-y-6 rounded-xl border bg-white p-4",children:[e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row sm:items-center",children:[e.jsx("span",{className:"text-gray-600",children:"Plan price"}),e.jsxs("div",{className:"text-left sm:text-right",children:[e.jsx("div",{className:"font-semibold",children:Ue(t.planAmount)}),e.jsxs("div",{className:"text-sm text-gray-500",children:["Billed"," ",t.billing_interval_count>1?`every ${t.billing_interval_count} ${(t==null?void 0:t.billing_interval)||"month"}s`:(t==null?void 0:t.billing_interval)||"monthly"]})]})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Purchased on"}),e.jsx("span",{className:"text-sm sm:text-base",children:te(t.createdAt)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{className:"text-sm sm:text-base",children:te(t.currentPeriodEnd)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Subscription ID"}),e.jsx("span",{className:"break-all text-sm sm:text-base",children:t.subId})]}),t.status==="active"&&e.jsx("div",{className:"flex justify-center sm:justify-start",children:e.jsx("button",{onClick:()=>{g(!0),G(t)},className:"w-full rounded-xl bg-red-500 px-5 py-2 text-white transition-colors hover:bg-red-600 sm:w-auto",children:"Cancel plan"})})]})})]},t.subId)),e.jsxs("div",{className:`fixed inset-0 z-50 flex items-center justify-center p-4 ${D?"":"hidden"}`,children:[e.jsx("div",{className:"fixed inset-0 bg-black opacity-50"}),e.jsxs("div",{className:"relative z-50 w-full max-w-lg rounded-3xl bg-white p-4 sm:p-6",children:[e.jsxs("div",{className:"flex flex-col gap-4 sm:flex-row",children:[e.jsx("div",{children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#FDEDF0"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1 22.7V24.5H20.9V22.7H19.1ZM19.1 15.5V20.9H20.9V15.5H19.1Z",fill:"#DF1C41"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-xl font-medium",children:"Are you sure?"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Are you sure you want to cancel membership?"})]})]}),e.jsxs("div",{className:"flex flex-col-reverse gap-3 border-t pt-4 sm:flex-row sm:justify-end",children:[e.jsx("button",{onClick:()=>{g(!1),G({})},className:"w-full rounded-lg border border-gray-300 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:"Go back"}),e.jsx(he,{onClick:()=>{l(S.subId),p()},className:"w-full rounded-lg bg-red-600 px-4 py-2 text-white hover:bg-red-700 sm:w-auto",loading:B,children:"Yes, cancel"})]})]})]}),e.jsx(Qe,{currentPage:n,pageSize:k,pageCount:u,canPreviousPage:W,canNextPage:M,updatePageSize:E,previousPage:()=>R(n-1),nextPage:()=>R(n+1),gotoPage:R})]})}let is=new ee;function os(){const{dispatch:b}=O.useContext(Q),[w,N]=a.useState({}),[y,k]=a.useState([]);a.useState(0),a.useState(!1),a.useState(!1);const[E,u]=a.useState(""),[f,n]=a.useState(""),[I,H]=a.useState(""),[A,W]=a.useState("desc"),[F,M]=a.useState(!1),[h,j]=a.useState(null),v=a.useRef({}),D=s=>{N(o=>({...o,[s]:!o[s]}))},g=async(s={})=>{M(!0);try{let o=new URLSearchParams;s.sort&&o.append("sort",s.sort),s.invoice_type&&o.append("invoice_type",s.invoice_type),s.search&&o.append("search",s.search);const p=await is.callRawAPI(`/v3/api/custom/courtmatchup/user/billing/invoices${o.toString()?`?${o.toString()}`:""}`,{},"GET");if(p.error){x(b,p.message,5e3);return}k(p.invoices||[])}catch(o){console.error("ERROR",o),x(b,o.message,5e3),q(dispatch,o.message)}finally{M(!1)}},z=s=>{u(s.target.value),g({sort:A})},Z=()=>!f&&!I?y:y.filter(s=>{try{const o=new Date(s.date);if(isNaN(o.getTime()))return!1;const p=f?new Date(f):null,c=I?new Date(I):null;return p&&c?o>=p&&o<=c:p?o>=p:c?o<=c:!0}catch{return console.error("Invalid date:",s.date),!1}}),B=()=>{const s=A==="asc"?"desc":"asc";W(s),g({sort:s})};a.useEffect(()=>{g({sort:A})},[]);const $=s=>new Date(s).toLocaleDateString(),S=s=>Number(s).toLocaleString("en-US",{style:"currency",currency:"usd"}),G=s=>{j(s);const o=window.open("","_blank");if(!o){x(b,"Please allow pop-ups to print receipts",5e3);return}if(!v.current[s]){x(b,"Receipt content not found",5e3);return}const c=y.find(_=>_.id===s);o.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Receipt #${c.receipt_id}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
            }
            .receipt-header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 1px solid #eee;
              padding-bottom: 20px;
            }
            .receipt-title {
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .receipt-id {
              font-size: 16px;
              color: #666;
            }
            .receipt-date {
              font-size: 14px;
              color: #666;
              margin-top: 5px;
            }
            .receipt-body {
              margin-bottom: 30px;
            }
            .receipt-row {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;
              padding: 5px 0;
              border-bottom: 1px solid #f5f5f5;
            }
            .receipt-label {
              font-weight: 500;
              color: #666;
            }
            .receipt-total {
              margin-top: 20px;
              font-size: 18px;
              font-weight: bold;
              border-top: 2px solid #eee;
              padding-top: 10px;
            }
            .receipt-footer {
              margin-top: 40px;
              text-align: center;
              font-size: 14px;
              color: #999;
            }
            @media print {
              body {
                padding: 0;
                margin: 0;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <div class="receipt-header">
            <div class="receipt-title">Payment Receipt</div>
            <div class="receipt-id">Invoice #${c.receipt_id}</div>
            <div class="receipt-date">Date: ${$(c.date)}</div>
          </div>

          <div class="receipt-body">
            <div class="receipt-row">
              <span class="receipt-label">Type:</span>
              <span>${c.type}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Amount:</span>
              <span>${S(c.amount)}</span>
            </div>
            ${c.coach_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Coach Fee:</span>
              <span>${S(c.coach_fee)}</span>
            </div>
            `:""}
            ${c.service_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Service Fee:</span>
              <span>${S(c.service_fee)}</span>
            </div>
            `:""}
            ${c.club_fee>0?`
            <div class="receipt-row">
              <span class="receipt-label">Club Fee:</span>
              <span>${S(c.club_fee)}</span>
            </div>
            `:""}
            <div class="receipt-row">
              <span class="receipt-label">Valid Until:</span>
              <span>${$(c.valid_until)}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Payment Method:</span>
              <span>${c.payment_method}</span>
            </div>
            <div class="receipt-row">
              <span class="receipt-label">Status:</span>
              <span class="capitalize">${c.status}</span>
            </div>

            <div class="receipt-total">
              <div class="receipt-row">
                <span class="receipt-label">Total:</span>
                <span>${S(c.total_amount)}</span>
              </div>
            </div>
          </div>

          <div class="receipt-footer">
            Thank you for your payment.
          </div>

          <script>
            window.onload = function() {
              window.print();
              setTimeout(function() {
                window.close();
              }, 500);
            };
          <\/script>
        </body>
      </html>
    `),o.document.close(),setTimeout(()=>{j(null)},1e3)};return e.jsxs("div",{className:"mx-auto max-w-3xl p-4 sm:p-6",children:[F&&e.jsx(ne,{}),e.jsxs("div",{className:"mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Billing"}),e.jsxs("select",{className:"w-full rounded-lg border-gray-200 px-4 py-2 capitalize sm:w-auto",onChange:s=>g({invoice_type:s.target.value,sort:A}),children:[e.jsx("option",{value:"",children:"All bills"}),e.jsx("option",{value:"subscription",children:"Subscription"}),e.jsx("option",{value:"lesson",children:"Lesson"}),e.jsx("option",{value:"clinic",children:"Clinic"}),e.jsx("option",{value:"club_court",children:"Club Court"})]})]}),e.jsx("div",{className:"mb-5 border-b border-gray-200"}),e.jsxs("div",{className:"mb-6 flex flex-col gap-3 sm:flex-row",children:[e.jsxs("div",{className:"relative flex-1",children:[e.jsx(Re,{className:"absolute left-3 top-1/2 -translate-y-1/2 transform text-gray-400"}),e.jsx("input",{type:"text",value:E,onChange:z,placeholder:"Search by plan name or invoice number",className:"w-full rounded-lg border border-gray-200 py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),e.jsxs("div",{className:"flex w-full gap-2 sm:w-auto",children:[e.jsx("input",{type:"date",value:f,onChange:s=>n(s.target.value),placeholder:"Start date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),e.jsx("input",{type:"date",value:I,onChange:s=>H(s.target.value),placeholder:"End date",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-sm sm:w-auto"}),(f||I)&&e.jsx("button",{onClick:()=>{n(""),H("")},className:"rounded-lg border border-gray-200 px-3 py-2 text-sm text-gray-500 hover:bg-gray-50 hover:text-gray-700",title:"Clear dates",children:"✕"})]}),e.jsxs("button",{onClick:B,className:"flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 px-4 py-2 hover:bg-gray-50 sm:w-auto",children:[A==="asc"?"Oldest first":"Latest first",e.jsx(Xe,{className:`transform ${A==="desc"?"rotate-180":""}`})]})]}),e.jsx("div",{className:"space-y-4",children:Z().length===0?e.jsx("div",{className:"py-8 text-center text-gray-500",children:"No billing records found"}):Z().map(s=>e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-3 shadow-sm",children:[e.jsxs("button",{onClick:()=>D(s.id),className:"flex w-full flex-col gap-2 sm:flex-row sm:items-center sm:justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ze,{className:`transform transition-transform ${w[s.id]?"":"-rotate-90"}`}),e.jsx("span",{className:"text-sm sm:text-base",children:s.type})]}),e.jsxs("div",{className:"flex items-center justify-between gap-4 pl-6 sm:pl-0",children:[e.jsx("span",{className:"text-sm text-gray-600",children:$(s.create_at)}),e.jsx("span",{className:"font-medium",children:S(s.total_amount)})]})]}),w[s.id]&&e.jsx("div",{className:"mt-2 rounded-lg bg-white p-4",children:e.jsxs("div",{className:"space-y-4",ref:o=>v.current[s.id]=o,children:[e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Amount"}),e.jsx("span",{children:S(s.amount)})]}),s.coach_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Coach fee"}),e.jsx("span",{children:S(s.coach_fee)})]}),s.service_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Service fee"}),e.jsx("span",{children:S(s.service_fee)})]}),s.club_fee>0&&e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Club fee"}),e.jsx("span",{children:S(s.club_fee)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Invoice ID"}),e.jsxs("span",{children:["#",s.receipt_id]})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Date"}),e.jsx("span",{children:$(s.date)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Valid until"}),e.jsx("span",{children:$(s.valid_until)})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Payment method"}),e.jsx("span",{children:s.payment_method})]}),e.jsxs("div",{className:"flex flex-col justify-between gap-1 sm:flex-row",children:[e.jsx("span",{className:"text-gray-600",children:"Status"}),e.jsx("span",{className:"capitalize",children:s.status})]}),e.jsxs("button",{onClick:o=>{o.stopPropagation(),G(s.id)},className:"mt-4 flex w-full items-center justify-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50",children:[e.jsx(Oe,{className:"text-lg"}),"Print Receipt"]})]})})]},s.id))})]})}const st=()=>{const{dispatch:b}=O.useContext(Q),[w]=Ee(),[N,y]=a.useState("profile"),k=ve();a.useEffect(()=>{const u=w.get("tab");u&&y({"payment-methods":"payment-methods",profile:"profile",membership:"membership",billing:"billing"}[u]||"profile")},[w.get("tab")]);const E=[{label:"Profile details",value:"profile",icon:Ze},{label:"Payment methods",value:"payment-methods",icon:Ye},{label:"Membership",value:"membership",icon:qe},{label:"Billing",value:"billing",icon:Ge}];return a.useEffect(()=>{b({type:"SETPATH",payload:{path:"profile"}})},[]),e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"mx-auto  max-w-6xl px-4",children:[e.jsx(Ve,{onBack:()=>k("/user/dashboard")}),e.jsxs("div",{className:"flex flex-col gap-8 md:flex-row",children:[e.jsx("div",{className:"max-h-fit w-full rounded-xl bg-white shadow-sm md:max-w-xs",children:e.jsx("nav",{className:"no-scrollbar flex w-full flex-row overflow-x-auto p-4 md:flex-col",children:E.map(u=>{const f=u.icon;return e.jsxs("button",{onClick:()=>{y(u.value),k(`/user/profile?tab=${u.value}`)},className:`mr-2 flex min-w-fit items-center whitespace-nowrap rounded-lg px-4 py-3 transition-colors md:mb-2 md:mr-0 ${N===u.value?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50"}`,children:[e.jsx(f,{className:"mr-2 h-5 w-5 text-gray-500"}),e.jsx("span",{className:"text-sm",children:u.label})]},u.value)})})}),e.jsxs("div",{className:" w-full rounded-xl bg-white p-6 shadow-sm",children:[N==="profile"&&e.jsx(ss,{}),N==="payment-methods"&&e.jsx(rs,{}),N==="membership"&&e.jsx(ns,{}),N==="billing"&&e.jsx(os,{})]})]})]})})};export{st as default};
