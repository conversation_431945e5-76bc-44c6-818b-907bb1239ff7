import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{f as b,j as N,r as l,k as g}from"./vendor-851db8c1.js";import{g as w}from"./index.esm-3f8dc7b8.js";import{u as v}from"./@tanstack/react-query-20158223.js";import{G as _,A as C,u as E,N as P,h as k,ah as h,W as i,M}from"./index-08a5dc5b.js";import{f as S}from"./date-fns-cca0f4f7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-icons-51bc3cff.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";let n=new M;function pe(){var o,p,x;const u=b(),{reservation_id:m}=N();l.useContext(_),l.useContext(C);const[j]=g(),c=j.get("type"),{club:t,sports:T,loading:f}=E(),{data:s,isPending:y}=v({queryKey:["reservation",m],queryFn:async()=>{const a=await n.callRawAPI(`/v3/api/custom/courtmatchup/user/reservations/${c}/${m}`,{},"GET");n.setTable("user");const r=await n.callRestAPI({id:a.reservation.booking_user_id},"GET");return console.log(a),{...a.reservation,user:r.model}}}),d={date:"August 29, 2024",time:"10:30 AM - 110:30 M",coach:"Pete Sampras",players:["Me","Jonathan Smith"],fees:{clubFee:20,coachFee:60,serviceFee:12.5,total:92.5},receipt:"1234567890",paymentMethod:"Card •0089",userEmail:"{user/email}"};return l.useEffect(()=>{P({path:`/user/reserve-court?type=${c}`,clubName:t==null?void 0:t.name,favicon:t==null?void 0:t.club_logo,description:"Reservation successful"})},[t==null?void 0:t.club_logo,c]),console.log(s),e.jsxs("div",{className:"min-h-screen ",children:[e.jsx("div",{className:"flex items-center justify-center bg-white p-4 shadow-sm",children:e.jsx("p",{children:"You're all set!"})}),y||f?e.jsx(k,{}):e.jsx("div",{className:"h-full py-8",children:e.jsxs("div",{className:"mx-auto max-w-md space-y-6 rounded-xl bg-white p-6 shadow-sm",children:[e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(w,{className:"h-5 w-5 text-green-500"}),e.jsx("p",{className:"!mb-0 !mt-0 text-lg font-medium",children:"Confirmation"})]}),e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-sm font-medium uppercase text-gray-500",children:"DETAILS"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{children:[e.jsx("span",{children:"Reserving"}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{children:s!=null&&s.booking_date?S(new Date(s.booking_date),"MMM d, yyyy"):""}),e.jsxs("div",{children:[h(s==null?void 0:s.start_time)," -"," ",h(s==null?void 0:s.end_time)]})]})]}),(s==null?void 0:s.coach_id)&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Coach"}),e.jsx("span",{children:s.coach.first_name})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Players"}),e.jsx("div",{className:"text-right",children:(o=s==null?void 0:s.players)==null?void 0:o.map((a,r)=>e.jsxs("div",{className:"capitalize",children:[a.first_name," ",a.last_name]},r))})]})]})]}),e.jsxs("div",{className:"border-t pt-3",children:[e.jsx("h2",{className:"mb-4 text-sm font-medium uppercase text-gray-500",children:"FEES"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Club fee"}),e.jsx("span",{children:i(Number(s==null?void 0:s.club_fee))})]}),(s==null?void 0:s.coach_id)&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Coach fee"}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{children:i(Number(s==null?void 0:s.coach_fee))}),e.jsxs("div",{className:"text-sm text-gray-500",children:[i(Number(s==null?void 0:s.club_fee))," ",e.jsxs("span",{className:"text-xs",children:["x ",(p=s==null?void 0:s.players)==null?void 0:p.length]})]})]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Service fee"}),e.jsx("span",{children:i(Number(s==null?void 0:s.service_fee))})]}),e.jsxs("div",{className:"flex justify-between border-b border-t pb-3 pt-3",children:[e.jsx("span",{className:"font-medium",children:"Total"}),e.jsx("span",{className:"font-medium",children:i(Number(s==null?void 0:s.club_fee)+Number(s==null?void 0:s.service_fee))})]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Receipt confirmation"}),e.jsxs("span",{children:["#",d.receipt]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"Payment method"}),e.jsx("span",{children:d.paymentMethod})]})]}),e.jsxs("p",{className:"text-center text-sm text-gray-500",children:["Confirmation has been sent to ",(x=s==null?void 0:s.user)==null?void 0:x.email]}),e.jsxs("div",{className:"flex flex-col gap-3",children:[e.jsx("button",{onClick:()=>u("/user/dashboard"),className:"w-full rounded-lg bg-[#1E2841] px-4 py-3 text-white transition hover:bg-[#1E2841]/90",children:"Back to Home"}),e.jsx("button",{onClick:()=>window.print(),className:"w-full rounded-lg border border-gray-300 px-4 py-3 text-gray-700 transition hover:bg-gray-50",children:"Print receipt"})]})]})]})})]})}export{pe as default};
