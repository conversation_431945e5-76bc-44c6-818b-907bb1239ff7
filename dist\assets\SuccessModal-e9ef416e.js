import{j as e}from"./@nivo/heatmap-ba1ecfff.js";function r({title:s,description:i,onContinue:l}){return e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsx("div",{className:"w-fit max-w-xl rounded-2xl bg-white ",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex  items-start gap-4 p-5",children:[e.jsx("div",{className:"",children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-3 text-xl font-medium",children:s}),e.jsx("p",{className:"mb-3  text-gray-600",children:i})]})]}),e.jsx("div",{className:"flex w-full justify-end border-t border-gray-200 p-4",children:e.jsx("button",{onClick:l,className:"w-fit rounded-xl bg-primaryGreen px-5 py-3 text-white",children:"Continue"})})]})})})}export{r as S};
