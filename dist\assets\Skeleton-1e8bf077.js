import{j as o}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import{S as f}from"./react-loading-skeleton-3d87d1f5.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const j=({className:m="",count:e=5,counts:t=[2,1,3,1,1],circle:l=!1})=>o.jsx("div",{className:`flex h-fit max-h-screen min-h-fit w-full flex-col gap-5 overflow-hidden p-4 ${m} `,children:Array.from({length:e}).map((a,r)=>o.jsx(f,{count:t[r]??1,height:t[r]&&t[r]>1||r+1===e?25:80,circle:l,style:{marginBottom:"0.6rem"}},`${a}${r}`))});export{j as default};
