import{j as r}from"./@nivo/heatmap-ba1ecfff.js";import{b as t}from"./vendor-851db8c1.js";const i=t.memo(({label:a,selected:e,onClick:o,subType:s})=>r.jsxs("div",{onClick:o,className:`rounded-xl border p-3 ${e?"border-[1.5px] border-primaryBlue":"border-[1.5px] border-gray-200"} mb-2 flex cursor-pointer items-center justify-between hover:bg-gray-50`,children:[r.jsx("span",{className:"text-sm",children:a}),r.jsx("span",{children:s&&r.jsx("span",{className:"text-xs text-gray-500",children:s})}),r.jsx("div",{className:`flex h-5 w-5 items-center justify-center rounded-full border-2 ${e?"border-primaryBlue bg-primaryBlue":"border-gray-300"}`,children:e&&r.jsx("div",{className:"h-2 w-2 rounded-full bg-white"})})]}));i.displayName="SelectionOption";export{i as S};
