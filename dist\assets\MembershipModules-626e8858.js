import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as oe}from"./index.esm-51ae62c8.js";import{r,b as re}from"./vendor-851db8c1.js";import{u as ce,f as de,G as ue,W as be,af as S,R as xe,D as me,M as ge,b as ne,c as ie,d as M}from"./index-08a5dc5b.js";import{b as $}from"./@headlessui/react-a5400090.js";import{C as pe}from"./ChevronRightIcon-efb4c46c.js";import{T as he}from"./TrashIcon-7d213648.js";function _e({title:h,titleId:d,...c},u){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:u,"aria-labelledby":d},c),h?r.createElement("title",{id:d},h):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))}const ye=r.forwardRef(_e),D=ye;function ve({onSubmit:h,onClose:d,initialData:c,mode:u,onDelete:E}){var v,w;const{sports:b}=ce(),[y,o]=r.useState({name:u==="create",price:u==="create",billing:u==="create",features:new Set});console.log("currentMode",u);const[T,j]=r.useState(!1),I=[{value:"day",label:"Daily"},{value:"week",label:"Weekly"},{value:"month",label:"Monthly"},{value:"year",label:"Yearly"}],N=c?{...c,billing_interval:c.billing_interval||"month",billing_interval_count:c.billing_interval_count||1,advance_booking_days:c.advance_booking_days||{court:10,lesson:10,clinic:10,buddy:10},advance_booking_enabled:c.advance_booking_enabled||{court:!0,lesson:!0,clinic:!0,buddy:!0},applicable_sports:c.applicable_sports||[]}:{plan_id:null,plan_name:"",price:0,billing_interval:"month",billing_interval_count:1,allow_clinic:!1,allow_buddy:!1,allow_coach:!1,allow_groups:!1,allow_court:!1,features:[],advance_booking_days:{court:10,lesson:10,clinic:10,buddy:10},advance_booking_enabled:{court:!0,lesson:!0,clinic:!0,buddy:!0},applicable_sports:[]},[t,i]=r.useState(N),R=async()=>{j(!0);const s={...t,advance_booking_enabled:{court:!!t.advance_booking_enabled.court,lesson:!!t.advance_booking_enabled.lesson,clinic:!!t.advance_booking_enabled.clinic,buddy:!!t.advance_booking_enabled.buddy}};console.log("Submitting plan data:",s),await h(s,u),j(!1)},B=(s,a)=>{i(l=>({...l,features:l.features.map(n=>n.id===s?{...n,text:a}:n)})),o(l=>{const n=new Set(l.features);return n.delete(s),{...l,features:n}})},m=s=>{i(a=>({...a,features:a.features.filter(l=>l.id!==s)}))},k=()=>{const s=Math.max(...t.features.map(a=>a.id),0)+1;i(a=>({...a,features:[...a.features,{id:s,text:""}]})),o(a=>({...a,features:new Set([...a.features,s])}))},F={allow_court:"Court booking",allow_clinic:"Clinics",allow_coach:"Lesson",allow_buddy:"Find a Buddy",allow_groups:"My Groups"},g=(s,a)=>{i(l=>({...l,[s]:a})),o(l=>({...l,[s]:!1}))},O=s=>{i(a=>({...a,[s]:!a[s]}))},L=s=>{i(a=>{var l;return{...a,applicable_sports:(l=a.applicable_sports)!=null&&l.includes(s)?a.applicable_sports.filter(n=>n!==s):[...a.applicable_sports||[],s]}})},q=()=>{const a=((b==null?void 0:b.filter(l=>l.status===1))||[]).map(l=>l.id);i(l=>({...l,applicable_sports:a}))},W=()=>{i(s=>({...s,applicable_sports:[]}))},G=()=>{const s=t.billing_interval_count||1,a=t.billing_interval||"month";return s===1?{day:"Daily",week:"Weekly",month:"Monthly",year:"Yearly"}[a]||"Monthly":{day:`Every ${s} days`,week:`Every ${s} weeks`,month:`Every ${s} months`,year:`Every ${s} years`}[a]||`Every ${s} months`};return e.jsxs("div",{className:"flex h-full flex-col gap-4",children:[e.jsxs("div",{className:"flex-1 space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Plan name"}),e.jsx("button",{className:"text-sm text-blue-600",onClick:()=>o(s=>({...s,name:!0})),children:"Edit"})]}),y.name?e.jsx("input",{type:"text",value:t.plan_name,onChange:s=>i(a=>({...a,plan_name:s.target.value})),onBlur:()=>g("plan_name",t.plan_name),className:"w-full rounded-md border border-gray-300 px-3 py-2",autoFocus:!0}):e.jsx("div",{children:t==null?void 0:t.plan_name})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Price"}),e.jsx("button",{className:"text-sm text-blue-600",onClick:()=>o(s=>({...s,price:!0})),children:"Edit"})]}),y.price?e.jsx("input",{type:"number",value:t.price,onChange:s=>i(a=>({...a,price:parseFloat(s.target.value)})),onBlur:()=>g("price",t.price),className:"w-full rounded-md border border-gray-300 px-3 py-2",step:"0.01",autoFocus:!0}):e.jsx("div",{children:t.price===0?e.jsx("span",{className:"font-semibold text-green-600",children:"Free"}):`$${(v=t.price)==null?void 0:v.toFixed(2)}`})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Billing frequency"}),e.jsx("button",{className:"text-sm text-blue-600",onClick:()=>o(s=>({...s,billing:!0})),children:"Edit"})]}),y.billing?e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-600",children:"Interval"}),e.jsx("select",{value:t.billing_interval,onChange:s=>i(a=>({...a,billing_interval:s.target.value})),className:"w-full rounded-md border border-gray-300 px-3 py-2",children:I.map(s=>e.jsx("option",{value:s.value,children:s.label},s.value))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-600",children:"Count"}),e.jsx("input",{type:"number",min:"1",value:t.billing_interval_count,onChange:s=>i(a=>({...a,billing_interval_count:parseInt(s.target.value)||1})),className:"w-full rounded-md border border-gray-300 px-3 py-2"})]}),e.jsx("button",{className:"text-sm text-blue-600",onClick:()=>o(s=>({...s,billing:!1})),children:"Done"})]}):e.jsx("div",{className:"text-sm text-gray-700",children:G()})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Module"}),e.jsx("div",{className:"space-y-4",children:Object.entries(F).map(([s,a])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:a}),e.jsx($,{checked:t[s],onChange:()=>O(s),className:`${t[s]?"bg-blue-500":"bg-gray-200"} relative inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${t[s]?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]},s))})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Applicable Sports"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",onClick:q,className:"text-xs text-blue-600 hover:text-blue-800",children:"Select All"}),e.jsx("span",{className:"text-xs text-gray-400",children:"|"}),e.jsx("button",{type:"button",onClick:W,className:"text-xs text-blue-600 hover:text-blue-800",children:"Clear All"})]})]}),e.jsx("div",{className:"space-y-3",children:(b==null?void 0:b.filter(s=>s.status===1).length)>0?b.filter(s=>s.status===1).map(s=>{var a;return e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("input",{type:"checkbox",id:`sport-${s.id}`,checked:((a=t.applicable_sports)==null?void 0:a.includes(s.id))||!1,onChange:()=>L(s.id),className:"h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),e.jsx("label",{htmlFor:`sport-${s.id}`,className:"cursor-pointer text-sm font-medium text-gray-700",children:s.name})]})},s.id)}):e.jsx("div",{className:"text-sm italic text-gray-500",children:"No active sports available. Please add sports in your club settings first."})}),((w=t.applicable_sports)==null?void 0:w.length)===0&&e.jsx("div",{className:"rounded-md bg-amber-50 p-2 text-xs text-amber-600",children:"⚠️ No sports selected. This membership will not apply to any specific sports."})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Advance Booking Days"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Court Reservation"}),e.jsx($,{checked:t.advance_booking_enabled.court,onChange:()=>{const s=!t.advance_booking_enabled.court;console.log("Toggling court booking enabled:",s),i(a=>({...a,advance_booking_enabled:{...a.advance_booking_enabled,court:s}}))},className:`${t.advance_booking_enabled.court?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${t.advance_booking_enabled.court?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),t.advance_booking_enabled.court&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:t.advance_booking_days.court,onChange:s=>i(a=>({...a,advance_booking_days:{...a.advance_booking_days,court:parseInt(s.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Lesson Booking"}),e.jsx($,{checked:t.advance_booking_enabled.lesson,onChange:()=>{const s=!t.advance_booking_enabled.lesson;console.log("Toggling lesson booking enabled:",s),i(a=>({...a,advance_booking_enabled:{...a.advance_booking_enabled,lesson:s}}))},className:`${t.advance_booking_enabled.lesson?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${t.advance_booking_enabled.lesson?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),t.advance_booking_enabled.lesson&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:t.advance_booking_days.lesson,onChange:s=>i(a=>({...a,advance_booking_days:{...a.advance_booking_days,lesson:parseInt(s.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Clinic/Program Booking"}),e.jsx($,{checked:t.advance_booking_enabled.clinic,onChange:()=>{const s=!t.advance_booking_enabled.clinic;console.log("Toggling clinic booking enabled:",s),i(a=>({...a,advance_booking_enabled:{...a.advance_booking_enabled,clinic:s}}))},className:`${t.advance_booking_enabled.clinic?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${t.advance_booking_enabled.clinic?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),t.advance_booking_enabled.clinic&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:t.advance_booking_days.clinic,onChange:s=>i(a=>({...a,advance_booking_days:{...a.advance_booking_days,clinic:parseInt(s.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{children:"Find-a-Buddy Booking"}),e.jsx($,{checked:t.advance_booking_enabled.buddy,onChange:()=>{const s=!t.advance_booking_enabled.buddy;console.log("Toggling buddy booking enabled:",s),i(a=>({...a,advance_booking_enabled:{...a.advance_booking_enabled,buddy:s}}))},className:`${t.advance_booking_enabled.buddy?"bg-blue-500":"bg-gray-200"} relative mr-2 inline-flex h-6 w-11 items-center rounded-full transition-colors`,children:e.jsx("span",{className:`${t.advance_booking_enabled.buddy?"translate-x-6":"translate-x-1"} inline-block h-4 w-4 transform rounded-full bg-white transition`})})]}),t.advance_booking_enabled.buddy&&e.jsxs("div",{className:"flex items-center justify-between pl-4",children:[e.jsx("div",{className:"text-sm text-gray-500",children:"Days in advance:"}),e.jsx("input",{type:"number",min:"1",max:"365",value:t.advance_booking_days.buddy,onChange:s=>i(a=>({...a,advance_booking_days:{...a.advance_booking_days,buddy:parseInt(s.target.value)||1}})),className:"w-20 rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",required:!0})]})]})]})]}),e.jsxs("div",{className:"space-y-4 pt-4",children:[e.jsx("div",{className:"text-sm text-gray-600",children:"Plan features"}),e.jsxs("div",{className:"space-y-4",children:[t.features.map(s=>e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"text-sm text-gray-600",children:["Feature ",s.id]}),e.jsxs("div",{className:"space-x-4",children:[e.jsx("button",{className:"text-sm text-red-600",onClick:()=>m(s.id),children:"Delete"}),e.jsx("button",{className:"text-sm text-blue-600",onClick:()=>o(a=>({...a,features:new Set([...a.features,s.id])})),children:"Edit"})]})]}),y.features.has(s.id)?e.jsx("input",{type:"text",value:s.text,onChange:a=>i(l=>({...l,features:l.features.map(n=>n.id===s.id?{...n,text:a.target.value}:n)})),onBlur:()=>B(s.id,s.text),className:"w-full rounded-md border border-gray-300 px-3 py-2",autoFocus:!0}):e.jsx("div",{children:s.text})]},s.id)),e.jsx("button",{className:"text-sm text-blue-600",onClick:k,children:"+ Add feature"})]})]})]}),e.jsxs("div",{className:"flex  flex-shrink-0 justify-between gap-4 border-t border-gray-200 px-4 py-4",children:[e.jsx("div",{className:"flex gap-2",children:u==="edit"&&E&&e.jsx("button",{type:"button",className:"rounded-xl border border-red-200 bg-red-50 px-3 py-2 text-sm font-semibold text-red-600 hover:bg-red-100",onClick:()=>E(c),children:"Delete Plan"})}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{type:"button",className:"rounded-xl border border-gray-200 px-3 py-2 text-sm font-semibold text-gray-900 hover:bg-gray-50",onClick:d,children:"Cancel"}),e.jsx(de,{loading:T,type:"submit",className:"rounded-xl bg-primaryBlue px-3 py-2 text-sm font-semibold text-white hover:bg-blue-700",onClick:R,children:u=="edit"?"Save changes":"Create plan"})]})]})]})}let p=new ge;function Me({fetchProfileSettings:h,membershipPlans:d,profileSettings:c,role:u}){const[E,b]=r.useState(null),[y,o]=r.useState(!1),[T,j]=r.useState(""),[I,N]=r.useState(!1),[t,i]=r.useState(null),[R,B]=r.useState(!1),{club:m,sports:k}=ce(),F=localStorage.getItem("user"),{dispatch:g,state:O}=re.useContext(ue),L=a=>{if(!a||a.length===0)return"All Sports";if(!k||k.length===0)return"Loading...";const l=a.map(n=>{var x;return(x=k.find(f=>f.id===n))==null?void 0:x.name}).filter(Boolean);return l.length===0?"No Sports":l.length>2?`${l.slice(0,2).join(", ")} +${l.length-2} more`:l.join(", ")},q=(a,l=1)=>{if(!a)return"Monthly";const n=l||1;return{day:n===1?"Daily":`Every ${n} days`,week:n===1?"Weekly":`Every ${n} weeks`,month:n===1?"Monthly":`Every ${n} months`,year:n===1?"Yearly":`Every ${n} years`}[a]||"Monthly"},W=a=>{b(a),o(!0)},G=async(a,l)=>{var n,x,f,V,J,U,Y,K,X,z,H;try{let _;if(l==="edit")_={membership_settings:d.map(C=>{var A,Z,P,ee,ae,se,te,le;return C.plan_id===a.plan_id?{plan_id:a.plan_id,plan_name:a.plan_name,price:a.price,billing_interval:a.billing_interval||"month",billing_interval_count:a.billing_interval_count||1,allow_clinic:a.allow_clinic,allow_buddy:a.allow_buddy,allow_coach:a.allow_coach,allow_groups:a.allow_groups,allow_court:a.allow_court,features:a.features,applicable_sports:a.applicable_sports||[],advance_booking_days:{court:((A=a.advance_booking_days)==null?void 0:A.court)||10,lesson:((Z=a.advance_booking_days)==null?void 0:Z.lesson)||10,clinic:((P=a.advance_booking_days)==null?void 0:P.clinic)||10,buddy:((ee=a.advance_booking_days)==null?void 0:ee.buddy)||10},advance_booking_enabled:{court:((ae=a.advance_booking_enabled)==null?void 0:ae.court)!==!1,lesson:((se=a.advance_booking_enabled)==null?void 0:se.lesson)!==!1,clinic:((te=a.advance_booking_enabled)==null?void 0:te.clinic)!==!1,buddy:((le=a.advance_booking_enabled)==null?void 0:le.buddy)!==!1}}:C})};else{const C=await p.callRawAPI("/v3/api/custom/courtmatchup/stripe/product",{name:a.plan_name,description:a.plan_name,club_id:(x=(n=O.clubProfile)==null?void 0:n.club)==null?void 0:x.id},"POST");console.log("stripeProductResponse",C);const A=await p.callRawAPI("/v3/api/custom/courtmatchup/stripe/price",{product_id:C.model,name:a.plan_name,amount:a.price,type:"recurring",interval:a.billing_interval||"month",interval_count:a.billing_interval_count||1,trial_days:0,usage_type:"licenced",usage_limit:0},"POST");_={membership_settings:[...d,{plan_id:A.model,plan_name:a.plan_name,price:a.price,billing_interval:a.billing_interval||"month",billing_interval_count:a.billing_interval_count||1,allow_clinic:a.allow_clinic,allow_buddy:a.allow_buddy,allow_coach:a.allow_coach,allow_groups:a.allow_groups,allow_court:a.allow_court,features:a.features,applicable_sports:a.applicable_sports||[],advance_booking_days:{court:((f=a.advance_booking_days)==null?void 0:f.court)||10,lesson:((V=a.advance_booking_days)==null?void 0:V.lesson)||10,clinic:((J=a.advance_booking_days)==null?void 0:J.clinic)||10,buddy:((U=a.advance_booking_days)==null?void 0:U.buddy)||10},advance_booking_enabled:{court:((Y=a.advance_booking_enabled)==null?void 0:Y.court)||!0,lesson:((K=a.advance_booking_enabled)==null?void 0:K.lesson)||!0,clinic:((X=a.advance_booking_enabled)==null?void 0:X.clinic)||!0,buddy:((z=a.advance_booking_enabled)==null?void 0:z.buddy)||!0}}]}}console.log("Submitting membership plan data:",JSON.stringify(_,null,2));const Q=await p.callRawAPI(u==="club"?"/v3/api/custom/courtmatchup/club/profile-edit":`/v3/api/custom/courtmatchup/admin/profile-edit/${(H=c==null?void 0:c.user)==null?void 0:H.id}`,_,"POST");p.setTable("activity_logs"),await p.callRestAPI({user_id:F,activity_type:ne.club_ui,action_type:ie.UPDATE,data:JSON.stringify(_),club_id:m==null?void 0:m.id,description:"Updated membership plans"},"POST"),Q.error&&M(g,Q.message||"Failed to save plan",3e3,"error"),o(!1),h(),v()}catch(_){M(g,_.message||"Failed to save plan",3e3,"error")}},v=()=>{o(!1),b(null)},w=a=>{i(a),N(!0)},s=async()=>{var a;if(t){B(!0);try{const l=d.filter(f=>f.plan_id!==t.plan_id),n={membership_settings:l};console.log("Deleting membership plan:",t.plan_name,"Remaining plans:",l.length);const x=await p.callRawAPI(u==="club"?"/v3/api/custom/courtmatchup/club/profile-edit":`/v3/api/custom/courtmatchup/admin/profile-edit/${(a=c==null?void 0:c.user)==null?void 0:a.id}`,n,"POST");p.setTable("activity_logs"),await p.callRestAPI({user_id:F,activity_type:ne.club_ui,action_type:ie.DELETE,data:JSON.stringify({deleted_plan:t,remaining_plans:l.length}),club_id:m==null?void 0:m.id,description:`Deleted membership plan: ${t.plan_name}`},"POST"),x.error?M(g,x.message||"Failed to delete plan",3e3,"error"):(M(g,`Successfully deleted plan: ${t.plan_name}`,3e3,"success"),h()),N(!1),i(null)}catch(l){M(g,l.message||"Failed to delete plan",3e3,"error")}finally{B(!1)}}};return e.jsxs("div",{className:"flex flex-col gap-4 p-5",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"text-xl font-medium",children:"Membership settings"}),e.jsxs("button",{onClick:()=>{j("create"),o(!0)},className:"flex items-center gap-2 rounded-lg border bg-primaryBlue px-3 py-2 text-sm text-white ",children:[e.jsx("span",{children:"New plan"}),e.jsx(pe,{className:"h-4 w-4"})]})]}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"min-w-full border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"text-left text-sm text-gray-500",children:[e.jsx("th",{className:"pb-4",children:"Plan"}),e.jsx("th",{className:"pb-4",children:"Price"}),e.jsx("th",{className:"pb-4",children:"Billing"}),e.jsx("th",{className:"pb-4 text-center",children:"Court booking"}),e.jsx("th",{className:"pb-4 text-center",children:"Lessons"}),e.jsx("th",{className:"pb-4 text-center",children:"Clinics"}),e.jsx("th",{className:"pb-4 text-center",children:"Find a Buddy"}),e.jsx("th",{className:"pb-4 text-center",children:"My Groups"}),e.jsx("th",{className:"pb-4 text-center",children:"Sports Covered"}),e.jsx("th",{className:"pb-4 text-center",children:"Advanced Booking"}),e.jsx("th",{className:"pb-4 text-center",children:"Actions"})]})}),e.jsx("tbody",{children:d.length>0?d==null?void 0:d.map(a=>{var l,n;return e.jsxs("tr",{className:"overflow-hidden",children:[e.jsx("td",{className:"rounded-l-xl bg-white px-4 py-3 text-gray-600",children:a.plan_name}),e.jsx("td",{className:"bg-white px-4 py-3",children:be(a.price)}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("span",{className:"text-sm text-gray-600",children:q(a.billing_interval,a.billing_interval_count)})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:a.allow_court?e.jsx(D,{className:"h-5 w-5 text-green-500"}):e.jsx(S,{className:"h-5 w-5 text-gray-400"})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:a.allow_coach?e.jsx(D,{className:"h-5 w-5 text-green-500"}):e.jsx(S,{className:"h-5 w-5 text-gray-400"})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:a.allow_clinic?e.jsx(D,{className:"h-5 w-5 text-green-500"}):e.jsx(S,{className:"h-5 w-5 text-gray-400"})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:a.allow_buddy?e.jsx(D,{className:"h-5 w-5 text-green-500"}):e.jsx(S,{className:"h-5 w-5 text-gray-400"})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"flex justify-center",children:a.allow_groups?e.jsx(D,{className:"h-5 w-5 text-green-500"}):e.jsx(S,{className:"h-5 w-5 text-gray-400"})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"text-xs text-gray-600",children:L(a.applicable_sports)})})}),e.jsx("td",{className:"bg-white px-4 py-3",children:e.jsx("div",{className:"text-center",children:e.jsx("span",{className:"whitespace-nowrap text-gray-500",children:((l=a.advance_booking_enabled)==null?void 0:l.court)===!1?"Disabled":`${((n=a.advance_booking_days)==null?void 0:n.court)||10}d`})})}),e.jsx("td",{className:"rounded-r-xl bg-white px-4 py-3",children:e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx("button",{className:"flex items-center justify-center text-gray-500 transition-colors hover:text-gray-700",onClick:()=>{j("edit"),W(a)},title:"Edit plan",children:e.jsx(oe,{className:"h-4 w-4"})}),e.jsx("button",{className:"flex items-center justify-center text-red-500 transition-colors hover:text-red-700",onClick:()=>w(a),title:"Delete plan",children:e.jsx(he,{className:"h-4 w-4"})})]})})]},a.name)}):e.jsx("tr",{className:"text-center text-sm text-gray-500",children:e.jsx("td",{colSpan:"11",children:"No plans available"})})})]})}),e.jsx(xe,{isOpen:y,onClose:v,title:"Plan details",onPrimaryAction:()=>{o(!1)},showFooter:!1,children:e.jsx(ve,{initialData:E,mode:T,onSubmit:a=>G(a,T),onClose:v,onDelete:w})}),e.jsx(me,{isOpen:I,onClose:()=>{N(!1),i(null)},title:"Delete Membership Plan",message:`Are you sure you want to delete the "${t==null?void 0:t.plan_name}" membership plan? This action cannot be undone and may affect existing members with this plan.`,onDelete:s,buttonText:"Delete Plan",loading:R,requireConfirmation:!0})]})}export{Me as M};
