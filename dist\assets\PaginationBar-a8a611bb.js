import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const v=({currentPage:d,pageCount:l,pageSize:x,canPreviousPage:r,canNextPage:a,updatePageSize:m,previousPage:o,nextPage:c,gotoPage:i})=>{const n=()=>{const s=[];if(l<=7)for(let t=1;t<=l;t++)s.push(t);else{s.push(1),d>3&&s.push("...");for(let t=Math.max(2,d-1);t<=Math.min(l-1,d+1);t++)s.push(t);d<l-2&&s.push("..."),s.push(l)}return s},h=()=>{const s=[];if(l<=5)for(let t=1;t<=l;t++)s.push(t);else d>1&&s.push(d-1),s.push(d),d<l&&s.push(d+1);return s};return e.jsxs("div",{className:"mt-5 flex flex-wrap items-center justify-between gap-3",children:[e.jsx("div",{className:"hidden sm:flex sm:items-center sm:gap-2",children:e.jsxs("span",{className:"text-sm",children:["Page"," ",e.jsxs("strong",{children:[d," of ",l]})]})}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("button",{onClick:()=>i(1),disabled:!r,className:"rounded border px-2 py-1 text-xs hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50 sm:px-3 sm:text-sm",title:"First page",children:"«"}),e.jsx("button",{onClick:o,disabled:!r,className:"rounded border px-2 py-1 text-xs hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50 sm:px-3 sm:text-sm",title:"Previous page",children:"‹"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"flex items-center gap-1 sm:hidden",children:h().map((s,t)=>e.jsx("button",{onClick:()=>i(s),className:`rounded border px-2 py-1 text-xs ${s===d?"bg-blue-500 text-white":"hover:bg-gray-100"}`,children:s},t))}),e.jsx("div",{className:"hidden items-center gap-1 sm:flex",children:n().map((s,t)=>e.jsx("button",{onClick:()=>typeof s=="number"&&i(s),className:`rounded border px-2 py-1 text-sm ${s===d?"bg-blue-500 text-white":"hover:bg-gray-100"} ${s==="..."?"cursor-default":""}`,disabled:s==="...",children:s},t))})]}),e.jsx("button",{onClick:c,disabled:!a,className:"rounded border px-2 py-1 text-xs hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50 sm:px-3 sm:text-sm",title:"Next page",children:"›"}),e.jsx("button",{onClick:()=>i(l),disabled:!a,className:"rounded border px-2 py-1 text-xs hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50 sm:px-3 sm:text-sm",title:"Last page",children:"»"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"flex items-center gap-1 sm:hidden",children:e.jsxs("span",{className:"text-xs",children:[d,"/",l]})}),e.jsx("select",{className:"h-7 max-h-7 rounded border border-gray-200 !py-0 text-xs sm:h-8 sm:text-sm",value:x,onChange:s=>{m(Number(s.target.value))},children:[8,16].map(s=>e.jsx("option",{value:s,children:s},s))})]})]})};export{v as default};
