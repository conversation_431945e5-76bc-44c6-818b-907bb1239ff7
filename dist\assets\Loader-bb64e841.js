import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{_ as o}from"./cal-heatmap-cf010ec4.js";import{r as e}from"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const i=e.lazy(()=>o(()=>import("./LoadingIndicator-7bc481d2.js"),["assets/LoadingIndicator-7bc481d2.js","assets/@nivo/heatmap-ba1ecfff.js","assets/@craftjs/core-d3c11b68.js","assets/vendor-851db8c1.js","assets/@fortawesome/react-fontawesome-13437837.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/framer-motion-c6ba3e69.js"])),l=({style:r})=>t.jsx("div",{style:{display:"flex",width:"100vw",height:"100vh",justifyContent:"center",alignItems:"center",...r},children:t.jsx(i,{})});export{l as default};
