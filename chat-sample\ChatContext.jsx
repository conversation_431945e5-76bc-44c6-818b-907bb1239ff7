import { createContext, useReducer, useEffect, useRef, useState } from "react";
import MkdSDK from "Utils/MkdSDK";

let sdk = new MkdSDK();

export const ChatContext = createContext();

const initialState = {
  rooms: [],
  unreadMessages: 0,
  sortOrder: "desc"
};

const reducer = (state, action) => {
  switch (action.type) {
    case "SET_CHAT":
      return { ...state, chat: action.payload };

    case "SET_UNREAD_MESSAGES":
      return { ...state, unreadMessages: action.payload };

    case "SET_ROOMS":
      return { ...state, rooms: action.payload };

    case "SET_SORT_ORDER":
      return { ...state, sortOrder: action.payload };

    default:
      return state;
  }
};

export const ChatProvider = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState);
  const pollingRef = useRef(null);
  const isVisibleRef = useRef(true);
  const user_id = localStorage.getItem("user");
  const manualSortRef = useRef(false);
  const currentSortOrderRef = useRef("desc");

  const setSortOrder = (order) => {
    manualSortRef.current = true;
    currentSortOrderRef.current = order;
    dispatch({ type: "SET_SORT_ORDER", payload: order });
  };

  async function getRooms(force = false) {
    try {
      const response = await sdk.getMyRoom();
      console.log("rooms", response);

      // Calculate unread messages count per room and add to room objects
      const roomsWithUnread = response.list.map((room) => {
        const roomMessages =
          response.messages?.filter((message) => message.room_id === room.id) ||
          [];

        const unreadCount = roomMessages.filter((message) => {
          try {
            const parsedChat = JSON.parse(message.chat);
            // Only count as unread if:
            // 1. The message has unread status
            // 2. The message was NOT sent by the current logged-in user
            // 3. The message is not older than 24 hours (to prevent showing old messages as unread)
            const messageTime = new Date(message.update_at);
            const now = new Date();
            const isWithin24Hours = (now - messageTime) <= 24 * 60 * 60 * 1000;
            
            return message.unread === 1 && 
                   parsedChat.user_id != user_id && 
                   isWithin24Hours;
          } catch (error) {
            console.error("Error parsing message chat:", error);
            return false;
          }
        }).length;

        const latestMessage = roomMessages.reduce((latest, current) => {
          const currentTime = new Date(current.update_at).getTime();
          const latestTime = latest ? new Date(latest.update_at).getTime() : 0;
          return currentTime > latestTime ? current : latest;
        }, null);

        return {
          ...room,
          unreadCount,
          latestMessageTime: latestMessage
            ? new Date(latestMessage.update_at)
            : new Date(room.update_at),
        };
      });
      
      // Sort rooms by latest message time based on current sortOrder
      const sortedRooms = roomsWithUnread.sort((a, b) => {
        const timeA = a.latestMessageTime.getTime();
        const timeB = b.latestMessageTime.getTime();
        return currentSortOrderRef.current === "desc" ? timeB - timeA : timeA - timeB;
      });

      const totalUnread = sortedRooms.reduce(
        (total, room) => total + room.unreadCount,
        0
      );
      console.log("totalUnread", totalUnread)

      dispatch({ type: "SET_ROOMS", payload: sortedRooms });
      dispatch({ type: "SET_UNREAD_MESSAGES", payload: totalUnread });
    } catch (error) {
      console.error("Error fetching rooms:", error);
    }
  }

  useEffect(() => {
    // Initial fetch
    getRooms();

    // Handle visibility change
    const handleVisibilityChange = () => {
      isVisibleRef.current = document.visibilityState === "visible";
      if (isVisibleRef.current) {
        getRooms();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Start polling with dynamic interval
    const startPolling = () => {
      // Clear any existing interval
      stopPolling();

      // Start with a 10-second interval
      let interval = 10000;

      pollingRef.current = setInterval(() => {
        if (isVisibleRef.current) {
          getRooms();
          // Gradually increase interval up to 30 seconds if no new messages
          interval = Math.min(interval + 5000, 30000);
        }
      }, interval);
    };

    const stopPolling = () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
        pollingRef.current = null;
      }
    };

    startPolling();

    // Cleanup
    return () => {
      stopPolling();
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  return (
    <ChatContext.Provider 
      value={{ 
        state, 
        dispatch, 
        getRooms,
        sortOrder: state.sortOrder,
        setSortOrder
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};
