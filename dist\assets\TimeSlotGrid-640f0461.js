import{j as p}from"./@nivo/heatmap-ba1ecfff.js";import{r as a,b as Z}from"./vendor-851db8c1.js";const _=({days:g,isSelected:b,handleTimeSelect:$,handleDeleteTime:O,containerRef:tt,renderTimeSlotContent:q,disableTimeSlot:v})=>{const I=a.useRef(null),[c,B]=a.useState(!1),[D,k]=a.useState(null),[M,F]=a.useState(null),[et,G]=a.useState(null),[nt,P]=a.useState(null),[u,A]=a.useState({start:null,current:null}),H=a.useRef(new Map),y=a.useRef(null),L=a.useRef(new Set),f=a.useRef(null),J=e=>{const[t,r]=e.split(":"),n=parseInt(t),s=n>=12?"PM":"AM";return`${n%12||12}:${r} ${s}`},l=(()=>{const e=[];for(let t=8;t<=22;t++)for(let r=0;r<60;r+=30){const n=`${t.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}:00`;e.push({display:J(n),value:n})}return e})(),K=(e,t)=>{if(!D||!M)return!1;const[r,n]=D.split("-"),[s,o]=M.split("-"),i=g.indexOf(t),h=g.indexOf(r),x=g.indexOf(s),R=Math.min(h,x),C=Math.max(h,x);if(i<R||i>C)return!1;const S=l.findIndex(m=>m.value===e),N=l.findIndex(m=>m.value===n),d=l.findIndex(m=>m.value===o),w=Math.min(N,d),T=Math.max(N,d);return S>=w&&S<=T},U=a.useCallback((e,t)=>{if(!e||!t.start||!t.current)return!1;const r=I.current.getBoundingClientRect(),n=e.getBoundingClientRect(),s=I.current.querySelector(".overflow-x-auto"),o=s?s.scrollLeft:0,i={left:n.left-r.left+o,right:n.right-r.left+o,top:n.top-r.top,bottom:n.bottom-r.top},h=Math.min(t.start.x,t.current.x),x=Math.max(t.start.x,t.current.x),R=Math.min(t.start.y,t.current.y),C=Math.max(t.start.y,t.current.y);return!(i.right<h||i.left>x||i.bottom<R||i.top>C)},[]),X=a.useCallback(e=>{y.current&&cancelAnimationFrame(y.current),y.current=requestAnimationFrame(()=>{const t=new Set;H.current.forEach((r,n)=>{if(U(r,e)){const[s,o]=n.split("|"),i=l.find(x=>x.value===o);(v&&i?v(i,s):!1)||(t.add(n),L.current.has(n)||$(o,s))}}),L.current=t})},[U,$,v,l]),Y=(e,t)=>{if(!I.current)return{x:0,y:0};const r=I.current.getBoundingClientRect(),n=I.current.querySelector(".overflow-x-auto"),s=n?n.scrollLeft:0;return{x:e-r.left+s,y:t-r.top}},Q=(e,t,r)=>{r.preventDefault(),f.current&&clearTimeout(f.current),k(`${t}-${e}`),F(`${t}-${e}`),G(e),P(t);const n=Y(r.clientX,r.clientY);A({start:n,current:n}),L.current.clear(),f.current=setTimeout(()=>{B(!0)},150)},V=(e,t)=>{f.current&&(clearTimeout(f.current),f.current=null),c||(b(e,t)?O(e,t):$(e,t)),B(!1),k(null),F(null)},z=(e,t)=>{c&&F(`${t}-${e}`)},j=a.useCallback(e=>{if(c){e.preventDefault();const t=Y(e.clientX,e.clientY),r={...u,current:t};A(r),X(r)}},[c,u,X]),E=a.useCallback(()=>{if(f.current&&(clearTimeout(f.current),f.current=null),c&&D&&M){const[e,t]=D.split("-"),[r,n]=M.split("-"),s=g.indexOf(e),o=g.indexOf(r),i=Math.min(s,o),h=Math.max(s,o),x=l.findIndex(d=>d.value===t),R=l.findIndex(d=>d.value===n),C=Math.min(x,R),S=Math.max(x,R),N=b(t,e);for(let d=i;d<=h;d++){const w=g[d];for(let T=C;T<=S;T++){const m=l[T].value,W=l[T];(v?v(W,w):!1)||(N?b(m,w)&&O(m,w):b(m,w)||$(m,w))}}}B(!1),k(null),F(null),G(null),P(null),A({start:null,current:null}),L.current.clear(),y.current&&(cancelAnimationFrame(y.current),y.current=null),window.removeEventListener("mousemove",j),window.removeEventListener("mouseup",E)},[j,c,D,M,g,l,b,O,$,v]);return a.useEffect(()=>{const e=()=>{c&&E()};return window.addEventListener("mouseup",e),()=>window.removeEventListener("mouseup",e)},[c,D,M,E]),Z.useEffect(()=>{if(c)return window.addEventListener("mousemove",j),window.addEventListener("mouseup",E),()=>{window.removeEventListener("mousemove",j),window.removeEventListener("mouseup",E)}},[c,j,E]),p.jsxs("div",{className:"relative w-full overflow-hidden",ref:I,children:[c&&u.start&&u.current&&p.jsx("div",{style:{position:"absolute",left:Math.min(u.start.x,u.current.x),top:Math.min(u.start.y,u.current.y),width:Math.abs(u.current.x-u.start.x),height:Math.abs(u.current.y-u.start.y),backgroundColor:"rgba(59, 130, 246, 0.2)",border:"2px solid rgb(59, 130, 246)",pointerEvents:"none",zIndex:1e3}}),p.jsx("div",{className:"w-full overflow-x-auto pb-4",children:p.jsx("div",{className:"grid min-w-[900px] grid-cols-7 gap-5",children:g.map(e=>p.jsxs("div",{className:"rounded-md bg-white p-2 text-center",children:[p.jsx("div",{className:"mb-2 rounded-md bg-[#F6F8FA] px-3 py-2 font-medium",children:e}),p.jsx("div",{className:"space-y-2",children:l.map(t=>{const r=b(t.value,e),n=v?v(t,e):!1,s=c&&K(t.value,e);return p.jsxs("div",{className:"relative",onMouseEnter:()=>!n&&z(t.value,e),children:[p.jsx("button",{ref:o=>{o&&H.current.set(`${e}|${t.value}`,o)},className:`w-full rounded-md border-2 border-gray-100 px-3 py-2 text-sm font-medium ${n?"cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400":r?"border-2 border-primaryBlue bg-[#EBF1FF] text-primaryBlue":s?"border-primaryBlue bg-[#EBF1FF] bg-opacity-50":"border-gray-300 text-gray-500 hover:border-gray-400"}`,disabled:n,onMouseDown:o=>!n&&Q(t.value,e,o),onMouseEnter:()=>!n&&z(t.value,e),onClick:o=>{!c&&!n&&V(t.value,e)},children:t.display}),q&&q(t,e)]},`${e}-${t.value}`)})})]},e))})})]})},ct=_;export{ct as T};
