import{j as r}from"./@nivo/heatmap-ba1ecfff.js";import{r as I}from"./vendor-851db8c1.js";import{f as rt}from"./index-08a5dc5b.js";import{M as gt}from"./react-tooltip-7a26650a.js";import{f as yt,g as wt}from"./date-fns-cca0f4f7.js";const It=({selectedDate:p,timeRange:w,onTimeClick:E,onNext:z,nextButtonText:ot="Next",startHour:q=8,endHour:F=24,interval:P=30,className:lt="",multipleSlots:M=!1,timeSlots:Tt=[],onTimeSlotsChange:T,individualSelection:Nt=!1,isTimeSlotAvailable:vt,clubTimes:$=[],isLoading:at,coachAvailability:H=[],height:ct="h-fit",minBookingTime:j=30,enforceMinBookingTime:J=!1,availabilityData:h=null,loadingAvailability:dt=!1,clubExceptions:R=[]})=>{var D;const[N,V]=I.useState([]),[d,g]=I.useState([]),[mt,_]=I.useState(350),ut=I.useRef(null),v=I.useCallback(()=>{const t=[];for(let e=q;e<=F;e++)for(let i=0;i<60;i+=P){const s=e===24?0:e,n=`${s.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`,a=s>=12?"PM":"AM",o=`${s===0?12:s>12?s-12:s}:${i.toString().padStart(2,"0")} ${a}`;t.push({time24:n,time12:o})}return t},[q,F,P]);I.useEffect(()=>{if(w&&w.length>0&&w[0].from&&w[0].until){const t=v(),e=w[0].from,i=w[0].until;if(!t.find(o=>o.time12===e))return;const n=t.findIndex(o=>o.time12===i);if(n===-1)return;const a=t.findIndex(o=>o.time12===e),l=[];for(let o=a;o<n;o++)l.push(t[o].time24);g(l)}else g([])},[w,v]),I.useEffect(()=>{const t=()=>{const e=window.innerHeight;e<600?_(200):e<800?_(300):_(350)};return t(),window.addEventListener("resize",t),()=>window.removeEventListener("resize",t)},[]);const W=t=>{if(!$||$.length===0)return!0;const[e,i]=t.split(":"),s=parseInt(e)*60+parseInt(i);return $.some(n=>{const[a,l]=n.from.split(":"),[o,c]=n.until.split(":"),f=parseInt(a)*60+parseInt(l),m=parseInt(o)*60+parseInt(c);return s>=f&&s<=m})},Z=t=>N.some(e=>{const i=v(),s=i.findIndex(l=>l.time12===e.from),n=i.findIndex(l=>l.time12===e.until),a=i.findIndex(l=>l.time24===t);return a>=s&&a<=n}),G=t=>{if(!p||!H||H.length===0)return!0;const e=p.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase(),i=H.find(n=>n.day===e);if(!i)return!1;const s=`${t}:00`;return i.timeslots.includes(s)},K=t=>{if(!p||!(h!=null&&h.unavailableTimeSlots))return!1;const e=p.toISOString().split("T")[0];return h.unavailableTimeSlots.some(i=>{if(i.date!==e)return!1;const[s,n]=i.start_time.split(":").map(Number),[a,l]=i.end_time.split(":").map(Number),[o,c]=t.split(":").map(Number),f=s*60+n,m=a*60+l,C=o*60+c;return C>=f&&C<m})},Q=t=>{if(!p||!h)return!0;if(h.qualifying_courts&&h.qualifying_courts.length>0){const n=p.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase();return h.qualifying_courts.some(l=>{try{const o=l.availability?JSON.parse(l.availability):[];if(!o||o.length===0)return!0;const c=o.find(m=>m.day===n);if(!c||!c.timeslots)return!1;const f=`${t}:00`;return c.timeslots.includes(f)}catch(o){return console.warn(`Failed to parse availability for court ${l.id}:`,o),!0}})}if(!h.availability)return!0;const e=p.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase(),i=h.availability.find(n=>n.day===e);if(!i||!i.timeslots)return!1;const s=`${t}:00`;return i.timeslots.includes(s)},X=t=>{if(!p||!wt(p))return!1;const e=new Date,[i,s]=t.split(":").map(Number),n=new Date(p);return n.setHours(i,s,0,0),e>n},Y=t=>{if(!p||!R||R.length===0)return!1;try{const e=p.toLocaleDateString("en-US",{weekday:"long"}).toLowerCase(),i=`${t}:00`;return R.some(s=>{if(!s.days||!Array.isArray(s.days))return!1;const n=s.days.find(a=>a.day===e);return!n||!n.timeslots?!1:n.timeslots.includes(i)})}catch(e){return console.warn("Error checking club exceptions:",e),!1}},ft=t=>{var m,C,O,tt;if(!Q(t.time24)||!G(t.time24)||!W(t.time24)||M&&Z(t.time24)||X(t.time24)||K(t.time24)||Y(t.time24))return;const e=v(),i=e.findIndex(u=>u.time24===t.time24);if(d.includes(t.time24)){if(d.length>=2){const u=[...d].sort();if(t.time24===u[0]||t.time24===u[u.length-1]){const S=d.filter(x=>x!==t.time24);g(S);const y=[...S].sort(),A=(m=e.find(x=>x.time24===y[0]))==null?void 0:m.time12;let B;const et=e.findIndex(x=>x.time24===y[y.length-1]),b=e[et+1];B=(b==null?void 0:b.time12)||((C=e.find(x=>x.time24===y[y.length-1]))==null?void 0:C.time12),E({from:A,until:B})}}else d.length===1&&(g([]),E({from:"",until:""}));return}let s;if(d.length===0)s=[t.time24],g(s);else{const u=[...d].sort(),S=u[u.length-1],y=e.findIndex(A=>A.time24===S);Math.abs(i-y)===1?(s=[...d,t.time24],g(s)):(s=[t.time24],g(s))}const n=[...s].sort(),a=(O=e.find(u=>u.time24===n[0]))==null?void 0:O.time12;let l;const o=e.findIndex(u=>u.time24===n[n.length-1]),c=e[o+1];l=(c==null?void 0:c.time12)||((tt=e.find(u=>u.time24===n[n.length-1]))==null?void 0:tt.time12);const f={from:a,until:l};if(J){const[u,S]=n[0].split(":").map(Number),[y,A]=n[n.length-1].split(":").map(Number),B=u*60+S,b=y*60+A-B+30;if(b<j){const st=Math.ceil((j-b)/30),x=[];for(let L=1;L<=st;L++){const it=o+L;it<e.length&&x.push(e[it].time24)}s=[...n,...x],g(s);const nt=e.findIndex(L=>L.time24===x[x.length-1]),U=e[nt+1];l=(U==null?void 0:U.time12)||e[nt].time12,f.until=l}}E(f)},pt=()=>{var c,f;if(d.length===0)return;const t=v(),e=[...d].sort(),i=(c=t.find(m=>m.time24===e[0]))==null?void 0:c.time12;let s;const n=t.findIndex(m=>m.time24===e[e.length-1]),a=t[n+1];s=(a==null?void 0:a.time12)||((f=t.find(m=>m.time24===e[e.length-1]))==null?void 0:f.time12);const l={from:i,until:s},o=[...N,l];V(o),g([]),T==null||T(o)},xt=t=>{const e=N.filter((i,s)=>s!==t);V(e),T==null||T(e)},ht=t=>d.includes(t),k=v();return r.jsxs("div",{className:`rounded-lg bg-white p-4 shadow-5 ${lt} ${ct}`,children:[p&&r.jsx("p",{className:"text-center font-medium",children:yt(p,"EEEE, MMMM d, yyyy")}),J&&r.jsxs("div",{className:"mb-3 mt-2 rounded-lg bg-blue-50 p-2 text-xs text-blue-700",children:["Minimum booking time: ",j," minutes"]}),dt&&r.jsx("div",{className:"mb-3 mt-2 rounded-lg bg-yellow-50 p-3 text-center",children:r.jsxs("div",{className:"flex items-center justify-center gap-2",children:[r.jsxs("svg",{className:"h-4 w-4 animate-spin text-yellow-600",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),r.jsx("span",{className:"text-sm text-yellow-700",children:"Loading available time slots..."})]})}),r.jsx("div",{ref:ut,className:"scrollbar-hide mb-5 mt-2 flex h-full flex-col gap-2 overflow-y-auto",style:{maxHeight:`${mt}px`},children:k.map((t,e)=>{const i=W(t.time24),s=G(t.time24),n=Q(t.time24),a=M&&Z(t.time24),l=X(t.time24),o=K(t.time24),c=Y(t.time24),f=n&&s&&i&&!a&&!l&&!o&&!c,m=i?n?s?l?"Time has passed":o?"Time slot unavailable":c?"Blocked by club exception":"":"Coach not available":"Not available for selected sport/type":"Club Closed";return r.jsxs("button",{onClick:()=>ft(t),disabled:!f,"data-tooltip-id":`time-${e}`,"data-tooltip-content":m,type:"button",className:`
                rounded-lg border-[1.5px] px-4 py-2 text-sm font-medium transition-colors
                ${f?ht(t.time24)?"border-[1.5px] border-primaryBlue bg-primaryBlue/10 text-primaryBlue":"border-gray-200 text-gray-500 hover:bg-gray-50":"cursor-not-allowed border-gray-200 bg-gray-100 text-gray-400"}
              `,children:[t.time12,!f&&r.jsx(gt,{id:`time-${e}`,place:"top",className:"z-50 !bg-gray-900 !px-2 !py-1 !text-xs !text-white"})]},t.time24)})}),d.length>0&&r.jsxs("div",{className:"space-y-2 border-t border-gray-200 pt-4",children:[r.jsxs("div",{className:"flex items-center justify-center gap-2 text-sm",children:[r.jsx("span",{className:"font-medium",children:"From: "}),r.jsx("span",{className:"text-primaryBlue",children:(D=k.find(t=>t.time24===d.sort()[0]))==null?void 0:D.time12}),r.jsx("span",{className:"font-medium",children:"Until: "}),r.jsx("span",{className:"text-primaryBlue",children:(()=>{var s;const t=[...d].sort(),e=k.findIndex(n=>n.time24===t[t.length-1]),i=k[e+1];return(i==null?void 0:i.time12)||((s=k.find(n=>n.time24===t[t.length-1]))==null?void 0:s.time12)})()})]}),M&&r.jsx(rt,{className:"mt-2 w-full rounded-lg border border-primaryBlue bg-primaryBlue/10 px-4 py-2 text-primaryBlue hover:bg-primaryBlue/20",onClick:pt,disabled:d.length===0,children:"Add Time Range"})]}),M&&N.length>0&&r.jsxs("div",{className:"mt-4 space-y-2 border-t border-gray-200 pt-4",children:[r.jsx("p",{className:"text-center font-medium",children:"Selected Time Ranges"}),r.jsx("div",{className:"flex flex-col justify-center gap-2 ",children:N.map((t,e)=>r.jsxs("div",{className:"grid grid-cols-[auto_auto_auto_auto_auto] items-center gap-2 rounded-lg px-3 py-1 text-sm",children:[r.jsx("span",{className:"text-gray-500",children:"From"}),r.jsx("span",{children:t.from}),r.jsx("span",{children:"-"}),r.jsx("span",{className:"text-gray-500",children:"Until"}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("span",{children:t.until}),r.jsx("button",{onClick:()=>xt(e),className:"text-primaryBlue hover:text-primaryBlue/80",children:r.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M15 9L9 15M15 15L9 9M21.25 12C21.25 17.1086 17.1086 21.25 12 21.25C6.89137 21.25 2.75 17.1086 2.75 12C2.75 6.89137 6.89137 2.75 12 2.75C17.1086 2.75 21.25 6.89137 21.25 12Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round"})})})]})]},e))})]}),z&&r.jsx("div",{className:"sticky bottom-0 bg-white pt-2",children:r.jsx(rt,{className:"mt-2 w-full rounded-lg bg-primaryBlue px-4 py-2 text-white disabled:opacity-50",onClick:z,disabled:M?N.length===0:d.length===0,loading:at,children:ot})})]})},At=It;export{At as T};
