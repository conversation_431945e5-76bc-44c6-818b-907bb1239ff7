import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{r as s,b as o}from"./vendor-851db8c1.js";import{_ as a}from"./cal-heatmap-cf010ec4.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const i=s.lazy(()=>a(()=>import("./Loader-bb64e841.js"),["assets/Loader-bb64e841.js","assets/@nivo/heatmap-ba1ecfff.js","assets/@craftjs/core-d3c11b68.js","assets/vendor-851db8c1.js","assets/@fortawesome/react-fontawesome-13437837.js","assets/@fortawesome/fontawesome-svg-core-4fa3e289.js","assets/cal-heatmap-cf010ec4.js"])),x=()=>{const[e,r]=o.useState(!0);return console.log(e),o.useEffect(()=>{setTimeout(()=>{r(!1)},5e3)},[]),t.jsx(t.Fragment,{children:e?t.jsx(i,{}):t.jsx("div",{className:"flex h-screen w-full items-center justify-center text-7xl text-gray-700 ",children:"Not Found"})})};export{x as default};
