import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{r as o}from"./vendor-851db8c1.js";import{u as y,h as j,b2 as A,M as w}from"./index-08a5dc5b.js";import{S as v}from"./react-select-c8303602.js";import{L as N}from"./ListBuddy-177c10c7.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./index-be4468eb.js";import"./AddButton.module-98aac587.js";import"./index-91353a7a.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./DataTable-84e69e98.js";import"./HistoryComponent-999cafaf.js";let l=new w;function ht(){const[L,d]=o.useState(null),[u,a]=o.useState(!1),[f,_]=o.useState([]),[C,x]=o.useState(null),[h,S]=o.useState([]),[m,c]=o.useState(null),{club:i}=y(),n=async t=>{l.setTable("club_permissions");const s=await l.callRestAPI({filter:[`club_id,eq,${t}`]},"GETALL");return s.list.length>0?JSON.parse(s.list[0].permission):null},g=async t=>{a(!0);try{x({id:t.value,name:t.label});const s=await n(t.club_id);c(s),await b(t.value)}catch(s){console.error("Error fetching data:",s)}finally{a(!1)}},b=async t=>{var s,p;a(!0);try{const r=await l.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${t}`,{},"GET");d((s=r==null?void 0:r.model)==null?void 0:s.club),S((p=r==null?void 0:r.model)==null?void 0:p.sports)}catch(r){console.log(r)}finally{a(!1)}};return o.useEffect(()=>{(async()=>{a(!0);const t=await n(i==null?void 0:i.id);c(t),a(!1)})()},[i==null?void 0:i.id]),e.jsxs("div",{className:"h-full bg-white p-4 sm:p-6 lg:p-8",children:[u&&e.jsx(j,{}),e.jsxs("div",{className:"mb-4 max-w-xl",children:[e.jsx("label",{className:"mb-2 block text-base font-medium text-gray-900",children:"Select club"}),e.jsx(v,{className:"w-full rounded-lg border border-gray-200 p-2.5 text-sm",options:f.map(t=>({value:t.user_id,label:t.name,club_id:t==null?void 0:t.id})),isMulti:!1,onChange:g})]}),m&&!m.staff.find_a_buddy?e.jsxs("div",{className:"flex h-[80vh] flex-col items-center justify-center",children:[e.jsx(A,{className:"mb-4 text-6xl text-gray-400"}),e.jsx("h2",{className:"mb-2 text-xl font-semibold text-gray-900",children:"Access Restricted"}),e.jsx("p",{className:"text-center text-gray-600",children:"You don't have permission to access the lessons for this club"})]}):e.jsx(N,{club:i,sports:h})]})}export{ht as default};
