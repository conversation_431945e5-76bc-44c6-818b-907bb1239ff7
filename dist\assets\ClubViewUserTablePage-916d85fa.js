import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{b as l,r as c,f as He,j as us}from"./vendor-851db8c1.js";import{M as Ce,G as ye,W as he,O as Oe,b as ve,c as Ne,d as b,t as xe,A as hs,a9 as xs,aa as Ve,aC as gs,h as fs,f as M,R as be,D as Me,u as bs}from"./index-08a5dc5b.js";import{B as js}from"./BottomDrawer-eee99403.js";import{M as ys}from"./MembershipCard-244db99f.js";import{c as vs,a as je}from"./yup-54691517.js";import{u as Ns}from"./react-hook-form-687afde5.js";import{o as Cs}from"./yup-2824f222.js";import{P as ws}from"./index-91353a7a.js";import"./lodash-91d5d207.js";import Ss from"./Skeleton-1e8bf077.js";import{G as _s,A as Es,C as ks,a as As,E as Ls,I as Ms}from"./EditGroupNameModal-46f70dbe.js";import{F as Ts}from"./FormattedPhoneNumber-40dd7178.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./@hookform/resolvers-67648cca.js";import"./react-loading-skeleton-3d87d1f5.js";import"./index.esm-09a3a6b8.js";import"./react-phone-input-2-57d1f0dd.js";import"./country-state-city-282f4569.js";/* empty css              */import"./react-tooltip-7a26650a.js";let ue=new Ce;const Is=({oldPlan:v,newPlan:p,onClose:i,user:t})=>(console.log("oldPlan",v),console.log("newPlan",p),e.jsx("div",{className:"mx-auto max-w-lg p-4",children:e.jsxs("div",{className:"rounded-xl bg-white",children:[e.jsxs("div",{className:"flex items-start gap-3 p-4",children:[e.jsx("div",{className:"",children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"mb-1 text-xl font-medium",children:"Membership plan has been updated."}),e.jsxs("p",{className:"text-sm text-gray-600",children:[t==null?void 0:t.first_name," ",t==null?void 0:t.last_name,"'s plan has been changed to"," ",p==null?void 0:p.plan_name,"."]})]})]}),e.jsx("div",{className:"mt-4 flex justify-end border-t border-gray-200 p-4",children:e.jsx("button",{onClick:i,className:"rounded-lg bg-green-800 px-4 py-2 text-sm text-white",children:"Close"})})]})})),Ps=({currentPlan:v,newPlan:p,onBack:i,onConfirm:t,user:o,loading:j})=>{const[C,T]=c.useState(!1);console.log(p,"new plan");const y=(()=>{if(!v)return{name:"None",price:0};try{if(v.object){const m=JSON.parse(v.object).plan;return{name:(m==null?void 0:m.nickname)||"Active Plan",price:m!=null&&m.amount?m.amount/100:0}}return{name:"Active Plan",price:0}}catch(x){return console.error("Error parsing current plan:",x),{name:"Active Plan",price:0}}})();return e.jsxs("div",{className:"mx-auto max-w-lg rounded-xl bg-white  shadow-5",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold",children:"Change membership plan profile"}),e.jsxs("p",{className:"text-gray-600",children:["You are about to change ",o==null?void 0:o.first_name," ",o==null?void 0:o.last_name,"'s membership plan. ",o==null?void 0:o.first_name," ",o==null?void 0:o.last_name," will be notified about plan change."]}),e.jsxs("div",{className:"my-6 space-y-3",children:[e.jsxs("div",{children:["Current plan:"," ",e.jsxs("span",{className:"font-semibold",children:[y.name," (",he(y.price),"/month)"]})]}),e.jsxs("div",{children:["New plan:"," ",e.jsxs("span",{className:"font-semibold",children:[p==null?void 0:p.plan_name," (",he((p==null?void 0:p.price)||0),"/month)"]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:C,onChange:x=>T(x.target.checked),className:"h-5 w-5 rounded border-gray-300"}),e.jsx("span",{children:"I understand and want to proceed."})]})})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t border-gray-200 p-4",children:[e.jsx("button",{onClick:i,disabled:j,className:"rounded-lg border border-gray-200 px-6 py-2 disabled:opacity-50",children:"Go back"}),e.jsx("button",{onClick:t,disabled:!C||j,className:"rounded-lg bg-primaryBlue px-6 py-2 text-white disabled:bg-gray-300",children:j?"Updating...":"Update membership"})]})]})},Ds=({currentPlan:v,onBack:p,onConfirm:i,user:t,loading:o})=>{const[j,C]=c.useState(!1),k=(()=>{if(!v)return{name:"None",price:0};try{if(v.object){const x=JSON.parse(v.object).plan;return{name:(x==null?void 0:x.nickname)||"Active Plan",price:x!=null&&x.amount?x.amount/100:0}}return{name:"Active Plan",price:0}}catch(y){return console.error("Error parsing current plan:",y),{name:"Active Plan",price:0}}})();return e.jsxs("div",{className:"mx-auto mt-5 max-w-lg rounded-2xl  bg-white shadow-5",children:[e.jsxs("div",{className:"p-4",children:[e.jsx("h2",{className:"mb-4 text-xl font-semibold",children:"Cancel membership plan profile"}),e.jsxs("p",{className:"text-gray-600",children:["You are about to cancel ",t==null?void 0:t.first_name," ",t==null?void 0:t.last_name,"'s membership plan."]}),e.jsxs("div",{className:"my-6 space-y-3",children:[e.jsxs("div",{children:["Current plan:"," ",e.jsxs("span",{className:"font-semibold",children:[k.name," (",he(k.price),"/month)"]})]}),e.jsxs("div",{children:["New plan: ",e.jsx("span",{className:"font-semibold",children:"Free (0.00$/month)"})]})]}),e.jsx("div",{className:"mb-6",children:e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:j,onChange:y=>C(y.target.checked),className:"h-5 w-5 rounded border-gray-300"}),e.jsx("span",{children:"I understand and want to proceed."})]})})]}),e.jsxs("div",{className:"flex justify-end gap-3 border-t border-gray-200 p-4",children:[e.jsx("button",{onClick:p,disabled:o,className:"rounded-lg border border-gray-200 px-6 py-2 disabled:opacity-50",children:"Go back"}),e.jsx("button",{onClick:i,disabled:!j||o,className:"rounded-lg bg-red-600 px-6 py-2 text-white disabled:opacity-50",children:o?"Canceling...":"Cancel membership"})]})]})},Rs=({onClose:v})=>e.jsx("div",{className:"mx-auto max-w-lg p-4",children:e.jsxs("div",{className:"rounded-xl bg-white",children:[e.jsxs("div",{className:"flex items-start gap-3 p-4",children:[e.jsx("div",{children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:"mb-1 text-xl font-medium",children:"Membership plan has been canceled."})})]}),e.jsx("div",{className:"mt-4 flex justify-end border-t border-gray-200 p-4",children:e.jsx("button",{onClick:v,className:"rounded-lg bg-green-800 px-4 py-2 text-sm text-white",children:"Close"})})]})}),Fs=({isOpen:v,onClose:p,currentPlan:i,tab:t,user:o,membershipPlans:j,onSuccess:C,isLoadingMembershipPlans:T=!1})=>{const{dispatch:k}=l.useContext(ye),[y,x]=c.useState(t||"edit"),[m,N]=c.useState(null),[S,se]=c.useState(!1),[W,K]=c.useState(!1),[V,G]=c.useState(!1),Q=g=>{N(g)},_=async()=>{if(!(!m||!o)){G(!0);try{const g=await ue.callRawAPI("/v3/api/custom/courtmatchup/admin/update-subscription",{user_id:o.id,plan_id:m.id,action:"change_plan"},"POST");g.error?b(k,g.message||"Failed to update membership plan",3e3,"error"):(await Oe(ue,{activity_type:ve.user_management,action_type:Ne.UPDATE,data:{subscription_id:i==null?void 0:i.stripe_id,action:"changed_plan",user_id:o.id,new_plan_id:m.id,new_plan_name:m.plan_name},description:`Changed subscription plan to ${m.plan_name}`}),b(k,"Membership plan updated successfully",3e3,"success"),se(!0),C&&C())}catch(g){console.error("Error updating subscription:",g),b(k,(g==null?void 0:g.message)||"Failed to update membership plan",3e3,"error"),xe(k,g.message)}finally{G(!1)}}},R=async()=>{if(!(!o||!i)){G(!0);try{ue.setTable("stripe_subscription");const g=await ue.callRestAPI({id:i.id},"DELETE");if(g.error){console.error(g.message),b(k,g.message,7500,"error");return}await Oe(ue,{activity_type:ve.user_management,action_type:Ne.UPDATE,data:{subscription_id:i.stripe_id,action:"cancelled",user_id:o.id},description:`Cancelled subscription ${i.stripe_id}`}),b(k,"Cancelled subscription successfully",1e4,"success"),K(!0),C&&C()}catch(g){console.error(g),b(k,g.message,7500,"error"),xe(k,g.code)}finally{G(!1)}}},te=()=>{N(null)},ae=()=>{se(!1),K(!1),N(null),G(!1),p()};return c.useEffect(()=>{v&&(console.log("Drawer opened - membershipPlans:",j),console.log("Drawer opened - membershipPlans length:",j==null?void 0:j.length))},[v,j]),console.log("membershipPlans",j),console.log("membershipPlans length",j==null?void 0:j.length),console.log("membershipPlans type",typeof j),console.log("currentPlan",i),console.log("activeTab",y),e.jsx(js,{isOpen:v,onClose:p,title:S||W?"":`${o==null?void 0:o.first_name} ${o==null?void 0:o.last_name} • Edit membership`,children:W?e.jsx(Rs,{onClose:ae}):S?e.jsx(Is,{oldPlan:i,newPlan:m,onClose:ae,user:o}):m?e.jsx(Ps,{currentPlan:i,newPlan:m,onBack:te,onConfirm:_,user:o,loading:V}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mx-auto flex max-w-4xl items-center justify-center",children:[e.jsx("button",{className:`w-32 rounded-l-xl border px-2 py-1 ${y==="edit"?"bg-white":""}`,onClick:()=>x("edit"),children:"Edit"}),e.jsx("button",{className:`w-32 rounded-r-xl border px-2 py-1 ${y==="cancel"?"bg-white":""}`,onClick:()=>x("cancel"),children:"Cancel"})]}),y==="edit"&&e.jsx("div",{className:"mt-4",children:T?e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-xl bg-gray-50 px-4 py-12 text-center",children:[e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"})}),e.jsx("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"Loading Membership Plans..."}),e.jsx("p",{className:"text-sm text-gray-600",children:"Please wait while we fetch the available plans."})]}):(j==null?void 0:j.length)>0?e.jsx("div",{className:"grid grid-cols-1 gap-4 md:grid-cols-3",children:j.map(g=>e.jsx(ys,{...g,isCurrentPlan:i&&i.id===g.id,onSelect:()=>Q(g)},g.id||g.plan_name))}):e.jsxs("div",{className:"flex flex-col items-center justify-center rounded-xl bg-gray-50 px-4 py-12 text-center",children:[e.jsx("div",{className:"mb-4 rounded-full bg-blue-100 p-3",children:e.jsx("svg",{className:"h-6 w-6 text-blue-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})}),e.jsx("h3",{className:"mb-2 text-lg font-medium text-gray-900",children:"No Membership Plans Available"}),e.jsx("p",{className:"mb-6 text-sm text-gray-600",children:"There are no membership plans configured for this club."})]})}),y==="cancel"&&e.jsx(Ds,{currentPlan:i,user:o,onBack:te,onConfirm:R,loading:V})]})})};let ce=new Ce;const Ge=[{header:"Date",accessor:"date"},{header:"Sport",accessor:"sport"},{header:"Type",accessor:"type"},{header:"Event",accessor:"event"},{header:"Players",accessor:"players"},{header:"Bill",accessor:"bill"},{header:"Status",accessor:"status"},{header:"",accessor:"actions"}],Os=({user:v,club:p})=>{const{dispatch:i}=l.useContext(ye),{dispatch:t}=l.useContext(hs),[o,j]=l.useState([]),[C,T]=l.useState(10),[k,y]=l.useState(0),[x,m]=l.useState(0),[N,S]=l.useState(0),[se,W]=l.useState(!1),[K,V]=l.useState(!1),[G,Q]=l.useState(!1);l.useState(!1),l.useState([]),l.useState([]),l.useState("eq");const[_,R]=l.useState(!0),[te,ae]=l.useState(!1),[g,Te]=l.useState(!1);l.useState(),He();const we=l.useRef(null),Se=vs({id:je(),email:je(),role:je(),status:je()});Ns({resolver:Cs(Se)});function ne(){H(N-1,C)}function _e(){H(N+1,C)}async function H(n,L,d={},f=[]){R(!(g||te));try{ce.setTable("reservation");const I=await ce.callRestAPI({payload:{...d},page:n,limit:L,join:["booking|booking_id","user|user_id","buddy|buddy_id"],filter:[...f,`courtmatchup_booking.player_ids,cs,${v.id}`,`courtmatchup_booking.club_id,eq,${p==null?void 0:p.id}`,`courtmatchup_reservation.user_id,eq,${v.id}`]},"PAGINATE");ce.setTable("sports");const X=await ce.callRestAPI({},"GETALL"),re=I.list.map(U=>{const $=X.list.find(ee=>{var pe;return ee.id===((pe=U.booking)==null?void 0:pe.sport_id)});return{...U,sport:($==null?void 0:$.name)||"--"}});I&&R(!1);const{total:B,limit:A,num_pages:Z,page:O}=I;j(re),T(A),y(Z),S(O),m(B),W(O>1),V(O+1<=Z)}catch(I){R(!1),console.log("ERROR",I),xe(t,I.message)}}l.useEffect(()=>{i({type:"SETPATH",payload:{path:"users"}});const L=setTimeout(async()=>{await H(1,C)},700);return()=>{clearTimeout(L)}},[]);const ge=n=>{we.current&&!we.current.contains(n.target)&&Q(!1)};l.useEffect(()=>(document.addEventListener("mousedown",ge),()=>{document.removeEventListener("mousedown",ge)}),[]);const de=async n=>{if(window.confirm("Are you sure you want to delete this reservation?"))try{ce.setTable("reservation"),await ce.callRestAPI({id:n},"DELETE"),H(N,C)}catch(L){console.error("Error deleting reservation:",L),xe(t,L.message)}};return e.jsxs("div",{className:"h-screen w-full",children:[_?e.jsx(Ss,{}):e.jsxs("div",{className:"",children:[e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full table-auto border-separate border-spacing-y-2",children:[e.jsx("thead",{children:e.jsx("tr",{children:Ge.map((n,L)=>e.jsx("th",{scope:"col",className:"whitespace-nowrap px-6 py-4 text-left text-sm font-medium text-gray-500",children:n.header},L))})}),e.jsx("tbody",{className:" ",children:o.map((n,L)=>e.jsx("tr",{className:"hover:bg-gray-40 rounded-xl bg-gray-100 px-4 py-3 text-gray-500",children:Ge.map((d,f)=>{var I,X,re,B,A,Z,O,U,$,ee;return d.accessor==""?e.jsx("td",{className:"whitespace-nowrap rounded-l-xl px-6 py-4",children:e.jsxs("div",{className:"flex items-center  gap-3",children:[e.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.87776 9.05723L17.5007 3.70443M7.47619 9.05227L3.20388 4.53065C2.70178 3.99926 3.07851 3.125 3.8096 3.125H17.0881C17.732 3.125 18.1326 3.82416 17.807 4.3797L10.9883 16.0142C10.6164 16.6488 9.66476 16.5255 9.46676 15.8172L7.67304 9.40025C7.63664 9.27 7.56907 9.15057 7.47619 9.05227Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"square","stroke-linejoin":"round"})})}),e.jsx("button",{className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.041 5.20951L13.5768 2.67377C13.9022 2.34833 14.4298 2.34833 14.7553 2.67376L17.3268 5.24525C17.6522 5.57069 17.6522 6.09833 17.3268 6.42377L14.791 8.95951M11.041 5.20951L2.53509 13.7154C2.37881 13.8717 2.29102 14.0837 2.29102 14.3047V17.7095H5.69584C5.91685 17.7095 6.12881 17.6217 6.28509 17.4654L14.791 8.95951M11.041 5.20951L14.791 8.95951",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})}),e.jsx("button",{onClick:()=>de(n.id),className:"rounded-full p-2 hover:bg-gray-100",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z",fill:"#868C98"})})})]})},f):d.mapping&&d.accessor==="status"?e.jsx("td",{className:"inline-block whitespace-nowrap px-6 py-5 text-sm",children:n[d.accessor]===1?e.jsx("span",{className:"rounded-md bg-[#D1FAE5] px-3 py-1 text-[#065F46]",children:d.mapping[n[d.accessor]]}):e.jsx("span",{className:"rounded-md bg-[#F4F4F4] px-3 py-1 text-[#393939]",children:d.mapping[n[d.accessor]]})},f):d.mapping?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:d.mapping[n[d.accessor]]},f):d.accessor==="date"?e.jsxs("td",{className:"whitespace-nowrap rounded-l-xl px-6 py-4",children:[xs((I=n==null?void 0:n.booking)==null?void 0:I.date)," "," | "," ",Ve((X=n==null?void 0:n.booking)==null?void 0:X.start_time)," "," - "," ",Ve((re=n==null?void 0:n.booking)==null?void 0:re.end_time)]},f):d.accessor==="players"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:(B=n==null?void 0:n.booking)!=null&&B.player_ids?`${JSON.parse((A=n==null?void 0:n.booking)==null?void 0:A.player_ids).length} players`:"0 players"},f):d.accessor==="type"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:((Z=n==null?void 0:n.booking)==null?void 0:Z.type)||"--"},f):d.accessor==="event"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:((O=n==null?void 0:n.booking)==null?void 0:O.receipt_id)||"--"},f):d.accessor==="bill"?e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:he((U=n==null?void 0:n.booking)==null?void 0:U.price)},f):d.accessor==="status"?e.jsxs("td",{className:"whitespace-nowrap rounded-r-xl px-6 py-4",children:[(($=n==null?void 0:n.booking)==null?void 0:$.status)==1&&e.jsxs("div",{className:"flex items-center justify-center gap-1 rounded-lg border px-2 py-1 text-sm",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.00065 1.33398C4.31875 1.33398 1.33398 4.31875 1.33398 8.00065C1.33398 11.6825 4.31875 14.6673 8.00065 14.6673C11.6825 14.6673 14.6673 11.6825 14.6673 8.00065C14.6673 4.31875 11.6825 1.33398 8.00065 1.33398ZM10.3876 6.6506C10.5625 6.43688 10.531 6.12187 10.3173 5.94701C10.1035 5.77214 9.78854 5.80364 9.61367 6.01737L6.96353 9.25643L6.02087 8.31376C5.82561 8.1185 5.50903 8.1185 5.31376 8.31376C5.1185 8.50903 5.1185 8.82561 5.31376 9.02087L6.6471 10.3542C6.74699 10.4541 6.88447 10.5071 7.02556 10.5C7.16665 10.493 7.29818 10.4266 7.38763 10.3173L10.3876 6.6506Z",fill:"#2D9F75"})})}),e.jsx("span",{children:"Paid"})]}),((ee=n==null?void 0:n.booking)==null?void 0:ee.status)==0&&e.jsxs("div",{className:"flex items-center justify-center gap-1 rounded-lg border px-2 py-1 text-sm",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5 4H4V3H12V4H11V5C11 5.80771 10.5919 6.45734 10.078 6.98834C9.7266 7.3515 9.299 7.686 8.86545 8C9.299 8.314 9.7266 8.6485 10.078 9.01165C10.5919 9.54265 11 10.1923 11 11V12H12V13H4V12H5V11C5 10.1923 5.40808 9.54265 5.92195 9.01165C6.27341 8.6485 6.70101 8.314 7.13455 8C6.70101 7.686 6.27341 7.3515 5.92195 6.98834C5.40808 6.45734 5 5.80771 5 5V4ZM6 4V5C6 5.34257 6.13013 5.6675 6.38565 6H9.61435C9.86985 5.6675 10 5.34257 10 5V4H6ZM8 8.61095C7.4774 8.9801 7.004 9.3315 6.64055 9.7071C6.54504 9.8058 6.46003 9.9032 6.38565 10H9.61435C9.53995 9.9032 9.45495 9.8058 9.35945 9.7071C8.996 9.3315 8.5226 8.9801 8 8.61095Z",fill:"#162664"})})}),e.jsx("span",{children:"Reserved"})]})]},f):e.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:d.accessor==="sport"?n.sport:n[d.accessor]},f)})},L))})]})}),_&&e.jsx("div",{className:"px-6 py-4",children:e.jsx("p",{className:"text-gray-500",children:"Loading..."})}),!_&&o.length===0&&e.jsx("div",{className:"w-full px-6 py-4 text-center",children:e.jsx("p",{className:"text-gray-500",children:"No data available"})})]}),e.jsx(ws,{currentPage:N,pageCount:k,pageSize:C,canPreviousPage:se,canNextPage:K,updatePageSize:n=>{T(n),H(1,n)},previousPage:ne,nextPage:_e,gotoPage:n=>H(n,C)})]})};let u=new Ce;const Vs=({sports:v,club:p})=>{const{dispatch:i}=l.useContext(ye),[t,o]=l.useState({}),[j,C]=l.useState(!0),[T,k]=l.useState("personal"),y=us(),[x,m]=l.useState(null),[N,S]=l.useState("");l.useState([]),l.useState(null),l.useState(null);const[se,W]=l.useState(!1),[K,V]=l.useState(null),[G,Q]=l.useState(!1),[_,R]=l.useState(!1),[te,ae]=c.useState(!1),[g,Te]=c.useState(null),[we,Se]=c.useState([]),[ne,_e]=c.useState([]),H=He(),[ge,de]=c.useState(!1),[n,L]=c.useState(!1),[d,f]=c.useState(null);c.useState(!1);const[I,X]=c.useState(!1),[re,B]=c.useState(!1),[A,Z]=c.useState(null);c.useState([]);const[O,U]=c.useState(!1),[$,ee]=c.useState([]),pe=localStorage.getItem("role"),[E,Be]=c.useState(null);c.useState(null);const[Hs,Ze]=c.useState(!1),[Ue,Ie]=c.useState(!1),[$e,Ee]=c.useState(!1),[Je,ke]=c.useState(!1),[Ye,Pe]=c.useState(!1),[qe,De]=c.useState(!1),[Bs,ze]=c.useState([]),[We,Ke]=c.useState([]),[Ae,Qe]=c.useState([]),[J,Le]=c.useState(null),[Xe,Re]=c.useState(!1),P=parseInt(localStorage.getItem("user"));async function Y(){var s,a,r,w;try{C(!0),u.setTable("profile");const h=await u.callRestAPI({id:Number(y==null?void 0:y.id),join:"user_id|user"},"GET");console.log("profile result",h),u.setTable("user");const z=await u.callRestAPI({},"GETALL");Qe(z.list),u.setTable("user");const F=await u.callRestAPI({filter:[`id,eq,${(s=h==null?void 0:h.model)==null?void 0:s.user_id}`]},"GETALL");Z(h==null?void 0:h.model),o((a=F==null?void 0:F.list)==null?void 0:a[0]);const fe=await u.callRawAPI(`/v3/api/custom/courtmatchup/club/groups?type=0&user_id=${(r=h==null?void 0:h.model)==null?void 0:r.user_id}`,{},"GET");u.setTable("clubs");const me=await u.callRestAPI({id:(w=F==null?void 0:F.list)==null?void 0:w[0].club_id},"GET");Be(me==null?void 0:me.model);const D=fe.groups.map(le=>({...le,members:es(le.members||[])}));_e(D),h.error||C(!1)}catch(h){C(!1),console.log("error",h),xe(i,h.message)}}const es=s=>s.filter(a=>a.guardian===P?parseInt(P)===parseInt(a.guardian):!0),ss=async()=>{try{const s=await u.callRawAPI("/v3/api/custom/courtmatchup/user/groups/pending-invites",{},"GET");ze(s.pending_invites)}catch(s){console.error("Error fetching pending invites:",s)}},ts=async()=>{try{const s=await u.callRawAPI("/v3/api/custom/courtmatchup/user/groups/sent-invites",{},"GET");console.log("sent invites",s),!s.error&&s.invites&&Ke(s.invites)}catch(s){console.error("Error fetching sent invites:",s)}},as=async()=>{try{u.setTable("stripe_price");const s=await u.callRestAPI({},"GETALL"),a=await u.getCustomerStripeSubscription();Te(a==null?void 0:a.customer),Se(s.list)}catch(s){console.log(s)}},Fe=async s=>{Re(!0);try{u.setTable("stripe_subscription");const a=await u.callRestAPI({filter:[`user_id,eq,${s}`]},"GETALL");if(console.log("stripe_subscription result",a),a.list&&Array.isArray(a.list)){const r=[...a.list].sort((w,h)=>{const z=new Date(w.create_at);return new Date(h.create_at)-z});return Le(r),r}else return Le([]),[]}catch(a){return console.error("Error fetching user subscription:",a),Le([]),[]}finally{Re(!1)}},ns=()=>{if(!J||!Array.isArray(J)||J.length===0)return"None";const s=J.find(a=>a.status==="active");if(!s)return"None";try{const r=JSON.parse(s.object).plan;return(r==null?void 0:r.nickname)||"Active Plan"}catch(a){return console.error("Error parsing subscription object:",a),"Active Plan"}},ie=()=>{var a,r;if(!J||!Array.isArray(J)||J.length===0)return null;const s=J.find(w=>w.status==="active");if(!s)return null;try{const w=JSON.parse(s.object);return{...s,planName:((a=w.plan)==null?void 0:a.nickname)||"Active Plan",planAmount:((r=w.plan)==null?void 0:r.amount)||0,currentPeriodEnd:w.current_period_end,currentPeriodStart:w.current_period_start,status:s.status,currency:w.currency||"usd"}}catch(w){return console.error("Error parsing subscription object:",w),s}};l.useEffect(function(){Y(),as(),ss(),ts(),i({type:"SETPATH",payload:{path:"users"}})},[p==null?void 0:p.id]),c.useEffect(()=>{t!=null&&t.id&&Fe(t.id)},[t==null?void 0:t.id]),console.log(t);const rs=({member:s})=>{const[a,r]=l.useState(null),[w,h]=l.useState(""),[z,F]=l.useState(!1),{dispatch:fe}=l.useContext(ye),me=async(D,le)=>{try{F(!0);const oe=new Ce;oe.setTable("users"),(await oe.callRestAPI({id:s.id,[D]:le},"PUT")).error||(b(fe,"Updated successfully",3e3,"success"),r(null),Y())}catch(oe){b(fe,oe==null?void 0:oe.message,3e3,"error")}finally{F(!1)}};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"text-sm font-medium uppercase text-gray-500",children:"MEMBER"}),e.jsxs("div",{className:"mt-2 flex items-center gap-3",children:[e.jsx("img",{src:(s==null?void 0:s.photo)||"/default-avatar.png",alt:"",className:"h-10 w-10 rounded-full object-cover"}),e.jsxs("span",{className:"text-lg",children:[s==null?void 0:s.first_name," ",s==null?void 0:s.last_name]})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-4 text-sm font-medium uppercase text-gray-500",children:"DETAILS"}),e.jsx("div",{className:"space-y-4",children:[{key:"first_name",label:"FIRST NAME"},{key:"last_name",label:"LAST NAME"},{key:"gender",label:"GENDER"},{key:"family_role",label:"FAMILY ROLE"},{key:"email",label:"EMAIL"},{key:"phone_number",label:"PHONE NUMBER"},{key:"date_of_birth",label:"DATE OF BIRTH"},{key:"address",label:"ADDRESS"},{key:"city",label:"CITY"},{key:"state",label:"STATE"},{key:"zip_code",label:"ZIP CODE"},{key:"alternate_phone_number",label:"ALTERNATE PHONE NUMBER"},{key:"age_group",label:"AGE GROUP"},{key:"bio",label:"ADDITIONAL INFO"}].map(D=>e.jsx("div",{children:a===D.key?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:D.label}),e.jsx("button",{onClick:()=>r(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("input",{type:"text",value:w,onChange:le=>h(le.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),e.jsx(M,{loading:z,onClick:()=>me(D.key,w),className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"})]}):e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("p",{className:"text-sm text-gray-500",children:D.label}),e.jsx("button",{onClick:()=>{r(D.key),h((s==null?void 0:s[D.key])||"")},className:"text-sm text-primaryBlue",children:"Edit"})]}),e.jsx("p",{className:"mt-1",children:(s==null?void 0:s[D.key])||"--"})]})},D.key))})]})]})})},q=async(s,a)=>{console.log(s,a);try{R(!0),s==="gender"?(u.setTable("profile"),(await u.callRestAPI({id:A==null?void 0:A.id,[s]:a},"PUT")).error||(b(i,"Profile updated successfully",3e3,"success"),Z({...A,[s]:a}),m(null),S(""))):(u.setTable("user"),(await u.callRestAPI({id:Number(y==null?void 0:y.id),[s]:a},"PUT")).error||(b(i,"Profile updated successfully",3e3,"success"),o({...t,[s]:a}),m(null),S("")))}catch(r){b(i,r==null?void 0:r.message,3e3,"error"),console.log(r)}finally{R(!1)}},is=async s=>{try{R(!0);let a=new FormData;a.append("file",s);let r=await u.uploadImage(a);q("photo",r==null?void 0:r.url)}catch(a){b(i,a==null?void 0:a.message,3e3,"error"),console.log(a)}finally{R(!1)}},ls=()=>{q("photo",null),o({...t,photo:null})},os=async()=>{L(!0);try{u.setTable("user_groups"),(await u.callRestAPI({id:d.id},"DELETE")).error||(u.setTable("activity_logs"),await u.callRestAPI({user_id:P,action:"Deleted group",activity_type:ve.group,action_type:Ne.DELETE,data:JSON.stringify({group:d}),club_id:E==null?void 0:E.id,description:"Deleted group"},"POST"),b(i,"Group deleted successfully",3e3,"success"),Y())}catch(s){b(i,s==null?void 0:s.message,3e3,"error")}finally{L(!1),de(!1),f(null)}},cs=async()=>{X(!0);try{const s={id:d.id,members:JSON.stringify(d.members.filter(r=>r.id!==K.id).map(r=>Number(r.id)))};u.setTable("user_groups"),(await u.callRestAPI(s,"PUT")).error||(u.setTable("activity_logs"),await u.callRestAPI({user_id:P,action:"Removed member from group",activity_type:ve.group,action_type:Ne.DELETE,data:JSON.stringify(s),club_id:E==null?void 0:E.id,description:"Removed member from group"},"POST"),b(i,"Member removed from group successfully",3e3,"success"),Y())}catch(s){b(i,s==null?void 0:s.message,3e3,"error")}finally{X(!1),B(!1),f(null),V(null)}},ds=async()=>{var s,a,r,w;U(!0);try{let h;pe=="admin"?h=await u.callRawAPI(`/v3/api/custom/courtmatchup/admin/profile/${E==null?void 0:E.user_id}`,{},"GET"):h=await u.callRawAPI(`/v3/api/custom/courtmatchup/club/profile/${E==null?void 0:E.user_id}`,{},"GET"),console.log("view model response",h),console.log("membership_settings",(a=(s=h==null?void 0:h.model)==null?void 0:s.club)==null?void 0:a.membership_settings);const z=JSON.parse((w=(r=h==null?void 0:h.model)==null?void 0:r.club)==null?void 0:w.membership_settings)||[];console.log("parsed membership plans",z),ee(z)}catch(h){console.log(h)}finally{U(!1)}};c.useEffect(()=>{if(pe=="admin")E!=null&&E.user_id&&ds();else{console.log("club membership_settings",p==null?void 0:p.membership_settings);const s=p!=null&&p.membership_settings?JSON.parse(p.membership_settings):[];console.log("parsed club membership plans",s),ee(s)}},[E==null?void 0:E.user_id]);const ps=s=>{const a=ne.find(r=>r.id===s||r.group_id===s);if(a&&a.group_owner_id&&parseInt(a.group_owner_id)!==parseInt(P)){b(i,"Only the group owner can add members",3e3,"error");return}f(a),Ze(!0)},ms=s=>{const a=ne.flatMap(r=>r.members).find(r=>r.id===s);a&&(V(a),W(!0))};return e.jsxs(gs,{isLoading:_||I||O||Xe,children:[e.jsxs("div",{className:"w-full  p-4",children:[j?e.jsx(fs,{}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[e.jsxs("h1",{className:"text-2xl font-medium",children:[t==null?void 0:t.first_name,"'s profile"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-gray-600",children:["Current plan: ",ns()]}),e.jsx("button",{className:"text-primaryBlue underline",onClick:()=>ae(!0),children:"Edit"}),e.jsx("button",{className:"text-red-600 hover:underline",onClick:()=>Q(!0),children:"Delete"})]})]}),ie()&&e.jsx("div",{className:"mb-6 rounded-lg bg-blue-50 p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-medium text-blue-900",children:"Active Membership"}),e.jsxs("p",{className:"text-sm text-blue-700",children:[ie().planName," -"," ",he(ie().planAmount/100)]}),e.jsxs("p",{className:"text-xs text-blue-600",children:["Valid until:"," ",new Date(ie().currentPeriodEnd*1e3).toLocaleDateString()]})]}),e.jsx("div",{className:"text-right",children:e.jsx("span",{className:"inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800",children:ie().status})})]})}),e.jsxs("div",{className:"mb-6 flex gap-4 border-b",children:[e.jsx("button",{className:`px-1 pb-2 ${T==="personal"?"border-b-2 border-primaryBlue text-primaryBlue":"text-gray-600"}`,onClick:()=>k("personal"),children:"Personal information"}),e.jsx("button",{className:`px-1 pb-2 ${T==="reservations"?"border-b-2 border-primaryBlue text-primaryBlue":"text-gray-600"}`,onClick:()=>k("reservations"),children:"Reservations"})]}),T==="personal"&&e.jsxs("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"flex items-end gap-4",children:[e.jsx("img",{src:(t==null?void 0:t.photo)||"/default-avatar.png",alt:"Profile",className:"h-24 w-24 rounded-full object-cover"}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1",children:"Upload Image"}),e.jsx("p",{className:"my-2 text-sm text-gray-600",children:"Min 400x400px, PNG or JPEG"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{disabled:!(t!=null&&t.photo),className:"rounded-lg border border-red-600 px-2 py-1 text-red-600 disabled:opacity-50",onClick:ls,children:"Remove"}),e.jsxs("label",{className:"cursor-pointer rounded-lg border border-gray-300 px-2 py-1 text-gray-500",children:[e.jsx("input",{type:"file",accept:".jpg,.jpeg,.png",className:"hidden",onChange:s=>{const a=s.target.files[0];a&&is(a)}}),"Change Photo"]})]})]})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:x==="first_name"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"First Name"}),e.jsx("button",{onClick:()=>m(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("input",{type:"text",value:N,onChange:s=>S(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),e.jsx(M,{loading:_,onClick:()=>{q(x,N)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"})]}):e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"First name"}),e.jsx("div",{children:t==null?void 0:t.first_name})]}),e.jsx(M,{loading:_,onClick:()=>{m("first_name"),S((t==null?void 0:t.first_name)||"")},className:"text-primaryBlue underline",children:"Edit"})]})}),e.jsx("div",{children:x==="last_name"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"Last Name"}),e.jsx("button",{onClick:()=>m(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("input",{type:"text",value:N,onChange:s=>S(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),e.jsx(M,{loading:_,onClick:()=>{q(x,N)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"})]}):e.jsxs("div",{className:"flex items-center justify-between py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Last name"}),e.jsx("div",{children:t==null?void 0:t.last_name})]}),e.jsx(M,{loading:_,onClick:()=>{m("last_name"),S((t==null?void 0:t.last_name)||"")},className:"text-primaryBlue underline",children:"Edit"})]})}),e.jsx("div",{children:x==="gender"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"Gender"}),e.jsx("button",{onClick:()=>m(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsxs("select",{value:N,onChange:s=>S(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2",children:[e.jsx("option",{disabled:!0,value:"",children:"Select Gender"}),e.jsx("option",{value:"male",children:"Male"}),e.jsx("option",{value:"female",children:"Female"})]}),e.jsx(M,{loading:_,onClick:()=>{q(x,N)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"})]}):e.jsxs("div",{className:"flex items-center justify-between  py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Gender"}),e.jsx("div",{children:(A==null?void 0:A.gender)||"(gender)"})]}),e.jsx(M,{loading:_,onClick:()=>{m("gender"),S((A==null?void 0:A.gender)||"")},className:"text-primaryBlue underline",children:"Edit"})]})}),e.jsx("div",{children:x==="email"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"Email"}),e.jsx("button",{onClick:()=>m(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("input",{type:"email",value:N,onChange:s=>S(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),e.jsx(M,{loading:_,onClick:()=>{q(x,N)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Your email is not shared with other users."})]}):e.jsxs("div",{className:"flex items-start justify-between border-t py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Email"}),e.jsx("div",{children:(t==null?void 0:t.email)||"<EMAIL>"}),e.jsxs("p",{className:"flex items-center gap-1 text-sm text-gray-500",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),"Your email is not shared with other users."]})]}),e.jsx(M,{loading:_,onClick:()=>{m("email"),S((t==null?void 0:t.email)||"")},className:"text-primaryBlue underline",children:"Edit"})]})}),e.jsx("div",{children:x==="phone"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"Phone Number"}),e.jsx("button",{onClick:()=>m(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("input",{type:"tel",value:N,onChange:s=>S(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2"}),e.jsx(M,{loading:_,onClick:()=>{q(x,N)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Your phone number is not shared with other users."})]}):e.jsxs("div",{className:"flex items-start justify-between border-t py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Phone number"}),e.jsx("div",{children:e.jsx(Ts,{phoneNumber:t==null?void 0:t.phone,format:"US"})}),e.jsxs("p",{className:"flex items-center gap-1 text-sm text-gray-500",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),"Your phone number is not shared with other users."]})]}),e.jsx(M,{loading:_,onClick:()=>{m("phone"),S((t==null?void 0:t.phone)||"")},className:"text-primaryBlue underline",children:"Edit"})]})}),e.jsx("div",{children:x==="bio"?e.jsxs("div",{children:[e.jsxs("label",{className:"flex justify-between",children:[e.jsx("span",{children:"Bio"}),e.jsx("button",{onClick:()=>m(null),className:"text-gray-500 underline hover:underline",children:"Cancel"})]}),e.jsx("textarea",{value:N,onChange:s=>S(s.target.value),className:"mt-1 w-full rounded-xl border border-gray-300 p-2",rows:"4"}),e.jsx(M,{loading:_,onClick:()=>{q(x,N)},className:"mt-2 rounded-xl bg-blue-900 px-6 py-2 text-white",children:"Save"})]}):e.jsxs("div",{className:"flex items-start justify-between border-t py-2",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-600",children:"Bio"}),e.jsx("div",{className:"max-w-lg",children:(t==null?void 0:t.bio)||"--"})]}),e.jsx(M,{loading:_,onClick:()=>{m("bio"),S((t==null?void 0:t.bio)||"")},className:"text-primaryBlue underline",children:"Edit"})]})})]})]}),e.jsx("div",{className:"h-fit space-y-4 rounded-lg bg-white p-5",children:e.jsx("div",{className:"grid grid-cols-1 gap-4",children:ne.length>0?ne.map(s=>e.jsx(_s,{group:s,currentUserId:P,sentInvites:We,onAddExistingUser:()=>{if(s&&s.group_owner_id&&parseInt(s.group_owner_id)!==parseInt(P)){b(i,"Only the group owner can add members",3e3,"error");return}ke(!0),f(s)},onAddMember:ps,onEditName:()=>{if(s&&s.group_owner_id&&parseInt(s.group_owner_id)!==parseInt(P)){b(i,"Only the group owner can edit the group name",3e3,"error");return}Pe(!0),f(s)},onDeleteGroup:()=>{if(s&&s.group_owner_id&&parseInt(s.group_owner_id)!==parseInt(P)){b(i,"Only the group owner can delete the group",3e3,"error");return}de(!0),f(s)},onViewProfile:ms,onRemoveMember:(a,r)=>{if(a&&a.group_owner_id&&parseInt(a.group_owner_id)!==parseInt(P)){b(i,"Only the group owner can remove members",3e3,"error");return}B(!0),V(r),f(a)},onAddFamilyMember:()=>{if(s&&s.group_owner_id&&parseInt(s.group_owner_id)!==parseInt(P)){b(i,"Only the group owner can add family members",3e3,"error");return}Ee(!0),f(s)},onInviteToCourtmatch:()=>{if(s&&s.group_owner_id&&parseInt(s.group_owner_id)!==parseInt(P)){b(i,"Only the group owner can invite to CourtMatch",3e3,"error");return}De(!0),f(s)}},s.id)):e.jsx("div",{className:"text-center text-gray-600",children:"No groups found"})})})]}),T==="reservations"&&e.jsx(Os,{user:t,club:p})]}),e.jsx(be,{isOpen:se,onClose:()=>W(!1),title:"Profile details",showFooter:!1,children:e.jsx(rs,{member:K})}),e.jsx(Me,{isOpen:G,onClose:()=>Q(!1),onDelete:async()=>{try{u.setTable("user"),(await u.callRestAPI({id:Number(y==null?void 0:y.id)},"DELETE")).error||(b(i,"Profile deleted successfully",3e3,"success"),Q(!1),b(i,"Profile deleted successfully",3e3,"success"),H("/club/users"))}catch(s){b(i,s==null?void 0:s.message,3e3,"error"),console.log(s)}},title:"Delete profile",message:"Are you sure you want to delete this profile?"}),e.jsx(Fs,{isOpen:te,onClose:()=>ae(!1),currentPlan:ie(),user:t,membershipPlans:$,isLoadingMembershipPlans:O,onSuccess:()=>{t!=null&&t.id&&Fe(t.id)}})]}),e.jsx(Me,{isOpen:ge,onClose:()=>de(!1),onDelete:os,loading:n,message:"Are you sure you want to delete this group?",title:"Delete group"}),e.jsx(Me,{isOpen:re,onClose:()=>B(!1),onDelete:cs,loading:I,message:"Are you sure you want to remove this member from the group?",title:"Remove from group"}),e.jsx(be,{isOpen:$e,onClose:()=>Ee(!1),title:"Add family member",showFooter:!1,children:e.jsx(Es,{users:Ae,user:t,fetchData:Y,group:d,onClose:()=>{Ee(!1),f(null)}})}),e.jsx(be,{isOpen:Ue,onClose:()=>Ie(!1),title:"Create group",showFooter:!1,children:e.jsx(ks,{users:Ae,fetchData:Y,onClose:()=>Ie(!1)})}),e.jsx(be,{isOpen:Je,onClose:()=>{ke(!1),f(null)},title:`Add user to ${d==null?void 0:d.group_name}`,showFooter:!1,children:e.jsx(As,{users:Ae,group:d,fetchData:Y,onClose:()=>{ke(!1),f(null)}})}),Ye&&e.jsx(Ls,{title:`Edit ${d==null?void 0:d.group_name}`,group:d,onClose:s=>{Pe(!1),f(null),s&&Y()}}),qe&&e.jsx(Ms,{title:"Invite a friend to Court Matchup",onClose:()=>{De(!1),f(null)},group:d})]})},Gs=Vs;function Ot(){const{club:v,sports:p}=bs();return e.jsx(Gs,{club:v,sports:p})}export{Ot as default};
