import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{h as Ce,f as se,M as ge,G as Ne,d as V,a as qe,af as Ze,at as Ye,au as We,ah as je,R as Ke,A as Qe,u as Xe,N as Ve,t as Te}from"./index-08a5dc5b.js";import{r as a,b as ve,f as es}from"./vendor-851db8c1.js";import{A as ss}from"./AuthLayout-3236f682.js";import{u as ts,b as rs}from"./react-hook-form-687afde5.js";import{u as ls,G as as,M as ns}from"./@react-google-maps/api-bec1613d.js";import{u as is,g as os,a as cs}from"./use-places-autocomplete-4cb4aca6.js";import{a as ye,q as ds}from"./@headlessui/react-a5400090.js";import{S as ms}from"./SplashScreenPagePreview-0dbde401.js";import{B as ps}from"./BottomDrawer-eee99403.js";import{I as xs}from"./ImageCropModal-266718bc.js";import{I as hs}from"./InformationCircleIcon-d35f3488.js";import{P as us}from"./PencilIcon-35185602.js";import{T as gs}from"./TrashIcon-aaaccaf2.js";import{P as Fe}from"./PlusIcon-7e8d14d7.js";import"./lodash-91d5d207.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./ContactModal-541f1b35.js";import"./react-image-crop-1f5038af.js";const fs=["places"],ys="AIzaSyC6zItKyKbnIcdpgwNoRIByQEvezUbdFAA",bs=({onNext:R,register:u,errors:w,setValue:N,defaultValues:t,isSubmitting:r})=>{const{isLoaded:p,loadError:v}=ls({googleMapsApiKey:ys,libraries:fs}),[f,C]=a.useState(()=>{if(t!=null&&t.club_location)try{const U=JSON.parse(t.club_location);return{lat:U.lat,lng:U.lng}}catch{return null}return null}),[d,x]=a.useState(null),G=a.useCallback(U=>{x(U)},[]),M=f||{lat:51.5074,lng:-.1278};return v?e.jsx("div",{children:"Error loading maps"}):p?e.jsxs("div",{className:"w-full max-w-xl",children:[e.jsxs("div",{className:"mb-6 flex flex-col items-center justify-center gap-4",children:[e.jsx("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg"}),e.jsx("h2",{className:"text-2xl font-bold",children:"Location"})]}),e.jsx(js,{setSelectedLocation:C,selectedLocation:f,map:d,onLoad:G,defaultCenter:M,onNext:R,setValueProp:N,isLoaded:p,defaultValues:t,isSubmitting:r})]}):e.jsx(Ce,{})},js=({setSelectedLocation:R,selectedLocation:u,map:w,onLoad:N,defaultCenter:t,onNext:r,setValueProp:p,isLoaded:v,defaultValues:f,isSubmitting:C})=>{const d=ve.useMemo(()=>{try{if(f!=null&&f.club_location)return JSON.parse(f.club_location).address||""}catch(F){console.error("Error parsing club_location:",F)}return""},[f]),{ready:x,value:G,suggestions:{status:M,data:U},setValue:D,clearSuggestions:ee}=is({debounce:300,initOnMount:!0,cache:!1,googleMaps:v?window.google.maps:void 0,defaultValue:d});a.useEffect(()=>{d&&D(d,!1)},[d,D]);const Y=async F=>{D(F,!1),ee();try{const S=await os({address:F}),{lat:y,lng:c}=await cs(S[0]);R({lat:y,lng:c}),p("club_location",JSON.stringify({lat:y,lng:c,address:S[0].formatted_address})),w==null||w.panTo({lat:y,lng:c})}catch(S){console.error("Error: ",S)}},X=F=>{D(F.target.value)};return e.jsxs("form",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Club location"}),e.jsx(ye,{onChange:Y,children:e.jsxs("div",{className:"relative mt-1",children:[e.jsx("div",{className:"relative w-full cursor-default overflow-hidden rounded bg-white text-left focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm",children:e.jsx(ye.Input,{className:"mb-3 w-full rounded border-[1px] border-[#C6C6C6] bg-[#F8F8F8] p-2 px-4 py-2 leading-tight text-gray-700 focus:border-blue-500 focus:ring-1 focus:ring-blue-500",displayValue:F=>F||G,onChange:X,value:G,disabled:!x,placeholder:x?"Search location":"Loading..."})}),e.jsx(ds,{as:a.Fragment,leave:"transition ease-in duration-100",leaveFrom:"opacity-100",leaveTo:"opacity-0",afterLeave:()=>D(null),children:e.jsx(ye.Options,{className:"absolute z-[999] mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base ring-1 ring-black/5 focus:outline-none sm:text-sm",children:M==="OK"&&e.jsx(e.Fragment,{children:U.length===0&&G!==""?e.jsx("div",{className:"relative cursor-default select-none px-4 py-2 text-gray-700",children:"Nothing found."}):U.map(({place_id:F,description:S})=>e.jsx(ye.Option,{className:({active:y})=>`relative cursor-default select-none py-2 pl-10 pr-4 ${y?"bg-blue-600 text-white":"text-gray-900"}`,value:S,children:({selected:y,active:c})=>e.jsxs(e.Fragment,{children:[e.jsx("span",{className:`block truncate ${y?"font-medium":"font-normal"}`,children:S}),y?e.jsx("span",{className:`absolute inset-y-0 left-0 flex items-center pl-3 ${c?"text-white":"text-blue-600"}`,children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"1em",height:"1em",viewBox:"0 0 50 50",children:[e.jsx("path",{fill:"currentColor",d:"M25 42c-9.4 0-17-7.6-17-17S15.6 8 25 8s17 7.6 17 17s-7.6 17-17 17m0-32c-8.3 0-15 6.7-15 15s6.7 15 15 15s15-6.7 15-15s-6.7-15-15-15"}),e.jsx("path",{fill:"currentColor",d:"m23 32.4l-8.7-8.7l1.4-1.4l7.3 7.3l11.3-11.3l1.4 1.4z"})]})}):null]})},F))})})})]})}),e.jsxs("div",{className:"mt-3 flex items-center gap-2",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("span",{className:"text-xs text-gray-500",children:"Search and drop pin to the location"})]})]}),e.jsx("div",{className:"mt-4 h-[300px] w-full",children:e.jsx(as,{mapContainerClassName:"w-full h-full rounded-xl",center:u||t,zoom:13,onLoad:N,children:u&&e.jsx(ns,{position:u})})}),e.jsx(se,{onClick:r,loading:C,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Continue"})]})};let vs=new ge;function ws({onNext:R,register:u,errors:w,setValue:N,clubProfile:t,defaultValues:r}){var ke,Me,Ee,Le,Ie,He,Be;const[p,v]=a.useState(0),[f,C]=a.useState([]),[d,x]=a.useState(null),[G,M]=a.useState(null),[U,D]=a.useState(r==null?void 0:r.club_logo),[ee,Y]=a.useState(!1),[X,F]=a.useState(!1),[S,y]=a.useState(null),[c,_]=a.useState(null),[B,z]=a.useState(!1),[l,L]=a.useState(!1),[E,P]=a.useState(r==null?void 0:r.name),[W,K]=a.useState(E),[ie,oe]=a.useState(!1),[h,s]=a.useState(((ke=r==null?void 0:r.splash_screen)==null?void 0:ke.slideshow_delay)||6e3),[o,m]=a.useState(()=>{var i;if((i=r==null?void 0:r.splash_screen)!=null&&i.images){const b=new Array(9).fill(null);return r.splash_screen.images.forEach((j,T)=>{j&&j.url&&(b[T]={url:j.url,isDefault:!0,id:j.id||`default-${T}`,type:j.type||"image"})}),b}return new Array(9).fill(null)}),[k,A]=a.useState(!1),I=a.useRef(),J=a.useRef(),q=a.useRef(),le=a.useRef(),{dispatch:n}=a.useContext(Ne),[g,O]=a.useState(!1);a.useEffect(()=>{if(r!=null&&r.splash_screen)try{const i=r==null?void 0:r.splash_screen;I.current&&i.bio&&(I.current.value=i.bio)}catch(i){console.error("Error parsing splash screen data:",i)}},[r]),a.useEffect(()=>{const i=p*3,b=o.slice(i,i+3);C(b)},[p,o]);const Z=async i=>{try{let b=new FormData;b.append("file",i);const j=i.type.startsWith("video/");let T;return T=await vs.uploadImage(b),T.url}catch(b){return console.error("Upload error:",b),V(n,"Failed to upload file. Please try again.",3e3,"error"),null}},$=()=>o.slice(0,3).filter(b=>b!==null).length,te=i=>{const b=Math.floor(i/3),j=i%3;if(b===0)return!0;if(!o.slice(0,3)[j])return!1;const ne=$(),ae=b*3;return o.slice(ae,ae+3).filter(fe=>fe!==null).length<ne},de=(i,b)=>{const j=i.target.files[0];if(!j)return;const T=p*3+b;if(!te(T)){const ce=$();V(n,`You can only upload up to ${ce} image${ce!==1?"s":""} per slide based on your Slide 1 pattern`,3e3,"error");return}if(!["image/jpeg","image/png","image/gif","video/mp4","video/quicktime","video/x-m4v","application/pdf"].includes(j.type)){V(n,"Please upload a valid file type (JPEG, PNG, GIF, MP4, or PDF)",3e3,"error");return}if(j.size>50*1024*1024){V(n,"File size must be less than 50MB",3e3,"error");return}const ne=[...o],ae=URL.createObjectURL(j);let re="image";j.type.startsWith("video/")?re="video":j.type==="application/pdf"&&(re="pdf"),ne[T]={file:j,url:ae,id:Date.now(),isDefault:!1,type:re,previewUrl:ae},m(ne)},pe=i=>{i.preventDefault(),i.stopPropagation()},he=i=>{i.preventDefault(),i.stopPropagation()},ue=i=>{i.preventDefault(),i.stopPropagation()},H=(i,b)=>{i.preventDefault(),i.stopPropagation();const j=i.dataTransfer.files;if(j.length>0){const T=p*3+b;if(!te(T)){const ne=$();V(n,`You can only upload up to ${ne} image${ne!==1?"s":""} per slide based on your Slide 1 pattern`,3e3,"error");return}const Q={target:{files:[j[0]]}};de(Q,b)}},me=i=>{const b=p*3+i,j=[...o],T=j[b];if(T){!T.isDefault&&T.url&&URL.revokeObjectURL(T.url),j[b]=null;const Q=Math.floor(b/3),ne=b%3;if(Q===0)for(let ae=1;ae<3;ae++){const re=ae*3+ne;j[re]&&(!j[re].isDefault&&j[re].url&&URL.revokeObjectURL(j[re].url),j[re]=null)}else Q===1&&ne===2&&j[8]&&(!j[8].isDefault&&j[8].url&&URL.revokeObjectURL(j[8].url),j[8]=null);m(j)}},_e=()=>{v(i=>i+1)},Re=()=>{v(i=>Math.max(0,i-1))},Se=()=>f,Ge=async()=>{var i,b,j,T;try{A(!0);const Q=(i=I.current)==null?void 0:i.value;if(!(Q!=null&&Q.trim())){V(n,"Please enter a bio",3e3,"error");return}const ne=await Promise.all(o.map(async ce=>{if(!ce)return null;if(ce.isDefault)return ce;{const fe=await Z(ce.file);return fe?(ce.previewUrl&&URL.revokeObjectURL(ce.previewUrl),{url:fe,isDefault:!0,id:`default-${Date.now()}-${Math.random()}`,type:ce.file.type.startsWith("video/")?"video":"image"}):null}})),ae={bio:Q.trim(),images:ne,slideshow_delay:h,button_text:((b=J.current)==null?void 0:b.value)||"Let the club know you're interested",phone:((j=q.current)==null?void 0:j.value)||"",email:((T=le.current)==null?void 0:T.value)||""};N("splash_screen",JSON.stringify(ae)),N("club_logo",U);const re={splash_screen:JSON.stringify(ae),club_logo:U};ie&&(re.name=E),console.log("Final submission data:",re),await R(re),m(new Array(9).fill(null)),I.current.value="",v(0)}catch(Q){console.error("Submission failed:",Q),V(n,"Failed to submit. Please try again.",3e3,"error")}finally{A(!1)}},$e=i=>{const b=i.target.files[0];if(!b)return;if(b.size>2*1024*1024){alert("File size exceeds 2MB limit. Please choose a smaller file.");return}_(b.type);const j=new FileReader;j.onload=()=>{y(j.result),F(!0)},j.readAsDataURL(b)},Pe=async i=>{Y(!0),x(URL.createObjectURL(i));const b=c==="image/png",j=await Z(i);D(j),M(new File([i],`cropped_logo.${b?"png":"jpg"}`,{type:b?"image/png":"image/jpeg"})),qe(URL.createObjectURL(i)),Y(!1)};a.useEffect(()=>()=>{d&&URL.revokeObjectURL(d)},[d]);const De=()=>{K((r==null?void 0:r.name)||""),z(!0)},ze=()=>{W!==(r==null?void 0:r.name)&&oe(!0),P(W),K(W),z(!1)},Je=()=>{K(E),z(!1)};return e.jsxs("div",{className:"flex max-w-xl flex-col gap-4 rounded-lg bg-white",children:[ee&&e.jsx(Ce,{}),e.jsxs("div",{className:"flex flex-col items-center justify-center text-center",children:[e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_507_13438)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_507_13438)"}),e.jsxs("g",{filter:"url(#filter0_d_507_13438)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24.5 29.1875C24.5 27.9794 25.4794 27 26.6875 27H47.3125C48.5206 27 49.5 27.9794 49.5 29.1875V44.8125C49.5 46.0206 48.5206 47 47.3125 47H26.6875C25.4794 47 24.5 46.0206 24.5 44.8125V29.1875ZM39.1875 33.25C37.9794 33.25 37 34.2294 37 35.4375V38.5625C37 39.7706 37.9794 40.75 39.1875 40.75H42.3125C43.5206 40.75 44.5 39.7706 44.5 38.5625V35.4375C44.5 34.2294 43.5206 33.25 42.3125 33.25H39.1875ZM30.4375 33.5625C29.9197 33.5625 29.5 33.9822 29.5 34.5C29.5 35.0178 29.9197 35.4375 30.4375 35.4375H33.5625C34.0803 35.4375 34.5 35.0178 34.5 34.5C34.5 33.9822 34.0803 33.5625 33.5625 33.5625H30.4375ZM30.4375 38.5625C29.9197 38.5625 29.5 38.9822 29.5 39.5C29.5 40.0178 29.9197 40.4375 30.4375 40.4375H33.5625C34.0803 40.4375 34.5 40.0178 34.5 39.5C34.5 38.9822 34.0803 38.5625 33.5625 38.5625H30.4375Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_507_13438",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_507_13438"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_507_13438",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_507_13438",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_507_13438",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]}),e.jsx("p",{className:"text-2xl font-medium",children:"Description"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("p",{className:"mb-2 text-lg font-semibold",children:"Tell us about your club"}),e.jsxs("p",{className:"flex items-start gap-2 text-sm text-gray-500",children:[e.jsx("span",{children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 14C4.6862 14 2 11.3138 2 8C2 4.6862 4.6862 2 8 2C11.3138 2 14 4.6862 14 8C14 11.3138 11.3138 14 8 14ZM7.4 9.8V11H8.6V9.8H7.4ZM7.4 5V8.6H8.6V5H7.4Z",fill:"#868C98"})})}),e.jsx("span",{children:"This info will be visible to users who want to book your club. You can edit this content into your Club Panel later."})]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:"relative h-[100px] w-[100px] overflow-hidden rounded-lg bg-gray-100",children:e.jsx("img",{src:d||U||((Me=t==null?void 0:t.club)==null?void 0:Me.club_logo)||"/logo.png",alt:"Club logo",className:"h-full w-full object-cover"})}),e.jsxs("div",{className:"w-full space-y-1",children:[e.jsx("span",{children:"Club logo"}),e.jsx("div",{className:"flex justify-between py-1 text-xs text-gray-500",children:e.jsx("span",{children:"Min 400x400px, PNG or JPEG"})}),e.jsx("p",{className:"mb-2 text-xs text-gray-500",children:"This logo will be displayed on your club portal and as a favicon in browser tabs. For best results, use a square image with a clear, recognizable design."}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(se,{onClick:()=>{x(null),M(null)},className:"rounded-md border border-red-500 bg-white px-2 py-1 text-xs text-red-500",children:"Remove"}),e.jsx("input",{type:"file",id:"logo-upload",className:"hidden",accept:"image/**",onChange:$e}),e.jsx(se,{onClick:()=>document.getElementById("logo-upload").click(),className:"rounded-md border border-gray-400 bg-white px-2 py-1 text-xs text-gray-600",children:"Change Logo"})]})]})]}),e.jsx("div",{className:"mt-4 flex items-center gap-4 border-y border-gray-200 py-3",children:e.jsxs("div",{className:" w-full",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Club name"}),B?e.jsxs("div",{className:"space-y-2",children:[e.jsx("input",{type:"text",value:W,onChange:i=>K(i.target.value),className:"w-full rounded-lg border border-gray-300 px-3 py-2 text-lg font-medium focus:border-blue-500 focus:outline-none",placeholder:"Enter club name"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(se,{onClick:ze,loading:l,className:"rounded-lg bg-primaryBlue px-4 py-1 text-sm text-white hover:bg-primaryBlue/80",children:"Save"}),e.jsx("button",{onClick:Je,className:"text-sm text-primaryBlue hover:underline",children:"Cancel"})]})]}):e.jsxs("div",{children:[e.jsx("p",{className:"text-lg font-medium",children:E||"Club name"}),e.jsx("button",{onClick:De,className:"text-sm text-primaryBlue hover:underline",children:"Edit"})]})]})}),e.jsx("div",{children:e.jsx("button",{className:"underline",onClick:()=>O(!0),children:"Page preview"})}),e.jsxs("div",{className:"mb-4 rounded-xl bg-gray-50 p-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Upload images"}),e.jsx("p",{className:"mb-2 text-sm text-gray-500",children:"JPEG, PNG, PDF, and MP4 formats, up to 50 MB. Drag and drop files or click to browse."}),p>0&&e.jsx("div",{className:"mb-2 rounded-lg bg-blue-50 p-2",children:e.jsxs("p",{className:"text-xs text-blue-700",children:[e.jsx("strong",{children:"Note:"})," You can upload up to"," ",$()," image",$()!==1?"s":""," per slide based on your Slide 1 pattern."]})}),e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"grid grid-rows-[1fr_1fr] gap-4",children:[e.jsx("div",{className:"h-[100px] max-h-[100px]",children:(()=>{const i=Se()[0],b=p*3+0,j=!te(b);return e.jsxs("div",{className:`relative h-full w-full rounded-xl border-2 border-dashed transition-colors ${j?"cursor-not-allowed border-gray-200 bg-gray-100":"border-gray-300 bg-white hover:border-gray-400"}`,onDragOver:j?void 0:pe,onDragEnter:j?void 0:he,onDragLeave:j?void 0:ue,onDrop:j?void 0:T=>H(T,0),children:[e.jsx("input",{type:"file",className:"hidden",accept:"image/*,video/*,application/pdf",onChange:T=>de(T,0),id:"file-input-0",disabled:j}),e.jsx("label",{htmlFor:j?void 0:"file-input-0",className:`absolute inset-0 ${j?"cursor-not-allowed":"cursor-pointer"}`,children:i&&i.url?e.jsxs("div",{className:"relative h-full w-full",children:[i.type==="video"?e.jsxs("video",{src:i.url,className:"h-full w-full rounded-lg object-cover",controls:!0,controlsList:"nodownload",preload:"metadata",playsInline:!0,children:[e.jsx("source",{src:i.url,type:"video/mp4"}),"Your browser does not support the video tag."]}):i.type==="pdf"?e.jsx("div",{className:"flex h-full w-full items-center justify-center rounded-lg bg-red-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-2",children:e.jsx("path",{d:"M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z",fill:"#DC2626"})}),e.jsx("p",{className:"text-xs font-medium text-red-600",children:"PDF"})]})}):e.jsx("img",{src:i.url,alt:"Upload 1",className:"h-full w-full rounded-lg object-cover"}),e.jsx("button",{onClick:T=>{T.preventDefault(),T.stopPropagation(),me(0)},className:"absolute right-2 top-2 rounded-full bg-white p-1.5 shadow-md hover:bg-gray-100",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"black"})})}),i.type==="video"&&e.jsx("div",{className:"absolute left-2 top-2 rounded-full bg-white/80 p-1.5",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5V7.5L16 12L10 16.5Z",fill:"#176448"})})})]}):e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:j?e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-1",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z",fill:"#9CA3AF"})}),e.jsx("p",{className:"text-xs text-gray-400",children:"Disabled"})]}):e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.9998 12.5274L15.8185 16.3452L14.545 17.6187L12.8998 15.9735V21H11.0998V15.9717L9.45461 17.6187L8.18111 16.3452L11.9998 12.5274ZM11.9998 3C13.5451 3.00007 15.0365 3.568 16.1904 4.59581C17.3443 5.62361 18.0803 7.03962 18.2584 8.5746C19.3782 8.87998 20.3552 9.56919 21.0184 10.5218C21.6816 11.4744 21.989 12.6297 21.8869 13.786C21.7847 14.9422 21.2794 16.0257 20.4594 16.8472C19.6394 17.6687 18.5567 18.1759 17.4007 18.2802V16.4676C17.8149 16.4085 18.2131 16.2674 18.5721 16.0527C18.9312 15.8379 19.2439 15.5539 19.4919 15.217C19.74 14.8801 19.9184 14.4972 20.0169 14.0906C20.1153 13.6839 20.1318 13.2618 20.0653 12.8488C19.9989 12.4357 19.8508 12.0401 19.6298 11.6849C19.4087 11.3297 19.1191 11.0221 18.7779 10.78C18.4367 10.538 18.0506 10.3663 17.6424 10.2751C17.2341 10.1838 16.8117 10.1748 16.3999 10.2486C16.5409 9.5924 16.5332 8.91297 16.3776 8.2601C16.222 7.60722 15.9223 6.99743 15.5004 6.47538C15.0786 5.95333 14.5454 5.53225 13.9397 5.24298C13.3341 4.9537 12.6714 4.80357 12.0003 4.80357C11.3291 4.80357 10.6664 4.9537 10.0608 5.24298C9.45515 5.53225 8.92189 5.95333 8.50007 6.47538C8.07825 6.99743 7.77854 7.60722 7.62291 8.2601C7.46728 8.91297 7.45966 9.5924 7.60061 10.2486C6.7795 10.0944 5.93076 10.2727 5.24112 10.7443C4.55147 11.2159 4.0774 11.9421 3.92321 12.7632C3.76901 13.5843 3.94731 14.433 4.41889 15.1227C4.89047 15.8123 5.6167 16.2864 6.43781 16.4406L6.59981 16.4676V18.2802C5.44371 18.1761 4.36097 17.669 3.54083 16.8476C2.72068 16.0261 2.2153 14.9426 2.11301 13.7863C2.01073 12.6301 2.31804 11.4747 2.98124 10.522C3.64444 9.56934 4.62134 8.88005 5.74121 8.5746C5.91914 7.03954 6.65507 5.62342 7.80903 4.59558C8.96298 3.56774 10.4545 2.99988 11.9998 3Z",fill:"#525866"})})})})]},`${p}-0`)})()}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:[1,2].map(i=>{const b=Se()[i],j=p*3+i,T=!te(j);return e.jsxs("div",{className:`relative h-[100px] max-h-[100px] w-full rounded-xl border-2 border-dashed transition-colors ${T?"cursor-not-allowed border-gray-200 bg-gray-100":"border-gray-300 bg-white hover:border-gray-400"}`,onDragOver:T?void 0:pe,onDragEnter:T?void 0:he,onDragLeave:T?void 0:ue,onDrop:T?void 0:Q=>H(Q,i),children:[e.jsx("input",{type:"file",className:"hidden",accept:"image/*,video/*,application/pdf",onChange:Q=>de(Q,i),id:`file-input-${i}`,disabled:T}),e.jsx("label",{htmlFor:T?void 0:`file-input-${i}`,className:`absolute inset-0 ${T?"cursor-not-allowed":"cursor-pointer"}`,children:b&&b.url?e.jsxs("div",{className:"relative h-full w-full",children:[b.type==="video"?e.jsxs("video",{src:b.url,className:"h-full w-full rounded-lg object-cover",controls:!0,controlsList:"nodownload",preload:"metadata",playsInline:!0,children:[e.jsx("source",{src:b.url,type:"video/mp4"}),"Your browser does not support the video tag."]}):b.type==="pdf"?e.jsx("div",{className:"flex h-full w-full items-center justify-center rounded-lg bg-red-50",children:e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-2",children:e.jsx("path",{d:"M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z",fill:"#DC2626"})}),e.jsx("p",{className:"text-xs font-medium text-red-600",children:"PDF"})]})}):e.jsx("img",{src:b.url,alt:`Upload ${i+1}`,className:"h-full w-full rounded-lg object-cover"}),e.jsx("button",{onClick:Q=>{Q.preventDefault(),Q.stopPropagation(),me(i)},className:"absolute right-2 top-2 rounded-full bg-white p-1.5 shadow-md hover:bg-gray-100",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M5.68964 20.3144L4.94119 20.3627L5.68964 20.3144ZM18.3104 20.3144L19.0588 20.3627V20.3627L18.3104 20.3144ZM2.75 5C2.33579 5 2 5.33579 2 5.75C2 6.16421 2.33579 6.5 2.75 6.5V5ZM21.25 6.5C21.6642 6.5 22 6.16421 22 5.75C22 5.33579 21.6642 5 21.25 5V6.5ZM10.5 10.75C10.5 10.3358 10.1642 10 9.75 10C9.33579 10 9 10.3358 9 10.75H10.5ZM9 16.25C9 16.6642 9.33579 17 9.75 17C10.1642 17 10.5 16.6642 10.5 16.25H9ZM15 10.75C15 10.3358 14.6642 10 14.25 10C13.8358 10 13.5 10.3358 13.5 10.75H15ZM13.5 16.25C13.5 16.6642 13.8358 17 14.25 17C14.6642 17 15 16.6642 15 16.25H13.5ZM15.1477 5.93694C15.2509 6.33808 15.6598 6.57957 16.0609 6.47633C16.4621 6.37308 16.7036 5.9642 16.6003 5.56306L15.1477 5.93694ZM4.00156 5.79829L4.94119 20.3627L6.43808 20.2661L5.49844 5.70171L4.00156 5.79829ZM6.68756 22H17.3124V20.5H6.68756V22ZM19.0588 20.3627L19.9984 5.79829L18.5016 5.70171L17.5619 20.2661L19.0588 20.3627ZM19.25 5H4.75V6.5H19.25V5ZM2.75 6.5H4.75V5H2.75V6.5ZM19.25 6.5H21.25V5H19.25V6.5ZM17.3124 22C18.2352 22 18.9994 21.2835 19.0588 20.3627L17.5619 20.2661C17.5534 20.3976 17.4443 20.5 17.3124 20.5V22ZM4.94119 20.3627C5.0006 21.2835 5.76481 22 6.68756 22V20.5C6.55574 20.5 6.44657 20.3976 6.43808 20.2661L4.94119 20.3627ZM9 10.75V16.25H10.5V10.75H9ZM13.5 10.75V16.25H15V10.75H13.5ZM12 3.5C13.5134 3.5 14.7868 4.53504 15.1477 5.93694L16.6003 5.56306C16.0731 3.51451 14.2144 2 12 2V3.5ZM8.85237 5.93694C9.21319 4.53504 10.4867 3.5 12 3.5V2C9.78568 2 7.92697 3.51451 7.39971 5.56306L8.85237 5.93694Z",fill:"black"})})}),b.type==="video"&&e.jsx("div",{className:"absolute left-2 top-2 rounded-full bg-white/80 p-1.5",children:e.jsx("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM10 16.5V7.5L16 12L10 16.5Z",fill:"#176448"})})})]}):e.jsx("div",{className:"flex h-full w-full items-center justify-center",children:T?e.jsxs("div",{className:"text-center",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mx-auto mb-1",children:e.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z",fill:"#9CA3AF"})}),e.jsx("p",{className:"text-xs text-gray-400",children:"Disabled"})]}):e.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M11.9998 12.5274L15.8185 16.3452L14.545 17.6187L12.8998 15.9735V21H11.0998V15.9717L9.45461 17.6187L8.18111 16.3452L11.9998 12.5274ZM11.9998 3C13.5451 3.00007 15.0365 3.568 16.1904 4.59581C17.3443 5.62361 18.0803 7.03962 18.2584 8.5746C19.3782 8.87998 20.3552 9.56919 21.0184 10.5218C21.6816 11.4744 21.989 12.6297 21.8869 13.786C21.7847 14.9422 21.2794 16.0257 20.4594 16.8472C19.6394 17.6687 18.5567 18.1759 17.4007 18.2802V16.4676C17.8149 16.4085 18.2131 16.2674 18.5721 16.0527C18.9312 15.8379 19.2439 15.5539 19.4919 15.217C19.74 14.8801 19.9184 14.4972 20.0169 14.0906C20.1153 13.6839 20.1318 13.2618 20.0653 12.8488C19.9989 12.4357 19.8508 12.0401 19.6298 11.6849C19.4087 11.3297 19.1191 11.0221 18.7779 10.78C18.4367 10.538 18.0506 10.3663 17.6424 10.2751C17.2341 10.1838 16.8117 10.1748 16.3999 10.2486C16.5409 9.5924 16.5332 8.91297 16.3776 8.2601C16.222 7.60722 15.9223 6.99743 15.5004 6.47538C15.0786 5.95333 14.5454 5.53225 13.9397 5.24298C13.3341 4.9537 12.6714 4.80357 12.0003 4.80357C11.3291 4.80357 10.6664 4.9537 10.0608 5.24298C9.45515 5.53225 8.92189 5.95333 8.50007 6.47538C8.07825 6.99743 7.77854 7.60722 7.62291 8.2601C7.46728 8.91297 7.45966 9.5924 7.60061 10.2486C6.7795 10.0944 5.93076 10.2727 5.24112 10.7443C4.55147 11.2159 4.0774 11.9421 3.92321 12.7632C3.76901 13.5843 3.94731 14.433 4.41889 15.1227C4.89047 15.8123 5.6167 16.2864 6.43781 16.4406L6.59981 16.4676V18.2802C5.44371 18.1761 4.36097 17.669 3.54083 16.8476C2.72068 16.0261 2.2153 14.9426 2.11301 13.7863C2.01073 12.6301 2.31804 11.4747 2.98124 10.522C3.64444 9.56934 4.62134 8.88005 5.74121 8.5746C5.91914 7.03954 6.65507 5.62342 7.80903 4.59558C8.96298 3.56774 10.4545 2.99988 11.9998 3Z",fill:"#525866"})})})})]},`${p}-${i}`)})})]},p),e.jsxs("div",{className:"mt-4 flex items-center justify-center gap-4",children:[e.jsx("button",{onClick:Re,disabled:p===0,className:`rounded-full bg-white p-2 shadow-md ${p===0?"cursor-not-allowed opacity-50":"hover:bg-gray-100"}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})}),e.jsxs("span",{className:"text-sm",children:["Slide ",p+1]}),e.jsx("button",{onClick:_e,disabled:p===2,className:`rounded-full bg-white p-2 shadow-md ${p===2?"cursor-not-allowed opacity-50":"hover:bg-gray-100"}`,children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})})]})]})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Slideshow Delay (seconds)"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"number",min:"1",max:"60",value:h/1e3,onChange:i=>s(Math.max(1e3,Math.min(6e4,i.target.value*1e3))),className:"w-24 rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"}),e.jsx("span",{className:"text-sm text-gray-500",children:"seconds"})]}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Choose between 1 and 60 seconds (default: 6 seconds)"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Bio"}),e.jsx("textarea",{ref:I,rows:4,className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-sm font-medium",children:"Button Text"}),e.jsx("input",{type:"text",ref:J,defaultValue:(r==null?void 0:r.button_text)||"Let the club know you're interested",onChange:i=>{const b={...JSON.parse((r==null?void 0:r.splash_screen)||"{}"),button_text:i.target.value};N("splash_screen",JSON.stringify(b))},className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter button text"})]}),e.jsxs("div",{className:"mb-4 space-y-4",children:[e.jsx("h3",{className:"text-sm font-medium",children:"Contact Information"}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm text-gray-600",children:"Phone Number"}),e.jsx("input",{type:"tel",ref:q,defaultValue:(r==null?void 0:r.phone)||"",onChange:i=>{const b={...JSON.parse((r==null?void 0:r.splash_screen)||"{}"),phone:i.target.value};N("splash_screen",JSON.stringify(b))},className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter phone number"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-1 block text-sm text-gray-600",children:"Email Address"}),e.jsx("input",{type:"email",ref:le,defaultValue:(r==null?void 0:r.email)||"",onChange:i=>{const b={...JSON.parse((r==null?void 0:r.splash_screen)||"{}"),email:i.target.value};N("splash_screen",JSON.stringify(b))},className:"w-full rounded-lg border border-gray-300 p-2 focus:border-blue-500 focus:outline-none",placeholder:"Enter email address"})]})]}),e.jsx(se,{onClick:Ge,loading:k,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Continue"}),e.jsx(ps,{isOpen:g,onClose:()=>O(!1),title:"Preview",children:e.jsx(ms,{clubName:(Ee=t==null?void 0:t.club)==null?void 0:Ee.name,image:t==null?void 0:t.name,description:(Le=I.current)==null?void 0:Le.value,imageList:o,clubLogo:U,slideshowDelay:h,buttonText:((Ie=J.current)==null?void 0:Ie.value)||"Let the club know you're interested",phone:((He=q.current)==null?void 0:He.value)||"",email:((Be=le.current)==null?void 0:Be.value)||""})}),e.jsx(xs,{isOpen:X,onClose:()=>F(!1),image:S,onCropComplete:Pe})]})}const xe=new ge;function Cs({onNext:R,register:u,errors:w,setValue:N,clubProfile:t,defaultValues:r,defaultSports:p,fetchClubProfile:v}){const[f,C]=a.useState(!1),[d,x]=a.useState(!1),[G,M]=a.useState(!1),[U,D]=a.useState("add"),[ee,Y]=a.useState(null),[X,F]=a.useState(!1),[S,y]=a.useState(!1),[c,_]=a.useState(!1),[B,z]=a.useState({name:"",types:[],sport_id:null}),{dispatch:l}=a.useContext(Ne),[L,E]=a.useState([]);a.useEffect(()=>{var n,g;if(((n=r==null?void 0:r.sports)==null?void 0:n.length)>0){const O=(g=r==null?void 0:r.sports)==null?void 0:g.map(Z=>{var $;return{sport_id:Z.id,name:Z.name,club_id:Z.club_id,types:(($=Z.sport_types)==null?void 0:$.map(te=>({name:te.type,sub_type:te.subtype||[],club_sport_type_id:te.club_sport_type_id})))||[],status:Z.status||0}});E(O)}else E([])},[r==null?void 0:r.sports]);const P=async n=>{F(!0);try{xe.setTable("sports");const g=await xe.callRestAPI({id:n.sport_id,status:n.status===1?0:1},"PUT");await v()}catch(g){console.error(g)}finally{F(!1)}},W=async n=>{F(!0);try{xe.setTable("sports");const g=await xe.callRestAPI({id:n.sport_id},"DELETE");E(O=>O.filter(Z=>Z.sport_id!==n.sport_id))}catch(g){console.error(g)}finally{F(!1)}},K=n=>{D("edit"),Y(n),z({name:n.name,sport_id:n.sport_id,types:n.types||[]}),M(!0)},ie=()=>{D("add"),Y(null),z({name:"",types:[],sport_id:null}),M(!0)},oe=()=>{M(!1),Y(null),z({name:"",types:[],sport_id:null})},h=()=>{z(n=>({...n,types:[...n.types,{name:"",sub_type:[]}]}))},s=async(n,g)=>{g&&await le(g),z(O=>({...O,types:O.types.filter((Z,$)=>$!==n)}))},o=(n,g)=>{z(O=>({...O,types:O.types.map((Z,$)=>$===n?{...Z,name:g}:Z)}))},m=n=>{z(g=>({...g,types:g.types.map((O,Z)=>Z===n?{...O,sub_type:[...O.sub_type,""]}:O)}))},k=(n,g)=>{z(O=>({...O,types:O.types.map((Z,$)=>$===n?{...Z,sub_type:Z.sub_type.filter((te,de)=>de!==g)}:Z)}))},A=(n,g,O)=>{z(Z=>({...Z,types:Z.types.map(($,te)=>te===n?{...$,sub_type:$.sub_type.map((de,pe)=>pe===g?O:de)}:$)}))},I=async()=>{var n;if(B.name.trim())try{y(!0);const g={name:B.name,club_id:(n=t==null?void 0:t.club)==null?void 0:n.id,types:B.types.map(Z=>({name:Z.name,sub_type:Z.sub_type,...Z.club_sport_type_id&&{club_sport_type_id:Z.club_sport_type_id}}))};U==="edit"&&(g.sport_id=ee.sport_id);const O=await xe.callRawAPI("/v3/api/custom/courtmatchup/club/profile-edit",{sports:[g]},"POST");U==="edit"?(await v(),V(l,"Sport updated successfully",3e3,"success")):(await v(),V(l,"Sport added successfully",3e3,"success")),oe()}catch(g){console.error(g),V(l,g.message,3e3,"error")}finally{y(!1)}},J=async()=>{try{C(!0),await R()}catch(n){console.error(n),V(l,"Failed to save sports. Please try again.",3e3,"error")}finally{C(!1)}},q=L.some(n=>n.status===1),le=async n=>{_(!0);try{xe.setTable("club_sport_type");const g=await xe.callRestAPI({id:n},"DELETE");console.log("response",g)}catch(g){console.error(g)}finally{_(!1)}};return e.jsxs("div",{className:"w-full max-w-xl",children:[(X||c)&&e.jsx(Ce,{}),e.jsxs("div",{className:"mb-6 flex flex-col items-center justify-center gap-4",children:[e.jsx("div",{children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_25940)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_25940)"}),e.jsxs("g",{filter:"url(#filter0_d_397_25940)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M48.25 27.9375C48.25 26.7294 47.2706 25.75 46.0625 25.75H27.9375C26.7294 25.75 25.75 26.7294 25.75 27.9375V46.0625C25.75 47.2706 26.7294 48.25 27.9375 48.25H46.0625C47.2706 48.25 48.25 47.2706 48.25 46.0625V27.9375ZM33.25 32C33.25 32.8629 32.5504 33.5625 31.6875 33.5625C30.8246 33.5625 30.125 32.8629 30.125 32C30.125 31.1371 30.8246 30.4375 31.6875 30.4375C32.5504 30.4375 33.25 31.1371 33.25 32ZM33.25 37C33.25 37.8629 32.5504 38.5625 31.6875 38.5625C30.8246 38.5625 30.125 37.8629 30.125 37C30.125 36.1371 30.8246 35.4375 31.6875 35.4375C32.5504 35.4375 33.25 36.1371 33.25 37ZM31.6875 43.5625C32.5504 43.5625 33.25 42.8629 33.25 42C33.25 41.1371 32.5504 40.4375 31.6875 40.4375C30.8246 40.4375 30.125 41.1371 30.125 42C30.125 42.8629 30.8246 43.5625 31.6875 43.5625Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_25940",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_25940"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_25940",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"text-2xl font-medium",children:"Select the sports offered at your club"}),e.jsxs("div",{className:"relative",children:[e.jsx(hs,{className:"h-6 w-6 cursor-pointer text-gray-400 hover:text-gray-600",onMouseEnter:()=>x(!0),onMouseLeave:()=>x(!1)}),d&&e.jsxs("div",{className:"absolute left-1/2 top-8 z-50 w-64 -translate-x-1/2 transform rounded-xl bg-white p-4 shadow-lg",children:[e.jsx("p",{className:"mb-2 font-medium",children:"An example of Sport, Type, and Sub-Type:"}),e.jsx("p",{children:"Sport: Tennis"}),e.jsx("p",{children:"Type: Indoor (optional)"}),e.jsx("p",{children:"Sub-type: Grass court (optional)"})]})]})]})]}),e.jsx("div",{className:"mx-auto mb-6 flex max-w-fit flex-col justify-center gap-6",children:L.map(n=>{var g;return e.jsxs("div",{className:"flex w-full flex-col gap-3",children:[e.jsxs("div",{className:"flex w-fit items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",className:"h-5 w-5 rounded-md border-gray-300 text-primaryGreen focus:ring-primaryGreen",value:n.name,checked:n.status==1,onChange:()=>P(n)}),e.jsx("label",{className:"font-medium capitalize",children:n.name})]}),e.jsxs("button",{onClick:()=>K(n),className:"flex items-center gap-1 rounded-lg px-2 py-1 text-sm text-gray-600 hover:bg-gray-100",children:[e.jsx(us,{className:"h-4 w-4"}),"Edit"]}),e.jsxs("button",{onClick:()=>W(n),className:"flex items-center gap-1 rounded-lg px-2 py-1 text-sm text-gray-600 hover:bg-gray-100",children:[e.jsx(gs,{className:"h-4 w-4"}),"Delete"]})]}),n.status===1&&e.jsx("div",{className:"ml-7 flex flex-col gap-2",children:(g=n.types)==null?void 0:g.map((O,Z)=>{var $;return e.jsxs("div",{className:"flex flex-col gap-1",children:[O.name&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Type:"," ",e.jsx("span",{className:"capitalize text-gray-900",children:O.name})]}),(($=O.sub_type)==null?void 0:$.length)>0&&e.jsxs("p",{className:"text-sm text-gray-600",children:["Sub-types:"," ",e.jsx("span",{className:"capitalize text-gray-900",children:O.sub_type.join(", ")})]})]},O.club_sport_type_id||Z)})})]},n.sport_id||n.name)})}),e.jsx("button",{className:"mb-5 text-primaryBlue underline",onClick:ie,children:"+Add sport"}),G&&e.jsx("div",{className:"fixed inset-0 z-50 flex min-h-screen items-center justify-center bg-black/30 p-4",children:e.jsxs("div",{className:"w-full max-w-lg rounded-3xl bg-white p-8",children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:U==="add"?"Add New Sport":"Edit Sport"}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"mb-2 block text-base",children:"Sport name"}),e.jsx("input",{type:"text",className:"w-full rounded-xl border border-gray-200 px-4 py-3 text-base focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:B.name,onChange:n=>z(g=>({...g,name:n.target.value})),placeholder:"Enter sport name"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("label",{className:"mb-2 block text-base",children:["Types ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsxs("button",{onClick:h,className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-primaryBlue/80",children:[e.jsx(Fe,{className:"h-4 w-4"}),"Add Type"]})]}),e.jsx("div",{className:"space-y-4",children:B.types.map((n,g)=>e.jsxs("div",{className:"rounded-lg border border-gray-100 bg-gray-50 p-4",children:[e.jsxs("div",{className:"mb-3 flex items-center gap-2",children:[e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-200 px-3 py-2 text-base focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:n.name,onChange:O=>o(g,O.target.value),placeholder:"Enter type name"}),e.jsx("button",{onClick:()=>s(g,n==null?void 0:n.club_sport_type_id),className:"text-gray-400 hover:text-gray-600",children:e.jsx(Ze,{className:"h-5 w-5"})})]}),e.jsxs("div",{className:"ml-4",children:[e.jsxs("div",{className:"mb-2 flex items-center justify-between",children:[e.jsxs("p",{className:"text-sm text-gray-600",children:["Sub-types"," ",e.jsx("span",{className:"text-gray-500",children:"(optional)"})]}),e.jsxs("button",{onClick:()=>m(g),className:"flex items-center gap-1 text-sm text-primaryBlue hover:text-primaryBlue/80",children:[e.jsx(Fe,{className:"h-4 w-4"}),"Add Sub-type"]})]}),e.jsx("div",{className:"space-y-2",children:n.sub_type.map((O,Z)=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-200 bg-white px-3 py-2 text-sm focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:O,onChange:$=>A(g,Z,$.target.value),placeholder:"Enter sub-type name"}),e.jsx("button",{onClick:()=>k(g,Z),className:"text-gray-400 hover:text-gray-600",children:e.jsx(Ze,{className:"h-5 w-5"})})]},Z))})]})]},n.club_sport_type_id||g))})]})]}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx(se,{onClick:oe,className:"w-full rounded-xl border border-gray-200 bg-white py-3 text-base font-medium text-gray-700 hover:bg-gray-50",children:"Cancel"}),e.jsx(se,{onClick:I,loading:S,className:"w-full rounded-xl bg-primaryGreen py-3 text-base font-medium text-white hover:bg-primaryGreen/90",children:U==="add"?"Add Sport":"Save Changes"})]})]})}),e.jsx(se,{className:`w-full rounded-xl py-3 text-white ${q?"bg-primaryGreen hover:bg-primaryGreen/90":"cursor-not-allowed bg-gray-400"}`,onClick:J,loading:f,disabled:!q,children:"Continue"})]})}const Ns=["00:00:00","01:00:00","02:00:00","03:00:00","04:00:00","05:00:00","06:00:00","07:00:00","08:00:00","09:00:00","10:00:00","11:00:00","12:00:00","13:00:00","14:00:00","15:00:00","16:00:00","17:00:00","18:00:00","19:00:00","20:00:00","21:00:00","22:00:00","23:00:00"],_s=R=>{const[u,w]=R.split(":"),N=parseInt(u,10),t=N>=12?"PM":"AM";return`${N%12||12}:${w} ${t}`},we=({label:R,value:u,onChange:w,timeOptions:N=Ns,minTime:t=null})=>{const r=t?N.filter(p=>{const[v]=t.split(":"),[f]=p.split(":");return parseInt(f)>parseInt(v)}):N;return e.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[e.jsx("div",{className:"gap-2 self-stretch whitespace-nowrap bg-slate-50 px-2 py-2.5 text-neutral-400",children:R}),e.jsxs("select",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:u,onChange:w,children:[e.jsx("option",{value:"",children:"Select time"}),r.map(p=>e.jsx("option",{value:p,children:_s(p)},p))]})]})};function Ss({onNext:R,register:u,errors:w,setValue:N,defaultValues:t,isSubmitting:r}){const[p,v]=a.useState(()=>t!=null&&t.days_off?Array.isArray(t.days_off)?t.days_off:[]:[]),[f,C]=a.useState(()=>(t==null?void 0:t.times.length)>0?t.times:[{from:"",until:""}]),[d,x]=a.useState(()=>{var S;return((S=t==null?void 0:t.max_players)==null?void 0:S.toString())||""});console.log("time slots",f),a.useEffect(()=>{t&&(t.days_off&&v(Array.isArray(t.days_off)?t.days_off:[]),t.opening_time&&t.closing_time&&C([{from:t.opening_time,until:t.closing_time}]),t.max_players&&x(t.max_players.toString()))},[t]);const[G,M]=a.useState(!1),U=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],D=(S,y)=>{v(y?c=>c.filter(B=>B!==S):c=>[...c,S])},ee=()=>{N("days_off",p),N("times",f),N("max_players",Number(d)),R()},Y=()=>{C(S=>[...S,{from:"",until:""}])},X=S=>{C(y=>y.filter((c,_)=>_!==S))},F=(S,y,c)=>{C(_=>{const B=[..._];return B[S]={...B[S],[y]:c},B})};return e.jsx("div",{className:"flex min-h-screen items-center justify-center",children:e.jsxs("div",{className:"w-full max-w-xl rounded-2xl bg-white p-8 shadow-lg",children:[e.jsxs("div",{className:"mb-6 flex flex-col items-center justify-center gap-4",children:[e.jsx("div",{children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_25940)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_25940)"}),e.jsxs("g",{filter:"url(#filter0_d_397_25940)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M48.25 27.9375C48.25 26.7294 47.2706 25.75 46.0625 25.75H27.9375C26.7294 25.75 25.75 26.7294 25.75 27.9375V46.0625C25.75 47.2706 26.7294 48.25 27.9375 48.25H46.0625C47.2706 48.25 48.25 47.2706 48.25 46.0625V27.9375ZM33.25 32C33.25 32.8629 32.5504 33.5625 31.6875 33.5625C30.8246 33.5625 30.125 32.8629 30.125 32C30.125 31.1371 30.8246 30.4375 31.6875 30.4375C32.5504 30.4375 33.25 31.1371 33.25 32ZM33.25 37C33.25 37.8629 32.5504 38.5625 31.6875 38.5625C30.8246 38.5625 30.125 37.8629 30.125 37C30.125 36.1371 30.8246 35.4375 31.6875 35.4375C32.5504 35.4375 33.25 36.1371 33.25 37ZM31.6875 43.5625C32.5504 43.5625 33.25 42.8629 33.25 42C33.25 41.1371 32.5504 40.4375 31.6875 40.4375C30.8246 40.4375 30.125 41.1371 30.125 42C30.125 42.8629 30.8246 43.5625 31.6875 43.5625Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_25940",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_25940"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_25940",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_25940",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsx("p",{className:"text-2xl font-medium",children:"Other details"})]}),e.jsx("p",{className:"mb-2",children:"Opening hours for:"}),e.jsx("div",{className:"mb-6 flex flex-wrap justify-start gap-4",children:U.map(S=>e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",className:"h-5 w-5 rounded-md border-gray-300 text-primaryGreen focus:ring-primaryGreen",checked:!p.includes(S),onChange:y=>D(S,y.target.checked)}),e.jsx("label",{className:"",children:S})]},S))}),f.map((S,y)=>e.jsxs("div",{className:"mb-5 flex items-center gap-4",children:[e.jsxs("div",{className:"flex flex-1 gap-4",children:[e.jsx(we,{label:"From",timeOptions:Ye,value:S.from,onChange:c=>F(y,"from",c.target.value)}),e.jsx(we,{label:"Until",timeOptions:We(S.from),value:S.until,onChange:c=>F(y,"until",c.target.value),disabled:!S.from})]}),f.length>1&&e.jsx("button",{onClick:()=>X(y),className:" text-red-500 hover:text-red-700",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M4.79167 4.79102V16.8743C4.79167 17.3346 5.16476 17.7077 5.625 17.7077H14.375C14.8352 17.7077 15.2083 17.3346 15.2083 16.8743V4.79102M4.79167 4.79102H15.2083M4.79167 4.79102H3.125M15.2083 4.79102H16.875M11.6667 8.95768V13.541M8.33333 8.95768V13.541M7.5 4.79102C7.5 3.4103 8.61929 2.29102 10 2.29102C11.3807 2.29102 12.5 3.4103 12.5 4.79102",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})})})]},y)),e.jsx("button",{onClick:Y,className:"mb-5 text-primaryBlue underline hover:text-primaryBlue/80",children:"+Add another time slot"}),e.jsxs("div",{className:"mb-5",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("p",{children:"Max number of players for a normal lesson:"}),e.jsxs("div",{className:"relative",onMouseEnter:()=>M(!0),onMouseLeave:()=>M(!1),children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10 16.25C13.4518 16.25 16.25 13.4518 16.25 10C16.25 6.54822 13.4518 3.75 10 3.75C6.54822 3.75 3.75 6.54822 3.75 10C3.75 13.4518 6.54822 16.25 10 16.25ZM11.1158 13.2086L11.2156 12.8006C11.164 12.8249 11.0807 12.8526 10.9665 12.8841C10.852 12.9157 10.7489 12.9318 10.6583 12.9318C10.4654 12.9318 10.3295 12.9001 10.2507 12.8366C10.1724 12.773 10.1333 12.6534 10.1333 12.4783C10.1333 12.4089 10.1451 12.3054 10.1697 12.17C10.1936 12.0337 10.2211 11.9126 10.2516 11.8067L10.6242 10.4876C10.6607 10.3665 10.6857 10.2334 10.6992 10.0882C10.7129 9.94325 10.7193 9.84185 10.7193 9.78429C10.7193 9.50614 10.6218 9.28041 10.4268 9.10629C10.2317 8.93229 9.95393 8.84529 9.59396 8.84529C9.39365 8.84529 9.18188 8.88088 8.95776 8.952C8.73363 9.02294 8.49933 9.1084 8.25421 9.2082L8.15415 9.6165C8.22719 9.58949 8.31419 9.56043 8.41598 9.53034C8.51732 9.50038 8.61674 9.48489 8.71347 9.48489C8.91096 9.48489 9.04399 9.51856 9.1137 9.58488C9.18342 9.65139 9.21844 9.7697 9.21844 9.93883C9.21844 10.0324 9.20736 10.1363 9.18438 10.2492C9.16172 10.3628 9.13342 10.483 9.10013 10.6098L8.72595 11.9342C8.69266 12.0734 8.66834 12.1979 8.65304 12.3084C8.63786 12.4189 8.63057 12.5272 8.63057 12.6326C8.63057 12.9048 8.73114 13.1292 8.93222 13.3063C9.13329 13.4826 9.41523 13.5714 9.77769 13.5714C10.0137 13.5714 10.2209 13.5406 10.3992 13.4785C10.5773 13.4167 10.8164 13.3268 11.1158 13.2086ZM11.0495 7.8502C11.2235 7.68882 11.3101 7.49254 11.3101 7.26272C11.3101 7.03341 11.2236 6.83675 11.0495 6.67331C10.8758 6.51032 10.6666 6.42857 10.4219 6.42857C10.1765 6.42857 9.96635 6.51013 9.79107 6.67331C9.61579 6.83675 9.52796 7.03334 9.52796 7.26272C9.52796 7.49254 9.61579 7.68875 9.79107 7.8502C9.96667 8.01217 10.1764 8.09321 10.4219 8.09321C10.6666 8.09321 10.8758 8.01217 11.0495 7.8502Z",fill:"#CDD0D5"})}),G&&e.jsx("div",{className:"absolute z-10 -translate-x-1/2 transform",children:e.jsx("div",{className:"mt-2 w-[400px] rounded-lg bg-white p-4 shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsx("p",{className:"text-sm text-gray-600",children:"This is the maximum number of players that can be booked with the “find by coach” and “find by time” options in the lesson section. If the booking party exceeds this number, they cannot outright book the lesson, and will need to submit a request through the “custom request” section. This ensures that coaches can effectively manage group sizes, while also allowing flexibility for larger parties through the “custom request” process."})})})]})]}),e.jsx("input",{type:"text",className:"mt-1 block w-full rounded-xl border border-gray-300 px-3 py-2",value:d,onChange:S=>x(S.target.value)})]}),e.jsx(se,{className:"w-full rounded-xl bg-primaryGreen py-3 text-white",onClick:ee,loading:r,children:"Continue"})]})})}let Ae=new ge;const ks=R=>{const u={};return R&&R.forEach(w=>{w.sport_id&&(u[w.sport_id]||(u[w.sport_id]={total:0,types:{}}),u[w.sport_id].total+=1,w.type&&(u[w.sport_id].types[w.type]||(u[w.sport_id].types[w.type]=0),u[w.sport_id].types[w.type]+=1))}),u};function Ms({onNext:R,register:u,errors:w,values:N,setValue:t,watch:r,defaultValues:p,isSubmitting:v}){var oe;console.log("default values",p);const[f,C]=a.useState(!1),d=a.useMemo(()=>{var h;return(h=p==null?void 0:p.sports)==null?void 0:h.map(s=>({...s,sport_types:s.sport_types||[]}))},[p==null?void 0:p.sports]),[x,G]=a.useState(()=>{var h;return((h=N==null?void 0:N.courts)==null?void 0:h.length)>0?N.courts.map(s=>({...s})):[]}),[M,U]=a.useState(null),D=a.useRef(),[ee,Y]=a.useState(!((oe=N==null?void 0:N.courts)!=null&&oe.length)),[X,F]=a.useState(""),[S,y]=a.useState({}),c=r("courts"),[_,B]=a.useState({}),z=()=>{const h=parseInt(X);if(h>0){const s=Array.from({length:h},(o,m)=>({sport_id:null,name:`Court ${m+1}`,type:null,sub_type:null}));G(s),t("courts",s),Y(!1)}};a.useEffect(()=>{var h;if(((h=N==null?void 0:N.courts)==null?void 0:h.length)>0&&!x.length){const s=N.courts.map(o=>({...o}));G(s),t("courts",s),Y(!1)}},[N]),a.useEffect(()=>{const h=s=>{D.current&&!D.current.contains(s.target)&&U(null)};return document.addEventListener("mousedown",h),()=>document.removeEventListener("mousedown",h)},[]);const l=()=>{const h={sport_id:null,name:`Court ${((c==null?void 0:c.length)||0)+1}`,type:null,sub_type:null};t("courts",[...c||[],h])},L=async(h,s)=>{C(!0);try{if(s!=null&&s.id){Ae.setTable("club_court");const o=await Ae.callRestAPI({id:s==null?void 0:s.id},"DELETE");t("courts",c.filter((m,k)=>k!==h))}else t("courts",c.filter((o,m)=>m!==h))}catch(o){console.log(o)}finally{C(!1)}},E=h=>{U(M===h?null:h)},P=(h,s)=>{var A,I;const o=parseInt(s),m=(A=d==null?void 0:d.filter(J=>J.status!==0))==null?void 0:A.find(J=>J.id===o),k=(I=m==null?void 0:m.sport_types)==null?void 0:I.some(J=>J.type);t("courts",c.map((J,q)=>q===h?{...J,sport_id:o,type:null,sub_type:null,hasTypes:k}:J))},W=(h,s)=>{t("courts",c.map((o,m)=>m===h?{...o,type:s,sub_type:null}:o))},K=(h,s)=>{t("courts",c.map((o,m)=>m===h?{...o,sub_type:s}:o))};a.useEffect(()=>{x.length>0&&!c&&t("courts",x)},[x,t,c]),a.useEffect(()=>{if((c==null?void 0:c.length)>=0){const h=ks(c);y(h)}},[c]);const ie=h=>{h.preventDefault();const s={};let o=!1;c==null||c.forEach((m,k)=>{var A,I,J;if(m.sport_id||(s[`courts.${k}.sport_id`]="Sport is required",o=!0),m.sport_id){const q=d==null?void 0:d.find(n=>n.id===m.sport_id);if(((A=q==null?void 0:q.sport_types)==null?void 0:A.some(n=>n.type))&&!m.type&&(s[`courts.${k}.type`]="Type is required for this sport",o=!0),m.type){const n=(I=q==null?void 0:q.sport_types)==null?void 0:I.find(g=>g.type===m.type);((J=n==null?void 0:n.subtype)==null?void 0:J.length)>0&&!m.sub_type&&(s[`courts.${k}.sub_type`]="Sub-type is required for this type",o=!0)}}}),B(s),o||R()};return e.jsxs("div",{className:"w-full max-w-xl",children:[f&&e.jsx(Ce,{}),ee&&e.jsx("div",{className:"fixed inset-0 z-50 flex min-h-screen items-center justify-center bg-black/30 p-4",children:e.jsxs("div",{className:"w-full max-w-lg rounded-3xl bg-white p-8",children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:"Number of courts/spaces"}),e.jsxs("div",{className:"mb-8",children:[e.jsxs("label",{className:"mb-4  flex items-center  text-base",children:[e.jsx("span",{children:"How many courts/spaces does your club have"}),e.jsx("span",{className:"inline-block",children:e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10 16.25C13.4518 16.25 16.25 13.4518 16.25 10C16.25 6.54822 13.4518 3.75 10 3.75C6.54822 3.75 3.75 6.54822 3.75 10C3.75 13.4518 6.54822 16.25 10 16.25ZM11.1158 13.2086L11.2156 12.8006C11.164 12.8249 11.0807 12.8526 10.9665 12.8841C10.852 12.9157 10.7489 12.9318 10.6583 12.9318C10.4654 12.9318 10.3295 12.9001 10.2507 12.8366C10.1724 12.773 10.1333 12.6534 10.1333 12.4783C10.1333 12.4089 10.1451 12.3054 10.1697 12.17C10.1936 12.0337 10.2211 11.9126 10.2516 11.8067L10.6242 10.4876C10.6607 10.3665 10.6857 10.2334 10.6992 10.0882C10.7129 9.94325 10.7193 9.84185 10.7193 9.78429C10.7193 9.50614 10.6218 9.28041 10.4268 9.10629C10.2317 8.93229 9.95393 8.84529 9.59396 8.84529C9.39365 8.84529 9.18188 8.88088 8.95776 8.952C8.73363 9.02294 8.49933 9.1084 8.25421 9.2082L8.15415 9.6165C8.22719 9.58949 8.31419 9.56043 8.41598 9.53034C8.51732 9.50038 8.61674 9.48489 8.71347 9.48489C8.91096 9.48489 9.04399 9.51856 9.1137 9.58488C9.18342 9.65139 9.21844 9.7697 9.21844 9.93883C9.21844 10.0324 9.20736 10.1363 9.18438 10.2492C9.16172 10.3628 9.13342 10.483 9.10013 10.6098L8.72595 11.9342C8.69266 12.0734 8.66834 12.1979 8.65304 12.3084C8.63786 12.4189 8.63057 12.5272 8.63057 12.6326C8.63057 12.9048 8.73114 13.1292 8.93222 13.3063C9.13329 13.4826 9.41523 13.5714 9.77769 13.5714C10.0137 13.5714 10.2209 13.5406 10.3992 13.4785C10.5773 13.4167 10.8164 13.3268 11.1158 13.2086ZM11.0495 7.8502C11.2235 7.68882 11.3101 7.49254 11.3101 7.26272C11.3101 7.03341 11.2236 6.83675 11.0495 6.67331C10.8758 6.51032 10.6666 6.42857 10.4219 6.42857C10.1765 6.42857 9.96635 6.51013 9.79107 6.67331C9.61579 6.83675 9.52796 7.03334 9.52796 7.26272C9.52796 7.49254 9.61579 7.68875 9.79107 7.8502C9.96667 8.01217 10.1764 8.09321 10.4219 8.09321C10.6666 8.09321 10.8758 8.01217 11.0495 7.8502Z",fill:"#CDD0D5"})})})]}),e.jsx("input",{type:"number",className:"mb-4 w-full rounded-xl border border-gray-200 px-4 py-3 text-base focus:border-primaryGreen focus:outline-none focus:ring-1 focus:ring-primaryGreen",value:X,onChange:h=>F(h.target.value),placeholder:"Enter number of courts"})]}),e.jsx(se,{onClick:z,className:"w-full rounded-xl bg-primaryGreen py-3 text-base font-medium text-white hover:bg-primaryGreen/90",children:"Continue"})]})}),c==null?void 0:c.map((h,s)=>{var o,m,k,A,I,J,q,le,n,g,O,Z,$,te,de,pe,he,ue;return e.jsxs("div",{className:"mb-4 rounded-xl bg-[#F6F8FA] p-5",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsxs("span",{className:"text-base font-medium",children:["Space ",s+1]}),e.jsxs("div",{className:"relative",ref:D,children:[e.jsx("button",{className:"text-sm text-gray-500",onClick:()=>E(s),children:e.jsxs("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M10.0013 10.8337C10.4615 10.8337 10.8346 10.4606 10.8346 10.0003C10.8346 9.54009 10.4615 9.16699 10.0013 9.16699C9.54107 9.16699 9.16797 9.54009 9.16797 10.0003C9.16797 10.4606 9.54107 10.8337 10.0013 10.8337Z",fill:"#868C98"}),e.jsx("path",{d:"M16.8763 10.8337C17.3365 10.8337 17.7096 10.4606 17.7096 10.0003C17.7096 9.54009 17.3365 9.16699 16.8763 9.16699C16.4161 9.16699 16.043 9.54009 16.043 10.0003C16.043 10.4606 16.4161 10.8337 16.8763 10.8337Z",fill:"#868C98"}),e.jsx("path",{d:"M3.1263 10.8337C3.58654 10.8337 3.95964 10.4606 3.95964 10.0003C3.95964 9.54009 3.58654 9.16699 3.1263 9.16699C2.66606 9.16699 2.29297 9.54009 2.29297 10.0003C2.29297 10.4606 2.66606 10.8337 3.1263 10.8337Z",fill:"#868C98"}),e.jsx("path",{d:"M10.0013 10.8337C10.4615 10.8337 10.8346 10.4606 10.8346 10.0003C10.8346 9.54009 10.4615 9.16699 10.0013 9.16699C9.54107 9.16699 9.16797 9.54009 9.16797 10.0003C9.16797 10.4606 9.54107 10.8337 10.0013 10.8337Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M16.8763 10.8337C17.3365 10.8337 17.7096 10.4606 17.7096 10.0003C17.7096 9.54009 17.3365 9.16699 16.8763 9.16699C16.4161 9.16699 16.043 9.54009 16.043 10.0003C16.043 10.4606 16.4161 10.8337 16.8763 10.8337Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M3.1263 10.8337C3.58654 10.8337 3.95964 10.4606 3.95964 10.0003C3.95964 9.54009 3.58654 9.16699 3.1263 9.16699C2.66606 9.16699 2.29297 9.54009 2.29297 10.0003C2.29297 10.4606 2.66606 10.8337 3.1263 10.8337Z",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]})}),M===s&&e.jsx("div",{className:"absolute right-0 z-10 mt-2 w-48 rounded-lg bg-white py-2 shadow-lg ring-1 ring-black ring-opacity-5",children:e.jsxs("button",{className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-50",onClick:()=>{L(s,h),U(null)},children:[e.jsxs("svg",{className:"mr-2 h-4 w-4",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M3 6H5H21",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),e.jsx("path",{d:"M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Delete court"]})})]})]}),e.jsxs("div",{className:"space-y-4 rounded-xl bg-white p-4 ",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"w-full",children:[e.jsx("label",{className:"mb-2 block",children:"Court name"}),e.jsx("input",{type:"text",className:"w-full rounded-lg border border-gray-300 px-3 py-2",...u(`courts.${s}.name`),defaultValue:h.name})]})}),e.jsxs("div",{children:[e.jsxs("label",{className:"mb-2 block",children:["Sport ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"flex flex-wrap gap-4",children:(o=d==null?void 0:d.filter(H=>H.status!==0))==null?void 0:o.map(H=>e.jsxs("label",{className:"flex items-center gap-1 capitalize",children:[e.jsx("input",{type:"radio",name:`courts.${s}.sport_id`,value:H.id,checked:h.sport_id===H.id,className:"h-5 w-5",onChange:me=>P(s,me.target.value),required:!0}),H.name," "]},H.id))}),_[`courts.${s}.sport_id`]&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:_[`courts.${s}.sport_id`]})]}),h.sport_id&&((A=(k=(m=d==null?void 0:d.filter(H=>H.status!==0))==null?void 0:m.find(H=>H.id===h.sport_id))==null?void 0:k.sport_types)==null?void 0:A.length)>0&&e.jsxs("div",{children:[((q=(J=(I=d==null?void 0:d.find(H=>H.id===h.sport_id))==null?void 0:I.sport_types)==null?void 0:J.filter(H=>H.type))==null?void 0:q.length)>0&&e.jsxs("label",{className:"mb-2 block",children:["Type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"flex flex-wrap gap-4",children:(g=(n=(le=d==null?void 0:d.find(H=>H.id===h.sport_id))==null?void 0:le.sport_types)==null?void 0:n.filter(H=>H.type))==null?void 0:g.map((H,me)=>e.jsxs("label",{className:"flex items-center gap-2 capitalize",children:[e.jsx("input",{type:"radio",name:`courts.${s}.type`,value:H.type,checked:h.type===H.type,className:"h-5 w-5",onChange:_e=>W(s,_e.target.value),required:!0}),H.type]},`${s}-${me}`))}),_[`courts.${s}.type`]&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:_[`courts.${s}.type`]})]}),h.sport_id&&h.type&&e.jsx("div",{children:((te=($=(Z=(O=d==null?void 0:d.find(H=>H.id===h.sport_id))==null?void 0:O.sport_types)==null?void 0:Z.find(H=>H.type===h.type))==null?void 0:$.subtype)==null?void 0:te.length)>0&&e.jsxs(e.Fragment,{children:[e.jsxs("label",{className:"mb-2 block",children:["Sub-type ",e.jsx("span",{className:"text-red-500",children:"*"})]}),e.jsx("div",{className:"flex flex-wrap gap-4",children:(ue=(he=(pe=(de=d==null?void 0:d.find(H=>H.id==h.sport_id))==null?void 0:de.sport_types)==null?void 0:pe.find(H=>H.type===h.type))==null?void 0:he.subtype)==null?void 0:ue.map(H=>e.jsxs("label",{className:"flex items-center gap-2 capitalize",children:[e.jsx("input",{type:"radio",name:`courts.${s}.sub_type`,value:H,checked:h.sub_type===H,className:"h-5 w-5",onChange:me=>K(s,me.target.value),required:!0}),H]},`${s}-${H}`))}),_[`courts.${s}.sub_type`]&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:_[`courts.${s}.sub_type`]})]})})]})]},s)}),e.jsx("button",{className:"mb-6 text-blue-600 underline",onClick:l,type:"button",children:"+ Add another court/space"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("p",{className:"mb-4 text-lg font-medium",children:"Summary:"}),e.jsxs("div",{className:"mb-4 space-y-2 rounded-xl bg-gray-50 p-4",children:[Object.entries(S).map(([h,s])=>{const o=d==null?void 0:d.find(A=>A.id===parseInt(h)),m=c==null?void 0:c.filter(A=>A.sport_id===parseInt(h)),k=[...new Set(m==null?void 0:m.map(A=>A.sub_type).filter(Boolean))];return e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("span",{children:[(o==null?void 0:o.name)||"Unknown Sport"," ",e.jsx("span",{className:"text-sm capitalize text-gray-500",children:k.length>0?`(${k.join(", ")})`:""})]}),e.jsxs("div",{className:"flex gap-4",children:[Object.entries(s.types).map(([A,I])=>e.jsxs("span",{className:"flex items-center gap-2 capitalize",children:[A," ",e.jsx("span",{className:"rounded-full bg-blue-100 px-2 py-1",children:I})]},A)),Object.keys(s.types).length===0&&e.jsx("span",{className:"rounded-full bg-blue-100 px-2 py-1",children:s.total})]})]},h)}),e.jsxs("div",{className:"mt-4 flex justify-between border-t border-gray-200 pt-4",children:[e.jsx("span",{children:"Courts/spaces total"}),e.jsx("span",{className:"rounded-full bg-blue-100 px-2 py-1",children:(c==null?void 0:c.length)||0})]})]})]}),e.jsx(se,{className:"w-full rounded-xl bg-[#176448] py-3 text-white disabled:opacity-50",onClick:ie,loading:v,type:"button",children:"Continue"})]})}const Oe=new ge;function Es({onNext:R,stripeConnectionData:u,isSubmitting:w,setStripeConnectionData:N}){const[t]=ve.useState(w||!1),[r,p]=a.useState(!1),[v,f]=a.useState(!1),C=localStorage.getItem("role"),d=async()=>{try{const M=await Oe.callRawAPI(`/v3/api/custom/courtmatchup/${C}/stripe/account/verify`,{},"POST");return N&&N(M),M}catch(M){return console.error("Error checking Stripe connection:",M),!1}},x=async()=>{p(!0);try{const M=await Oe.callRawAPI(`/v3/api/custom/courtmatchup/${C}/stripe/onboarding`,{},"POST");M&&M.url&&window.open(M.url,"_blank")}catch(M){console.error("Error connecting to Stripe:",M)}p(!1)};a.useEffect(()=>{if(r===!1){const M=setTimeout(()=>{d()},2e3);return()=>clearTimeout(M)}},[r]);const G=async()=>{f(!0),await R(),f(!1)};return e.jsx("div",{className:"flex flex-col bg-white pb-7",children:e.jsxs("section",{className:"flex w-[432px] max-w-full flex-col justify-center",children:[e.jsx("div",{className:"flex w-full max-w-[432px] flex-col self-center max-md:max-w-full",children:e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3",children:[e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_26852)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_26852)"}),e.jsxs("g",{filter:"url(#filter0_d_397_26852)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M47.3125 34.4999H47V42.7054C47.6877 42.8975 48.2531 43.4195 48.4868 44.1207L48.9035 45.3707C49.3756 46.7872 48.3213 48.2499 46.8282 48.2499H27.1718C25.6787 48.2499 24.6244 46.7872 25.0965 45.3707L25.5132 44.1207C25.7469 43.4195 26.3123 42.8975 27 42.7054V34.4999H26.6875C25.4794 34.4999 24.5 33.5206 24.5 32.3124V31.7352C24.5 30.9099 24.9645 30.1549 25.7011 29.7828L36.0136 24.5729C36.6339 24.2596 37.3661 24.2596 37.9864 24.5729L48.2989 29.7828C49.0355 30.1549 49.5 30.9099 49.5 31.7352V32.3124C49.5 33.5206 48.5206 34.4999 47.3125 34.4999ZM42 34.4999H45.125V42.6249H42V34.4999ZM32 42.6249H28.875V34.4999H32V42.6249ZM33.875 42.6249V34.4999H40.125V42.6249H33.875Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_26852",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_26852"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_26852",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_26852",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]}),e.jsx("p",{className:"w-full text-center text-2xl font-medium leading-none text-gray-950 max-md:max-w-full",children:"Connect stripe"})]})}),e.jsxs("div",{className:"mt-10 flex w-full flex-col gap-4 self-center max-md:max-w-full",children:[((u==null?void 0:u.complete)||(u==null?void 0:u.details_submitted))&&e.jsxs("div",{className:"flex flex-col gap-4 rounded-lg border border-green-200 bg-green-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-green-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-lg font-medium text-gray-900",children:u!=null&&u.complete?"Stripe account connected":"Stripe account details submitted"})]}),e.jsxs("div",{className:"mt-2 grid grid-cols-1 gap-3",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsx("span",{className:"text-xs text-gray-500",children:"Account ID"}),e.jsx("span",{className:"text-sm font-medium text-gray-700",children:u.account_id})]}),e.jsxs("div",{className:"flex flex-col space-y-2",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${u.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${u.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${u.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("div",{className:"mt-2 text-sm text-gray-600",children:u!=null&&u.complete?"You can now receive payments from your club members.":"Your Stripe account details have been submitted and are pending approval. You can continue with the setup process."}),e.jsx(se,{onClick:G,className:"mt-4 w-full rounded-xl bg-primaryGreen px-4 py-3 text-sm font-medium text-white",loading:t||v,children:t?"Processing...":"Continue"})]}),!(u!=null&&u.complete||u!=null&&u.details_submitted)&&e.jsxs("div",{className:"flex flex-col gap-4",children:[e.jsxs("div",{className:"rounded-lg border border-yellow-200 bg-yellow-50 p-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-yellow-500",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),e.jsx("div",{className:"text-sm font-medium text-gray-900",children:"No Stripe account connected"})]}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"Connect your Stripe account to pay your club staffs and coaches. This is required for processing payments on the platform."}),u&&e.jsxs("div",{className:"mt-3 grid grid-cols-1 gap-2",children:[e.jsx("div",{className:"text-xs text-gray-500",children:"Account Status"}),e.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${u.complete?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Complete"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${u.details_submitted?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Details Submitted"})]}),e.jsxs("div",{className:"flex flex-col items-center rounded-md border bg-white p-2",children:[e.jsx("div",{className:`h-3 w-3 rounded-full ${u.charges_enabled?"bg-green-500":"bg-gray-300"}`}),e.jsx("span",{className:"mt-1 text-xs text-gray-600",children:"Charges Enabled"})]})]})]})]}),e.jsx("button",{onClick:x,className:"w-full rounded-xl bg-primaryBlue px-4 py-3 text-sm font-medium text-white",disabled:r,children:r?"Connecting...":"Connect Stripe Account"})]})]})]})})}const Ue=({label:R,value:u,onChange:w})=>e.jsxs("div",{className:"flex h-10 min-h-[40px] flex-1 shrink basis-0 items-start justify-center overflow-hidden rounded-xl border border-solid border-zinc-200 bg-white shadow-sm",children:[e.jsx("div",{className:"flex w-10 items-center justify-center gap-2 self-stretch whitespace-nowrap bg-slate-50 px-2 py-2.5 text-neutral-400",children:"$"}),e.jsx("input",{className:"flex flex-1 shrink basis-0 items-center justify-between overflow-hidden border-l border-none border-zinc-200 bg-white px-2 py-2.5 text-gray-950 outline-none focus:outline-none focus:ring-0",value:u,type:"number",onChange:w})]});function Ls({handleSave:R,setValue:u,setPricingRows:w,pricingRows:N,editingPricing:t=null,values:r,isSubmitting:p,sportsOffered:v}){var c,_,B,z;const[f,C]=a.useState(()=>{var l;return t?{sport_id:t.sport_id,type:t.type,sub_type:t.sub_type||"",is_general:t.is_general||!1,general_rate:t.general_rate||"",price_by_hours:(l=t==null?void 0:t.price_by_hours)==null?void 0:l.map(L=>({start_time:L.start_time,end_time:L.end_time,rate:L.rate,showInput:!1})),showSportInput:!1,showTypeInput:!1}:{sport_id:"",type:"",sub_type:"",is_general:!1,general_rate:"",price_by_hours:[{start_time:"",end_time:"",rate:"",showInput:!0}],showSportInput:!0,showTypeInput:!0}}),{dispatch:d}=a.useContext(Ne),x=v==null?void 0:v.find(l=>l.id==f.sport_id),G=((c=x==null?void 0:x.sport_types)==null?void 0:c.filter(l=>l.type))||[],M=((B=(_=x==null?void 0:x.sport_types)==null?void 0:_.find(l=>l.type===f.type))==null?void 0:B.subtype)||[],U=()=>{const l=f;if(l.is_general){if(!l.general_rate){V(d,"Please enter a general rate",3e3,"warning");return}}else{if(l.price_by_hours.length===0){V(d,"Please add pricing details",3e3,"warning");return}if(l.price_by_hours.some(P=>!P.start_time||!P.end_time||!P.rate)){V(d,"Please fill all time slot details",3e3,"warning");return}}const L={sport_id:l.sport_id,type:l.type,sub_type:l.sub_type,is_general:l.is_general,general_rate:l.is_general?l.general_rate:null,price_by_hours:l.is_general?[{rate:l.general_rate}]:l.price_by_hours.map(E=>({start_time:E.start_time,end_time:E.end_time,rate:E.rate}))};w(t?E=>E.map((P,W)=>W===t.index?L:P):E=>[...E,L]),C({sport_id:"",type:"",sub_type:"",is_general:!1,general_rate:"",price_by_hours:[{start_time:"",end_time:"",rate:"",showInput:!0}],showSportInput:!0,showTypeInput:!0})},D=(l,L)=>{var P;const E=((P=L==null?void 0:L.target)==null?void 0:P.value)||L;C(W=>({...W,[l]:E}))},ee=()=>{C(l=>({...l,showSportInput:!1}))},Y=()=>{C(l=>({...l,showSportInput:!0}))},X=(l,L,E)=>{var W;const P=((W=E==null?void 0:E.target)==null?void 0:W.value)||E;C(K=>({...K,price_by_hours:K.price_by_hours.map((ie,oe)=>oe===l?{...ie,[L]:P}:ie)}))},F=()=>{C(l=>({...l,price_by_hours:[...l.price_by_hours,{start_time:"",end_time:"",rate:"",showInput:!0}]}))};console.log({sportsOffered:v});const S=l=>{const L=f.price_by_hours[l];if(!L.start_time||!L.end_time||!L.rate){V(d,"Please fill all hours and rate fields",3e3,"warning");return}C(E=>({...E,price_by_hours:E.price_by_hours.map((P,W)=>W===l?{...P,showInput:!1}:P)}))},y=l=>{C(L=>({...L,price_by_hours:L.price_by_hours.map((E,P)=>P===l?{...E,showInput:!0}:E)}))};return e.jsx("div",{className:"space-y-5",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"mb-6 divide-y",children:[e.jsx("div",{className:"py-4",children:f.showSportInput?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("label",{className:"text-base font-medium text-gray-900",children:["Sport"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("button",{onClick:()=>C(l=>({...l,showSportInput:!1})),className:"text-sm text-primaryBlue hover:text-blue-700",children:"Cancel"})]}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 px-3 py-2 capitalize",value:f.sport_id,onChange:l=>{C(L=>({...L,sport_id:l.target.value,type:"",sub_type:""}))},children:[e.jsx("option",{value:"",children:"-All sports-"}),(z=v==null?void 0:v.filter(l=>l.status===1))==null?void 0:z.map(l=>e.jsx("option",{value:l.id,children:l.name},l.id))]}),f.sport_id&&G.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 px-3 py-2 capitalize",value:f.type,onChange:l=>{D("type",l.target.value),C(L=>({...L,type:l.target.value,sub_type:""}))},children:[e.jsx("option",{value:"",children:"-Select Type-"}),G.map(l=>e.jsx("option",{value:l.type,children:l.type},l.type))]})]}),f.sport_id&&f.type&&M.length>0&&e.jsxs("div",{className:"mt-4",children:[e.jsxs("label",{className:"mb-1 block text-sm font-medium text-gray-700",children:["Sub-type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsxs("select",{className:"w-full rounded-lg border border-gray-300 px-3 py-2 capitalize",value:f.sub_type,onChange:l=>D("sub_type",l.target.value),children:[e.jsx("option",{value:"",children:"-Select Sub-type-"}),M.map(l=>e.jsx("option",{value:l,children:l},l))]})]}),e.jsx("button",{onClick:ee,className:"mt-4 w-full rounded-lg bg-primaryBlue px-4 py-2 text-sm text-white hover:bg-blue-700",children:"Save"})]}):e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("h3",{className:"text-base font-medium text-gray-900",children:["Sport"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("p",{className:"text-sm capitalize text-gray-500",children:(x==null?void 0:x.name)||"All sports"})]}),e.jsx("button",{onClick:Y,className:"text-sm text-primaryBlue hover:text-blue-700",children:"Edit"})]})}),G.length>0&&e.jsx("div",{className:"py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("h3",{className:"text-base font-medium capitalize text-gray-900",children:["Type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("p",{className:"text-sm capitalize text-gray-500",children:f.type||"Not set"})]}),e.jsx("button",{onClick:Y,className:"text-sm text-primaryBlue hover:text-blue-700",children:"Edit"})]})}),M.length>0&&e.jsx("div",{className:"py-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("h3",{className:"text-base font-medium text-gray-900",children:["Sub type"," ",e.jsx("span",{className:"text-xs text-gray-500",children:"(optional)"})]}),e.jsx("p",{className:"text-sm capitalize capitalize text-gray-500",children:f.sub_type||"Not set"})]}),e.jsx("button",{onClick:Y,className:"text-sm text-primaryBlue hover:text-blue-700",children:"Edit"})]})})]}),e.jsxs("div",{className:"mt-6 border-t pt-4",children:[e.jsxs("div",{className:"mb-4 flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",id:"is_general",name:"is_general",checked:f.is_general,onChange:l=>{C({...f,is_general:l.target.checked})},className:"h-4 w-4 rounded border-gray-300 text-primaryBlue focus:ring-primaryBlue"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("label",{htmlFor:"is_general",className:"text-sm font-medium",children:"General price"}),e.jsxs("div",{className:"group relative",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4 text-gray-500",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z",clipRule:"evenodd"})}),e.jsx("div",{className:"absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100",children:"This price will be applied to all sports/types/sub-types for all times of day that are not otherwise defined."})]})]})]}),f.is_general?e.jsx("div",{className:"mb-6 space-y-4 rounded-lg bg-gray-50 p-4",children:e.jsx("div",{className:"flex w-full max-w-[300px]",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"General rate per hour"}),e.jsx("div",{className:"relative",children:e.jsx(Ue,{value:f.general_rate,onChange:l=>C({...f,general_rate:l.target.value})})})]})})}):e.jsxs("div",{className:"space-y-2",children:[f.price_by_hours.map((l,L)=>e.jsx("div",{className:"space-y-4",children:l.showInput?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Hours"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsx(we,{label:"Start time",value:l.start_time,onChange:E=>X(L,"start_time",E.target.value)})}),e.jsx("div",{className:"flex-1",children:e.jsx(we,{label:"End time",value:l.end_time,onChange:E=>X(L,"end_time",E.target.value),minTime:l.start_time})})]})]}),e.jsx("div",{className:"flex w-full max-w-[300px]",children:e.jsxs("div",{className:"flex flex-col",children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Rate per hour"}),e.jsx("div",{className:"relative",children:e.jsx(Ue,{value:l.rate,onChange:E=>X(L,"rate",E.target.value)})})]})}),e.jsx("div",{className:"flex gap-4",children:e.jsx("button",{onClick:()=>S(L),className:"rounded-lg bg-primaryBlue px-4 py-2 text-sm text-white",children:"Save"})})]}):e.jsx("div",{className:"rounded-lg bg-gray-50 p-4",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Hours"}),e.jsxs("div",{className:"text-sm text-gray-900",children:[je(l.start_time)," -"," ",je(l.end_time)]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("label",{className:"text-sm font-medium text-gray-700",children:"Rate"}),e.jsxs("div",{className:"text-sm text-gray-900",children:["$",l.rate,"/hour"]})]})]}),e.jsx("button",{onClick:()=>y(L),className:"text-sm text-primaryBlue underline",children:"Edit"})]})})},L)),e.jsx("button",{onClick:F,className:"text-primaryBlue underline underline-offset-2",children:"+ Add pricing"})]})]}),e.jsxs("div",{className:"mb-3 flex gap-4 border-b pb-4",children:[e.jsx("div",{className:"rounded-lg border border-gray-200 px-4 py-2 text-sm text-gray-500",children:"Cancel"}),e.jsx(se,{className:"rounded-lg bg-primaryBlue px-4 py-2 text-sm text-white",onClick:U,loading:p,children:t?"Update":"Save"})]})]})})}function Is({onNext:R,setValue:u,defaultValues:w,onSubmit:N,sportsOffered:t}){const[r,p]=a.useState([]),[v,f]=a.useState(null),[C,d]=a.useState(!1),[x,G]=a.useState(null),[M,U]=ve.useState(!1);ve.useEffect(()=>{const y=c=>{v!==null&&!c.target.closest(".dropdown-container")&&f(null)};return document.addEventListener("mousedown",y),()=>{document.removeEventListener("mousedown",y)}},[v]);const D=(y,c)=>{c.stopPropagation(),f(v===y?null:y)},ee=()=>{d(!0)},Y=y=>{d(!1)},X=async()=>{u("pricing",r),U(!0),await N(),U(!1)},F=y=>{p(r.filter((c,_)=>_!==y)),f(null)},S=(y,c)=>{G({...y,index:c}),d(!0),f(null)};return console.log("pricing rows",r),e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"w-full max-w-xl p-4",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center gap-3",children:[e.jsx("div",{children:e.jsxs("svg",{width:"74",height:"74",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",fill:"url(#paint0_linear_397_27001)"}),e.jsx("rect",{x:"0.5",y:"0.5",width:"73",height:"73",rx:"36.5",stroke:"url(#paint1_linear_397_27001)"}),e.jsxs("g",{filter:"url(#filter0_d_397_27001)",children:[e.jsx("rect",{x:"8",y:"8",width:"58",height:"58",rx:"29",fill:"white"}),e.jsx("rect",{x:"8.5",y:"8.5",width:"57",height:"57",rx:"28.5",stroke:"#E2E4E9"}),e.jsx("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24.5 37C24.5 30.0964 30.0964 24.5 37 24.5C43.9036 24.5 49.5 30.0964 49.5 37C49.5 43.9036 43.9036 49.5 37 49.5C30.0964 49.5 24.5 43.9036 24.5 37ZM37 28.9965C37.5178 28.9965 37.9375 29.4163 37.9375 29.934V31.0132C39.0083 31.2211 39.9523 31.7812 40.5418 32.5965C40.8452 33.0161 40.751 33.6021 40.3315 33.9055C39.9119 34.2089 39.3258 34.1147 39.0224 33.6952C38.6686 33.2059 37.9339 32.7986 37 32.7986H36.6431C35.3781 32.7986 34.7257 33.5858 34.7257 34.1451V34.2431C34.7257 34.6719 35.0377 35.2054 35.7823 35.5032L38.9141 36.7559C40.1869 37.2651 41.1493 38.3812 41.1493 39.7569C41.1493 41.5317 39.6337 42.7526 37.9375 43.0208V44.066C37.9375 44.5837 37.5178 45.0035 37 45.0035C36.4822 45.0035 36.0625 44.5837 36.0625 44.066V42.9868C34.9917 42.7789 34.0477 42.2188 33.4582 41.4035C33.1548 40.9839 33.249 40.3979 33.6685 40.0945C34.0881 39.7911 34.6742 39.8853 34.9776 40.3048C35.3314 40.7941 36.0661 41.2014 37 41.2014H37.2343C38.567 41.2014 39.2743 40.3703 39.2743 39.7569C39.2743 39.3281 38.9623 38.7946 38.2177 38.4968L35.0859 37.2441C33.8131 36.7349 32.8507 35.6188 32.8507 34.2431V34.1451C32.8507 32.3833 34.3834 31.1896 36.0625 30.9629V29.934C36.0625 29.4163 36.4822 28.9965 37 28.9965Z",fill:"#176448"})]}),e.jsxs("defs",{children:[e.jsxs("filter",{id:"filter0_d_397_27001",x:"4",y:"6",width:"66",height:"66",filterUnits:"userSpaceOnUse","color-interpolation-filters":"sRGB",children:[e.jsx("feFlood",{"flood-opacity":"0",result:"BackgroundImageFix"}),e.jsx("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),e.jsx("feOffset",{dy:"2"}),e.jsx("feGaussianBlur",{stdDeviation:"2"}),e.jsx("feColorMatrix",{type:"matrix",values:"0 0 0 0 0.105882 0 0 0 0 0.109804 0 0 0 0 0.113725 0 0 0 0.04 0"}),e.jsx("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_397_27001"}),e.jsx("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_397_27001",result:"shape"})]}),e.jsxs("linearGradient",{id:"paint0_linear_397_27001",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7","stop-opacity":"0.48"}),e.jsx("stop",{offset:"1","stop-color":"#F7F8F8","stop-opacity":"0"}),e.jsx("stop",{offset:"1","stop-color":"#E4E5E7","stop-opacity":"0"})]}),e.jsxs("linearGradient",{id:"paint1_linear_397_27001",x1:"37",y1:"0",x2:"37",y2:"74",gradientUnits:"userSpaceOnUse",children:[e.jsx("stop",{"stop-color":"#E4E5E7"}),e.jsx("stop",{offset:"0.765625","stop-color":"#E4E5E7","stop-opacity":"0"})]})]})]})}),e.jsx("p",{className:"mb-6 text-2xl font-semibold",children:"Pricing"})]}),e.jsxs("div",{className:"space-y-4",children:[r==null?void 0:r.map((y,c)=>{var _;return e.jsxs("div",{className:"relative rounded-xl bg-gray-50 p-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[y.sport_id?e.jsx("span",{className:"capitalize",children:((_=t==null?void 0:t.find(B=>B.id==y.sport_id&&B.status===1))==null?void 0:_.name)||""}):e.jsx("span",{className:"text-gray-500",children:"All sports"}),y.type&&y.type!==""&&e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"•"}),e.jsx("span",{className:"capitalize",children:y.type})]}),y.sub_type&&y.sub_type!==""&&e.jsxs(e.Fragment,{children:[e.jsx("span",{children:"•"}),e.jsx("span",{className:"capitalize",children:y.sub_type})]})]}),e.jsxs("div",{className:"dropdown-container relative",children:[e.jsx("button",{className:"text-gray-400 hover:text-gray-600",onClick:B=>D(c,B),children:e.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 110-2 1 1 0 010 2zm7 0a1 1 0 110-2 1 1 0 010 2zm7 0a1 1 0 110-2 1 1 0 010 2z"})})}),v===c&&e.jsx("div",{className:"absolute right-0 top-8 z-10 w-36 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5",onClick:B=>B.stopPropagation(),children:e.jsxs("div",{className:"py-1",children:[e.jsxs("button",{onClick:()=>S(y,c),className:"flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})}),"Edit"]}),e.jsxs("button",{onClick:()=>F(c),className:"flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:[e.jsx("svg",{className:"mr-3 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})}),"Delete"]})]})})]})]}),e.jsx("div",{className:"mt-2 space-y-2",children:y.is_general?e.jsxs("div",{className:"flex items-center gap-5",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{className:"font-medium text-gray-600",children:"General price"}),e.jsxs("div",{className:"group relative",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",className:"h-4 w-4 text-gray-500",children:e.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.94 6.94a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm-.75 3a.75.75 0 100 ********* 0 000-1.5z",clipRule:"evenodd"})}),e.jsx("div",{className:"absolute bottom-full left-1/2 z-10 mb-2 w-64 -translate-x-1/2 transform rounded-lg bg-gray-800 p-2 text-xs text-white opacity-0 shadow-lg transition-opacity group-hover:opacity-100",children:"This price will be applied to all sports/types/sub-types for all times of day that are not otherwise defined."})]})]}),e.jsxs("span",{className:"rounded-full bg-[#CBF5E5] px-3 py-1 font-medium text-primaryGreen",children:["$",y.general_rate,"/H"]})]}):y.price_by_hours.map((B,z)=>e.jsxs("div",{className:"flex items-center gap-5",children:[B.start_time&&B.end_time?e.jsxs("span",{className:"text-gray-600",children:[je(B.start_time)," -"," ",je(B.end_time)]}):e.jsx("span",{className:"text-gray-600",children:"All times"}),e.jsxs("span",{className:"rounded-full bg-[#CBF5E5] px-3 py-1 font-medium text-primaryGreen",children:["$",B.rate,"/H"]})]},z))})]},c)}),(r==null?void 0:r.length)===0&&e.jsxs("button",{onClick:ee,className:"flex w-fit items-center justify-center space-x-2 border-b border-black",children:[e.jsx("span",{className:"text-xl",children:"+"}),e.jsx("span",{children:"Add pricing"})]}),(r==null?void 0:r.length)>0&&e.jsxs("button",{onClick:ee,className:"flex w-fit items-center justify-center space-x-2 border-b border-black",children:[e.jsx("span",{className:"text-xl",children:"+"}),e.jsx("span",{children:"Add another"})]}),e.jsx(se,{onClick:X,loading:M,className:"w-full rounded-xl bg-primaryGreen py-3 text-white",children:"Continue"})]})]}),e.jsx(Ke,{isOpen:C,onClose:()=>{d(!1),G(null)},title:x?"Edit pricing":"Add pricing",showFooter:!1,children:e.jsx(Ls,{handleSave:Y,setValue:u,setPricingRows:p,pricingRows:r,editingPricing:x,defaultValues:w,sportsOffered:t})})]})}function Hs({onNext:R,setValue:u,defaultValues:w,isSubmitting:N}){const[t,r]=a.useState({contact_name:"",contact_email:"",contact_phone:""}),[p,v]=a.useState({});a.useEffect(()=>{if(w!=null&&w.private_contact){const x=typeof w.private_contact=="string"?JSON.parse(w.private_contact):w.private_contact;r({contact_name:(x==null?void 0:x.contact_name)||"",contact_email:(x==null?void 0:x.contact_email)||"",contact_phone:(x==null?void 0:x.contact_phone)||""})}},[w]);const f=(x,G)=>{r(M=>({...M,[x]:G})),p[x]&&v(M=>({...M,[x]:""}))},C=()=>{const x={};return t.contact_name.trim()||(x.contact_name="Contact name is required"),t.contact_email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t.contact_email)||(x.contact_email="Please enter a valid email address"):x.contact_email="Contact email is required",t.contact_phone.trim()||(x.contact_phone="Contact phone number is required"),v(x),Object.keys(x).length===0},d=()=>{C()&&(u("private_contact",JSON.stringify(t)),R())};return e.jsxs("div",{className:"flex w-full max-w-2xl flex-col items-center",children:[e.jsxs("div",{className:"mb-8 text-center",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-center gap-3",children:[e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#E8F5E8"}),e.jsx("path",{d:"M20 12C17.7909 12 16 13.7909 16 16C16 18.2091 17.7909 20 20 20C22.2091 20 24 18.2091 24 16C24 13.7909 22.2091 12 20 12Z",fill:"#005954"}),e.jsx("path",{d:"M20 22C15.5817 22 12 25.5817 12 30H28C28 25.5817 24.4183 22 20 22Z",fill:"#005954"})]}),e.jsx("h2",{className:"text-3xl font-semibold text-[#005954]",children:"Private Contact Information"})]}),e.jsx("p",{className:"text-lg text-gray-600",children:"Please provide your private contact details for administrative purposes. This information will be kept confidential and used for account management only."})]}),e.jsxs("div",{className:"w-full space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Contact Name *"}),e.jsx("input",{type:"text",value:t.contact_name,onChange:x=>f("contact_name",x.target.value),className:`w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#005954] ${p.contact_name?"border-red-500 focus:ring-red-500":"border-gray-300"}`,placeholder:"Enter your full name"}),p.contact_name&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:p.contact_name})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Contact Email *"}),e.jsx("input",{type:"email",value:t.contact_email,onChange:x=>f("contact_email",x.target.value),className:`w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#005954] ${p.contact_email?"border-red-500 focus:ring-red-500":"border-gray-300"}`,placeholder:"Enter your email address"}),p.contact_email&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:p.contact_email})]}),e.jsxs("div",{children:[e.jsx("label",{className:"mb-2 block text-sm font-medium text-gray-700",children:"Contact Phone Number *"}),e.jsx("input",{type:"tel",value:t.contact_phone,onChange:x=>f("contact_phone",x.target.value),className:`w-full rounded-xl border px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#005954] ${p.contact_phone?"border-red-500 focus:ring-red-500":"border-gray-300"}`,placeholder:"Enter your phone number"}),p.contact_phone&&e.jsx("p",{className:"mt-1 text-sm text-red-500",children:p.contact_phone})]}),e.jsx("div",{className:"rounded-lg bg-blue-50 p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"mt-0.5 flex-shrink-0",children:e.jsx("path",{d:"M10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18ZM9 9V15H11V9H9ZM9 5V7H11V5H9Z",fill:"#3B82F6"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-blue-900",children:"Privacy Notice"}),e.jsx("p",{className:"mt-1 text-sm text-blue-700",children:"This contact information is for administrative purposes only and will not be shared publicly. It will be used for account management, important notifications, and support communications."})]})]})}),e.jsx(se,{onClick:d,loading:N,className:"w-full rounded-xl bg-[#005954] py-3 text-white hover:bg-[#004540] focus:outline-none focus:ring-2 focus:ring-[#005954] focus:ring-offset-2",children:"Continue"})]})]})}const be=new ge;function Nt(){const R=es(),{dispatch:u}=a.useContext(Qe),[w,N]=a.useState(0),[t,r]=a.useState(!1),{dispatch:p}=a.useContext(Ne),[v,f]=a.useState(null),[C,d]=a.useState(!1),[x,G]=a.useState([]),[M]=a.useState([]),[U,D]=a.useState(null),ee=localStorage.getItem("role"),{triggerRefetch:Y}=Xe(),X=s=>{var m,k,A;if(!(s!=null&&s.club))return 0;const o=s.sports.some(I=>I.status===1);return s.club.private_contact?s.club.club_location?s.club.splash_screen?o?(m=s.courts)!=null&&m.length?!s.club.account_details||s.club.account_details==='"[]"'?5:(k=s.club.days_off)!=null&&k.length?((A=s.pricing)!=null&&A.length,7):6:4:3:2:1:0},{register:F,handleSubmit:S,control:y,getValues:c,setValue:_,formState:{errors:B},watch:z}=ts({defaultValues:{private_contact:null,courts:[]}}),{fields:l,append:L,remove:E}=rs({control:y,name:"courts"}),P=async()=>{var s;try{const o=await be.callRawAPI("/v3/api/custom/courtmatchup/club/profile",{},"GET");if(((s=o==null?void 0:o.model)==null?void 0:s.club.completed)==1)return R("/club/dashboard"),o.model;if(f(o==null?void 0:o.model),o!=null&&o.model){const{club:m,courts:k=[]}=o.model;let A=null;try{A=m.club_location&&m.club_location.trim()!==""?JSON.parse(m.club_location):null}catch(g){console.error("Error parsing club_location:",g),A=null}let I=[];try{I=m.account_details&&m.account_details!=='"[]"'&&m.account_details.trim()!==""?JSON.parse(m.account_details):[]}catch(g){console.error("Error parsing account_details:",g),I=[]}const J=A?JSON.stringify({lat:A.lat,lng:A.lng,address:A.address||""}):null;G(o.model.sports),_("private_contact",m.private_contact||null),_("club_location",J);let q=[];try{q=m.splash_screen&&m.splash_screen.trim()!==""?JSON.parse(m.splash_screen):[]}catch(g){console.error("Error parsing splash_screen:",g),q=[]}_("splash_screen",q),_("sports",o.model.sports);let le=[];try{le=m.times&&m.times.trim()!==""?JSON.parse(m.times):[]}catch(g){console.error("Error parsing times:",g),le=[]}_("times",le),_("max_players",m.max_players),_("account_details",I);let n=[];try{n=m.days_off&&m.days_off.trim()!==""?JSON.parse(m.days_off):[]}catch(g){console.error("Error parsing days_off:",g),n=[]}_("days_off",n),_("courts",k.length?k:[]),_("pricing",o.model.pricing||[]),_("club_logo",m.club_logo),_("name",m.name)}return o.model}catch(o){return Te(u,o.code),V(p,o.message,3e3,"error"),null}},W=async()=>{try{const s=await be.callRawAPI(`/v3/api/custom/courtmatchup/${ee}/stripe/account/verify`,{},"POST");D(s)}catch(s){return console.error("Error checking Stripe connection:",s),!1}},K=async(s=null)=>{var m;const o=c();d(!0);try{let k={};switch(w){case 0:k.private_contact=o.private_contact;break;case 1:k.club_location=o.club_location;break;case 2:s?k=s:(k.splash_screen=o.splash_screen,k.club_logo=o.club_logo);break;case 3:break;case 4:k.courts=(m=o.courts)==null?void 0:m.map(I=>({type:I.type,sub_type:I.sub_type,sport_id:I.sport_id,club_sport_id:I==null?void 0:I.sport_id,name:I.name,...(I==null?void 0:I.id)&&{court_id:I.id}}));break;case 5:k.account_details=o.account_details;break;case 6:k.times=o.times,k.days_off=o.days_off,k.max_players=o.max_players;break;case 7:k.pricing=o.pricing,k.completed=1;break}await be.callRawAPI("/v3/api/custom/courtmatchup/club/profile-edit",k,"POST");const A=await P();Y(),A&&f(A),N(I=>I+1)}catch(k){console.error(k),V(p,k.message,3e3,"error"),Te(u,k.code)}finally{d(!1)}},ie=()=>{N(Math.max(w-1,0))},oe=async s=>{d(!0);try{await be.callRawAPI("/v3/api/custom/courtmatchup/club/profile-edit",{pricing:s.pricing,completed:1},"POST"),r(!0)}catch(o){console.error(o),V(p,"Error saving data",3e3,"error")}finally{d(!1)}};a.useEffect(()=>{var s,o,m;(async()=>{const k=await P();N(X(k))})(),W(),Ve({title:(s=v==null?void 0:v.club)==null?void 0:s.name,path:"/club/profile-setup",clubName:(o=v==null?void 0:v.club)==null?void 0:o.name,favicon:(m=v==null?void 0:v.club)==null?void 0:m.club_logo,description:"Club Profile Setup"})},[]),a.useEffect(()=>{if(w===5){const s=setInterval(()=>{W()},5e3);return()=>clearInterval(s)}},[w]);const h=()=>{const s=z();switch(w){case 0:return e.jsx(Hs,{onNext:K,setValue:_,defaultValues:s,isSubmitting:C});case 1:return e.jsx(bs,{onNext:K,register:F,setValue:_,errors:B,defaultValues:s,isSubmitting:C});case 2:return e.jsx(ws,{onNext:K,onBack:ie,register:F,fields:l,append:L,remove:E,setValue:_,clubProfile:v,defaultValues:s,isSubmitting:C});case 3:return e.jsx(Cs,{onNext:K,register:F,errors:B,setValue:_,clubProfile:v,defaultValues:s,isSubmitting:C,defaultSports:M,fetchClubProfile:P});case 4:return e.jsx(Ms,{onNext:K,register:F,errors:B,values:s,setValue:_,watch:z,defaultValues:s,isSubmitting:C});case 5:return e.jsx(Es,{onNext:K,isSubmitting:C,stripeConnectionData:U,setStripeConnectionData:D});case 6:return e.jsx(Ss,{onNext:K,register:F,errors:B,setValue:_,defaultValues:s,isSubmitting:C});case 7:return e.jsx(Is,{onNext:K,register:F,onSubmit:S(oe),errors:B,setValue:_,defaultValues:s,isSubmitting:C,sportsOffered:x});default:return null}};return e.jsx(ss,{children:e.jsxs("div",{className:"flex flex-col bg-white pb-7",children:[e.jsxs("div",{className:"flex w-full flex-col px-20 pt-4 max-md:max-w-full max-md:px-5",children:[e.jsx("div",{className:"mb-10",children:w!==0&&e.jsxs("button",{className:"mt-5 flex items-center gap-2 text-[#525866]",onClick:ie,children:[e.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8.33333 4.79199L3.125 10.0003L8.33333 15.2087M3.75 10.0003H16.875",stroke:"#525866","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),e.jsx("span",{children:"Back"})]})}),e.jsx("div",{className:"flex flex-1 items-center justify-center",children:h()})]}),t&&e.jsx("div",{className:"fixed inset-0 z-[999] flex items-center justify-center bg-black bg-opacity-50",children:e.jsx("div",{className:"w-full max-w-xl rounded-2xl bg-white ",children:e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsxs("div",{className:"flex items-start gap-4 p-5",children:[e.jsx("div",{className:"",children:e.jsxs("svg",{width:"40",height:"40",viewBox:"0 0 40 40",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("rect",{width:"40",height:"40",rx:"10",fill:"#EFFAF6"}),e.jsx("path",{d:"M20 29C15.0293 29 11 24.9707 11 20C11 15.0293 15.0293 11 20 11C24.9707 11 29 15.0293 29 20C29 24.9707 24.9707 29 20 29ZM19.1027 23.6L25.4657 17.2361L24.1931 15.9635L19.1027 21.0548L16.5566 18.5087L15.284 19.7813L19.1027 23.6Z",fill:"#38C793"})]})}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-2xl font-medium",children:"Sign up complete!"}),e.jsx("p",{className:"mb-6 text-gray-600",children:"Congratulations! You have successfully signed up for Court Matchup. You can now access your club portal, manage your club, and start creating courts."})]})]}),e.jsx("div",{className:"flex w-full justify-end border-t border-gray-200 p-5",children:e.jsx(se,{onClick:()=>R("/club/dashboard"),className:"w-fit rounded-xl bg-primaryGreen px-5 py-3 text-white",children:"Continue to Club portal!"})})]})})})]})})}export{Nt as default};
