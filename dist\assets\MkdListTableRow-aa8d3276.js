import{j as r}from"./@nivo/heatmap-ba1ecfff.js";import{f as _}from"./vendor-851db8c1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";const $=({i:m,row:p,columns:g,actions:e,actionPostion:w,actionId:t="id",handleTableCellChange:h,selectedIds:v=[],handleSelectRow:N,setDeleteId:b,table:k,tableRole:y})=>{const o=_();return r.jsx(r.Fragment,{children:r.jsx("tr",{children:g.map((s,d)=>{var u,a,i,f,n,j,l,c;return s.accessor.indexOf("image")>-1?r.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:r.jsx("img",{src:p[s.accessor],className:"h-[3.rem] w-[9.375rem]",alt:""})},d):s.accessor.indexOf("pdf")>-1||s.accessor.indexOf("doc")>-1||s.accessor.indexOf("file")>-1||s.accessor.indexOf("video")>-1?r.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:r.jsxs("a",{className:"text-blue-500",target:"_blank",href:p[s.accessor],rel:"noreferrer",children:[" ","View"]})},d):s.accessor===""?[(u=e==null?void 0:e.select)==null?void 0:u.show,(a=e==null?void 0:e.view)==null?void 0:a.show,(i=e==null?void 0:e.edit)==null?void 0:i.show,(f=e==null?void 0:e.delete)==null?void 0:f.show].includes(!0)?r.jsxs("td",{className:"flex !w-full gap-2 whitespace-nowrap px-6 py-4",children:[((n=e==null?void 0:e.select)==null?void 0:n.show)&&r.jsx("span",{children:r.jsx("input",{className:"mr-1",type:"checkbox",name:"select_item",checked:v.includes(p[t]),onChange:()=>N(p[t])})}),w==="ontable"&&r.jsxs(r.Fragment,{children:[((j=e==null?void 0:e.edit)==null?void 0:j.show)&&r.jsx("button",{className:"cursor-pointer text-xs font-medium text-indigo-600 hover:underline",onClick:()=>{var x;(x=e==null?void 0:e.edit)!=null&&x.action&&e.edit.action([p[t]])},children:r.jsx("span",{children:"Edit"})}),((l=e==null?void 0:e.view)==null?void 0:l.show)&&r.jsx("button",{className:"cursor-pointer px-1 text-xs font-medium text-blue-500 hover:underline",onClick:()=>{var x;(x=e==null?void 0:e.view)!=null&&x.action&&e.view.action([p[t]]),o(`/${y}/view-${k}/`+p[t],{state:p})},children:r.jsx("span",{children:"View"})}),((c=e==null?void 0:e.delete)==null?void 0:c.show)&&r.jsx("button",{className:"cursor-pointer px-1 text-xs font-medium text-red-500 hover:underline",onClick:()=>{b(p[t])},children:r.jsx("span",{children:"Delete"})})]})]},d):null:s.mappingExist?r.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:r.jsx("select",{onChange:x=>h(p[t],x.target.value,m,s.accessor),children:Object.keys(s.mappings).map((x,O)=>r.jsx("option",{value:x,selected:x===p[s.accessor],children:s.mappings[x]},O))})},d):!s.mappingExist&&s.accessor!=="id"&&s.accessor!=="create_at"&&s.accessor!=="update_at"&&s.accessor!=="user_id"?r.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:r.jsx("input",{className:"text-ellipsis border-0",type:"text",value:p[s.accessor],onChange:x=>h(p[t],x.target.value,m,s.accessor)})},d):r.jsx("td",{className:"whitespace-nowrap px-6 py-4",children:p[s.accessor]},d)})})})};export{$ as default};
