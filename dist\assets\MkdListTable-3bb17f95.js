import{j as t}from"./@nivo/heatmap-ba1ecfff.js";import{b as n,f as T}from"./vendor-851db8c1.js";import{L as f,bl as L}from"./index-08a5dc5b.js";import{S as $}from"./index-4c2eb5df.js";import k from"./MkdListTableRow-aa8d3276.js";import z from"./MkdListTableHead-8984d225.js";import{M as C}from"./index-15e3ddc1.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const H={primary:"#0ea5e9",signup:"signup",add:"add",edit:"edit",search:"search",custom:"custom",secondary:"#F594C9",lightInfo:"#29282980"},Ie=({table:h,tableTitle:Y,onSort:I,loading:u,columns:x,actions:m,actionPostion:g,tableRole:q,deleteItem:M,deleteLoading:N,actionId:j="id",showDeleteModal:S,currentTableData:a,setShowDeleteModal:w,handleTableCellChange:E,setSelectedItems:i})=>{const[y,b]=n.useState(null),[B,d]=n.useState(!1),[v,p]=n.useState(!1),[s,l]=n.useState([]);function A(e){var R;d(!0);const r=s;if((R=m==null?void 0:m.select)!=null&&R.multiple)if(r.includes(e)){const o=r.filter(c=>c!==e);l(()=>[...o]),i(o)}else{const o=[...r,e];l(()=>[...o]),i(o)}else if(r.includes(e)){const o=r.filter(c=>c!==e);l(()=>[...o]),i(o)}else{const o=[e];l(()=>[...o]),i(o)}console.log(e)}const F=()=>{if(p(e=>!e),v)l([]),i([]);else{const e=a.map(r=>r[j]);l(e),i(e)}};T();const O=async e=>{console.log("id >>",e),w(!0),b(e)};return n.useEffect(()=>{s.length<=0&&(d(!1),p(!1)),s.length===a.length&&(p(!0),d(!0)),s.length<a.length&&s.length>0&&p(!1)},[s,a]),t.jsxs(t.Fragment,{children:[t.jsx("div",{className:`${u?"":"overflow-x-auto"} border-b border-gray-200 shadow`,children:u?t.jsx("div",{className:"flex max-h-fit min-h-fit min-w-fit max-w-full items-center justify-center  py-5",children:t.jsx($,{size:50,color:H.primary})}):t.jsx(t.Fragment,{children:t.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[t.jsx("thead",{className:"bg-gray-50",children:t.jsx(f,{children:t.jsx(z,{onSort:I,columns:x,actions:m,actionPostion:g,areAllRowsSelected:v,handleSelectAll:F})})}),t.jsx("tbody",{className:"divide-y divide-gray-200 bg-white",children:a.map((e,r)=>t.jsx(f,{children:t.jsx(k,{i:r,row:e,columns:x,actions:m,actionPostion:g,actionId:j,handleTableCellChange:E,handleSelectRow:A,selectedIds:s,setDeleteId:O},r)},r))})]})})}),t.jsx(f,{children:t.jsx(C,{open:S,actionHandler:()=>{M(y)},closeModalFunction:()=>{b(null),w(!1)},title:`Delete ${L(h)} `,message:`You are about to delete ${L(h)} ${y}, note that this action is irreversible`,acceptText:"DELETE",rejectText:"CANCEL",loading:N})})]})};export{Ie as default};
