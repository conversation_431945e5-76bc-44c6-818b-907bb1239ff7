import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{b as a,f as Le}from"./vendor-851db8c1.js";import{M as Ne,T as _e,G as Te,A as Ae,e as De,b as N,l as U,c as q,d as C,D as Ee,t as G}from"./index-08a5dc5b.js";import"./index-be4468eb.js";import"./yup-54691517.js";import{a as ke}from"./index.esm-9c6194ba.js";import"./AddButton.module-98aac587.js";import{P as Pe}from"./index-91353a7a.js";import"./lodash-91d5d207.js";import Ve from"./Skeleton-1e8bf077.js";import"./numeral-ea653b2a.js";import{T as He}from"./TimeslotPicker-2266a0a0.js";import{R as Ze}from"./RoleAccessManager-1bbc23b9.js";import{D as $e}from"./DataTable-84e69e98.js";import{H as we}from"./HistoryComponent-999cafaf.js";import{I as Ie}from"./InvitationForm-14649b13.js";const Re=n=>{if(!n)return"--";const h=n.replace(/\D/g,"");if(h.length<10)return n;const u=h.match(/^(\d{1})(\d{3})(\d{3})(\d{4})$/);if(u)return`(${u[2]}) ${u[3]}-${u[4]}`;if(h.length===10){const x=h.match(/^(\d{3})(\d{3})(\d{4})$/);return`(${x[1]}) ${x[2]}-${x[3]}`}return n};let f=new Ne,Oe=new _e;const Fe=["monday","tuesday","wednesday","thursday","friday","saturday","sunday"],Be=({club:n,sports:h})=>{var z,J;const{dispatch:u,state:x}=a.useContext(Te),{dispatch:S}=a.useContext(Ae),[K,Q]=a.useState([]),[c,_]=a.useState(10),[T,W]=a.useState(0),[m,X]=a.useState(1),[Y,ee]=a.useState(!1),[te,se]=a.useState(!1),[ze,ae]=a.useState(!1),[A,b]=a.useState(!0),[ie,Je]=a.useState(!1),[ne,Ue]=a.useState(!1),[D,j]=a.useState(!1),[M,y]=a.useState([]),[o,E]=a.useState(null),[oe,k]=a.useState(!1);a.useState([]);const[re,P]=a.useState(!1),[le,V]=a.useState(!1),[ce,de]=a.useState(null),[H,ue]=a.useState(!1);a.useState(null),a.useState(!1);const[Z,$]=a.useState(""),[w,me]=a.useState(null),[fe,I]=a.useState(!1),[ge,qe]=a.useState([]),he=localStorage.getItem("role"),R=localStorage.getItem("user"),pe=Le(),O=a.useRef(null),[xe,F]=a.useState(!1);function ye(){d(m-1,c)}function ve(){d(m+1,c)}async function d(e,t,i={},l=[]){b(!(ne||ie));try{const r=await Oe.getPaginate("staff",{page:e,limit:t,filter:[...l,`courtmatchup_staff.club_id,eq,${n==null?void 0:n.id}`],join:["user|user_id"]});r&&b(!1);const{list:g,limit:v,num_pages:p,page:L}=r;Q(g),_(v),W(p),X(L),ee(L>1),se(L+1<=p)}catch(r){b(!1),console.log("ERROR",r),G(S,r.message)}}a.useEffect(()=>{u({type:"SETPATH",payload:{path:"staff"}});const t=setTimeout(async()=>{await d(m,c,{})},700);return()=>{clearTimeout(t)}},[n==null?void 0:n.id]);const B=e=>{O.current&&!O.current.contains(e.target)&&ae(!1)};a.useEffect(()=>(document.addEventListener("mousedown",B),()=>{document.removeEventListener("mousedown",B)}),[]);const Ce=async({staff_id:e,user_id:t})=>{V(!0);try{f.setTable("user"),await f.callRestAPI({id:t},"DELETE"),f.setTable("staff"),await f.callRestAPI({id:e},"DELETE"),await U(f,{user_id:R,activity_type:N.staff_management,action_type:q.DELETE,data:{staff_id:e,staff_user_id:t},club_id:n==null?void 0:n.id,description:"Deleted staff"}),C(u,"Staff deleted successfully",3e3,"success"),d(m,c)}catch(i){console.error("Error deleting staff:",i),C(u,i.message,3e3,"error"),G(S,i.message)}V(!1)},Se=e=>{if(E(e),j(!0),e.availability)try{const t=JSON.parse(e.availability),i=Fe.map(l=>({day:l.toLowerCase(),timeslots:[]}));t&&Array.isArray(t)&&t.length>0&&t.forEach(l=>{const r=i.findIndex(g=>g.day===l.day.toLowerCase());r!==-1&&(i[r].timeslots=l.timeslots)}),y(i)}catch(t){console.error("Error parsing availability:",t),y([])}else y([])};a.useEffect(()=>{const e=t=>{H&&!t.target.closest("#contact-info-button")&&!t.target.closest(".contact-info-popover")&&ue(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[H]);const be=e=>{const t=e.target.value;$(t),w&&clearTimeout(w);const i=setTimeout(()=>{t.trim()?d(m,c,{},[`${f._project_id}_user.first_name,cs,${t}`]):d(m,c)},500);me(i)},je={name:e=>{var t,i,l,r,g,v,p;return s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("img",{src:((t=e.user)==null?void 0:t.photo)||"/default-avatar.png",alt:`${(i=e.user)==null?void 0:i.first_name} ${(l=e.user)==null?void 0:l.last_name}`,className:"h-10 w-10 rounded-full object-cover"}),s.jsx("span",{className:"font-medium capitalize text-gray-900",children:!((r=e==null?void 0:e.user)!=null&&r.first_name)||!((g=e==null?void 0:e.user)!=null&&g.last_name)?"--":`${(v=e==null?void 0:e.user)==null?void 0:v.first_name} ${(p=e==null?void 0:e.user)==null?void 0:p.last_name}`})]})},status:e=>{var t;return s.jsx("span",{className:"text-gray-600",children:((t=e==null?void 0:e.user)==null?void 0:t.status)===1?"Active":"Inactive"})},role:e=>{var t,i;return s.jsx("span",{className:"capitalize text-gray-600",children:(t=e==null?void 0:e.user)!=null&&t.role?(i=e==null?void 0:e.user)==null?void 0:i.role:"--"})},email:e=>{var t;return s.jsx("span",{className:"text-gray-600",children:((t=e==null?void 0:e.user)==null?void 0:t.email)||"--"})},phone:e=>{var t;return s.jsx("span",{className:"text-gray-600",children:(t=e==null?void 0:e.user)!=null&&t.phone?Re(e.user.phone):"--"})},bank_details:e=>s.jsx("span",{className:"text-gray-600",children:(()=>{var t;try{if(e!=null&&e.account_details){const i=JSON.parse(e.account_details);return i&&Array.isArray(i)&&i.length>0&&((t=i[0])!=null&&t.account_number)?`•••• ${i[0].account_number.slice(-4)}`:"--"}return"--"}catch{return"--"}})()}),actions:e=>s.jsxs("div",{className:"flex items-center justify-end gap-3",children:[s.jsx("button",{onClick:t=>{t.stopPropagation(),Se(e)},className:"rounded-full p-2 hover:bg-gray-100",children:s.jsx("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M2.46209 9.95722L3.76445 17.3433C3.86035 17.8872 4.37901 18.2503 4.9229 18.1544L10.1162 17.2387M2.46209 9.95722L1.94114 7.0028C1.84524 6.45891 2.20841 5.94025 2.7523 5.84434L14.0776 3.84739C14.6215 3.75149 15.1401 4.11466 15.236 4.65855L15.757 7.61297L2.46209 9.95722ZM16.0002 11.7509V14.0009L18.0002 16.0009M22.2502 14.0009C22.2502 17.4527 19.452 20.2509 16.0002 20.2509C12.5485 20.2509 9.75025 17.4527 9.75025 14.0009C9.75025 10.5491 12.5485 7.75092 16.0002 7.75092C19.452 7.75092 22.2502 10.5491 22.2502 14.0009Z",stroke:"#868C98",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}),s.jsx("button",{onClick:t=>{t.stopPropagation(),P(!0),de({staff_id:e.id,user_id:e.user_id})},className:"rounded-full p-2 hover:bg-gray-100",children:s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M4.74071 16.928L3.99227 16.9763L4.74071 16.928ZM15.258 16.928L16.0064 16.9763L16.0064 16.9763L15.258 16.928ZM2.29102 4.04102C1.8768 4.04102 1.54102 4.3768 1.54102 4.79102C1.54102 5.20523 1.8768 5.54102 2.29102 5.54102V4.04102ZM17.7077 5.54102C18.1219 5.54102 18.4577 5.20523 18.4577 4.79102C18.4577 4.3768 18.1219 4.04102 17.7077 4.04102V5.54102ZM8.87435 8.95768C8.87435 8.54347 8.53856 8.20768 8.12435 8.20768C7.71014 8.20768 7.37435 8.54347 7.37435 8.95768H8.87435ZM7.37435 13.541C7.37435 13.9552 7.71014 14.291 8.12435 14.291C8.53856 14.291 8.87435 13.9552 8.87435 13.541H7.37435ZM12.6243 8.95768C12.6243 8.54347 12.2886 8.20768 11.8743 8.20768C11.4601 8.20768 11.1243 8.54347 11.1243 8.95768H12.6243ZM11.1243 13.541C11.1243 13.9552 11.4601 14.291 11.8743 14.291C12.2886 14.291 12.6243 13.9552 12.6243 13.541H11.1243ZM12.5014 4.97796C12.6046 5.3791 13.0135 5.62059 13.4146 5.51734C13.8158 5.4141 14.0573 5.00521 13.954 4.60407L12.5014 4.97796ZM3.20924 4.8393L3.99227 16.9763L5.48916 16.8797L4.70613 4.74273L3.20924 4.8393ZM5.57232 18.4577H14.4264V16.9577H5.57232V18.4577ZM16.0064 16.9763L16.7895 4.8393L15.2926 4.74273L14.5095 16.8797L16.0064 16.9763ZM16.041 4.04102H3.95768V5.54102H16.041V4.04102ZM2.29102 5.54102H3.95768V4.04102H2.29102V5.54102ZM16.041 5.54102H17.7077V4.04102H16.041V5.54102ZM14.4264 18.4577C15.2613 18.4577 15.9527 17.8094 16.0064 16.9763L14.5095 16.8797C14.5067 16.9236 14.4703 16.9577 14.4264 16.9577V18.4577ZM3.99227 16.9763C4.04602 17.8094 4.73744 18.4577 5.57232 18.4577V16.9577C5.52838 16.9577 5.49199 16.9236 5.48916 16.8797L3.99227 16.9763ZM7.37435 8.95768V13.541H8.87435V8.95768H7.37435ZM11.1243 8.95768V13.541H12.6243V8.95768H11.1243ZM9.99937 3.04102C11.2021 3.04102 12.2145 3.86359 12.5014 4.97796L13.954 4.60407C13.5008 2.84306 11.9031 1.54102 9.99937 1.54102V3.04102ZM7.49738 4.97796C7.78419 3.86359 8.79666 3.04102 9.99937 3.04102V1.54102C8.09567 1.54102 6.49797 2.84306 6.04472 4.60407L7.49738 4.97796Z",fill:"#868C98"})})})]})},Me=[{header:"Name",accessor:"name"},{header:"Status",accessor:"status"},{header:"Role",accessor:"role"},{header:"Email",accessor:"email"},{header:"Phone",accessor:"phone"},{header:"Bank details",accessor:"bank_details"},{header:"Actions",accessor:"actions"}];return s.jsxs("div",{className:"h-full",children:[s.jsxs("div",{className:"flex w-full flex-col justify-between gap-4 py-3 md:flex-row md:items-center",children:[s.jsx("div",{className:"flex flex-col gap-4 md:flex-row md:items-center md:gap-4",children:s.jsxs("div",{className:"relative flex max-w-md flex-1 items-center",children:[s.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:s.jsx(De,{className:"text-gray-500"})}),s.jsx("input",{type:"text",value:Z,onChange:be,className:"block w-full min-w-[200px] rounded-md border border-gray-200 py-2 pl-10 pr-12 text-sm placeholder:text-gray-500 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500",placeholder:"Search staff by name or email"}),Z&&s.jsx("button",{onClick:()=>{$(""),d(m,c)},className:"absolute right-2 rounded-full p-1 hover:bg-gray-100",children:s.jsx(ke,{className:"text-gray-500"})})]})}),s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsxs("button",{onClick:()=>I(!0),className:"inline-flex items-center gap-2 rounded-lg border border-gray-200 px-4 py-2 text-sm font-semibold text-gray-700 hover:bg-gray-50",children:[s.jsx("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M4.75038 16.8788V11.6684M4.75038 8.54214V3.12305M9.9992 16.6703V10.6259M9.99922 7.49978V3.33138M15.248 16.8785V13.3354M15.248 10.2095V3.12305M3.12109 11.46H6.45753M8.33253 7.70801H11.6659M13.5409 13.1271H16.8742",stroke:"#868C98","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})}),"Manage role access"]}),s.jsxs("button",{onClick:()=>F(!0),className:"inline-flex items-center gap-2 rounded-lg bg-[#1D275F] px-4 py-2 text-sm font-semibold text-white hover:bg-blue-700",children:[s.jsx("span",{className:"",children:"+"}),"Add new"]})]}),s.jsx("div",{className:" ",children:s.jsx(we,{title:"Staff History",emptyMessage:"No staff history found",activityType:N.staff_management,club:n})})]}),A?s.jsx(Ve,{}):s.jsx($e,{columns:Me,data:K,loading:A,renderCustomCell:je,rowClassName:"hover:bg-gray-50 bg-gray-100 px-4 py-3 cursor-pointer",emptyMessage:"No staff members found",loadingMessage:"Loading staff members...",onClick:e=>pe(`/${he}/view-staff/${e.id}`)}),T>0&&s.jsx(Pe,{currentPage:m,pageCount:T,pageSize:c,canPreviousPage:Y,canNextPage:te,updatePageSize:e=>{_(e),d(1,e)},previousPage:ye,nextPage:ve,gotoPage:e=>d(e,c)}),D&&o&&s.jsx(He,{showTimesAvailableModal:D,setShowTimesAvailableModal:j,selectedTimes:M,setSelectedTimes:y,title:`${(z=o==null?void 0:o.user)==null?void 0:z.first_name} ${(J=o==null?void 0:o.user)==null?void 0:J.last_name}'s availability`,isSubmitting:oe,onSave:async e=>{var t,i;try{k(!0);const r=(()=>M?M.filter(g=>g.timeslots.length>0):[])();f.setTable("staff"),await f.callRestAPI({id:o.id,availability:JSON.stringify(r)},"PUT"),await U(f,{user_id:R,activity_type:N.staff_management,action_type:q.UPDATE,data:{staff_id:o.id,availability:r},club_id:n==null?void 0:n.id,description:`Updated availability for ${(t=o==null?void 0:o.user)==null?void 0:t.first_name} ${(i=o==null?void 0:o.user)==null?void 0:i.last_name}`}),C(u,"Availability updated successfully",3e3,"success"),d(m,c)}catch(l){console.error("Error saving availability:",l),C(S,l.message,3e3,"error")}finally{k(!1),j(!1),E(null)}}}),s.jsx(Ee,{isOpen:re,onClose:()=>P(!1),onDelete:()=>Ce(ce),message:"Are you sure you want to delete this staff?",loading:le,title:"Delete Staff"}),xe&&s.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50",children:s.jsx("div",{className:"w-full max-w-md rounded-lg bg-white shadow-lg",children:s.jsx(Ie,{onClose:()=>F(!1),role:"staff",club_id:n==null?void 0:n.id})})}),s.jsx(Ze,{isOpen:fe,onClose:()=>I(!1),roleAccessData:ge,club:n})]})},dt=Be;export{dt as L};
