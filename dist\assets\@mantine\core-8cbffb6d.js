import{r as Ft}from"../vendor-851db8c1.js";function ue(t,e){if(t==null)return{};var n={};for(var o in t)if({}.hasOwnProperty.call(t,o)){if(e.includes(o))continue;n[o]=t[o]}return n}const j=Math.min,H=Math.max,nt=Math.round,et=Math.floor,T=t=>({x:t,y:t}),Wt={left:"right",right:"left",bottom:"top",top:"bottom"},Bt={start:"end",end:"start"};function ft(t,e,n){return H(t,j(e,n))}function Q(t,e){return typeof t=="function"?t(e):t}function $(t){return t.split("-")[0]}function Z(t){return t.split("-")[1]}function At(t){return t==="x"?"y":"x"}function dt(t){return t==="y"?"height":"width"}function Y(t){return["top","bottom"].includes($(t))?"y":"x"}function mt(t){return At(Y(t))}function Mt(t,e,n){n===void 0&&(n=!1);const o=Z(t),i=mt(t),r=dt(i);let s=i==="x"?o===(n?"end":"start")?"right":"left":o==="start"?"bottom":"top";return e.reference[r]>e.floating[r]&&(s=ot(s)),[s,ot(s)]}function Nt(t){const e=ot(t);return[at(t),e,at(e)]}function at(t){return t.replace(/start|end/g,e=>Bt[e])}function Vt(t,e,n){const o=["left","right"],i=["right","left"],r=["top","bottom"],s=["bottom","top"];switch(t){case"top":case"bottom":return n?e?i:o:e?o:i;case"left":case"right":return e?r:s;default:return[]}}function Ht(t,e,n,o){const i=Z(t);let r=Vt($(t),n==="start",o);return i&&(r=r.map(s=>s+"-"+i),e&&(r=r.concat(r.map(at)))),r}function ot(t){return t.replace(/left|right|bottom|top/g,e=>Wt[e])}function $t(t){return{top:0,right:0,bottom:0,left:0,...t}}function Ot(t){return typeof t!="number"?$t(t):{top:t,right:t,bottom:t,left:t}}function it(t){const{x:e,y:n,width:o,height:i}=t;return{width:o,height:i,top:n,left:e,right:e+o,bottom:n+i,x:e,y:n}}function xt(t,e,n){let{reference:o,floating:i}=t;const r=Y(e),s=mt(e),c=dt(s),l=$(e),f=r==="y",d=o.x+o.width/2-i.width/2,u=o.y+o.height/2-i.height/2,m=o[c]/2-i[c]/2;let a;switch(l){case"top":a={x:d,y:o.y-i.height};break;case"bottom":a={x:d,y:o.y+o.height};break;case"right":a={x:o.x+o.width,y:u};break;case"left":a={x:o.x-i.width,y:u};break;default:a={x:o.x,y:o.y}}switch(Z(e)){case"start":a[s]-=m*(n&&f?-1:1);break;case"end":a[s]+=m*(n&&f?-1:1);break}return a}const _t=async(t,e,n)=>{const{placement:o="bottom",strategy:i="absolute",middleware:r=[],platform:s}=n,c=r.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(e));let f=await s.getElementRects({reference:t,floating:e,strategy:i}),{x:d,y:u}=xt(f,o,l),m=o,a={},h=0;for(let g=0;g<c.length;g++){const{name:w,fn:p}=c[g],{x,y,data:b,reset:v}=await p({x:d,y:u,initialPlacement:o,placement:m,strategy:i,middlewareData:a,rects:f,platform:s,elements:{reference:t,floating:e}});d=x??d,u=y??u,a={...a,[w]:{...a[w],...b}},v&&h<=50&&(h++,typeof v=="object"&&(v.placement&&(m=v.placement),v.rects&&(f=v.rects===!0?await s.getElementRects({reference:t,floating:e,strategy:i}):v.rects),{x:d,y:u}=xt(f,m,l)),g=-1)}return{x:d,y:u,placement:m,strategy:i,middlewareData:a}};async function Rt(t,e){var n;e===void 0&&(e={});const{x:o,y:i,platform:r,rects:s,elements:c,strategy:l}=t,{boundary:f="clippingAncestors",rootBoundary:d="viewport",elementContext:u="floating",altBoundary:m=!1,padding:a=0}=Q(e,t),h=Ot(a),w=c[m?u==="floating"?"reference":"floating":u],p=it(await r.getClippingRect({element:(n=await(r.isElement==null?void 0:r.isElement(w)))==null||n?w:w.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(c.floating)),boundary:f,rootBoundary:d,strategy:l})),x=u==="floating"?{x:o,y:i,width:s.floating.width,height:s.floating.height}:s.reference,y=await(r.getOffsetParent==null?void 0:r.getOffsetParent(c.floating)),b=await(r.isElement==null?void 0:r.isElement(y))?await(r.getScale==null?void 0:r.getScale(y))||{x:1,y:1}:{x:1,y:1},v=it(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:x,offsetParent:y,strategy:l}):x);return{top:(p.top-v.top+h.top)/b.y,bottom:(v.bottom-p.bottom+h.bottom)/b.y,left:(p.left-v.left+h.left)/b.x,right:(v.right-p.right+h.right)/b.x}}const zt=t=>({name:"arrow",options:t,async fn(e){const{x:n,y:o,placement:i,rects:r,platform:s,elements:c,middlewareData:l}=e,{element:f,padding:d=0}=Q(t,e)||{};if(f==null)return{};const u=Ot(d),m={x:n,y:o},a=mt(i),h=dt(a),g=await s.getDimensions(f),w=a==="y",p=w?"top":"left",x=w?"bottom":"right",y=w?"clientHeight":"clientWidth",b=r.reference[h]+r.reference[a]-m[a]-r.floating[h],v=m[a]-r.reference[a],E=await(s.getOffsetParent==null?void 0:s.getOffsetParent(f));let M=E?E[y]:0;(!M||!await(s.isElement==null?void 0:s.isElement(E)))&&(M=c.floating[y]||r.floating[h]);const U=b/2-v/2,N=M/2-g[h]/2-1,k=j(u[p],N),K=j(u[x],N),V=k,G=M-g[h]-K,A=M/2-g[h]/2+U,z=ft(V,A,G),D=!l.arrow&&Z(i)!=null&&A!==z&&r.reference[h]/2-(A<V?k:K)-g[h]/2<0,L=D?A<V?A-V:A-G:0;return{[a]:m[a]+L,data:{[a]:z,centerOffset:A-z-L,...D&&{alignmentOffset:L}},reset:D}}}),It=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var n,o;const{placement:i,middlewareData:r,rects:s,initialPlacement:c,platform:l,elements:f}=e,{mainAxis:d=!0,crossAxis:u=!0,fallbackPlacements:m,fallbackStrategy:a="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:g=!0,...w}=Q(t,e);if((n=r.arrow)!=null&&n.alignmentOffset)return{};const p=$(i),x=Y(c),y=$(c)===c,b=await(l.isRTL==null?void 0:l.isRTL(f.floating)),v=m||(y||!g?[ot(c)]:Nt(c)),E=h!=="none";!m&&E&&v.push(...Ht(c,g,h,b));const M=[c,...v],U=await Rt(e,w),N=[];let k=((o=r.flip)==null?void 0:o.overflows)||[];if(d&&N.push(U[p]),u){const A=Mt(i,s,b);N.push(U[A[0]],U[A[1]])}if(k=[...k,{placement:i,overflows:N}],!N.every(A=>A<=0)){var K,V;const A=(((K=r.flip)==null?void 0:K.index)||0)+1,z=M[A];if(z)return{data:{index:A,overflows:k},reset:{placement:z}};let D=(V=k.filter(L=>L.overflows[0]<=0).sort((L,F)=>L.overflows[1]-F.overflows[1])[0])==null?void 0:V.placement;if(!D)switch(a){case"bestFit":{var G;const L=(G=k.filter(F=>{if(E){const W=Y(F.placement);return W===x||W==="y"}return!0}).map(F=>[F.placement,F.overflows.filter(W=>W>0).reduce((W,kt)=>W+kt,0)]).sort((F,W)=>F[1]-W[1])[0])==null?void 0:G[0];L&&(D=L);break}case"initialPlacement":D=c;break}if(i!==D)return{reset:{placement:D}}}return{}}}};async function jt(t,e){const{placement:n,platform:o,elements:i}=t,r=await(o.isRTL==null?void 0:o.isRTL(i.floating)),s=$(n),c=Z(n),l=Y(n)==="y",f=["left","top"].includes(s)?-1:1,d=r&&l?-1:1,u=Q(e,t);let{mainAxis:m,crossAxis:a,alignmentAxis:h}=typeof u=="number"?{mainAxis:u,crossAxis:0,alignmentAxis:null}:{mainAxis:u.mainAxis||0,crossAxis:u.crossAxis||0,alignmentAxis:u.alignmentAxis};return c&&typeof h=="number"&&(a=c==="end"?h*-1:h),l?{x:a*d,y:m*f}:{x:m*f,y:a*d}}const Yt=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var n,o;const{x:i,y:r,placement:s,middlewareData:c}=e,l=await jt(e,t);return s===((n=c.offset)==null?void 0:n.placement)&&(o=c.arrow)!=null&&o.alignmentOffset?{}:{x:i+l.x,y:r+l.y,data:{...l,placement:s}}}}},Xt=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){const{x:n,y:o,placement:i}=e,{mainAxis:r=!0,crossAxis:s=!1,limiter:c={fn:w=>{let{x:p,y:x}=w;return{x:p,y:x}}},...l}=Q(t,e),f={x:n,y:o},d=await Rt(e,l),u=Y($(i)),m=At(u);let a=f[m],h=f[u];if(r){const w=m==="y"?"top":"left",p=m==="y"?"bottom":"right",x=a+d[w],y=a-d[p];a=ft(x,a,y)}if(s){const w=u==="y"?"top":"left",p=u==="y"?"bottom":"right",x=h+d[w],y=h-d[p];h=ft(x,h,y)}const g=c.fn({...e,[m]:a,[u]:h});return{...g,data:{x:g.x-n,y:g.y-o,enabled:{[m]:r,[u]:s}}}}}};function st(){return typeof window<"u"}function q(t){return Ct(t)?(t.nodeName||"").toLowerCase():"#document"}function O(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function S(t){var e;return(e=(Ct(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function Ct(t){return st()?t instanceof Node||t instanceof O(t).Node:!1}function R(t){return st()?t instanceof Element||t instanceof O(t).Element:!1}function P(t){return st()?t instanceof HTMLElement||t instanceof O(t).HTMLElement:!1}function yt(t){return!st()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof O(t).ShadowRoot}function tt(t){const{overflow:e,overflowX:n,overflowY:o,display:i}=C(t);return/auto|scroll|overlay|hidden|clip/.test(e+o+n)&&!["inline","contents"].includes(i)}function qt(t){return["table","td","th"].includes(q(t))}function rt(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch{return!1}})}function ht(t){const e=gt(),n=R(t)?C(t):t;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!e&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!e&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(o=>(n.willChange||"").includes(o))||["paint","layout","strict","content"].some(o=>(n.contain||"").includes(o))}function Ut(t){let e=B(t);for(;P(e)&&!X(e);){if(ht(e))return e;if(rt(e))return null;e=B(e)}return null}function gt(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function X(t){return["html","body","#document"].includes(q(t))}function C(t){return O(t).getComputedStyle(t)}function ct(t){return R(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function B(t){if(q(t)==="html")return t;const e=t.assignedSlot||t.parentNode||yt(t)&&t.host||S(t);return yt(e)?e.host:e}function Et(t){const e=B(t);return X(e)?t.ownerDocument?t.ownerDocument.body:t.body:P(e)&&tt(e)?e:Et(e)}function J(t,e,n){var o;e===void 0&&(e=[]),n===void 0&&(n=!0);const i=Et(t),r=i===((o=t.ownerDocument)==null?void 0:o.body),s=O(i);if(r){const c=ut(s);return e.concat(s,s.visualViewport||[],tt(i)?i:[],c&&n?J(c):[])}return e.concat(i,J(i,[],n))}function ut(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function Lt(t){const e=C(t);let n=parseFloat(e.width)||0,o=parseFloat(e.height)||0;const i=P(t),r=i?t.offsetWidth:n,s=i?t.offsetHeight:o,c=nt(n)!==r||nt(o)!==s;return c&&(n=r,o=s),{width:n,height:o,$:c}}function pt(t){return R(t)?t:t.contextElement}function I(t){const e=pt(t);if(!P(e))return T(1);const n=e.getBoundingClientRect(),{width:o,height:i,$:r}=Lt(e);let s=(r?nt(n.width):n.width)/o,c=(r?nt(n.height):n.height)/i;return(!s||!Number.isFinite(s))&&(s=1),(!c||!Number.isFinite(c))&&(c=1),{x:s,y:c}}const Kt=T(0);function Tt(t){const e=O(t);return!gt()||!e.visualViewport?Kt:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function Gt(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==O(t)?!1:e}function _(t,e,n,o){e===void 0&&(e=!1),n===void 0&&(n=!1);const i=t.getBoundingClientRect(),r=pt(t);let s=T(1);e&&(o?R(o)&&(s=I(o)):s=I(t));const c=Gt(r,n,o)?Tt(r):T(0);let l=(i.left+c.x)/s.x,f=(i.top+c.y)/s.y,d=i.width/s.x,u=i.height/s.y;if(r){const m=O(r),a=o&&R(o)?O(o):o;let h=m,g=ut(h);for(;g&&o&&a!==h;){const w=I(g),p=g.getBoundingClientRect(),x=C(g),y=p.left+(g.clientLeft+parseFloat(x.paddingLeft))*w.x,b=p.top+(g.clientTop+parseFloat(x.paddingTop))*w.y;l*=w.x,f*=w.y,d*=w.x,u*=w.y,l+=y,f+=b,h=O(g),g=ut(h)}}return it({width:d,height:u,x:l,y:f})}function wt(t,e){const n=ct(t).scrollLeft;return e?e.left+n:_(S(t)).left+n}function Pt(t,e,n){n===void 0&&(n=!1);const o=t.getBoundingClientRect(),i=o.left+e.scrollLeft-(n?0:wt(t,o)),r=o.top+e.scrollTop;return{x:i,y:r}}function Jt(t){let{elements:e,rect:n,offsetParent:o,strategy:i}=t;const r=i==="fixed",s=S(o),c=e?rt(e.floating):!1;if(o===s||c&&r)return n;let l={scrollLeft:0,scrollTop:0},f=T(1);const d=T(0),u=P(o);if((u||!u&&!r)&&((q(o)!=="body"||tt(s))&&(l=ct(o)),P(o))){const a=_(o);f=I(o),d.x=a.x+o.clientLeft,d.y=a.y+o.clientTop}const m=s&&!u&&!r?Pt(s,l,!0):T(0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-l.scrollLeft*f.x+d.x+m.x,y:n.y*f.y-l.scrollTop*f.y+d.y+m.y}}function Qt(t){return Array.from(t.getClientRects())}function Zt(t){const e=S(t),n=ct(t),o=t.ownerDocument.body,i=H(e.scrollWidth,e.clientWidth,o.scrollWidth,o.clientWidth),r=H(e.scrollHeight,e.clientHeight,o.scrollHeight,o.clientHeight);let s=-n.scrollLeft+wt(t);const c=-n.scrollTop;return C(o).direction==="rtl"&&(s+=H(e.clientWidth,o.clientWidth)-i),{width:i,height:r,x:s,y:c}}function te(t,e){const n=O(t),o=S(t),i=n.visualViewport;let r=o.clientWidth,s=o.clientHeight,c=0,l=0;if(i){r=i.width,s=i.height;const f=gt();(!f||f&&e==="fixed")&&(c=i.offsetLeft,l=i.offsetTop)}return{width:r,height:s,x:c,y:l}}function ee(t,e){const n=_(t,!0,e==="fixed"),o=n.top+t.clientTop,i=n.left+t.clientLeft,r=P(t)?I(t):T(1),s=t.clientWidth*r.x,c=t.clientHeight*r.y,l=i*r.x,f=o*r.y;return{width:s,height:c,x:l,y:f}}function vt(t,e,n){let o;if(e==="viewport")o=te(t,n);else if(e==="document")o=Zt(S(t));else if(R(e))o=ee(e,n);else{const i=Tt(t);o={x:e.x-i.x,y:e.y-i.y,width:e.width,height:e.height}}return it(o)}function St(t,e){const n=B(t);return n===e||!R(n)||X(n)?!1:C(n).position==="fixed"||St(n,e)}function ne(t,e){const n=e.get(t);if(n)return n;let o=J(t,[],!1).filter(c=>R(c)&&q(c)!=="body"),i=null;const r=C(t).position==="fixed";let s=r?B(t):t;for(;R(s)&&!X(s);){const c=C(s),l=ht(s);!l&&c.position==="fixed"&&(i=null),(r?!l&&!i:!l&&c.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||tt(s)&&!l&&St(t,s))?o=o.filter(d=>d!==s):i=c,s=B(s)}return e.set(t,o),o}function oe(t){let{element:e,boundary:n,rootBoundary:o,strategy:i}=t;const s=[...n==="clippingAncestors"?rt(e)?[]:ne(e,this._c):[].concat(n),o],c=s[0],l=s.reduce((f,d)=>{const u=vt(e,d,i);return f.top=H(u.top,f.top),f.right=j(u.right,f.right),f.bottom=j(u.bottom,f.bottom),f.left=H(u.left,f.left),f},vt(e,c,i));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function ie(t){const{width:e,height:n}=Lt(t);return{width:e,height:n}}function se(t,e,n){const o=P(e),i=S(e),r=n==="fixed",s=_(t,!0,r,e);let c={scrollLeft:0,scrollTop:0};const l=T(0);if(o||!o&&!r)if((q(e)!=="body"||tt(i))&&(c=ct(e)),o){const m=_(e,!0,r,e);l.x=m.x+e.clientLeft,l.y=m.y+e.clientTop}else i&&(l.x=wt(i));const f=i&&!o&&!r?Pt(i,c):T(0),d=s.left+c.scrollLeft-l.x-f.x,u=s.top+c.scrollTop-l.y-f.y;return{x:d,y:u,width:s.width,height:s.height}}function lt(t){return C(t).position==="static"}function bt(t,e){if(!P(t)||C(t).position==="fixed")return null;if(e)return e(t);let n=t.offsetParent;return S(t)===n&&(n=n.ownerDocument.body),n}function Dt(t,e){const n=O(t);if(rt(t))return n;if(!P(t)){let i=B(t);for(;i&&!X(i);){if(R(i)&&!lt(i))return i;i=B(i)}return n}let o=bt(t,e);for(;o&&qt(o)&&lt(o);)o=bt(o,e);return o&&X(o)&&lt(o)&&!ht(o)?n:o||Ut(t)||n}const re=async function(t){const e=this.getOffsetParent||Dt,n=this.getDimensions,o=await n(t.floating);return{reference:se(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:o.width,height:o.height}}};function ce(t){return C(t).direction==="rtl"}const le={convertOffsetParentRelativeRectToViewportRelativeRect:Jt,getDocumentElement:S,getClippingRect:oe,getOffsetParent:Dt,getElementRects:re,getClientRects:Qt,getDimensions:ie,getScale:I,isElement:R,isRTL:ce};function fe(t,e){let n=null,o;const i=S(t);function r(){var c;clearTimeout(o),(c=n)==null||c.disconnect(),n=null}function s(c,l){c===void 0&&(c=!1),l===void 0&&(l=1),r();const{left:f,top:d,width:u,height:m}=t.getBoundingClientRect();if(c||e(),!u||!m)return;const a=et(d),h=et(i.clientWidth-(f+u)),g=et(i.clientHeight-(d+m)),w=et(f),x={rootMargin:-a+"px "+-h+"px "+-g+"px "+-w+"px",threshold:H(0,j(1,l))||1};let y=!0;function b(v){const E=v[0].intersectionRatio;if(E!==l){if(!y)return s();E?s(!1,E):o=setTimeout(()=>{s(!1,1e-7)},1e3)}y=!1}try{n=new IntersectionObserver(b,{...x,root:i.ownerDocument})}catch{n=new IntersectionObserver(b,x)}n.observe(t)}return s(!0),r}function de(t,e,n,o){o===void 0&&(o={});const{ancestorScroll:i=!0,ancestorResize:r=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:l=!1}=o,f=pt(t),d=i||r?[...f?J(f):[],...J(e)]:[];d.forEach(p=>{i&&p.addEventListener("scroll",n,{passive:!0}),r&&p.addEventListener("resize",n)});const u=f&&c?fe(f,n):null;let m=-1,a=null;s&&(a=new ResizeObserver(p=>{let[x]=p;x&&x.target===f&&a&&(a.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var y;(y=a)==null||y.observe(e)})),n()}),f&&!l&&a.observe(f),a.observe(e));let h,g=l?_(t):null;l&&w();function w(){const p=_(t);g&&(p.x!==g.x||p.y!==g.y||p.width!==g.width||p.height!==g.height)&&n(),g=p,h=requestAnimationFrame(w)}return n(),()=>{var p;d.forEach(x=>{i&&x.removeEventListener("scroll",n),r&&x.removeEventListener("resize",n)}),u==null||u(),(p=a)==null||p.disconnect(),a=null,l&&cancelAnimationFrame(h)}}const me=Yt,he=Xt,ge=It,pe=zt,we=(t,e,n)=>{const o=new Map,i={platform:le,...n},r={...i.platform,_c:o};return _t(t,e,{...i,platform:r})};var xe=Ft.useLayoutEffect;export{ue as _,de as a,pe as b,we as c,ge as f,xe as i,me as o,he as s};
