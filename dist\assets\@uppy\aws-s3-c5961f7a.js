import{g as U}from"../vendor-851db8c1.js";import{K as ot,f as H}from"../@craftjs/core-d3c11b68.js";import{i as ut}from"../@nivo/heatmap-ba1ecfff.js";function M(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw new TypeError("attempted to use private field on non-instance");return e}var lt=0;function et(e){return"__private_"+lt+++"_"+e}function ct(e,t,r){const s=[];return e.forEach(u=>typeof u!="string"?s.push(u):t[Symbol.split](u).forEach((o,i,a)=>{o!==""&&s.push(o),i<a.length-1&&s.push(r)})),s}/**
 * Takes a string with placeholder variables like `%{smart_count} file selected`
 * and replaces it with values from options `{smart_count: 5}`
 *
 * @license https://github.com/airbnb/polyglot.js/blob/master/LICENSE
 * taken from https://github.com/airbnb/polyglot.js/blob/master/lib/polyglot.js#L299
 *
 * @param phrase that needs interpolation, with placeholders
 * @param options with values that will be used to replace placeholders
 */function G(e,t){const r=/\$/g,s="$$$$";let u=[e];if(t==null)return u;for(const o of Object.keys(t))if(o!=="_"){let i=t[o];typeof i=="string"&&(i=r[Symbol.replace](i,s)),u=ct(u,new RegExp(`%\\{${o}\\}`,"g"),i)}return u}const ft=e=>{throw new Error(`missing string: ${e}`)};var I=et("onMissingKey"),F=et("apply");class ht{constructor(t,r){let{onMissingKey:s=ft}=r===void 0?{}:r;Object.defineProperty(this,F,{value:dt}),Object.defineProperty(this,I,{writable:!0,value:void 0}),this.locale={strings:{},pluralize(u){return u===1?0:1}},Array.isArray(t)?t.forEach(M(this,F)[F],this):M(this,F)[F](t),M(this,I)[I]=s}translate(t,r){return this.translateArray(t,r).join("")}translateArray(t,r){let s=this.locale.strings[t];if(s==null&&(M(this,I)[I](t),s=t),typeof s=="object"){if(r&&typeof r.smart_count<"u"){const o=this.locale.pluralize(r.smart_count);return G(s[o],r)}throw new Error("Attempted to use a string with plural forms, but no value was given for %{smart_count}")}if(typeof s!="string")throw new Error("string was not a string");return G(s,r)}}function dt(e){if(!(e!=null&&e.strings))return;const t=this.locale;Object.assign(this.locale,{strings:{...t.strings,...e.strings},pluralize:e.pluralize||t.pluralize})}var mt=function(){var t={},r=t._fns={};t.emit=function(i,a,l,c,f,d,v){var b=s(i);b.length&&u(i,b,[a,l,c,f,d,v])},t.on=function(i,a){r[i]||(r[i]=[]),r[i].push(a)},t.once=function(i,a){function l(){a.apply(this,arguments),t.off(i,l)}this.on(i,l)},t.off=function(i,a){var l=[];if(i&&a){var c=this._fns[i],f=0,d=c?c.length:0;for(f;f<d;f++)c[f]!==a&&l.push(c[f])}l.length?this._fns[i]=l:delete this._fns[i]};function s(o){var i=r[o]?r[o]:[],a=o.indexOf(":"),l=a===-1?[o]:[o.substring(0,a),o.substring(a+1)],c=Object.keys(r),f=0,d=c.length;for(f;f<d;f++){var v=c[f];if(v==="*"&&(i=i.concat(r[v])),l.length===2&&l[0]===v){i=i.concat(r[v]);break}}return i}function u(o,i,a){var l=0,c=i.length;for(l;l<c&&i[l];l++)i[l].event=o,i[l].apply(i[l],a)}return t};const ee=U(mt);let pt="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict",re=(e=21)=>{let t="",r=e;for(;r--;)t+=pt[Math.random()*64|0];return t};var vt=ot,bt=function(){return vt.Date.now()},gt=bt,yt=/\s/;function _t(e){for(var t=e.length;t--&&yt.test(e.charAt(t)););return t}var wt=_t,Pt=wt,Tt=/^\s+/;function Ot(e){return e&&e.slice(0,Pt(e)+1).replace(Tt,"")}var Et=Ot,jt=Et,J=H,xt=ut,V=0/0,Lt=/^[-+]0x[0-9a-f]+$/i,$t=/^0b[01]+$/i,qt=/^0o[0-7]+$/i,St=parseInt;function It(e){if(typeof e=="number")return e;if(xt(e))return V;if(J(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=J(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=jt(e);var r=$t.test(e);return r||qt.test(e)?St(e.slice(2),r?2:8):Lt.test(e)?V:+e}var Ft=It,At=H,C=gt,Z=Ft,Nt="Expected a function",Rt=Math.max,Mt=Math.min;function kt(e,t,r){var s,u,o,i,a,l,c=0,f=!1,d=!1,v=!0;if(typeof e!="function")throw new TypeError(Nt);t=Z(t)||0,At(r)&&(f=!!r.leading,d="maxWait"in r,o=d?Rt(Z(r.maxWait)||0,t):o,v="trailing"in r?!!r.trailing:v);function b(h){var E=s,S=u;return s=u=void 0,c=h,i=e.apply(S,E),i}function it(h){return c=h,a=setTimeout(R,t),f?b(h):i}function nt(h){var E=h-l,S=h-c,Y=t-E;return d?Mt(Y,o-S):Y}function Q(h){var E=h-l,S=h-c;return l===void 0||E>=t||E<0||d&&S>=o}function R(){var h=C();if(Q(h))return X(h);a=setTimeout(R,nt(h))}function X(h){return a=void 0,v&&s?b(h):(s=u=void 0,i)}function st(){a!==void 0&&clearTimeout(a),c=0,s=l=u=a=void 0}function at(){return a===void 0?i:X(C())}function B(){var h=C(),E=Q(h);if(s=arguments,u=this,l=h,E){if(a===void 0)return it(l);if(d)return clearTimeout(a),a=setTimeout(R,t),b(l)}return a===void 0&&(a=setTimeout(R,t)),i}return B.cancel=st,B.flush=at,B}var rt=kt;const ie=U(rt);var Bt=rt,Ct=H,Kt="Expected a function";function Wt(e,t,r){var s=!0,u=!0;if(typeof e!="function")throw new TypeError(Kt);return Ct(r)&&(s="leading"in r?!!r.leading:s,u="trailing"in r?!!r.trailing:u),Bt(e,t,{leading:s,maxWait:t,trailing:u})}var zt=Wt;const ne=U(zt);class se{constructor(t,r){this.uppy=t,this.opts=r??{}}getPluginState(){const{plugins:t}=this.uppy.getState();return(t==null?void 0:t[this.id])||{}}setPluginState(t){const{plugins:r}=this.uppy.getState();this.uppy.setState({plugins:{...r,[this.id]:{...r[this.id],...t}}})}setOptions(t){this.opts={...this.opts,...t},this.setPluginState(void 0),this.i18nInit()}i18nInit(){const t=new ht([this.defaultLocale,this.uppy.locale,this.opts.locale]);this.i18n=t.translate.bind(t),this.i18nArray=t.translateArray.bind(t),this.setPluginState(void 0)}addTarget(t){throw new Error("Extend the addTarget method to add your plugin to another plugin's target")}install(){}uninstall(){}update(t){}afterUpdate(){}}function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw new TypeError("attempted to use private field on non-instance");return e}var Dt=0;function g(e){return"__private_"+Dt+++"_"+e}function Ut(e){return new Error("Cancelled",{cause:e})}function tt(e){if(e!=null){var t;const r=()=>this.abort(e.reason);e.addEventListener("abort",r,{once:!0});const s=()=>{e.removeEventListener("abort",r)};(t=this.then)==null||t.call(this,s,s)}return this}var _=g("activeRequests"),p=g("queuedHandlers"),y=g("paused"),j=g("pauseTimer"),m=g("downLimit"),x=g("upperLimit"),P=g("rateLimitingTimer"),N=g("call"),O=g("queueNext"),z=g("next"),K=g("queue"),D=g("dequeue"),W=g("resume"),L=g("increaseLimit");class ae{constructor(t){Object.defineProperty(this,D,{value:Gt}),Object.defineProperty(this,K,{value:Yt}),Object.defineProperty(this,z,{value:Xt}),Object.defineProperty(this,O,{value:Qt}),Object.defineProperty(this,N,{value:Ht}),Object.defineProperty(this,_,{writable:!0,value:0}),Object.defineProperty(this,p,{writable:!0,value:[]}),Object.defineProperty(this,y,{writable:!0,value:!1}),Object.defineProperty(this,j,{writable:!0,value:void 0}),Object.defineProperty(this,m,{writable:!0,value:1}),Object.defineProperty(this,x,{writable:!0,value:void 0}),Object.defineProperty(this,P,{writable:!0,value:void 0}),Object.defineProperty(this,W,{writable:!0,value:()=>this.resume()}),Object.defineProperty(this,L,{writable:!0,value:()=>{if(n(this,y)[y]){n(this,P)[P]=setTimeout(n(this,L)[L],0);return}n(this,m)[m]=this.limit,this.limit=Math.ceil((n(this,x)[x]+n(this,m)[m])/2);for(let r=n(this,m)[m];r<=this.limit;r++)n(this,O)[O]();n(this,x)[x]-n(this,m)[m]>3?n(this,P)[P]=setTimeout(n(this,L)[L],2e3):n(this,m)[m]=Math.floor(n(this,m)[m]/2)}}),typeof t!="number"||t===0?this.limit=1/0:this.limit=t}run(t,r){return!n(this,y)[y]&&n(this,_)[_]<this.limit?n(this,N)[N](t):n(this,K)[K](t,r)}wrapSyncFunction(t,r){var s=this;return function(){for(var u=arguments.length,o=new Array(u),i=0;i<u;i++)o[i]=arguments[i];const a=s.run(()=>(t(...o),queueMicrotask(()=>a.done()),()=>{}),r);return{abortOn:tt,abort(){a.abort()}}}}wrapPromiseFunction(t,r){var s=this;return function(){for(var u=arguments.length,o=new Array(u),i=0;i<u;i++)o[i]=arguments[i];let a;const l=new Promise((c,f)=>{a=s.run(()=>{let d,v;try{v=Promise.resolve(t(...o))}catch(b){v=Promise.reject(b)}return v.then(b=>{d?f(d):(a.done(),c(b))},b=>{d?f(d):(a.done(),f(b))}),b=>{d=Ut(b)}},r)});return l.abort=c=>{a.abort(c)},l.abortOn=tt,l}}resume(){n(this,y)[y]=!1,clearTimeout(n(this,j)[j]);for(let t=0;t<this.limit;t++)n(this,O)[O]()}pause(t){t===void 0&&(t=null),n(this,y)[y]=!0,clearTimeout(n(this,j)[j]),t!=null&&(n(this,j)[j]=setTimeout(n(this,W)[W],t))}rateLimit(t){clearTimeout(n(this,P)[P]),this.pause(t),this.limit>1&&Number.isFinite(this.limit)&&(n(this,x)[x]=this.limit-1,this.limit=n(this,m)[m],n(this,P)[P]=setTimeout(n(this,L)[L],t))}get isPaused(){return n(this,y)[y]}}function Ht(e){n(this,_)[_]+=1;let t=!1,r;try{r=e()}catch(s){throw n(this,_)[_]-=1,s}return{abort:s=>{t||(t=!0,n(this,_)[_]-=1,r==null||r(s),n(this,O)[O]())},done:()=>{t||(t=!0,n(this,_)[_]-=1,n(this,O)[O]())}}}function Qt(){queueMicrotask(()=>n(this,z)[z]())}function Xt(){if(n(this,y)[y]||n(this,_)[_]>=this.limit||n(this,p)[p].length===0)return;const e=n(this,p)[p].shift();if(e==null)throw new Error("Invariant violation: next is null");const t=n(this,N)[N](e.fn);e.abort=t.abort,e.done=t.done}function Yt(e,t){const r={fn:e,priority:(t==null?void 0:t.priority)||0,abort:()=>{n(this,D)[D](r)},done:()=>{throw new Error("Cannot mark a queued request as done: this indicates a bug")}},s=n(this,p)[p].findIndex(u=>r.priority>u.priority);return s===-1?n(this,p)[p].push(r):n(this,p)[p].splice(s,0,r),r}function Gt(e){const t=n(this,p)[p].indexOf(e);t!==-1&&n(this,p)[p].splice(t,1)}const oe=Symbol("__queue");class ue extends Error{constructor(t,r){r===void 0&&(r=null),super("This looks like a network error, the endpoint might be blocked by an internet provider or a firewall."),this.cause=t,this.isNetworkError=!0,this.request=r}}function le(e){return e?e.readyState!==0&&e.readyState!==4||e.status===0:!1}function w(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw new TypeError("attempted to use private field on non-instance");return e}var Jt=0;function k(e){return"__private_"+Jt+++"_"+e}var T=k("aliveTimer"),$=k("isDone"),A=k("onTimedOut"),q=k("timeout");class ce{constructor(t,r){Object.defineProperty(this,T,{writable:!0,value:void 0}),Object.defineProperty(this,$,{writable:!0,value:!1}),Object.defineProperty(this,A,{writable:!0,value:void 0}),Object.defineProperty(this,q,{writable:!0,value:void 0}),w(this,q)[q]=t,w(this,A)[A]=()=>r(t)}progress(){w(this,$)[$]||w(this,q)[q]>0&&(clearTimeout(w(this,T)[T]),w(this,T)[T]=setTimeout(w(this,A)[A],w(this,q)[q]))}done(){w(this,$)[$]||(clearTimeout(w(this,T)[T]),w(this,T)[T]=void 0,w(this,$)[$]=!0)}}function fe(e){const t=r=>"error"in r&&!!r.error;return e.filter(r=>!t(r))}function he(e){return e.filter(t=>{var r;return!((r=t.progress)!=null&&r.uploadStarted)||!t.isRestored})}export{se as B,ue as N,ce as P,ae as R,ht as T,he as a,le as b,Ft as c,ie as d,ee as e,fe as f,oe as i,re as n,ne as t};
