import{g as L}from"./vendor-851db8c1.js";var b={exports:{}};(function(w,R){(function(){function S(){var t=window,a=document;if("scrollBehavior"in a.documentElement.style&&t.__forceSmoothScrollPolyfill__!==!0)return;var u=t.HTMLElement||t.Element,T=468,i={scroll:t.scroll||t.scrollTo,scrollBy:t.scrollBy,elementScroll:u.prototype.scroll||g,scrollIntoView:u.prototype.scrollIntoView},p=t.performance&&t.performance.now?t.performance.now.bind(t.performance):Date.now;function X(e){var r=["MSIE ","Trident/","Edge/"];return new RegExp(r.join("|")).test(e)}var d=X(t.navigator.userAgent)?1:0;function g(e,r){this.scrollLeft=e,this.scrollTop=r}function Y(e){return .5*(1-<PERSON>.cos(Math.PI*e))}function f(e){if(e===null||typeof e!="object"||e.behavior===void 0||e.behavior==="auto"||e.behavior==="instant")return!0;if(typeof e=="object"&&e.behavior==="smooth")return!1;throw new TypeError("behavior member of ScrollOptions "+e.behavior+" is not a valid value for enumeration ScrollBehavior.")}function h(e,r){if(r==="Y")return e.clientHeight+d<e.scrollHeight;if(r==="X")return e.clientWidth+d<e.scrollWidth}function v(e,r){var o=t.getComputedStyle(e,null)["overflow"+r];return o==="auto"||o==="scroll"}function E(e){var r=h(e,"Y")&&v(e,"Y"),o=h(e,"X")&&v(e,"X");return r||o}function O(e){for(;e!==a.body&&E(e)===!1;)e=e.parentNode||e.host;return e}function y(e){var r=p(),o,n,s,l=(r-e.startTime)/T;l=l>1?1:l,o=Y(l),n=e.startX+(e.x-e.startX)*o,s=e.startY+(e.y-e.startY)*o,e.method.call(e.scrollable,n,s),(n!==e.x||s!==e.y)&&t.requestAnimationFrame(y.bind(t,e))}function c(e,r,o){var n,s,l,m,B=p();e===a.body?(n=t,s=t.scrollX||t.pageXOffset,l=t.scrollY||t.pageYOffset,m=i.scroll):(n=e,s=e.scrollLeft,l=e.scrollTop,m=g),y({scrollable:n,method:m,startTime:B,startX:s,startY:l,x:r,y:o})}t.scroll=t.scrollTo=function(){if(arguments[0]!==void 0){if(f(arguments[0])===!0){i.scroll.call(t,arguments[0].left!==void 0?arguments[0].left:typeof arguments[0]!="object"?arguments[0]:t.scrollX||t.pageXOffset,arguments[0].top!==void 0?arguments[0].top:arguments[1]!==void 0?arguments[1]:t.scrollY||t.pageYOffset);return}c.call(t,a.body,arguments[0].left!==void 0?~~arguments[0].left:t.scrollX||t.pageXOffset,arguments[0].top!==void 0?~~arguments[0].top:t.scrollY||t.pageYOffset)}},t.scrollBy=function(){if(arguments[0]!==void 0){if(f(arguments[0])){i.scrollBy.call(t,arguments[0].left!==void 0?arguments[0].left:typeof arguments[0]!="object"?arguments[0]:0,arguments[0].top!==void 0?arguments[0].top:arguments[1]!==void 0?arguments[1]:0);return}c.call(t,a.body,~~arguments[0].left+(t.scrollX||t.pageXOffset),~~arguments[0].top+(t.scrollY||t.pageYOffset))}},u.prototype.scroll=u.prototype.scrollTo=function(){if(arguments[0]!==void 0){if(f(arguments[0])===!0){if(typeof arguments[0]=="number"&&arguments[1]===void 0)throw new SyntaxError("Value could not be converted");i.elementScroll.call(this,arguments[0].left!==void 0?~~arguments[0].left:typeof arguments[0]!="object"?~~arguments[0]:this.scrollLeft,arguments[0].top!==void 0?~~arguments[0].top:arguments[1]!==void 0?~~arguments[1]:this.scrollTop);return}var e=arguments[0].left,r=arguments[0].top;c.call(this,this,typeof e>"u"?this.scrollLeft:~~e,typeof r>"u"?this.scrollTop:~~r)}},u.prototype.scrollBy=function(){if(arguments[0]!==void 0){if(f(arguments[0])===!0){i.elementScroll.call(this,arguments[0].left!==void 0?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,arguments[0].top!==void 0?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop);return}this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior})}},u.prototype.scrollIntoView=function(){if(f(arguments[0])===!0){i.scrollIntoView.call(this,arguments[0]===void 0?!0:arguments[0]);return}var e=O(this),r=e.getBoundingClientRect(),o=this.getBoundingClientRect();e!==a.body?(c.call(this,e,e.scrollLeft+o.left-r.left,e.scrollTop+o.top-r.top),t.getComputedStyle(e).position!=="fixed"&&t.scrollBy({left:r.left,top:r.top,behavior:"smooth"})):t.scrollBy({left:o.left,top:o.top,behavior:"smooth"})}}w.exports={polyfill:S}})()})(b);var I=b.exports;const C=L(I);export{C as s};
