import{j as s}from"./@nivo/heatmap-ba1ecfff.js";import{r as i}from"./vendor-851db8c1.js";import{u,h as f,b2 as x,M as d}from"./index-08a5dc5b.js";import{L as h}from"./ListClinics-b4d3e775.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";import"./yup-54691517.js";import"./yup-2824f222.js";import"./@hookform/resolvers-67648cca.js";import"./index-91353a7a.js";import"./Skeleton-1e8bf077.js";import"./react-loading-skeleton-3d87d1f5.js";import"./CategoryTagManager-69e8d2f6.js";import"./PlusIcon-7e8d14d7.js";import"./ChevronRightIcon-efb4c46c.js";import"./HistoryComponent-999cafaf.js";import"./DataTable-84e69e98.js";let p=new d;function lt(){i.useState(null);const[a,e]=i.useState(!1);i.useState([]),i.useState(null);const[l,S]=i.useState([]),[o,c]=i.useState(null),{club:t}=u(),n=async r=>{p.setTable("club_permissions");const m=await p.callRestAPI({filter:[`club_id,eq,${r}`]},"GETALL");return m.list.length>0?JSON.parse(m.list[0].permission):null};return i.useEffect(()=>{(async()=>{e(!0);const r=await n(t==null?void 0:t.id);c(r),e(!1)})()},[t==null?void 0:t.id]),s.jsxs("div",{className:"h-full bg-white p-4 sm:p-6 lg:p-8",children:[a&&s.jsx(f,{}),o&&!o.staff.clinics?s.jsxs("div",{className:"flex h-[80vh] flex-col items-center justify-center",children:[s.jsx(x,{className:"mb-4 text-6xl text-gray-400"}),s.jsx("h2",{className:"mb-2 text-xl font-semibold text-gray-900",children:"Access Restricted"}),s.jsx("p",{className:"text-center text-gray-600",children:"You don't have permission to access the clinics for this club"})]}):s.jsx(h,{club:t,sports:l})]})}export{lt as default};
