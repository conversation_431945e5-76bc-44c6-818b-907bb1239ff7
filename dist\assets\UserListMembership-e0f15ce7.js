import{j as e}from"./@nivo/heatmap-ba1ecfff.js";import{M as se}from"./MembershipCard-244db99f.js";import{r as a,f as te,k as re,b as W}from"./vendor-851db8c1.js";import{P as ae,Q as ie,G as oe,A as ne,d as b,t as A,N as le,W as k,f as ce,M as me}from"./index-08a5dc5b.js";import{B as de}from"./BackButton-11ba52b2.js";import{b as xe,c as he}from"./index.esm-c561e951.js";import{S as pe}from"./index.esm-92169588.js";import"./@craftjs/core-d3c11b68.js";import"./@fortawesome/react-fontawesome-13437837.js";import"./@fortawesome/fontawesome-svg-core-4fa3e289.js";import"./react-confirm-alert-cd7ccfe7.js";import"./@tanstack/react-query-20158223.js";import"./@stripe/stripe-js-6b714a86.js";import"./moment-a9aaa855.js";import"./cal-heatmap-cf010ec4.js";import"./react-icons-51bc3cff.js";import"./smoothscroll-polyfill-a5c0a116.js";import"./date-fns-cca0f4f7.js";import"./lodash-91d5d207.js";import"./numeral-ea653b2a.js";import"./@stripe/react-stripe-js-64f0e61f.js";import"./react-hook-form-687afde5.js";import"./react-select-c8303602.js";import"./@mantine/core-8cbffb6d.js";import"./@emotion/react-89b506c3.js";import"./@emotion/cache-9a5b99cd.js";import"./@emotion/utils-8a8f62c5.js";import"./@emotion/serialize-460cad7f.js";import"./@uppy/dashboard-4a19149e.js";import"./@fullcalendar/core-8ccc1ac4.js";import"./@uppy/core-0760343f.js";import"./@uppy/aws-s3-c5961f7a.js";import"./@uppy/compressor-11f993e4.js";import"./@headlessui/react-a5400090.js";import"./@fortawesome/free-solid-svg-icons-0a9c4907.js";import"./@fortawesome/free-regular-svg-icons-0a88e957.js";import"./@fortawesome/free-brands-svg-icons-fae0dcac.js";const ue=f=>{const x={size:24,className:"text-white"},h={Visa:{icon:e.jsx(xe,{...x}),bgColor:"bg-[#1A1F71]"},Mastercard:{icon:e.jsx(he,{...x}),bgColor:"bg-[#EB001B]"},"American Express":{icon:e.jsx(pe,{...x}),bgColor:"bg-[#006FCF]"},Discover:{text:"DISC",bgColor:"bg-[#FF6000]"},"Diners Club":{icon:e.jsx(ae,{...x}),bgColor:"bg-[#0069AA]"},JCB:{icon:e.jsx(ie,{...x}),bgColor:"bg-[#0B4EA2]"},UnionPay:{text:"UP",bgColor:"bg-[#00447C]"}}[f];return h?e.jsx("div",{className:`flex h-8 w-12 items-center justify-center rounded ${h.bgColor} text-white`,children:h.icon||e.jsx("span",{className:"text-sm font-bold",children:h.text})}):null};let d=new me;function es(){var U,P;const[f,x]=a.useState([]),[B,h]=a.useState(!0),[z,I]=a.useState(!1),[J,ge]=a.useState(null),[s,O]=a.useState(null),[_,L]=a.useState(1),[i,Y]=a.useState(null),[q,F]=a.useState(!1),y=te(),[E]=re(),{dispatch:l}=W.useContext(oe),{state:N,dispatch:w}=W.useContext(ne),C=localStorage.getItem("user"),[o,H]=a.useState(null),[p,K]=a.useState(null),m=E.get("familyMemberId"),j=E.get("familyMemberName"),n=N.isFamilyMemberSwitch,u=N.familyMemberDetails,S=a.useMemo(()=>n&&N.user?N.user:m||C,[n,N.user,m,C]),Q=a.useCallback(()=>n&&u?u.first_name:j||"Your",[n,u,j]),v=a.useCallback(()=>n&&u?`${u.first_name} ${u.last_name}`:j||"Your",[n,u,j]),D=a.useCallback(async()=>{var t,r,M;h(!0);try{d.setTable("user");const g=await d.callRestAPI({id:C},"GET");d.setTable("clubs");const c=await d.callRestAPI({id:(t=g==null?void 0:g.model)==null?void 0:t.club_id},"GET");H(c.model),console.log("view model response",(r=c==null?void 0:c.model)==null?void 0:r.membership_settings),x(JSON.parse((M=c==null?void 0:c.model)==null?void 0:M.membership_settings)||[])}catch(g){console.log(g)}finally{h(!1)}},[C]),$=a.useCallback(async()=>{var t;try{I(!0);const{data:r,error:M,message:g}=await d.getCustomerStripeCards();if(console.log(r),M&&b(l,g,5e3),!r)return;const c=(t=r==null?void 0:r.data)==null?void 0:t.find(ee=>{var G,V;return ee.id===((V=(G=r==null?void 0:r.data[0])==null?void 0:G.customer)==null?void 0:V.default_source)});Y(c)}catch(r){console.error("ERROR",r),b(l,r.message,5e3),A(w,r.code)}finally{I(!1)}},[l,w]);a.useEffect(()=>{D(),$()},[D,$]),a.useEffect(()=>{if(o){const t=v(),r=n||m;le({title:r?`Membership for ${t}`:"Membership",path:"/user/membership",clubName:o==null?void 0:o.name,favicon:o==null?void 0:o.club_logo,description:r?`Membership for ${t}`:"Membership"})}},[n,m,v,o==null?void 0:o.name,o==null?void 0:o.club_logo]);const R=t=>{O(t),L(2)},X=async()=>{F(!0);try{if(p!=null&&p.subId){const t=await d.changeStripeSubscription({userId:S,activeSubscriptionId:p.subId,newPlanId:s.plan_id});t.error?(console.error(t.message),b(l,t.message,7500,"error")):(b(l,"Subscription updated successfully",3e3),y("/user/profile?tab=membership"))}else{const t=await d.createStripeSubscription({planId:s.plan_id,userId:S});t.error?(console.error(t.message),b(l,t.message,7500,"error")):(b(l,"Subscription created successfully",3e3),y("/user/profile?tab=membership"))}}catch(t){console.error("Error",t),b(l,t.message,7500,"error"),A(l,t.code)}finally{F(!1)}},T=a.useCallback(async()=>{try{const t=await d.getCustomerStripeSubscription(S);K(t.customer)}catch(t){console.error(t),A(w,t.code)}},[S,w]);a.useEffect(()=>{T()},[T]);const Z=()=>e.jsxs("div",{className:"animate-pulse rounded-lg border border-gray-100 bg-white p-6",children:[e.jsxs("div",{className:"mb-4 flex items-center justify-between",children:[e.jsx("div",{className:"h-5 w-24 rounded bg-gray-200"}),e.jsx("div",{className:"h-4 w-12 rounded bg-gray-200"})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("div",{className:"mb-1 h-7 w-20 rounded bg-gray-200"}),e.jsx("div",{className:"h-3 w-16 rounded bg-gray-200"})]}),e.jsx("div",{className:"mb-6 space-y-2",children:[1,2,3].map(t=>e.jsx("div",{className:"h-3 w-full rounded bg-gray-200"},t))}),e.jsx("div",{className:"h-9 w-full rounded bg-gray-200"})]});return e.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e.jsx("div",{className:"border-b border-gray-200 bg-white",children:e.jsx("div",{className:"mx-auto max-w-4xl px-4 py-4",children:e.jsx(de,{onBack:()=>{_===1?y(-1):L(1)}})})}),_===1&&e.jsxs("div",{className:"mx-auto max-w-4xl px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-semibold text-gray-900",children:n||m?`Membership for ${v()}`:"Membership Plans"}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:n||m?`Choose a plan for ${Q()}`:"Choose the plan that works best for you"})]}),e.jsx("div",{className:"space-y-4",children:B?e.jsx("div",{className:"grid gap-4 sm:grid-cols-2 lg:grid-cols-3",children:Array.from({length:3}).map((t,r)=>e.jsx(Z,{},r))}):(f==null?void 0:f.length)>0?e.jsx("div",{className:"grid gap-4 sm:grid-cols-2 lg:grid-cols-3",children:f.map(t=>e.jsx("div",{children:e.jsx(se,{...t,isCurrentPlan:J===t.id,onSelect:()=>R(t),isActive:(p==null?void 0:p.planId)===t.plan_id})},t.plan_name))}):e.jsxs("div",{className:"py-16 text-center",children:[e.jsx("div",{className:"mx-auto h-12 w-12 text-gray-300",children:e.jsx("svg",{fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:1,children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),e.jsx("h3",{className:"mt-4 text-sm font-medium text-gray-900",children:"No membership plans available"}),e.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"Contact support for assistance"})]})})]}),_===2&&e.jsxs("div",{className:"mx-auto max-w-4xl px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-2xl font-semibold text-gray-900",children:(s==null?void 0:s.price)===0?n||m?`Confirm Selection for ${v()}`:"Confirm Selection":n||m?`Complete Purchase for ${v()}`:"Complete Purchase"}),e.jsx("p",{className:"mt-2 text-sm text-gray-600",children:(s==null?void 0:s.price)===0?"Review and activate your membership":"Review your selection and complete payment"})]}),e.jsxs("div",{className:"grid gap-8 lg:grid-cols-2",children:[e.jsx("div",{className:"order-2 lg:order-1",children:e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-white p-6",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:s==null?void 0:s.plan_name}),e.jsxs("div",{className:"mt-2 text-2xl font-bold text-primaryBlue",children:[k(s==null?void 0:s.price),e.jsxs("span",{className:"ml-1 text-sm font-normal text-gray-500",children:["/",(s==null?void 0:s.billing_interval_count)>1?`${s.billing_interval_count} ${(s==null?void 0:s.billing_interval)||"month"}s`:(s==null?void 0:s.billing_interval)||"month"]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Features included:"}),e.jsx("div",{className:"space-y-2",children:(U=s==null?void 0:s.features)==null?void 0:U.map((t,r)=>e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"flex h-4 w-4 flex-shrink-0 items-center justify-center",children:e.jsx("svg",{className:"h-4 w-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsx("span",{className:"text-sm text-gray-700",children:t.text})]},r))})]})]})}),e.jsx("div",{className:"order-1 lg:order-2",children:e.jsxs("div",{className:"rounded-lg border border-gray-200 bg-white p-6",children:[e.jsx("div",{className:"mb-6",children:e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:(s==null?void 0:s.price)===0?"Subscription":"Payment"})}),e.jsx("div",{className:"mb-6",children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Plan price"}),e.jsx("span",{className:"font-medium text-gray-900",children:k(s==null?void 0:s.price)})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Taxes & fees"}),e.jsx("span",{className:"font-medium text-gray-900",children:k(0)})]}),e.jsx("div",{className:"border-t border-gray-200 pt-3",children:e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"font-semibold text-gray-900",children:"Total"}),e.jsx("span",{className:"font-semibold text-primaryBlue",children:k(s==null?void 0:s.price)})]})})]})}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"mb-3 text-sm font-medium text-gray-900",children:"Payment Method"}),z?e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"h-12 rounded bg-gray-200"})}):i?e.jsxs("div",{className:"flex items-center justify-between rounded border border-gray-200 bg-gray-50 p-3",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[ue(i==null?void 0:i.brand),e.jsxs("div",{children:[e.jsxs("p",{className:"text-sm font-medium text-gray-900",children:[(P=i==null?void 0:i.brand)==null?void 0:P.toUpperCase()," ••••"," ",i==null?void 0:i.last4]}),e.jsx("p",{className:"text-xs text-gray-500",children:"Default payment method"})]})]}),e.jsx("button",{onClick:()=>y("/user/profile?tab=payment-methods"),className:"text-xs font-medium text-primaryBlue hover:text-blue-700",children:"Change"})]}):e.jsx("div",{className:"rounded border border-red-200 bg-red-50 p-3",children:e.jsxs("div",{className:"flex items-start space-x-2",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-4 w-4 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-1 text-xs font-medium text-red-800",children:"Payment method required"}),e.jsx("p",{className:"mb-2 text-xs text-red-700",children:"Add a payment method to continue"}),e.jsx("button",{onClick:()=>y("/user/profile?tab=payment-methods"),className:"text-xs font-medium text-red-600 underline hover:text-red-700",children:"Add Payment Method →"})]})]})})]}),e.jsx(ce,{onClick:X,loading:q,disabled:!i,className:`w-full rounded py-2 text-sm font-medium transition-colors ${i?"bg-primaryBlue text-white hover:bg-blue-700":"cursor-not-allowed bg-gray-300 text-gray-500"}`,children:(s==null?void 0:s.price)===0?"Activate Free Plan":i?"Complete Purchase":"Add payment method to continue"}),e.jsx("p",{className:"mt-4 text-center text-xs text-gray-500",children:"By completing this purchase, you agree to our Terms of Service and Privacy Policy."})]})})]})]})]})}export{es as default};
